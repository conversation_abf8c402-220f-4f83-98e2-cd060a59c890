@charset "UTF-8";
/*
Name: 			theme-elements.css
Written by: 	<PERSON><PERSON> Themes - (http://www.okler.net)
Theme Version:	12.1.0
*/
/* Accordion */
.accordion .card {
	margin-top: 5px;
}

.accordion .card:first-child {
	margin-top: 0;
}

.accordion .card:first-of-type, .accordion .card:not(:first-of-type):not(:last-of-type), .accordion .card:last-of-type {
	border: 1px solid rgba(0, 0, 0, 0.125);
	border-radius: .25rem;
}

.accordion .card:not(:first-of-type) .card-header:first-child {
	border-radius: .25rem;
}

.accordion .card-title {
	font-size: 16px;
}

.accordion .card-header {
	padding: 0;
	border-radius: .25rem;
	border-bottom: 0;
	margin: -1px;
}

.accordion .card-header a {
	cursor: pointer;
	display: block;
	padding: 10px 20px;
}

.accordion .card-header a:hover, .accordion .card-header a:focus {
	text-decoration: none;
}

.accordion .card-header a [class^="icon-"] {
	margin-right: 4px;
	position: relative;
	top: 1px;
}

.accordion .card-header a [class*="fa-"], .accordion .card-header a .icons {
	display: inline-block;
	margin-right: 5px;
	position: relative;
	top: -1px;
}

.accordion .card-header a .icons {
	top: 1px;
}

.accordion.accordion-sm .card-header a {
	padding: 5px 15px;
	font-size: 0.85em;
}

.accordion.accordion-sm .card-body {
	font-size: 0.9em;
}

.accordion.accordion-lg .card-header a {
	padding: 15px 15px;
	font-size: 1em;
	font-weight: 600;
}

.accordion.without-bg .card-default {
	background: transparent !important;
}

.accordion.without-bg .card-default > .card-header {
	background: transparent !important;
}

.accordion.without-bg .card-body {
	padding-top: 0;
}

.accordion.without-borders .card {
	border: 0 !important;
	box-shadow: none;
	border-radius: 0;
}

.accordion.without-borders .card-header a {
	padding-left: 0;
	padding-right: 0;
}

.accordion.without-borders .card-body {
	padding: 0;
}

.accordion.accordion-modern .card:first-of-type, .accordion.accordion-modern .card:not(:first-of-type):not(:last-of-type), .accordion.accordion-modern .card:last-of-type {
	border: 4px solid var(--grey-100);
}

.accordion.accordion-modern .card .card-header {
	background-color: var(--grey-100);
	border-radius: 0;
}

.accordion.accordion-modern-2 > .card {
	border: 1px solid transparent !important;
	transition: ease border-color 300ms;
}

.accordion.accordion-modern-2 > .card .card-header a {
	position: relative;
	padding: 23px 30px;
	background-color: #f4f4f4;
	transition: ease background-color 300ms, ease color 300ms;
	border-radius: var(--border-radius);
}

.accordion.accordion-modern-2 > .card .card-header a:after {
	content: '+';
	position: absolute;
	top: 50%;
	right: 25px;
	font-size: inherit;
	font-weight: inherit;
	transform: translate3d(0, -50%, 0) rotate(45deg);
	transform-origin: 50% 50%;
	transition: ease transform 300ms;
}

.accordion.accordion-modern-2 > .card .card-header a.collapsed:after {
	transform: translate3d(0, -50%, 0) rotate(0deg);
}

.accordion.accordion-modern-2 > .card .card-header + .collapse.show,
.accordion.accordion-modern-2 > .card .card-header + .collapsing {
	border: 1px solid transparent;
	border-top: 0;
}

.accordion.accordion-modern-2 > .card + .card {
	margin-top: 17px;
}

.accordion.accordion-modern-status > .card {
	border: 1px solid transparent !important;
	transition: ease border-color 300ms;
}

.accordion.accordion-modern-status > .card .card-header a {
	position: relative;
	padding: 23px 30px;
	background-color: #f4f4f4;
	transition: ease background-color 300ms, ease color 300ms;
}

.accordion.accordion-modern-status > .card .card-header a:after {
	content: '+';
	position: absolute;
	top: 50%;
	right: 25px;
	font-size: 24px;
	font-weight: 500;
	transform: translate3d(0, -50%, 0) rotate(45deg);
	transform-origin: 50% 50%;
	transition: ease transform 300ms;
}

.accordion.accordion-modern-status > .card .card-header a.collapsed:after {
	transform: translate3d(0, -50%, 0) rotate(0deg);
}

.accordion.accordion-modern-status > .card .card-header + .collapse.show,
.accordion.accordion-modern-status > .card .card-header + .collapsing {
	border: 0;
}

.accordion.accordion-modern-status > .card + .card {
	margin-top: 17px;
}

.accordion.accordion-modern-status.accordion-modern-status-borders > .card {
	border: 1px solid rgba(0, 0, 0, 0.085) !important;
}

.accordion.accordion-modern-status.accordion-modern-status-borders > .card .card-header {
	background: transparent !important;
}

.accordion.accordion-modern-status.accordion-modern-status-borders > .card .card-header a {
	background: transparent !important;
}

.accordion.accordion-modern-status.accordion-modern-status-arrow > .card .card-header a:after {
	transform: translate3d(0, -50%, 0) rotate(180deg);
	font-family: 'Font Awesome 6 Free';
	content: "\f107";
	font-weight: 900;
	font-size: inherit;
}

.accordion.accordion-modern-status.accordion-modern-status-arrow > .card .card-header a.collapsed:after {
	transform: translate3d(0, -50%, 0) rotate(0deg);
}

.accordion.accordion-modern-status.accordion-modern-status-arrow-dark > .card .card-header a:after {
	border-radius: 50%;
	background: var(--dark);
	width: 30px;
	text-align: center;
	height: 30px;
	line-height: 30px;
	color: #FFF;
}

.accordion.accordion-modern-grey-scale-1 .card:first-of-type, .accordion.accordion-modern-grey-scale-1 .card:not(:first-of-type):not(:last-of-type), .accordion.accordion-modern-grey-scale-1 .card:last-of-type {
	border-color: #EDEDED;
}

.accordion.accordion-modern-grey-scale-1 .card .card-header {
	background-color: #EDEDED;
}

/* Accordion - Skin */
.accordion .card-header a {
	color: var(--primary);
}

.accordion.accordion-primary .card-header {
	background-color: var(--primary) !important;
}

.accordion.accordion-primary .card-header a {
	color: var(--primary-inverse);
}

.accordion-modern-status-primary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: var(--primary) !important;
	color: var(--primary-inverse) !important;
}

.accordion-modern-status-primary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: var(--primary) !important;
}

.accordion.accordion-secondary .card-header {
	background-color: var(--secondary) !important;
}

.accordion.accordion-secondary .card-header a {
	color: var(--secondary-inverse);
}

.accordion-modern-status-secondary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: var(--secondary) !important;
	color: var(--secondary-inverse) !important;
}

.accordion-modern-status-secondary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: var(--secondary) !important;
}

.accordion.accordion-tertiary .card-header {
	background-color: var(--tertiary) !important;
}

.accordion.accordion-tertiary .card-header a {
	color: var(--tertiary-inverse);
}

.accordion-modern-status-tertiary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: var(--tertiary) !important;
	color: var(--tertiary-inverse) !important;
}

.accordion-modern-status-tertiary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: var(--tertiary) !important;
}

.accordion.accordion-quaternary .card-header {
	background-color: var(--quaternary) !important;
}

.accordion.accordion-quaternary .card-header a {
	color: var(--quaternary-inverse);
}

.accordion-modern-status-quaternary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: var(--quaternary) !important;
	color: var(--quaternary-inverse) !important;
}

.accordion-modern-status-quaternary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: var(--quaternary) !important;
}

.accordion.accordion-dark .card-header {
	background-color: var(--dark) !important;
}

.accordion.accordion-dark .card-header a {
	color: var(--dark-inverse);
}

.accordion-modern-status-dark.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: var(--dark) !important;
	color: var(--dark-inverse) !important;
}

.accordion-modern-status-dark.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: var(--dark) !important;
}

.accordion.accordion-light .card-header {
	background-color: var(--light) !important;
}

.accordion.accordion-light .card-header a {
	color: var(--light-inverse);
}

.accordion-modern-status-light.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: var(--light) !important;
	color: var(--light-inverse) !important;
}

.accordion-modern-status-light.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: var(--light) !important;
}

/* Animations */
.appear-animation {
	opacity: 0;
}

.appear-animation-visible {
	opacity: 1;
}

.block-appear-animation {
	opacity: 1;
	overflow: hidden;
	position: relative;
}

.block-appear-animation .block-appear-animation-wrapper {
	animation-duration: 1s;
	animation-duration: var(--animate-duration);
	animation-fill-mode: both;
	animation-delay: 300ms;
	opacity: 0;
}

.block-appear-animation.appear-animation-visible .block-appear-animation-wrapper {
	animation-name: fadeIn;
}

/* Transitions */
.transition-2ms {
	transition: all 0.2s ease-in-out;
}

.transition-3ms {
	transition: all 0.3s ease-in-out;
}

.transition-4ms {
	transition: all 0.4s ease-in-out;
}

.transition-5ms {
	transition: all 0.5s ease-in-out;
}

.transition-1s {
	transition: all 1s ease-in-out;
}

/* Transition Magnetic */
.transition-magnetic {
	transition: transform 0.5s cubic-bezier(0.25, 1, 0.2, 1);
}

/* Translate Animations */
.anim-hover-translate-top-0px:hover {
	transform: translateY(0px) !important;
}

.anim-hover-translate-bottom-0px:hover {
	transform: translateY(0px) !important;
}

.anim-hover-translate-left-0px:hover {
	transform: translateX(0px) !important;
}

.anim-hover-translate-right-0px:hover {
	transform: translateX(0px) !important;
}

.anim-hover-translate-top-5px:hover {
	transform: translateY(-5px) !important;
}

.anim-hover-translate-bottom-5px:hover {
	transform: translateY(5px) !important;
}

.anim-hover-translate-left-5px:hover {
	transform: translateX(-5px) !important;
}

.anim-hover-translate-right-5px:hover {
	transform: translateX(5px) !important;
}

.anim-hover-translate-top-10px:hover {
	transform: translateY(-10px) !important;
}

.anim-hover-translate-bottom-10px:hover {
	transform: translateY(10px) !important;
}

.anim-hover-translate-left-10px:hover {
	transform: translateX(-10px) !important;
}

.anim-hover-translate-right-10px:hover {
	transform: translateX(10px) !important;
}

.anim-hover-translate-top-20px:hover {
	transform: translateY(-20px) !important;
}

.anim-hover-translate-bottom-20px:hover {
	transform: translateY(20px) !important;
}

.anim-hover-translate-left-20px:hover {
	transform: translateX(-20px) !important;
}

.anim-hover-translate-right-20px:hover {
	transform: translateX(20px) !important;
}

.anim-hover-translate-top-30px:hover {
	transform: translateY(-30px) !important;
}

.anim-hover-translate-bottom-30px:hover {
	transform: translateY(30px) !important;
}

.anim-hover-translate-left-30px:hover {
	transform: translateX(-30px) !important;
}

.anim-hover-translate-right-30px:hover {
	transform: translateX(30px) !important;
}

.anim-hover-translate-top-40px:hover {
	transform: translateY(-40px) !important;
}

.anim-hover-translate-bottom-40px:hover {
	transform: translateY(40px) !important;
}

.anim-hover-translate-left-40px:hover {
	transform: translateX(-40px) !important;
}

.anim-hover-translate-right-40px:hover {
	transform: translateX(40px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-top-0px {
	transform: translateY(0px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-bottom-0px {
	transform: translateY(0px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-left-0px {
	transform: translateX(0px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-right-0px {
	transform: translateX(0px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-top-5px {
	transform: translateY(-5px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-bottom-5px {
	transform: translateY(5px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-left-5px {
	transform: translateX(-5px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-right-5px {
	transform: translateX(5px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-top-10px {
	transform: translateY(-10px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-bottom-10px {
	transform: translateY(10px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-left-10px {
	transform: translateX(-10px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-right-10px {
	transform: translateX(10px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-top-20px {
	transform: translateY(-20px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-bottom-20px {
	transform: translateY(20px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-left-20px {
	transform: translateX(-20px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-right-20px {
	transform: translateX(20px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-top-30px {
	transform: translateY(-30px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-bottom-30px {
	transform: translateY(30px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-left-30px {
	transform: translateX(-30px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-right-30px {
	transform: translateX(30px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-top-40px {
	transform: translateY(-40px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-bottom-40px {
	transform: translateY(40px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-left-40px {
	transform: translateX(-40px) !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-translate-right-40px {
	transform: translateX(40px) !important;
}

.anim-hover-scale-1-1:hover {
	transform: scale(1.1);
}

.anim-hover-scale-1-2:hover {
	transform: scale(1.2);
}

/* Opacity Animations */
.anim-hover-opacity-0:hover {
	opacity: 0 !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-opacity-0 {
	opacity: 0 !important;
}

.anim-hover-opacity-10:hover {
	opacity: 1 !important;
}

.anim-hover-inner-wrapper:hover .anim-hover-inner-opacity-10 {
	opacity: 1 !important;
}

/* Side Header Arrow Animations */
@keyframes sideMenuArrow {
	0% {
		left: 5px;
	}

	50% {
		left: 10px;
	}

	100% {
		left: 5px;
	}
}

@keyframes sideMenuArrowBack {
	0% {
		right: 5px;
	}

	50% {
		right: 10px;
	}

	100% {
		right: 5px;
	}
}

/* Mask Up */
@keyframes maskUp {
	from {
		transform: translate(0, 100%);
	}

	to {
		transform: translate(0, 0);
	}
}

/* Mask Right */
@keyframes maskRight {
	from {
		transform: translate(-100%, 0);
	}

	to {
		transform: translate(0, 0);
	}
}

/* Mask Down */
@keyframes maskDown {
	from {
		transform: translate(0, -100%);
	}

	to {
		transform: translate(0, 0);
	}
}

/* Mask Left */
@keyframes maskLeft {
	from {
		transform: translate(100%, 0);
	}

	to {
		transform: translate(0, 0);
	}
}

.maskUp {
	animation-name: maskUp;
}

.maskRight {
	animation-name: maskRight;
}

.maskDown {
	animation-name: maskDown;
}

.maskLeft {
	animation-name: maskLeft;
}

/* Text Entrance */
@keyframes textEntrance {
	from {
		opacity: 0;
		transform: translateY(100%) rotateX(-80deg);
	}

	to {
		transform: none;
		opacity: 1;
	}
}

.textEntrance {
	transform-origin: center top;
	transform-style: preserve-3d;
	transition: opacity 0s cubic-bezier(0.215, 0.61, 0.355, 1), transform 0s cubic-bezier(0.215, 0.61, 0.355, 1);
	transition-duration: 0.8s;
	animation-name: textEntrance;
}

@keyframes headerReveal {
	from {
		top: -150px;
	}

	to {
		top: 0;
	}
}

/* Fade In Up Shorter */
@keyframes fadeInUpShorter {
	from {
		opacity: 0;
		transform: translate(0, 50px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInUpShorter {
	animation-name: fadeInUpShorter;
}

/* Fade In Up Shorter Plus */
@keyframes fadeInUpShorterPlus {
	from {
		opacity: 0;
		transform: translate(0, 15px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInUpShorterPlus {
	animation-name: fadeInUpShorterPlus;
}

/* Fade In Left Shorter */
@keyframes fadeInLeftShorter {
	from {
		opacity: 0;
		transform: translate(50px, 0);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInLeftShorter {
	animation-name: fadeInLeftShorter;
}

/* Fade In Left Shorter Plus */
@keyframes fadeInLeftShorterPlus {
	from {
		opacity: 0;
		transform: translate(15px, 0);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInLeftShorterPlus {
	animation-name: fadeInLeftShorterPlus;
}

/* Fade In Right Shorter */
@keyframes fadeInRightShorter {
	from {
		opacity: 0;
		transform: translate(-50px, 0);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInRightShorter {
	animation-name: fadeInRightShorter;
}

/* Fade In Right Shorter Plus */
@keyframes fadeInRightShorterPlus {
	from {
		opacity: 0;
		transform: translate(-15px, 0);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInRightShorterPlus {
	animation-name: fadeInRightShorterPlus;
}

/* Fade In Right Shorter With Opacity */
@keyframes fadeInRightShorterOpacity {
	from {
		opacity: 0;
		transform: translate(-50px, 0);
	}

	to {
		opacity: 0.7;
		transform: none;
	}
}

.fadeInRightShorterOpacity {
	animation-name: fadeInRightShorterOpacity;
}

/* Fade In Down Shorter */
@keyframes fadeInDownShorter {
	from {
		opacity: 0;
		transform: translate(0, -50px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInDownShorter {
	animation-name: fadeInDownShorter;
}

/* Fade In Down Shorter Plus */
@keyframes fadeInDownShorterPlus {
	from {
		opacity: 0;
		transform: translate(0, -15px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInDownShorterPlus {
	animation-name: fadeInDownShorterPlus;
}

/* Fade In Right and Down Shorter */
@keyframes fadeInRightDownShorter {
	from {
		opacity: 0;
		transform: translate(-50px, -50px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInRightDownShorter {
	animation-name: fadeInRightDownShorter;
}

/* Fade In Right and Up Shorter */
@keyframes fadeInRightUpShorter {
	from {
		opacity: 0;
		transform: translate(-50px, 50px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInRightUpShorter {
	animation-name: fadeInRightUpShorter;
}

/* Fade In Left and Down Shorter */
@keyframes fadeInLeftDownShorter {
	from {
		opacity: 0;
		transform: translate(50px, -50px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInLeftDownShorter {
	animation-name: fadeInLeftDownShorter;
}

/* Fade In Left and Up Shorter */
@keyframes fadeInLeftUpShorter {
	from {
		opacity: 0;
		transform: translate(50px, 50px);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

.fadeInLeftUpShorter {
	animation-name: fadeInLeftUpShorter;
}

/* Expand In */
@keyframes expandIn {
	from {
		opacity: 0;
		transform: scale(0.8);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

.expandIn {
	animation-name: expandIn;
}

/* Gradient Transition */
@keyframes gradientTransition {
	0% {
		background-position: 0% 50%;
	}

	50% {
		background-position: 100% 50%;
	}

	100% {
		background-position: 0% 50%;
	}
}

/* Slider Scroll Button Animation */
@keyframes sliderScrollButton {
	0% {
		opacity: 1;
		transform: translate3d(-50%, 0, 0);
	}

	70% {
		opacity: 1;
		transform: translate3d(-50%, 10px, 0);
	}

	100% {
		opacity: 0;
		transform: translate3d(-50%, 10px, 0);
	}
}

/* Arrow Left Right */
@keyframes arrowLeftRight {
	0% {
		transform: translate3d(-1px, 0, 0);
	}

	50% {
		transform: translate3d(1px, 0, 0);
	}

	100% {
		transform: translate3d(-1px, 0, 0);
	}
}

/* Nav Item Arrow */
@keyframes navItemArrow {
	0% {
		position: relative;
		left: -1px;
	}

	50% {
		position: relative;
		left: 3px;
	}

	100% {
		position: relative;
		left: -1px;
	}
}

/* Hover 3d Effect */
.hover-in {
	transition: .1s ease-out !important;
}

.hover-out {
	transition: .1s ease-in !important;
}

.hover-3d {
	transition: none;
}

@keyframes upDownInfinite {
	0% {
		transform: translateY(5px);
	}

	50% {
		transform: translateY(10px);
	}

	100% {
		transform: translateY(5px);
	}
}

.anim-up-down-infinite {
	animation: upDownInfinite 600ms ease;
	animation-iteration-count: infinite;
}

@keyframes leftRightInfinite {
	0% {
		transform: translateX(5px);
	}

	50% {
		transform: translateX(10px);
	}

	100% {
		transform: translateX(5px);
	}
}

.anim-left-right-infinite {
	animation: leftRightInfinite 600ms ease;
	animation-iteration-count: infinite;
}

/* Hotspot */
@keyframes hotspot-pulsate {
	0% {
		transform: scale(1);
		opacity: 0.8;
	}

	45% {
		transform: scale(1.75);
		opacity: 0;
	}
}

@keyframes stop-hotspot-pulsate {
	from {
		opacity: 0.4;
	}

	to {
		transform: scale(2);
		opacity: 0;
	}
}

/* Dot Pulse */
@keyframes dotPulse {
	from {
		opacity: 1;
		transform: scale(0.2);
	}

	to {
		opacity: 0;
		transform: scale(1);
	}
}

.dotPulse {
	animation-name: dotPulse;
	animation-iteration-count: infinite;
	animation-duration: 4s;
}

/* Blur In */
@keyframes blurIn {
	from {
		opacity: 0;
		filter: blur(20px);
		transform: scale(1.3);
	}

	to {
		opacity: 1;
		filter: blur(0.1px);
		transform: none;
	}
}

.blurIn {
	animation-name: blurIn;
}

/* Ken Burns To Right */
@keyframes kenBurnsToRight {
	from {
		transform: scale(1.2);
	}

	to {
		transform: scale(1);
	}
}

.kenBurnsToRight {
	animation-name: kenBurnsToRight;
	animation-timing-function: linear;
	transform-origin: right;
}

/* Ken Burns To Left and Top */
.kenBurnsToRightTop {
	animation-name: kenBurnsToRight;
	animation-timing-function: linear;
	transform-origin: right top;
}

/* Ken Burns To Left */
@keyframes kenBurnsToLeft {
	from {
		transform: scale(1.2);
	}

	to {
		transform: scale(1);
	}
}

.kenBurnsToLeft {
	animation-name: kenBurnsToLeft;
	animation-timing-function: linear;
	transform-origin: left;
}

/* Ken Burns To Left and Top */
.kenBurnsToLeftTop {
	animation-name: kenBurnsToLeft;
	animation-timing-function: linear;
	transform-origin: left top;
}

/* Moving From Top to Bottom */
@keyframes bgPositionBottomToTop {
	from {
		background-position: bottom;
	}

	to {
		background-position: top;
	}
}

.bgPositionBottomToTop {
	animation-name: bgPositionBottomToTop;
	animation-timing-function: linear;
	transform-origin: right;
}

/* Pulse */
@keyframes pulseAnim {
	from {
		transform: scale(1);
		opacity: 1;
	}

	to {
		transform: scale(1.4);
		opacity: 0;
	}
}

.pulseAnim:not(:hover):after {
	animation-name: pulseAnim;
}

.pulseAnim:after {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: inherit;
	border-radius: inherit;
	z-index: -1;
}

.pulseAnim.pulseAnimAnimated:after {
	animation-duration: 1.5s;
	animation-iteration-count: infinite;
	animation-delay: 1s;
}

.pulseAnim:not(i):after {
	z-index: 0;
}

.pulseAnim:not(i) * {
	position: relative;
	z-index: 1;
}

/* Basic Rotation */
@keyframes basicRotation {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

.basicRotation {
	animation-name: basicRotation;
}

/* Block Slide */
@keyframes blockSlideToRight {
	from {
		transform: translate(-110%, 0);
	}

	to {
		transform: translate(110%, 0);
	}
}

@keyframes blockSlideToLeft {
	from {
		transform: translate(110%, 0);
	}

	to {
		transform: translate(-110%, 0);
	}
}

@keyframes blockSlideToTop {
	from {
		transform: translate(0, 110%);
	}

	to {
		transform: translate(0, -110%);
	}
}

@keyframes blockSlideToBottom {
	from {
		transform: translate(0, -110%);
	}

	to {
		transform: translate(0, 110%);
	}
}

.blockSlide:after {
	content: "";
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: var(--dark);
	position: absolute;
	z-index: 1000;
	animation-duration: 1s;
	animation-duration: var(--animate-duration);
	animation-fill-mode: both;
	animation-name: blockSlideToRight;
}

.blockSlideLight:after {
	background: var(--grey-100);
}

.blockSlideToLeft:after {
	animation-name: blockSlideToLeft;
}

.blockSlideToTop:after {
	animation-name: blockSlideToTop;
}

.blockSlideToBottom:after {
	animation-name: blockSlideToBottom;
}

/* SVG Animation Effect 1 */
@keyframes svgAnimationEffect1 {
	from {
		stroke-dasharray: 1000;
		stroke-dashoffset: -500;
	}

	to {
		stroke-dasharray: 1000;
		stroke-dashoffset: 0;
	}
}

.svg-animation-effect-1-hover svg {
	transform: scale(1);
	transition: ease transform 300ms;
}

.svg-animation-effect-1-hover svg path, .svg-animation-effect-1-hover svg polygon, .svg-animation-effect-1-hover svg polyline, .svg-animation-effect-1-hover svg rect {
	transition: ease fill-opacity 300ms;
}

.svg-animation-effect-1-hover:hover svg {
	transform: scale(1.1);
}

.svg-animation-effect-1-hover:hover svg path, .svg-animation-effect-1-hover:hover svg polygon, .svg-animation-effect-1-hover:hover svg polyline, .svg-animation-effect-1-hover:hover svg rect {
	animation-name: svgAnimationEffect1;
	animation-duration: 1s;
	animation-fill-mode: forwards;
	fill-opacity: 0;
	stroke-width: 0.4px;
}

[data-plugin-animated-words] {
	opacity: 0;
}

[data-plugin-animated-words].initialized {
	opacity: 1;
}

.animated-words-wrapper {
	display: inline-block;
}

.animated-words-item {
	display: inline-block;
}

.animated-letters-wrapper {
	display: inline-block;
}

.animated-letters-item {
	display: inline-block;
}

.rotate-animation {
	animation: rotate-animation 10s infinite linear;
}

@keyframes rotate-animation {
	0% {
		transform: rotate(0deg);
	}

	50% {
		transform: rotate(180deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.rotate-animation-inverse {
	animation: rotate-animation-inverse 10s infinite linear;
}

@keyframes rotate-animation-inverse {
	0% {
		transform: rotate(0deg);
	}

	50% {
		transform: rotate(-180deg);
	}

	100% {
		transform: rotate(-360deg);
	}
}

.scroll-rotate {
	position: relative;
	transform: translatex(-50%) translatey(-50%);
}

.highlight-scribble-1 {
	position: absolute;
	left: 0;
	top: 0;
	z-index: -1;
	width: 100%;
	height: 45%;
	top: auto;
	bottom: -25%;
}

.highlight-scribble-1 path {
	stroke-dasharray: 1;
	stroke-dashoffset: 1;
	stroke-linecap: round;
}

@media (max-width: 991px) {
	.highlight-scribble-1 path {
		stroke-dasharray: none;
	}
}

@keyframes highlightScribble1 {
	0% {
		stroke-dashoffset: 1;
		opacity: 0;
	}

	1% {
		opacity: 1;
	}

	100% {
		stroke-dashoffset: 0;
	}
}

.highlightScribble1 path {
	animation: highlightScribble1 1.3s cubic-bezier(0.65, 0, 0.35, 1) forwards;
}

/* Highlighted Word */
@keyframes pencilAnimation {
	from {
		width: 0;
	}

	to {
		width: 74px;
	}
}

@keyframes pencilAnimation2 {
	from {
		width: 0;
	}

	to {
		width: 115px;
	}
}

.highlighted-word {
	font-family: "Shadows Into Light", cursive;
	font-weight: 500;
}

.highlighted-word.highlighted-word-animation-1 {
	position: relative;
}

.highlighted-word.highlighted-word-animation-1:after {
	content: '';
	position: absolute;
	right: calc(100% - 74px);
	bottom: -6px;
	background: url(../img/pencil-blue-line.png);
	width: 0;
	height: 15px;
	animation-name: pencilAnimation;
	animation-duration: 500ms;
	animation-delay: 1s;
	animation-fill-mode: forwards;
	overflow: hidden;
	transform: rotate(180deg);
}

.highlighted-word.highlighted-word-animation-1.highlighted-word-animation-1-end:after {
	right: 0 !important;
}

.highlighted-word.highlighted-word-animation-1.highlighted-word-animation-1-light:after {
	filter: brightness(0) invert(1);
}

.highlighted-word.highlighted-word-animation-1.highlighted-word-animation-1-dark:after {
	background: url(../img/pencil-blue-line-dark.png);
}

.highlighted-word.highlighted-word-animation-1.highlighted-word-animation-1-no-rotate:after {
	transform: rotate(0);
}

.highlighted-word.highlighted-word-animation-1.highlighted-word-animation-1-no-animate:after {
	animation-duration: 0ms;
	animation-delay: 0ms;
}

.highlighted-word.highlighted-word-animation-1.highlighted-word-animation-1-2:after {
	animation-name: pencilAnimation2;
	right: calc(100% - 115px);
	background: url(../img/pencil-blue-line-2.png);
	background-repeat: no-repeat;
}

.highlighted-word.highlighted-word-animation-1.highlighted-word-animation-1-2-dark:after {
	background-image: url(../img/pencil-blue-line-2-dark.png);
}

.highlighted-word.highlighted-word-rotate {
	transform: rotate(-15deg);
	transform-origin: 0 0;
}

/* Animated Letters */
[data-plugin-animated-letters] {
	opacity: 0;
	visibility: hidden;
}

[data-plugin-animated-letters].initialized {
	opacity: 1;
	visibility: visible;
}

/* Animated Letters - Type Writer Effect */
@keyframes typeWriterFlash {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

.letters-wrapper + .typeWriter:last-child {
	position: relative;
	padding-right: 4px;
	opacity: 0.7;
}

.letters-wrapper + .typeWriter:last-child:after {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	height: 100%;
	width: 1px;
	background: #212121;
	animation-name: typeWriterFlash;
	animation-iteration-count: infinite;
	animation-duration: 600ms;
}

/* Arrows */
.arrow {
	background: transparent url(../img/arrows.png) no-repeat 0 0;
	width: 47px;
	height: 120px;
	display: inline-block;
	position: relative;
}

.arrow.arrow-light {
	background-image: url(../img/arrows-dark.png);
}

.arrow.vtl {
	background-position: 0 0;
	width: 47px;
	height: 96px;
}

.arrow.vtr {
	background-position: -101px 0;
	width: 47px;
	height: 96px;
}

.arrow.vbl {
	background-position: 0 -144px;
	width: 47px;
	height: 96px;
}

.arrow.vbr {
	background-position: -101px -144px;
	width: 47px;
	height: 96px;
}

.arrow.hlt {
	background-position: -209px 0;
	width: 120px;
	height: 47px;
}

.arrow.hlb {
	background-position: -209px -101px;
	width: 120px;
	height: 47px;
}

.arrow.hrt {
	background-position: -353px 0;
	width: 120px;
	height: 47px;
}

.arrow.hrb {
	background-position: -353px -101px;
	width: 120px;
	height: 47px;
}

@media (max-width: 575px) {
	.arrow {
		display: none;
	}
}

/* Badges */
.badge.badge-xs {
	padding: 0.3em 0.5em;
}

.badge.badge-sm {
	padding: 0.6em 1em 0.5em;
	line-height: 1.3;
}

.badge.badge-md {
	padding: 1em 1.6em;
	font-size: 85%;
}

.badge.badge-lg {
	padding: 1.5em 2em;
	font-size: 90%;
}

.badge.badge-xl {
	padding: 2em 2.5em;
	font-size: 100%;
}

.badge-ecommerce {
	font-size: 9.6px;
	font-size: 0.6rem;
	font-weight: 600;
	border-radius: 0;
	padding: 6.4px 8px;
	padding: 0.4rem 0.5rem;
}

.badge-comments-count {
	min-width: 24px;
	min-height: 24px;
	line-height: 24px;
}

/* Badges - Skin */
.badge-primary {
	background-color: var(--primary);
}

.badge-secondary {
	background-color: var(--secondary);
}

.badge-tertiary {
	background-color: var(--tertiary);
}

.badge-quaternary {
	background-color: var(--quaternary);
}

.badge-dark {
	background-color: var(--dark);
}

.badge-light {
	background-color: var(--light);
}

/* Circular Bars */
.circular-bar {
	text-align: center;
	margin-bottom: 25px;
}

.circular-bar .circular-bar-chart {
	position: relative;
}

.circular-bar strong {
	display: block;
	font-weight: 600;
	font-size: 18px;
	line-height: 30px;
	position: absolute;
	top: 35%;
	width: 80%;
	left: 10%;
	text-align: center;
}

.circular-bar label {
	display: block;
	font-weight: 100;
	font-size: 17px;
	line-height: 20px;
	position: absolute;
	top: 50%;
	width: 80%;
	left: 10%;
	text-align: center;
}

.circular-bar.only-icon [class*="fa-"], .circular-bar.only-icon .icons {
	display: block;
	font-size: 38px;
	line-height: 30px;
	position: absolute;
	top: 40%;
	width: 80%;
	left: 10%;
	text-align: center;
}

.circular-bar.single-line strong {
	top: 40%;
}

.circular-bar.circular-bar-sm label {
	font-size: 14px;
	top: 36%;
}

.circular-bar.circular-bar-lg strong {
	font-size: 20px;
	top: 40%;
}

/* Circular Bar - Skin */
.circular-bar.only-icon [class*="fa-"], .circular-bar.only-icon .icons {
	color: var(--primary);
}

/* Progress */
.progress {
	border-radius: 25px;
	height: 20px;
	background: #FAFAFA;
	overflow: visible;
}

.progress.progress-border-radius {
	border-radius: 3px;
}

.progress.progress-border-radius .progress-bar {
	border-radius: 3px;
}

.progress.progress-no-border-radius {
	border-radius: 0;
}

.progress.progress-no-border-radius .progress-bar {
	border-radius: 0;
}

.progress.progress-xs {
	height: 5px;
}

.progress.progress-xs .progress-bar {
	line-height: 5px;
	height: auto;
}

.progress.progress-sm {
	height: 10px;
}

.progress.progress-sm .progress-bar {
	line-height: 10px;
	height: auto;
}

.progress.progress-lg {
	height: 35px;
}

.progress.progress-lg .progress-bar {
	font-size: 13px;
	line-height: 35px;
	height: auto;
}

.progress.progress-dark {
	background: #333333;
}

.progress.progress-right-side-value {
	position: relative;
	margin-right: 40px;
}

.progress.progress-right-side-value .progress-bar-value {
	position: absolute;
	top: 50%;
	right: 0;
	transform: translate3d(40px, -50%, 0);
}

.progress-bar {
	background-color: var(--grey-500);
	box-shadow: none;
	position: relative;
	border-radius: 25px;
	overflow: visible;
}

.progress-bar-tooltip {
	position: absolute;
	padding: 4px 8px;
	background-color: var(--dark--200);
	color: var(--light);
	line-height: 15px;
	font-size: 11px;
	display: block;
	position: absolute;
	top: -28px;
	right: 5px;
	border-radius: 3px;
	opacity: 0;
}

.progress-bar-tooltip:after {
	border-color: var(--dark--200) transparent;
	border-style: solid;
	border-width: 5px 5px 0;
	bottom: -5px;
	content: "";
	display: block;
	left: 13px;
	position: absolute;
	width: 0;
}

/* Progress Bar - Skin */
.progress-bar-primary {
	background-color: var(--primary);
}

.progress-bar-secondary {
	background-color: var(--secondary);
}

.progress-bar-tertiary {
	background-color: var(--tertiary);
}

.progress-bar-quaternary {
	background-color: var(--quaternary);
}

.progress-bar-dark {
	background-color: var(--dark);
}

.progress-bar-light {
	background-color: var(--light);
}

/* Breadcrumb */
.breadcrumb {
	background: none;
	margin: 0;
	padding: 0;
	position: relative;
	z-index: 1;
}

.breadcrumb > li {
	display: inline-block;
	font-size: 0.8em;
	text-transform: uppercase;
	text-shadow: none;
}

.breadcrumb > li + li:before {
	color: inherit;
	opacity: 0.5;
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	content: "\f105";
	padding: 0 7px 0 5px;
}

.breadcrumb.breadcrumb-dividers-no-opacity > li + li:before {
	opacity: 1;
}

.breadcrumb.breadcrumb-style-2 > li + li:before {
	content: "/";
}

.breadcrumb.breadcrumb-block {
	background: var(--dark);
	display: inline-block !important;
	padding: 7.2px 19.2px;
	padding: .45rem 1.2rem;
	border-radius: var(--border-radius);
	box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

.breadcrumb.breadcrumb-light {
	color: var(--light);
}

.breadcrumb.breadcrumb-light a {
	color: var(--light);
}

.breadcrumb.breadcrumb-light .active {
	opacity: 0.7;
}

/* Buttons */
.btn {
	font-size: 12.8px;
	font-size: 0.8rem;
	padding: 8.528px 14.928px;
	padding: 0.533rem 0.933rem;
	cursor: pointer;
	font-weight: 600;
}

.btn.btn-xs {
	font-size: 11.2px;
	font-size: 0.7rem;
	padding: 3.2px 8px;
	padding: 0.2rem 0.5rem;
}

.btn.btn-sm {
	font-size: 12.8px;
	font-size: 0.8rem;
	padding: 4.8px 10.4px;
	padding: 0.3rem 0.65rem;
}

.btn.btn-lg {
	font-size: 16px;
	font-size: 1rem;
	padding: 8px 16px;
	padding: 0.5rem 1rem;
}

.btn.btn-xl {
	font-size: 16px;
	font-size: 1rem;
	padding: 12.8px 32px;
	padding: 0.8rem 2rem;
}

.btn.btn-px-1 {
	padding-left: 5.6px;
	padding-left: .35rem;
	padding-right: 5.6px;
	padding-right: .35rem;
}

.btn.btn-px-2 {
	padding-left: 12px;
	padding-left: .75rem;
	padding-right: 12px;
	padding-right: .75rem;
}

.btn.btn-px-3 {
	padding-left: 20px;
	padding-left: 1.25rem;
	padding-right: 20px;
	padding-right: 1.25rem;
}

.btn.btn-px-4 {
	padding-left: 28px;
	padding-left: 1.75rem;
	padding-right: 28px;
	padding-right: 1.75rem;
}

.btn.btn-px-5 {
	padding-left: 38.4px;
	padding-left: 2.4rem;
	padding-right: 38.4px;
	padding-right: 2.4rem;
}

.btn.btn-py-1 {
	padding-top: 5.6px;
	padding-top: .35rem;
	padding-bottom: 5.6px;
	padding-bottom: .35rem;
}

.btn.btn-py-2 {
	padding-top: 12px;
	padding-top: .75rem;
	padding-bottom: 12px;
	padding-bottom: .75rem;
}

.btn.btn-py-3 {
	padding-top: 20px;
	padding-top: 1.25rem;
	padding-bottom: 20px;
	padding-bottom: 1.25rem;
}

.btn.btn-py-4 {
	padding-top: 28px;
	padding-top: 1.75rem;
	padding-bottom: 28px;
	padding-bottom: 1.75rem;
}

.btn.btn-py-5 {
	padding-top: 32px;
	padding-top: 2rem;
	padding-bottom: 32px;
	padding-bottom: 2rem;
}

html body .btn.focus,
html body .btn:focus,
html body .btn.active,
html body .btn:active, html body.body .btn.focus,
html body.body .btn:focus,
html body.body .btn.active,
html body.body .btn:active {
	box-shadow: none !important;
}

/* Dropdown Buttons */
.btn + .dropdown-menu .dropdown-item {
	font-size: 12.8px;
	font-size: 0.8rem;
}

/* Default */
.btn-default {
	color: var(--dark--200);
	background-color: var(--light);
	border-color: var(--grey-500);
	box-shadow: none !important;
}

.btn-default:hover, .btn-default:active, .btn-default:focus {
	color: var(--dark--200);
	background-color: #e6e6e6;
	border-color: #adadad;
}

/* Outline */
.btn-outline {
	border-width: 2px;
	padding: 7.728px 14.928px;
	padding: 0.483rem 0.933rem;
}

.btn-outline.btn-xs {
	padding: 2.4px 8px;
	padding: 0.15rem 0.5rem;
}

.btn-outline.btn-sm {
	padding: 4px 10.4px;
	padding: 0.25rem 0.65rem;
}

.btn-outline.btn-lg {
	padding: 7.2px 16px;
	padding: 0.45rem 1rem;
}

.btn-outline.btn-xl {
	padding: 12px 32px;
	padding: 0.75rem 2rem;
}

.btn-outline.btn-outline-thin {
	border-width: 1px;
}

/* Modern */
.btn-modern {
	text-transform: uppercase;
	font-size: .8em;
	padding: 12.8px 24px;
	padding: 0.8rem 1.5rem;
	font-weight: 600;
}

/* Rounded */
.btn-rounded {
	border-radius: 35px;
}

/* Rounded */
.btn-rounded-icon {
	border-radius: 50px;
	padding: 0;
	width: 50px;
	height: 50px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
}

.btn-rounded-icon[data-hash] > i {
	pointer-events: none;
}

/* 3d */
.btn-3d {
	border-bottom-width: 3px;
}

/* Arrow */
.btn-with-arrow,
.btn-with-arrow-solid {
	position: relative;
	padding-right: 40px;
	font-weight: 600;
}

.btn-with-arrow:not(.btn-with-arrow-solid),
.btn-with-arrow-solid:not(.btn-with-arrow-solid) {
	background-color: transparent;
}

.btn-with-arrow span,
.btn-with-arrow-solid span {
	background-color: var(--grey-500);
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	height: 25px;
	width: 25px;
	border-radius: 50%;
	transform: translateY(-50%);
	right: 8px;
	top: 50%;
	transition: all 0.1s linear;
}

.btn-with-arrow i,
.btn-with-arrow-solid i {
	color: var(--light);
	font-size: 0.8em;
}

.btn-with-arrow.btn-lg,
.btn-with-arrow-solid.btn-lg {
	padding-right: 50px;
}

.btn-with-arrow.btn-lg span,
.btn-with-arrow-solid.btn-lg span {
	height: 35px;
	width: 35px;
	right: 8px;
}

.btn-with-arrow.btn-lg i,
.btn-with-arrow-solid.btn-lg i {
	font-size: 0.9em;
}

.btn-with-arrow.btn-xl,
.btn-with-arrow-solid.btn-xl {
	padding-right: 70px;
}

.btn-with-arrow.btn-xl span,
.btn-with-arrow-solid.btn-xl span {
	height: 50px;
	width: 50px;
	right: 10px;
}

.btn-with-arrow.btn-xl.btn-with-arrow-sm span,
.btn-with-arrow-solid.btn-xl.btn-with-arrow-sm span {
	height: 35px;
	width: 35px;
	right: 15px;
}

.btn-with-arrow.btn-xl i,
.btn-with-arrow-solid.btn-xl i {
	font-size: 1em;
}

/* Swap */
.btn-swap-1 {
	position: relative;
}

.btn-swap-1 > * {
	transition: cubic-bezier(0.55, 0, 0.1, 1) all 300ms;
	white-space: nowrap;
}

.btn-swap-1 > :nth-child(1) {
	max-width: 100%;
	text-align: center;
	top: calc(50% - 10px);
	left: 50%;
	top: 50%;
	margin: 0;
	transform: translate(-50%, -50%);
	position: absolute;
}

.btn-swap-1 > :nth-child(2) {
	display: inline-block;
	transform: translateY(120%);
	opacity: 0;
}

.btn-swap-1:hover > :nth-child(1) {
	transform: translate(-50%, -120%);
	opacity: 0;
}

.btn-swap-1:hover > :nth-child(2) {
	transform: none;
	opacity: 1;
}

@keyframes btnArrowEffect1 {
	0% {
		transform: translateX(0);
	}

	51% {
		transform: translateX(20px);
		opacity: 1;
	}

	52% {
		opacity: 0;
		transform: translateX(-20px);
	}

	53% {
		opacity: 0;
	}

	54% {
		opacity: 1;
	}

	100% {
		transform: translateX(0);
	}
}

.btn-arrow-effect-1:hover > :nth-child(1) {
	animation: btnArrowEffect1 ease-out .3s;
}

/* Link Effect */
.btn-link-underline-1:before {
	height: 1px !important;
}

.btn-link-effect-1 {
	position: relative;
}

.btn-link-effect-1:before {
	content: "";
	position: absolute;
	left: 0;
	bottom: -2px;
	width: 0;
	height: 2px;
	background: var(--dark);
	transition: width 0.25s ease-out;
}

.btn-link-effect-1:hover:before, .btn-link-effect-1:active:before {
	width: 100%;
}

.btn-link-effect-2 {
	position: relative;
}

.btn-link-effect-2:before {
	content: "";
	position: absolute;
	right: 0;
	bottom: -2px;
	width: 0;
	height: 2px;
	background: var(--dark);
	transition: width 0.25s ease-out;
}

.btn-link-effect-2:hover:before, .btn-link-effect-2:active:before {
	width: 100%;
}

.btn-link-effect-3 {
	position: relative;
}

.btn-link-effect-3:before {
	content: "";
	position: absolute;
	left: 50%;
	bottom: -2px;
	width: 0;
	height: 2px;
	background: var(--dark);
	transition: width 0.3s ease 0s, left 0.3s ease 0s;
}

.btn-link-effect-3:hover:before, .btn-link-effect-3:active:before {
	width: 100%;
	left: 0;
}

.btn-link-effect-4 {
	position: relative;
}

.btn-link-effect-4:before {
	content: "";
	position: absolute;
	left: 0;
	bottom: -2px;
	width: 100%;
	height: 2px;
	background: var(--dark);
	transform: scaleX(0);
	transform-origin: bottom right;
	transition: transform .3s;
}

.btn-link-effect-4:hover:before, .btn-link-effect-4:active:before {
	transform-origin: bottom left;
	transform: scaleX(1);
}

.btn-link-effect-5 {
	position: relative;
}

.btn-link-effect-5:before {
	content: "";
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 0;
	background: var(--dark);
	transition: height 0.2s ease;
}

.btn-link-effect-5:hover:before, .btn-link-effect-5:active:before {
	height: 100%;
}

/* Icons	*/
.btn-icon i {
	margin-right: 10px;
}

.btn-icon-right i {
	margin-right: 0;
	margin-left: 10px;
}

/* Dropdown Button */
.btn.dropdown-toggle:after {
	margin-left: .155em;
	vertical-align: .155em;
}

/* Button Light */
html body .btn-light,
html body .btn-outline.btn-light {
	border-color: #d9d9d9 #d9d9d9 #bfbfbf;
	color: var(--default);
}

html body .btn-light.btn-outline-light-opacity-2,
html body .btn-outline.btn-light.btn-outline-light-opacity-2 {
	border-color: rgba(255, 255, 255, 0.2);
}

html body .btn-light:hover, html body .btn-light.hover,
html body .btn-outline.btn-light:hover,
html body .btn-outline.btn-light.hover {
	background-color: #f2f2f2;
	border-color: #e6e5e5 #e6e5e5 #cccccc;
	color: var(--default);
}

html body .btn-light:focus, html body .btn-light.focus,
html body .btn-outline.btn-light:focus,
html body .btn-outline.btn-light.focus {
	box-shadow: 0 0 0 3px rgba(191, 191, 191, 0.5);
}

html body .btn-light.disabled, html body .btn-light:disabled,
html body .btn-outline.btn-light.disabled,
html body .btn-outline.btn-light:disabled {
	border-color: #bfbfbf #bfbfbf #bfbfbf;
}

html body .btn-light:active, html body .btn-light.active,
.show > html body .btn-light.dropdown-toggle,
html body .btn-outline.btn-light:active,
html body .btn-outline.btn-light.active,
.show >
html body .btn-outline.btn-light.dropdown-toggle {
	border-color: #bfbfbf #bfbfbf #bfbfbf;
}

html body .btn-light-2,
html body .btn-outline.btn-light-2 {
	border-color: var(--light);
	color: var(--light);
}

html body .btn-light-2:hover, html body .btn-light-2.hover,
html body .btn-outline.btn-light-2:hover,
html body .btn-outline.btn-light-2.hover {
	background-color: var(--light);
	border-color: var(--light);
	color: #0e0e0e;
}

html body .btn-light-2:focus, html body .btn-light-2.focus,
html body .btn-outline.btn-light-2:focus,
html body .btn-outline.btn-light-2.focus {
	box-shadow: 0 0 0 3px rgba(179, 178, 178, 0.5);
}

html body .btn-light-2.disabled, html body .btn-light-2:disabled,
html body .btn-outline.btn-light-2.disabled,
html body .btn-outline.btn-light-2:disabled {
	border-color: #b3b2b2 #b3b2b2 #b3b2b2;
}

html body .btn-light-2:active, html body .btn-light-2.active,
.show > html body .btn-light-2.dropdown-toggle,
html body .btn-outline.btn-light-2:active,
html body .btn-outline.btn-light-2.active,
.show >
html body .btn-outline.btn-light-2.dropdown-toggle {
	border-color: var(--light);
}

html[dir="rtl"] .input-group .form-control:not(:first-child),
html[dir="rtl"] .input-group-addon:not(:first-child),
html[dir="rtl"] .input-group-btn:not(:first-child) > .btn,
html[dir="rtl"] .input-group-btn:not(:first-child) > .btn-group > .btn,
html[dir="rtl"] .input-group-btn:not(:first-child) > .dropdown-toggle,
html[dir="rtl"] .input-group-btn:not(:last-child) > .btn-group:not(:first-child) > .btn,
html[dir="rtl"] .input-group-btn:not(:last-child) > .btn:not(:first-child) {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}

/* Hamburguer */
.hamburguer-btn {
	background: transparent;
	outline: 0 !important;
	border: none;
	width: 30px;
	height: 30px;
	padding: 0;
	margin: 15px;
	display: inline-block;
	position: relative;
	cursor: pointer;
}

.hamburguer-btn.hamburguer-btn-side-header {
	position: absolute;
	top: 15px;
	right: 8px;
}

.hamburguer-btn.hamburguer-btn-side-header-overlay {
	right: 30px;
	z-index: 9999;
}

.hamburguer-btn .hamburguer {
	pointer-events: none;
}

.hamburguer-btn .hamburguer span {
	position: absolute;
	top: 50%;
	left: 0;
	width: 100%;
	height: 1px;
	transition: ease width 300ms;
	transform: translateY(-50%);
	background: #333;
}

.hamburguer-btn .hamburguer span:nth-child(1) {
	top: 30%;
}

.hamburguer-btn .hamburguer span:nth-child(2) {
	transition-delay: 100ms;
}

.hamburguer-btn .hamburguer span:nth-child(3) {
	top: 70%;
	transition-delay: 200ms;
}

.hamburguer-btn .close {
	opacity: 0;
	pointer-events: none;
	transition: ease all 300ms;
}

.hamburguer-btn .close span {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 0;
	height: 1px;
	background: #333;
	transition: ease all 300ms;
	transform-origin: 50% 0;
}

.hamburguer-btn .close span:nth-child(1) {
	transform: translateX(-50%) rotate(45deg);
}

.hamburguer-btn .close span:nth-child(2) {
	transform: translateX(-50%) rotate(-45deg);
}

.hamburguer-btn.active .hamburguer {
	opacity: 0;
}

.hamburguer-btn.active .hamburguer span {
	width: 0;
}

.hamburguer-btn.active .close {
	opacity: 1;
	transition: ease all 300ms;
}

.hamburguer-btn.active .close span {
	width: 80%;
	transition: ease all 300ms;
}

.hamburguer-btn.active .close span:nth-child(2) {
	transition-delay: 300ms;
}

.hamburguer-btn.hamburguer-btn-light .hamburguer span,
.hamburguer-btn.hamburguer-btn-light .close span {
	background: var(--light);
}

html.overflow-hidden .hamburguer-btn.hamburguer-btn-side-header-overlay {
	right: 13px;
}

/* Hamburguer Sticky Colors */
html.sticky-header-active .hamburguer-btn.hamburguer-btn-sticky-light .hamburguer span,
html.sticky-header-active .hamburguer-btn.hamburguer-btn-sticky-light .close span,
.pin-wrapper .sticky-active:not([class*="sticky-wrapper-effect"]) .hamburguer-btn.hamburguer-btn-sticky-light .hamburguer span,
.pin-wrapper .sticky-active:not([class*="sticky-wrapper-effect"]) .hamburguer-btn.hamburguer-btn-sticky-light .close span,
.pin-wrapper .sticky-effect-active .hamburguer-btn.hamburguer-btn-sticky-light .hamburguer span,
.pin-wrapper .sticky-effect-active .hamburguer-btn.hamburguer-btn-sticky-light .close span {
	background: var(--light);
}

html.sticky-header-active .hamburguer-btn.hamburguer-btn-sticky-dark .hamburguer span,
html.sticky-header-active .hamburguer-btn.hamburguer-btn-sticky-dark .close span,
.pin-wrapper .sticky-active:not([class*="sticky-wrapper-effect"]) .hamburguer-btn.hamburguer-btn-sticky-dark .hamburguer span,
.pin-wrapper .sticky-active:not([class*="sticky-wrapper-effect"]) .hamburguer-btn.hamburguer-btn-sticky-dark .close span,
.pin-wrapper .sticky-effect-active .hamburguer-btn.hamburguer-btn-sticky-dark .hamburguer span,
.pin-wrapper .sticky-effect-active .hamburguer-btn.hamburguer-btn-sticky-dark .close span {
	background: var(--dark);
}

/* Badge */
.btn-badge {
	position: relative;
}

.btn-badge .badge {
	top: -8px;
	top: -0.5rem;
	right: -8px;
	right: -0.5rem;
	position: absolute;
}

/* Gradient */
.btn-gradient:not(.btn-outline) {
	border: 0;
}

.btn-gradient:not(.btn-outline):hover, .btn-gradient:not(.btn-outline).hover {
	border: 0;
}

.btn-gradient:not(.btn-outline):focus, .btn-gradient:not(.btn-outline).focus {
	border: 0;
}

.btn-gradient:not(.btn-outline).disabled, .btn-gradient:not(.btn-outline):disabled {
	border: 0;
}

.btn-gradient:not(.btn-outline):active, .btn-gradient:not(.btn-outline).active {
	border: 0;
}

.btn-gradient.btn-outline {
	border: 2px solid transparent;
	background: transparent;
	border-image-slice: 1 !important;
}

.btn-gradient.btn-outline:hover, .btn-gradient.btn-outline.hover {
	border: 2px solid transparent;
}

.btn-gradient.btn-outline.disabled, .btn-gradient.btn-outline:disabled {
	border: 2px solid transparent;
}

.btn-gradient.btn-outline:active, .btn-gradient.btn-outline.active {
	border: 2px solid transparent;
}

/* Play Button Size */
.play-button-sm {
	width: 35px;
	height: 35px;
}

.play-button-md {
	width: 60px;
	height: 60px;
}

.play-button-lg {
	width: 90px;
	height: 90px;
}

/* Curcle Buttons */
.btn-circle {
	width: 30px;
	height: 30px;
	text-align: center;
	padding: 6px 0;
	font-size: 12px;
	line-height: 1.428571429;
	border-radius: 15px !important;
}

.btn-circle.btn-lg {
	width: 50px;
	height: 50px;
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.6;
	border-radius: 25px !important;
}

.btn-circle.btn-xl {
	width: 70px;
	height: 70px;
	padding: 10px 16px;
	font-size: 24px;
	line-height: 2;
	border-radius: 35px !important;
}

/* Buttons - Skin */
.btn-link {
	color: var(--primary);
}

.btn-link:hover {
	color: var(--primary--100);
}

.btn-link:active {
	color: var(--primary-100);
}

.btn-primary {
	--hover: var(--primary--200);
	background-color: var(--primary);
	border-color: var(--primary) var(--primary) var(--primary-300);
	color: var(--primary-inverse);
}

.btn-primary:hover, .btn-primary.hover {
	border-color: var(--primary--200) var(--primary--200) var(--primary);
	color: var(--primary-inverse);
}

.btn-primary:hover:not(.bg-transparent), .btn-primary.hover:not(.bg-transparent) {
	background-color: var(--primary--200);
}

.btn-primary:focus, .btn-primary.focus {
	border-color: var(--primary-300) var(--primary-300) var(--primary-300);
	color: var(--primary-inverse);
	box-shadow: 0 0 0 3px var(--primary-rgba-50);
}

.btn-primary:focus:not(.bg-transparent), .btn-primary.focus:not(.bg-transparent) {
	background-color: var(--primary-200);
}

.btn-primary.disabled, .btn-primary:disabled {
	border-color: var(--primary) var(--primary) var(--primary-300);
}

.btn-primary.disabled:not(.bg-transparent), .btn-primary:disabled:not(.bg-transparent) {
	background-color: var(--primary);
}

.btn-primary:active, .btn-primary.active {
	border-color: var(--primary-300) var(--primary-300) var(--primary-300) !important;
}

.btn-primary:active:not(.bg-transparent), .btn-primary.active:not(.bg-transparent) {
	background-color: var(--primary-200) !important;
	background-image: none !important;
}

.show > .btn-primary.dropdown-toggle {
	background-color: var(--primary-200) !important;
	background-image: none !important;
	border-color: var(--primary-300) var(--primary-300) var(--primary-300) !important;
}

.btn-check:active + .btn-primary, .btn-check:checked + .btn-primary, .btn-check:focus + .btn-primary {
	background-color: var(--primary-100);
	border-color: var(--primary) var(--primary) var(--primary-100);
	color: var(--primary-inverse);
}

.btn-secondary {
	--hover: var(--secondary--200);
	background-color: var(--secondary);
	border-color: var(--secondary) var(--secondary) var(--secondary-300);
	color: var(--secondary-inverse);
}

.btn-secondary:hover, .btn-secondary.hover {
	border-color: var(--secondary--200) var(--secondary--200) var(--secondary);
	color: var(--secondary-inverse);
}

.btn-secondary:hover:not(.bg-transparent), .btn-secondary.hover:not(.bg-transparent) {
	background-color: var(--secondary--200);
}

.btn-secondary:focus, .btn-secondary.focus {
	border-color: var(--secondary-300) var(--secondary-300) var(--secondary-300);
	color: var(--secondary-inverse);
	box-shadow: 0 0 0 3px var(--secondary-rgba-50);
}

.btn-secondary:focus:not(.bg-transparent), .btn-secondary.focus:not(.bg-transparent) {
	background-color: var(--secondary-200);
}

.btn-secondary.disabled, .btn-secondary:disabled {
	border-color: var(--secondary) var(--secondary) var(--secondary-300);
}

.btn-secondary.disabled:not(.bg-transparent), .btn-secondary:disabled:not(.bg-transparent) {
	background-color: var(--secondary);
}

.btn-secondary:active, .btn-secondary.active {
	border-color: var(--secondary-300) var(--secondary-300) var(--secondary-300) !important;
}

.btn-secondary:active:not(.bg-transparent), .btn-secondary.active:not(.bg-transparent) {
	background-color: var(--secondary-200) !important;
	background-image: none !important;
}

.show > .btn-secondary.dropdown-toggle {
	background-color: var(--secondary-200) !important;
	background-image: none !important;
	border-color: var(--secondary-300) var(--secondary-300) var(--secondary-300) !important;
}

.btn-check:active + .btn-secondary, .btn-check:checked + .btn-secondary, .btn-check:focus + .btn-secondary {
	background-color: var(--secondary-100);
	border-color: var(--secondary) var(--secondary) var(--secondary-100);
	color: var(--secondary-inverse);
}

.btn-tertiary {
	--hover: var(--tertiary--200);
	background-color: var(--tertiary);
	border-color: var(--tertiary) var(--tertiary) var(--tertiary-300);
	color: var(--tertiary-inverse);
}

.btn-tertiary:hover, .btn-tertiary.hover {
	border-color: var(--tertiary--200) var(--tertiary--200) var(--tertiary);
	color: var(--tertiary-inverse);
}

.btn-tertiary:hover:not(.bg-transparent), .btn-tertiary.hover:not(.bg-transparent) {
	background-color: var(--tertiary--200);
}

.btn-tertiary:focus, .btn-tertiary.focus {
	border-color: var(--tertiary-300) var(--tertiary-300) var(--tertiary-300);
	color: var(--tertiary-inverse);
	box-shadow: 0 0 0 3px var(--tertiary-rgba-50);
}

.btn-tertiary:focus:not(.bg-transparent), .btn-tertiary.focus:not(.bg-transparent) {
	background-color: var(--tertiary-200);
}

.btn-tertiary.disabled, .btn-tertiary:disabled {
	border-color: var(--tertiary) var(--tertiary) var(--tertiary-300);
}

.btn-tertiary.disabled:not(.bg-transparent), .btn-tertiary:disabled:not(.bg-transparent) {
	background-color: var(--tertiary);
}

.btn-tertiary:active, .btn-tertiary.active {
	border-color: var(--tertiary-300) var(--tertiary-300) var(--tertiary-300) !important;
}

.btn-tertiary:active:not(.bg-transparent), .btn-tertiary.active:not(.bg-transparent) {
	background-color: var(--tertiary-200) !important;
	background-image: none !important;
}

.show > .btn-tertiary.dropdown-toggle {
	background-color: var(--tertiary-200) !important;
	background-image: none !important;
	border-color: var(--tertiary-300) var(--tertiary-300) var(--tertiary-300) !important;
}

.btn-check:active + .btn-tertiary, .btn-check:checked + .btn-tertiary, .btn-check:focus + .btn-tertiary {
	background-color: var(--tertiary-100);
	border-color: var(--tertiary) var(--tertiary) var(--tertiary-100);
	color: var(--tertiary-inverse);
}

.btn-quaternary {
	--hover: var(--quaternary--200);
	background-color: var(--quaternary);
	border-color: var(--quaternary) var(--quaternary) var(--quaternary-300);
	color: var(--quaternary-inverse);
}

.btn-quaternary:hover, .btn-quaternary.hover {
	border-color: var(--quaternary--200) var(--quaternary--200) var(--quaternary);
	color: var(--quaternary-inverse);
}

.btn-quaternary:hover:not(.bg-transparent), .btn-quaternary.hover:not(.bg-transparent) {
	background-color: var(--quaternary--200);
}

.btn-quaternary:focus, .btn-quaternary.focus {
	border-color: var(--quaternary-300) var(--quaternary-300) var(--quaternary-300);
	color: var(--quaternary-inverse);
	box-shadow: 0 0 0 3px var(--quaternary-rgba-50);
}

.btn-quaternary:focus:not(.bg-transparent), .btn-quaternary.focus:not(.bg-transparent) {
	background-color: var(--quaternary-200);
}

.btn-quaternary.disabled, .btn-quaternary:disabled {
	border-color: var(--quaternary) var(--quaternary) var(--quaternary-300);
}

.btn-quaternary.disabled:not(.bg-transparent), .btn-quaternary:disabled:not(.bg-transparent) {
	background-color: var(--quaternary);
}

.btn-quaternary:active, .btn-quaternary.active {
	border-color: var(--quaternary-300) var(--quaternary-300) var(--quaternary-300) !important;
}

.btn-quaternary:active:not(.bg-transparent), .btn-quaternary.active:not(.bg-transparent) {
	background-color: var(--quaternary-200) !important;
	background-image: none !important;
}

.show > .btn-quaternary.dropdown-toggle {
	background-color: var(--quaternary-200) !important;
	background-image: none !important;
	border-color: var(--quaternary-300) var(--quaternary-300) var(--quaternary-300) !important;
}

.btn-check:active + .btn-quaternary, .btn-check:checked + .btn-quaternary, .btn-check:focus + .btn-quaternary {
	background-color: var(--quaternary-100);
	border-color: var(--quaternary) var(--quaternary) var(--quaternary-100);
	color: var(--quaternary-inverse);
}

.btn-dark {
	--hover: var(--dark--200);
	background-color: var(--dark);
	border-color: var(--dark) var(--dark) var(--dark-300);
	color: var(--dark-inverse);
}

.btn-dark:hover, .btn-dark.hover {
	border-color: var(--dark--200) var(--dark--200) var(--dark);
	color: var(--dark-inverse);
}

.btn-dark:hover:not(.bg-transparent), .btn-dark.hover:not(.bg-transparent) {
	background-color: var(--dark--200);
}

.btn-dark:focus, .btn-dark.focus {
	border-color: var(--dark-300) var(--dark-300) var(--dark-300);
	color: var(--dark-inverse);
	box-shadow: 0 0 0 3px var(--dark-rgba-50);
}

.btn-dark:focus:not(.bg-transparent), .btn-dark.focus:not(.bg-transparent) {
	background-color: var(--dark-200);
}

.btn-dark.disabled, .btn-dark:disabled {
	border-color: var(--dark) var(--dark) var(--dark-300);
}

.btn-dark.disabled:not(.bg-transparent), .btn-dark:disabled:not(.bg-transparent) {
	background-color: var(--dark);
}

.btn-dark:active, .btn-dark.active {
	border-color: var(--dark-300) var(--dark-300) var(--dark-300) !important;
}

.btn-dark:active:not(.bg-transparent), .btn-dark.active:not(.bg-transparent) {
	background-color: var(--dark-200) !important;
	background-image: none !important;
}

.show > .btn-dark.dropdown-toggle {
	background-color: var(--dark-200) !important;
	background-image: none !important;
	border-color: var(--dark-300) var(--dark-300) var(--dark-300) !important;
}

.btn-check:active + .btn-dark, .btn-check:checked + .btn-dark, .btn-check:focus + .btn-dark {
	background-color: var(--dark-100);
	border-color: var(--dark) var(--dark) var(--dark-100);
	color: var(--dark-inverse);
}

.btn-light {
	--hover: var(--light--200);
	background-color: var(--light);
	border-color: var(--light) var(--light) var(--light-300);
	color: var(--light-inverse);
}

.btn-light:hover, .btn-light.hover {
	border-color: var(--light--200) var(--light--200) var(--light);
	color: var(--light-inverse);
}

.btn-light:hover:not(.bg-transparent), .btn-light.hover:not(.bg-transparent) {
	background-color: var(--light--200);
}

.btn-light:focus, .btn-light.focus {
	border-color: var(--light-300) var(--light-300) var(--light-300);
	color: var(--light-inverse);
	box-shadow: 0 0 0 3px var(--light-rgba-50);
}

.btn-light:focus:not(.bg-transparent), .btn-light.focus:not(.bg-transparent) {
	background-color: var(--light-200);
}

.btn-light.disabled, .btn-light:disabled {
	border-color: var(--light) var(--light) var(--light-300);
}

.btn-light.disabled:not(.bg-transparent), .btn-light:disabled:not(.bg-transparent) {
	background-color: var(--light);
}

.btn-light:active, .btn-light.active {
	border-color: var(--light-300) var(--light-300) var(--light-300) !important;
}

.btn-light:active:not(.bg-transparent), .btn-light.active:not(.bg-transparent) {
	background-color: var(--light-200) !important;
	background-image: none !important;
}

.show > .btn-light.dropdown-toggle {
	background-color: var(--light-200) !important;
	background-image: none !important;
	border-color: var(--light-300) var(--light-300) var(--light-300) !important;
}

.btn-check:active + .btn-light, .btn-check:checked + .btn-light, .btn-check:focus + .btn-light {
	background-color: var(--light-100);
	border-color: var(--light) var(--light) var(--light-100);
	color: var(--light-inverse);
}

.btn-outline.btn-primary {
	color: var(--primary);
	background-color: transparent;
	background-image: none;
	border-color: var(--primary);
}

.btn-outline.btn-primary:hover, .btn-outline.btn-primary.hover {
	color: var(--primary-inverse);
	background-color: var(--primary);
	border-color: var(--primary);
}

.btn-outline.btn-primary:focus, .btn-outline.btn-primary.focus {
	box-shadow: 0 0 0 3px var(--primary-rgba-50);
}

.btn-outline.btn-primary.disabled, .btn-outline.btn-primary:disabled {
	color: var(--primary);
	background-color: transparent;
}

.btn-outline.btn-primary:active, .btn-outline.btn-primary.active {
	color: var(--primary-inverse) !important;
	background-color: var(--primary) !important;
	border-color: var(--primary) !important;
}

.show > .btn-outline.btn-primary.dropdown-toggle {
	color: var(--primary-inverse) !important;
	background-color: var(--primary) !important;
	border-color: var(--primary) !important;
}

.btn-outline.btn-secondary {
	color: var(--secondary);
	background-color: transparent;
	background-image: none;
	border-color: var(--secondary);
}

.btn-outline.btn-secondary:hover, .btn-outline.btn-secondary.hover {
	color: var(--secondary-inverse);
	background-color: var(--secondary);
	border-color: var(--secondary);
}

.btn-outline.btn-secondary:focus, .btn-outline.btn-secondary.focus {
	box-shadow: 0 0 0 3px var(--secondary-rgba-50);
}

.btn-outline.btn-secondary.disabled, .btn-outline.btn-secondary:disabled {
	color: var(--secondary);
	background-color: transparent;
}

.btn-outline.btn-secondary:active, .btn-outline.btn-secondary.active {
	color: var(--secondary-inverse) !important;
	background-color: var(--secondary) !important;
	border-color: var(--secondary) !important;
}

.show > .btn-outline.btn-secondary.dropdown-toggle {
	color: var(--secondary-inverse) !important;
	background-color: var(--secondary) !important;
	border-color: var(--secondary) !important;
}

.btn-outline.btn-tertiary {
	color: var(--tertiary);
	background-color: transparent;
	background-image: none;
	border-color: var(--tertiary);
}

.btn-outline.btn-tertiary:hover, .btn-outline.btn-tertiary.hover {
	color: var(--tertiary-inverse);
	background-color: var(--tertiary);
	border-color: var(--tertiary);
}

.btn-outline.btn-tertiary:focus, .btn-outline.btn-tertiary.focus {
	box-shadow: 0 0 0 3px var(--tertiary-rgba-50);
}

.btn-outline.btn-tertiary.disabled, .btn-outline.btn-tertiary:disabled {
	color: var(--tertiary);
	background-color: transparent;
}

.btn-outline.btn-tertiary:active, .btn-outline.btn-tertiary.active {
	color: var(--tertiary-inverse) !important;
	background-color: var(--tertiary) !important;
	border-color: var(--tertiary) !important;
}

.show > .btn-outline.btn-tertiary.dropdown-toggle {
	color: var(--tertiary-inverse) !important;
	background-color: var(--tertiary) !important;
	border-color: var(--tertiary) !important;
}

.btn-outline.btn-quaternary {
	color: var(--quaternary);
	background-color: transparent;
	background-image: none;
	border-color: var(--quaternary);
}

.btn-outline.btn-quaternary:hover, .btn-outline.btn-quaternary.hover {
	color: var(--quaternary-inverse);
	background-color: var(--quaternary);
	border-color: var(--quaternary);
}

.btn-outline.btn-quaternary:focus, .btn-outline.btn-quaternary.focus {
	box-shadow: 0 0 0 3px var(--quaternary-rgba-50);
}

.btn-outline.btn-quaternary.disabled, .btn-outline.btn-quaternary:disabled {
	color: var(--quaternary);
	background-color: transparent;
}

.btn-outline.btn-quaternary:active, .btn-outline.btn-quaternary.active {
	color: var(--quaternary-inverse) !important;
	background-color: var(--quaternary) !important;
	border-color: var(--quaternary) !important;
}

.show > .btn-outline.btn-quaternary.dropdown-toggle {
	color: var(--quaternary-inverse) !important;
	background-color: var(--quaternary) !important;
	border-color: var(--quaternary) !important;
}

.btn-outline.btn-dark {
	color: var(--dark);
	background-color: transparent;
	background-image: none;
	border-color: var(--dark);
}

.btn-outline.btn-dark:hover, .btn-outline.btn-dark.hover {
	color: var(--dark-inverse);
	background-color: var(--dark);
	border-color: var(--dark);
}

.btn-outline.btn-dark:focus, .btn-outline.btn-dark.focus {
	box-shadow: 0 0 0 3px var(--dark-rgba-50);
}

.btn-outline.btn-dark.disabled, .btn-outline.btn-dark:disabled {
	color: var(--dark);
	background-color: transparent;
}

.btn-outline.btn-dark:active, .btn-outline.btn-dark.active {
	color: var(--dark-inverse) !important;
	background-color: var(--dark) !important;
	border-color: var(--dark) !important;
}

.show > .btn-outline.btn-dark.dropdown-toggle {
	color: var(--dark-inverse) !important;
	background-color: var(--dark) !important;
	border-color: var(--dark) !important;
}

.btn-outline.btn-light {
	color: var(--light);
	background-color: transparent;
	background-image: none;
	border-color: var(--light);
}

.btn-outline.btn-light:hover, .btn-outline.btn-light.hover {
	color: var(--light-inverse);
	background-color: var(--light);
	border-color: var(--light);
}

.btn-outline.btn-light:focus, .btn-outline.btn-light.focus {
	box-shadow: 0 0 0 3px var(--light-rgba-50);
}

.btn-outline.btn-light.disabled, .btn-outline.btn-light:disabled {
	color: var(--light);
	background-color: transparent;
}

.btn-outline.btn-light:active, .btn-outline.btn-light.active {
	color: var(--light-inverse) !important;
	background-color: var(--light) !important;
	border-color: var(--light) !important;
}

.show > .btn-outline.btn-light.dropdown-toggle {
	color: var(--light-inverse) !important;
	background-color: var(--light) !important;
	border-color: var(--light) !important;
}

.btn-with-arrow.btn-primary {
	background-color: transparent;
	border-color: transparent;
	color: var(--primary);
}

.btn-with-arrow.btn-primary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: var(--primary) !important;
}

.btn-with-arrow.btn-primary span {
	background-color: var(--primary);
	box-shadow: 2px 3px 18px -3px var(--primary);
}

.btn-with-arrow.btn-primary.btn-outline {
	border-color: var(--primary) !important;
}

.btn-with-arrow.btn-primary.btn-outline:hover span {
	background-color: var(--primary--100) !important;
	box-shadow: none;
}

.btn-with-arrow:not(.btn-with-arrow-solid) {
	background: transparent !important;
	border-color: transparent !important;
}

.btn-with-arrow-solid.btn-primary span {
	background-color: var(--primary-100);
}

.btn-with-arrow.btn-secondary {
	background-color: transparent;
	border-color: transparent;
	color: var(--secondary);
}

.btn-with-arrow.btn-secondary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: var(--secondary) !important;
}

.btn-with-arrow.btn-secondary span {
	background-color: var(--secondary);
	box-shadow: 2px 3px 18px -3px var(--secondary);
}

.btn-with-arrow.btn-secondary.btn-outline {
	border-color: var(--secondary) !important;
}

.btn-with-arrow.btn-secondary.btn-outline:hover span {
	background-color: var(--secondary--100) !important;
	box-shadow: none;
}

.btn-with-arrow:not(.btn-with-arrow-solid) {
	background: transparent !important;
	border-color: transparent !important;
}

.btn-with-arrow-solid.btn-secondary span {
	background-color: var(--secondary-100);
}

.btn-with-arrow.btn-tertiary {
	background-color: transparent;
	border-color: transparent;
	color: var(--tertiary);
}

.btn-with-arrow.btn-tertiary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: var(--tertiary) !important;
}

.btn-with-arrow.btn-tertiary span {
	background-color: var(--tertiary);
	box-shadow: 2px 3px 18px -3px var(--tertiary);
}

.btn-with-arrow.btn-tertiary.btn-outline {
	border-color: var(--tertiary) !important;
}

.btn-with-arrow.btn-tertiary.btn-outline:hover span {
	background-color: var(--tertiary--100) !important;
	box-shadow: none;
}

.btn-with-arrow:not(.btn-with-arrow-solid) {
	background: transparent !important;
	border-color: transparent !important;
}

.btn-with-arrow-solid.btn-tertiary span {
	background-color: var(--tertiary-100);
}

.btn-with-arrow.btn-quaternary {
	background-color: transparent;
	border-color: transparent;
	color: var(--quaternary);
}

.btn-with-arrow.btn-quaternary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: var(--quaternary) !important;
}

.btn-with-arrow.btn-quaternary span {
	background-color: var(--quaternary);
	box-shadow: 2px 3px 18px -3px var(--quaternary);
}

.btn-with-arrow.btn-quaternary.btn-outline {
	border-color: var(--quaternary) !important;
}

.btn-with-arrow.btn-quaternary.btn-outline:hover span {
	background-color: var(--quaternary--100) !important;
	box-shadow: none;
}

.btn-with-arrow:not(.btn-with-arrow-solid) {
	background: transparent !important;
	border-color: transparent !important;
}

.btn-with-arrow-solid.btn-quaternary span {
	background-color: var(--quaternary-100);
}

.btn-with-arrow.btn-dark {
	background-color: transparent;
	border-color: transparent;
	color: var(--dark);
}

.btn-with-arrow.btn-dark:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: var(--dark) !important;
}

.btn-with-arrow.btn-dark span {
	background-color: var(--dark);
	box-shadow: 2px 3px 18px -3px var(--dark);
}

.btn-with-arrow.btn-dark.btn-outline {
	border-color: var(--dark) !important;
}

.btn-with-arrow.btn-dark.btn-outline:hover span {
	background-color: var(--dark--100) !important;
	box-shadow: none;
}

.btn-with-arrow:not(.btn-with-arrow-solid) {
	background: transparent !important;
	border-color: transparent !important;
}

.btn-with-arrow-solid.btn-dark span {
	background-color: var(--dark-100);
}

.btn-with-arrow.btn-light {
	background-color: transparent;
	border-color: transparent;
	color: var(--light);
}

.btn-with-arrow.btn-light:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: var(--light) !important;
}

.btn-with-arrow.btn-light span {
	background-color: var(--light);
	box-shadow: 2px 3px 18px -3px var(--light);
}

.btn-with-arrow.btn-light.btn-outline {
	border-color: var(--light) !important;
}

.btn-with-arrow.btn-light.btn-outline:hover span {
	background-color: var(--light--100) !important;
	box-shadow: none;
}

.btn-with-arrow:not(.btn-with-arrow-solid) {
	background: transparent !important;
	border-color: transparent !important;
}

.btn-with-arrow-solid.btn-light span {
	background-color: var(--light-100);
}

.btn-gradient:not(.btn-outline) {
	background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%) !important;
	color: var(--primary-inverse);
}

.btn-gradient:not(.btn-outline):hover, .btn-gradient:not(.btn-outline).hover {
	background: linear-gradient(135deg, var(--primary--200) 0%, var(--secondary--200) 80%) !important;
	color: var(--primary-inverse);
}

.btn-gradient:not(.btn-outline):focus, .btn-gradient:not(.btn-outline).focus {
	box-shadow: 0 0 0 3px var(--primary-rgba-50);
}

.btn-gradient:not(.btn-outline).disabled, .btn-gradient:not(.btn-outline):disabled {
	background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%) !important;
}

.btn-gradient:not(.btn-outline):active, .btn-gradient:not(.btn-outline).active {
	background: linear-gradient(135deg, var(--primary-200) 0%, var(--secondary-200) 80%) !important;
	color: var(--primary-inverse) !important;
}

.btn-gradient.btn-outline {
	-o-border-image: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%);
	border-image: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%);
	color: var(--primary);
}

.btn-gradient.btn-outline:hover, .btn-gradient.btn-outline.hover {
	-o-border-image: linear-gradient(135deg, var(--primary--200) 0%, var(--secondary--200) 80%);
	border-image: linear-gradient(135deg, var(--primary--200) 0%, var(--secondary--200) 80%);
	color: var(--primary--200);
}

.btn-gradient.btn-outline:focus, .btn-gradient.btn-outline.focus {
	box-shadow: 0 0 0 3px var(--primary-rgba-50);
}

.btn-gradient.btn-outline.disabled, .btn-gradient.btn-outline:disabled {
	-o-border-image: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%);
	border-image: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%);
}

.btn-gradient.btn-outline:active, .btn-gradient.btn-outline.active {
	-o-border-image: linear-gradient(135deg, var(--primary-200) 0%, var(--secondary-200) 80%);
	border-image: linear-gradient(135deg, var(--primary-200) 0%, var(--secondary-200) 80%);
	color: var(--primary-200);
}

.btn-gradient-primary:not(.btn-outline) {
	background: linear-gradient(135deg, var(--primary--200) 0%, var(--primary-100) 80%) !important;
	color: var(--primary-inverse);
}

.btn-gradient-primary:not(.btn-outline):hover, .btn-gradient-primary:not(.btn-outline).hover {
	background: linear-gradient(135deg, var(--primary--100) 0%, var(--primary--200) 80%) !important;
	color: var(--primary-inverse);
}

.btn-gradient-primary:not(.btn-outline):focus, .btn-gradient-primary:not(.btn-outline).focus {
	box-shadow: 0 0 0 3px var(--primary-rgba-50);
}

.btn-gradient-primary:not(.btn-outline).disabled, .btn-gradient-primary:not(.btn-outline):disabled {
	background: linear-gradient(135deg, var(--primary--200) 80%, var(--primary-100) 0%) !important;
}

.btn-gradient-primary:not(.btn-outline):active, .btn-gradient-primary:not(.btn-outline).active {
	background: linear-gradient(135deg, var(--primary--100) 0%, var(--primary--200) 80%) !important;
	color: var(--primary-inverse) !important;
}

.btn-gradient-primary.btn-outline {
	-o-border-image: linear-gradient(135deg, var(--primary) 0%, var(--primary--200) 80%);
	border-image: linear-gradient(135deg, var(--primary) 0%, var(--primary--200) 80%);
	color: var(--primary);
}

.btn-gradient-primary.btn-outline:hover, .btn-gradient-primary.btn-outline.hover {
	-o-border-image: linear-gradient(135deg, var(--primary--200) 0%, var(--primary--200) 80%);
	border-image: linear-gradient(135deg, var(--primary--200) 0%, var(--primary--200) 80%);
	color: var(--primary--200);
}

.btn-gradient-primary.btn-outline:focus, .btn-gradient-primary.btn-outline.focus {
	box-shadow: 0 0 0 3px var(--primary-rgba-50);
}

.btn-gradient-primary.btn-outline.disabled, .btn-gradient-primary.btn-outline:disabled {
	-o-border-image: linear-gradient(135deg, var(--primary) 0%, var(--primary--200) 80%);
	border-image: linear-gradient(135deg, var(--primary) 0%, var(--primary--200) 80%);
}

.btn-gradient-primary.btn-outline:active, .btn-gradient-primary.btn-outline.active {
	-o-border-image: linear-gradient(135deg, var(--primary--200) 0%, var(--primary--200) 80%);
	border-image: linear-gradient(135deg, var(--primary--200) 0%, var(--primary--200) 80%);
	color: var(--primary--200);
}

/* Effect */
.btn.btn-effect-1 {
	transition: 0.25s;
}

.btn.btn-effect-1:hover, .btn.btn-effect-1:focus {
	box-shadow: inset 0 0 0 2em var(--hover);
}

.btn.btn-effect-2 {
	transition: 0.25s;
}

.btn.btn-effect-2:hover, .btn.btn-effect-2:focus {
	animation: btn-pulse 1s;
	box-shadow: 0 0 0 2em rgba(255, 255, 255, 0);
}

.btn.btn-effect-3 {
	transition: 0.25s;
}

.btn.btn-effect-3:hover, .btn.btn-effect-3:focus {
	box-shadow: inset -3.5em 0 0 0 var(--hover), inset 3.5em 0 0 0 var(--hover);
}

.btn.btn-effect-4 {
	transition: 0.25s;
}

.btn.btn-effect-4:hover, .btn.btn-effect-4:focus {
	box-shadow: 0 0.5em 0.5em -0.4em var(--hover);
	transform: translateY(-0.25em);
}

.btn.btn-effect-5 {
	transition: 0.25s;
}

.btn.btn-effect-5:hover, .btn.btn-effect-5:focus {
	box-shadow: inset 0 -3.25em 0 0 var(--hover);
}

.btn.btn-effect-6 {
	transition: 0.25s;
}

.btn.btn-effect-6:hover, .btn.btn-effect-6:focus {
	box-shadow: inset 6.5em 0 0 0 var(--hover);
}

.btn.btn-effect-7 {
	position: relative;
}

.btn.btn-effect-7:before {
	background: var(--hover);
	content: "";
	display: block;
	width: 100%;
	height: 100%;
	margin: auto;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	z-index: 0;
	transition: transform 0.2s cubic-bezier(0.4, 0.3, 0.35, 0.9) 0s;
	transform: scaleX(0);
	transform-origin: right center;
}

.btn.btn-effect-7:hover:before, .btn.btn-effect-7:focus:before {
	transform: scale(1);
	transform-origin: left center;
}

.btn.btn-effect-7 span {
	position: relative;
	z-index: 1;
}

@keyframes btn-pulse {
	0% {
		box-shadow: 0 0 0 0 var(--hover);
	}
}

/* Close */
.btn-close:focus {
	box-shadow: none;
}

/* Pagination */
.pagination {
	position: relative;
	z-index: 1;
}

.pagination > li > a,
.pagination > li > span,
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
	color: var(--grey-500);
	box-shadow: none !important;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
	background-color: var(--grey-500);
	border-color: var(--grey-500);
	box-shadow: none !important;
}

.pagination.pagination-rounded .page-item {
	margin-right: 8px;
	margin-right: .5rem;
}

.pagination.pagination-rounded .page-item:last-child {
	margin-right: 0;
}

.pagination.pagination-rounded .page-link {
	border-radius: 50% !important;
	padding: 0;
	width: 30px;
	text-align: center;
	height: 30px;
	line-height: 30px;
}

.pagination.pagination-rounded.pagination-md .page-link {
	padding: 0;
	width: 40px;
	text-align: center;
	height: 40px;
	line-height: 40px;
	font-size: 16px;
	font-size: 1rem;
}

.pagination.pagination-rounded.pagination-lg .page-link {
	padding: 0;
	width: 60px;
	text-align: center;
	height: 60px;
	line-height: 60px;
}

/* Pagination - Skin */
.pagination > a,
.pagination > a:hover,
.pagination > a:focus,
.pagination > li > a,
.pagination > li > span,
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
	color: var(--primary);
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus,
.pagination .page-item.active .page-link {
	background-color: var(--primary);
	border-color: var(--primary);
	color: var(--primary-inverse);
}

body.dark .pagination > li > a,
body.dark .pagination > li > span,
body.dark .pagination > li > a:hover,
body.dark .pagination > li > span:hover,
body.dark .pagination > li > a:focus,
body.dark .pagination > li > span:focus {
	color: var(--primary);
}

body.dark .pagination > .active > a,
body.dark .pagination > .active > span,
body.dark .pagination > .active > a:hover,
body.dark .pagination > .active > span:hover,
body.dark .pagination > .active > a:focus,
body.dark .pagination > .active > span:focus {
	background-color: var(--primary);
	border-color: var(--primary);
}

.pagination > .active > a,
body.dark .pagination > .active > a {
	border-color: var(--primary) var(--primary) var(--primary-300);
}

/* Read More */
.read-more,
.learn-more {
	display: inline-block;
	white-space: nowrap;
}

.learn-more-hover-animate-icon > i {
	transition: ease transform 300ms;
}

.learn-more-hover-animate-icon:hover > i {
	transform: translate3d(5px, 0, 0);
}

/* Call to Action */
.call-to-action {
	display: flex;
	flex-wrap: wrap;
	position: relative;
	padding: 32px 55px;
	clear: both;
}

.call-to-action::after {
	clear: both;
	content: "";
	display: block;
}

@media (max-width: 767px) {
	.call-to-action {
		padding: 10px 10px;
	}
}

.call-to-action h3 {
	text-transform: none;
	color: var(--dark);
	padding: 0;
	margin: 0 0 9px;
}

.call-to-action p {
	font-size: 1.2em;
}

.call-to-action > [class*="col-"] {
	display: flex;
	align-items: center;
}

.call-to-action > [class*="col-"]:nth-child(2) {
	justify-content: flex-end;
	text-align: center;
}

@media (max-width: 767px) {
	.call-to-action > [class*="col-"]:nth-child(2) {
		justify-content: center;
	}
}

.call-to-action .container > .row, .call-to-action .container-fluid > .row {
	display: flex;
	flex-wrap: wrap;
}

.call-to-action .container > .row > [class*="col-"], .call-to-action .container-fluid > .row > [class*="col-"] {
	display: flex;
	align-items: center;
}

.call-to-action .container > .row > [class*="col-"]:nth-child(2), .call-to-action .container-fluid > .row > [class*="col-"]:nth-child(2) {
	justify-content: flex-end;
	text-align: center;
}

@media (max-width: 767px) {
	.call-to-action .container > .row > [class*="col-"]:nth-child(2), .call-to-action .container-fluid > .row > [class*="col-"]:nth-child(2) {
		justify-content: center;
	}
}

@media (max-width: 767px) {
	.call-to-action > [class*="col-"]:not([class*="col-xs"]) {
		width: 100%;
	}

	.call-to-action .container > .row [class*="col-"]:not([class*="col-xs"]) {
		width: 100%;
	}
}

.call-to-action .call-to-action-content {
	text-align: center;
}

.call-to-action.call-to-action-big {
	padding: 63px 55px;
}

@media (max-width: 575px) {
	.call-to-action.call-to-action-big {
		padding: 63px 0;
	}
}

.call-to-action.call-to-action-front {
	z-index: 1;
}

.call-to-action.call-to-action-default {
	background: var(--grey-100);
}

.call-to-action.call-to-action-strong-grey {
	background: #EDEDED;
}

.call-to-action.call-to-action-dark {
	background: var(--dark--200);
}

.call-to-action.call-to-action-dark h3, .call-to-action.call-to-action-dark p {
	color: var(--light);
}

.call-to-action.with-borders {
	border-top: 1px solid #DFDFDF;
	border-bottom: 1px solid #DFDFDF;
	border-left: 1px solid #ECECEC;
	border-right: 1px solid #ECECEC;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.04);
}

.call-to-action.with-full-borders {
	border: 2px solid var(--grey-500);
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.04);
}

.call-to-action.featured {
	background: var(--light);
	margin: 0;
	border-top: 3px solid var(--grey-500);
	border-bottom: 1px solid #DFDFDF;
	border-left: 1px solid #ECECEC;
	border-right: 1px solid #ECECEC;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.04);
}

.call-to-action.with-button-arrow .call-to-action-btn {
	position: relative;
}

@media (min-width: 992px) {
	.call-to-action.with-button-arrow .call-to-action-btn {
		left: -80px;
	}
}

.call-to-action.with-button-arrow .call-to-action-btn .arrow {
	position: absolute;
}

@media (max-width: 991px) {
	.call-to-action.with-button-arrow .call-to-action-btn .arrow {
		display: none;
	}
}

.call-to-action.with-button-arrow .call-to-action-btn.no-left {
	left: 0;
}

@media (max-width: 991px) {
	.call-to-action.with-button-arrow .call-to-action-btn.no-left {
		margin-top: -25px !important;
	}
}

.call-to-action.with-button-arrow.button-arrow-price-centered .call-to-action-btn > span {
	position: relative;
}

.call-to-action.with-button-arrow.button-arrow-price-centered .call-to-action-btn > span .arrow {
	right: 50% !important;
}

.call-to-action.content-align-center .call-to-action-content, .call-to-action.content-align-center .call-to-action-btn {
	width: 100%;
	text-align: center;
}

.call-to-action.button-centered .call-to-action-content {
	text-align: center;
	width: 100%;
	padding: 35px 0 0;
	margin: 0;
}

.call-to-action.button-centered .call-to-action-btn {
	text-align: center;
	position: static;
	margin: 20px auto 25px;
	width: auto;
}

.call-to-action.call-to-action-in-footer {
	padding-top: 70px;
	padding-bottom: 70px;
	margin-top: 30px;
	margin-bottom: -50px !important;
}

.call-to-action.call-to-action-in-footer:before {
	content: "";
	display: block;
	position: absolute;
	border-left: 20px solid transparent;
	border-right: 20px solid transparent;
	border-top: 20px solid #FFF;
	height: 20px;
	width: 20px;
	top: -5px;
	left: 50%;
	margin-left: -20px;
}

.call-to-action.no-top-arrow:before {
	display: none;
}

.call-to-action.call-to-action-in-footer-margin-top {
	margin-top: 60px;
}

.container .call-to-action {
	border-radius: var(--border-radius2x);
}

@media (max-width: 767px) {
	.call-to-action .call-to-action-content,
		.call-to-action .call-to-action-btn {
		margin: 0;
		padding: 20px;
		position: static;
		width: auto;
	}
}

/* Call to Action - Skin */
.call-to-action.featured.featured-primary {
	border-top-color: var(--primary);
}

.call-to-action.call-to-action-primary {
	background: var(--primary);
}

.call-to-action.with-full-borders-primary {
	border-color: var(--primary);
}

.call-to-action.call-to-action-primary {
	background: var(--primary);
}

.call-to-action.call-to-action-primary h3, .call-to-action.call-to-action-primary p {
	color: var(--primary-inverse);
}

.call-to-action.featured.featured-secondary {
	border-top-color: var(--secondary);
}

.call-to-action.call-to-action-secondary {
	background: var(--secondary);
}

.call-to-action.with-full-borders-secondary {
	border-color: var(--secondary);
}

.call-to-action.call-to-action-secondary {
	background: var(--secondary);
}

.call-to-action.call-to-action-secondary h3, .call-to-action.call-to-action-secondary p {
	color: var(--secondary-inverse);
}

.call-to-action.featured.featured-tertiary {
	border-top-color: var(--tertiary);
}

.call-to-action.call-to-action-tertiary {
	background: var(--tertiary);
}

.call-to-action.with-full-borders-tertiary {
	border-color: var(--tertiary);
}

.call-to-action.call-to-action-tertiary {
	background: var(--tertiary);
}

.call-to-action.call-to-action-tertiary h3, .call-to-action.call-to-action-tertiary p {
	color: var(--tertiary-inverse);
}

.call-to-action.featured.featured-quaternary {
	border-top-color: var(--quaternary);
}

.call-to-action.call-to-action-quaternary {
	background: var(--quaternary);
}

.call-to-action.with-full-borders-quaternary {
	border-color: var(--quaternary);
}

.call-to-action.call-to-action-quaternary {
	background: var(--quaternary);
}

.call-to-action.call-to-action-quaternary h3, .call-to-action.call-to-action-quaternary p {
	color: var(--quaternary-inverse);
}

.call-to-action.featured.featured-dark {
	border-top-color: var(--dark);
}

.call-to-action.call-to-action-dark {
	background: var(--dark);
}

.call-to-action.with-full-borders-dark {
	border-color: var(--dark);
}

.call-to-action.call-to-action-dark {
	background: var(--dark);
}

.call-to-action.call-to-action-dark h3, .call-to-action.call-to-action-dark p {
	color: var(--dark-inverse);
}

.call-to-action.featured.featured-light {
	border-top-color: var(--light);
}

.call-to-action.call-to-action-light {
	background: var(--light);
}

.call-to-action.with-full-borders-light {
	border-color: var(--light);
}

.call-to-action.call-to-action-light {
	background: var(--light);
}

.call-to-action.call-to-action-light h3, .call-to-action.call-to-action-light p {
	color: var(--light-inverse);
}

/* Cards */
.card {
	position: relative;
	border: 1px solid rgba(0, 0, 0, 0.06);
}

.card-header {
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.card-footer {
	border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.card-body {
	padding: 32px;
	padding: 2rem;
	position: relative;
	z-index: 1;
}

.card-text-color-hover-light:hover * {
	color: var(--light) !important;
}

.card-background-image-hover {
	background-size: cover;
	background-position: center;
}

.card-background-image-hover .card-body {
	background: var(--light-rgba-90);
	margin: 16px;
	margin: 1rem;
	padding: 16px;
	padding: 1rem;
}

/* Border */
.card-border {
	overflow: hidden;
}

.card-border:after {
	content: "";
	transition: .2s all ease;
	backface-visibility: hidden;
	position: absolute;
	left: 50%;
	height: 5px;
	width: 100%;
	transform: translateX(-50%);
	background-color: var(--primary);
}

.card-border.card-border-top:after {
	top: 0;
}

.card-border.card-border-bottom:after {
	bottom: 0;
}

.card-border.card-border-hover:after {
	width: 0;
}

.card-border.card-border-hover:hover:after {
	width: 100%;
}

/* Flip Card */
.flip-card {
	position: relative;
	border: none;
	perspective: 1000px;
}

.flip-card .flip-front,
.flip-card .flip-back {
	padding: 32px;
	padding: 2rem;
	backface-visibility: hidden;
	background: var(--light);
	box-shadow: 0px 0px 44px -10px rgba(0, 0, 0, 0.15);
	transform-style: preserve-3d;
	transition: ease transform 500ms;
}

.flip-card .flip-front .flip-content,
.flip-card .flip-back .flip-content {
	transform: translate3d(0, 0, 1px);
	backface-visibility: hidden;
}

.flip-card .flip-front.overlay:before,
.flip-card .flip-back.overlay:before {
	transform: translate3d(0, 0, 0px);
	backface-visibility: hidden;
}

.flip-card .flip-front {
	transform: translate3d(0, 0, 1px) rotateY(0deg);
}

.flip-card .flip-back {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	transform: translate3d(0, 0, -1px) rotateY(180deg);
}

.flip-card:hover .flip-front {
	transform: translate3d(0, 0, -1px) rotateY(-180deg);
}

.flip-card:hover .flip-back {
	transform: translate3d(0, 0, 1px) rotateY(0deg);
}

.flip-card.flip-card-vertical .flip-front {
	transform: translate3d(0, 0, 1px) rotateX(0deg);
}

.flip-card.flip-card-vertical .flip-back {
	transform: translate3d(0, 0, -1px) rotateX(180deg);
}

.flip-card.flip-card-vertical:hover .flip-front {
	transform: translate3d(0, 0, -1px) rotateX(-180deg);
}

.flip-card.flip-card-vertical:hover .flip-back {
	transform: translate3d(0, 0, 1px) rotateX(0deg);
}

.flip-card.flip-card-3d .flip-front .flip-content, .flip-card.flip-card-3d .flip-back .flip-content {
	perspective: inherit;
	transform: translate3d(0, 0, 60px);
}

/* Owl Carousel */
.owl-carousel {
	display: block;
	margin-bottom: 20px;
	opacity: 0;
}

.owl-carousel.owl-loaded {
	opacity: 1;
}

.owl-carousel:not(.owl-loaded):not(.owl-carousel-light) > div, .owl-carousel:not(.owl-loaded):not(.owl-carousel-light) span {
	display: none;
}

.owl-carousel:not(.owl-loaded):not(.owl-carousel-light) > div:first-child, .owl-carousel:not(.owl-loaded):not(.owl-carousel-light) span:first-child {
	display: block;
}

.owl-carousel .owl-item img {
	transform-style: unset;
}

.owl-carousel .owl-item img[data-icon] {
	display: inline;
}

.owl-carousel .thumbnail {
	margin-right: 1px;
}

.owl-carousel .item-video {
	height: 300px;
}

.owl-carousel .owl-nav {
	top: 50%;
	position: absolute;
	width: 100%;
	margin-top: 0;
	transform: translate3d(0, -50%, 0);
}

.owl-carousel .owl-nav button.owl-prev, .owl-carousel .owl-nav button.owl-next {
	display: inline-block;
	position: absolute;
	top: 50%;
	width: 30px;
	height: 30px;
	outline: 0;
	margin: 0;
	transform: translate3d(0, -50%, 0);
}

.owl-carousel .owl-nav button.owl-prev {
	left: 0;
}

.owl-carousel .owl-nav button.owl-prev:before {
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	font-size: 11.2px;
	font-size: 0.7rem;
	content: "\f053";
	position: relative;
	left: -1px;
	top: -1px;
}

.owl-carousel .owl-nav button.owl-next {
	right: 0;
}

.owl-carousel .owl-nav button.owl-next:before {
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	font-size: 11.2px;
	font-size: 0.7rem;
	content: "\f054";
	position: relative;
	left: 1px;
	top: -1px;
}

.owl-carousel.stage-margin .owl-stage-outer {
	margin-left: 40px !important;
	margin-right: 40px !important;
}

.owl-carousel.stage-margin .owl-stage-outer .owl-stage {
	padding-left: 0 !important;
	padding-right: 0 !important;
}

.owl-carousel.stage-margin.stage-margin-sm .owl-stage-outer {
	margin-left: 50px !important;
	margin-right: 50px !important;
}

.owl-carousel.stage-margin.stage-margin-md .owl-stage-outer {
	margin-left: 75px !important;
	margin-right: 75px !important;
}

.owl-carousel.stage-margin.stage-margin-lg .owl-stage-outer {
	margin-left: 100px !important;
	margin-right: 100px !important;
}

.owl-carousel.top-border {
	border-top: 1px solid #eaeaea;
	padding-top: 18px;
}

.owl-carousel.nav-remove-prev .owl-nav .owl-prev {
	display: none;
}

.owl-carousel.nav-remove-next .owl-nav .owl-next {
	display: none;
}

.owl-carousel.nav-full-height .owl-stage-outer {
	z-index: 1;
}

.owl-carousel.nav-full-height .owl-nav {
	height: 100%;
}

.owl-carousel.nav-full-height .owl-nav .owl-next,
.owl-carousel.nav-full-height .owl-nav .owl-prev {
	height: 100% !important;
}

.owl-carousel.show-nav-hover .owl-nav {
	opacity: 0;
	transition: all 0.2s ease-in-out;
}

.owl-carousel.show-nav-hover .owl-nav button.owl-prev {
	left: 0;
	transition: all 0.2s ease-in-out;
}

.owl-carousel.show-nav-hover .owl-nav button.owl-next {
	right: 0;
	transition: all 0.2s ease-in-out;
}

.owl-carousel.show-nav-hover:hover .owl-nav {
	opacity: 1;
}

.owl-carousel.show-nav-hover:hover .owl-nav button.owl-prev {
	left: -40px;
}

.owl-carousel.show-nav-hover:hover .owl-nav button.owl-next {
	right: -40px;
}

.owl-carousel.show-nav-hover.show-nav-hover-pos-2:hover .owl-nav button.owl-prev {
	left: -15px;
}

.owl-carousel.show-nav-hover.show-nav-hover-pos-2:hover .owl-nav button.owl-next {
	right: -15px;
}

.owl-carousel.show-nav-hover.show-nav-hover-pos-2.nav-md:hover .owl-nav button.owl-prev {
	left: -20px;
}

.owl-carousel.show-nav-hover.show-nav-hover-pos-2.nav-md:hover .owl-nav button.owl-next {
	right: -20px;
}

.owl-carousel.show-nav-hover.show-nav-hover-pos-3:hover .owl-nav button.owl-prev {
	left: 10px;
}

.owl-carousel.show-nav-hover.show-nav-hover-pos-3:hover .owl-nav button.owl-next {
	right: 10px;
}

.owl-carousel.show-nav-title .owl-nav {
	top: 0;
	right: 0;
	margin-top: -25px;
	width: auto;
}

.owl-carousel.show-nav-title .owl-nav button[class*="owl-"], .owl-carousel.show-nav-title .owl-nav button[class*="owl-"]:hover, .owl-carousel.show-nav-title .owl-nav button[class*="owl-"]:active {
	font-size: 18px;
	background: transparent !important;
	width: 18px;
}

.owl-carousel.show-nav-title .owl-nav button.owl-prev {
	left: -40px;
}

.owl-carousel.show-nav-title .owl-nav button.owl-prev:before, .owl-carousel.show-nav-title .owl-nav button.owl-prev:after, .owl-carousel.show-nav-title .owl-nav button.owl-next:before, .owl-carousel.show-nav-title .owl-nav button.owl-next:after {
	font-size: inherit;
}

.owl-carousel.show-nav-title.show-nav-title-both-sides .owl-nav {
	width: 100%;
}

.owl-carousel.show-nav-title.show-nav-title-both-sides button.owl-prev {
	left: 0;
}

.owl-carousel.show-nav-title.show-nav-title-both-sides button.owl-next {
	right: 0;
}

.owl-carousel.show-nav-title.show-nav-title-both-sides-style-2 .owl-nav {
	margin-top: 15px;
}

.owl-carousel.rounded-nav .owl-nav button[class*="owl-"] {
	padding: 3px 7px;
	border-radius: 50%;
	background: transparent;
	border: 1px solid #999;
	color: #999;
}

.owl-carousel.rounded-nav .owl-nav button[class*="owl-"]:hover, .owl-carousel.rounded-nav .owl-nav button[class*="owl-"].hover {
	background: transparent;
	border: 1px solid #a1a1a1;
	color: #a1a1a1;
}

.owl-carousel.rounded-nav .owl-nav button[class*="owl-"]:active, .owl-carousel.rounded-nav .owl-nav button[class*="owl-"].active {
	background: transparent;
	border: 1px solid #666;
	color: #666;
}

.owl-carousel.nav-bottom .owl-stage-outer {
	margin-bottom: 10px;
}

.owl-carousel.nav-bottom .owl-nav {
	position: static;
	margin: 0;
	padding: 0;
	width: auto;
	transform: none;
}

.owl-carousel.nav-bottom .owl-nav button.owl-prev, .owl-carousel.nav-bottom .owl-nav button.owl-next {
	position: static;
	transform: none;
}

.owl-carousel.nav-bottom .owl-nav button.owl-prev {
	margin-right: 5px;
}

.owl-carousel.nav-bottom .owl-nav button.owl-next {
	margin-left: 5px;
}

.owl-carousel.nav-bottom.nav-bottom-align-left .owl-nav {
	text-align: left;
}

.owl-carousel.nav-bottom.nav-bottom-align-right .owl-nav {
	text-align: right;
}

.owl-carousel.nav-bottom-inside .owl-nav {
	position: relative;
	margin: -68.8px 0 0 0;
	margin: -4.3rem 0 0 0;
	padding: 0;
	width: auto;
}

.owl-carousel.nav-bottom-inside .owl-nav button.owl-prev, .owl-carousel.nav-bottom-inside .owl-nav button.owl-next {
	position: static;
}

.owl-carousel.nav-inside .owl-nav button.owl-prev {
	left: 15px;
}

.owl-carousel.nav-inside .owl-nav button.owl-next {
	right: 15px;
	left: auto;
}

.owl-carousel.nav-inside.nav-inside-edge .owl-nav button.owl-prev {
	left: 0;
}

.owl-carousel.nav-inside.nav-inside-edge .owl-nav button.owl-next {
	right: 0;
	left: auto;
}

.owl-carousel.nav-inside.nav-inside-plus .owl-nav button.owl-prev {
	left: 30px;
}

.owl-carousel.nav-inside.nav-inside-plus .owl-nav button.owl-next {
	right: 30px;
	left: auto;
}

.owl-carousel.nav-inside.nav-bottom .owl-nav {
	position: absolute;
	top: auto;
	bottom: 40px;
	width: 100%;
}

.owl-carousel.nav-inside.nav-bottom .owl-nav button.owl-prev, .owl-carousel.nav-inside.nav-bottom .owl-nav button.owl-next {
	position: relative;
}

.owl-carousel.nav-inside.nav-bottom .owl-nav button.owl-prev {
	left: 0;
}

.owl-carousel.nav-inside.nav-bottom .owl-nav button.owl-next {
	right: 0;
}

.owl-carousel.nav-inside.nav-bottom.nav-style-2 .owl-nav button.owl-prev, .owl-carousel.nav-inside.nav-bottom.nav-style-2 .owl-nav button.owl-next {
	position: relative;
}

.owl-carousel.nav-inside.nav-inside-half-section .owl-nav {
	top: auto;
	bottom: 60px;
}

.owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-prev, .owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-next {
	transform: none;
	width: 60px !important;
	height: 60px !important;
}

.owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-prev:before, .owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-next:before {
	font-size: 12.8px;
	font-size: 0.8rem;
	left: 0;
	top: 0;
}

.owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-prev {
	left: -60px;
	top: -61px;
}

.owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-next {
	left: -60px;
}

@media (max-width: 991px) {
	.owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-prev {
		left: 0;
	}

	.owl-carousel.nav-inside.nav-inside-half-section .owl-nav button.owl-next {
		left: 0;
	}
}

.owl-carousel.nav-outside .owl-nav button.owl-prev {
	left: 0;
}

.owl-carousel.nav-outside .owl-nav button.owl-next {
	right: 0;
}

@media (min-width: 992px) {
	.owl-carousel.nav-outside .owl-nav button.owl-prev {
		left: -50px;
	}

	.owl-carousel.nav-outside .owl-nav button.owl-next {
		right: -50px;
	}
}

.owl-carousel.nav-position-1 .owl-nav button.owl-prev {
	left: 20px;
}

.owl-carousel.nav-position-1 .owl-nav button.owl-next {
	right: 20px;
}

.owl-carousel.nav-icon-1 .owl-nav .owl-next:before {
	content: "\f061";
}

.owl-carousel.nav-icon-1 .owl-nav .owl-prev:before {
	content: "\f060";
}

.owl-carousel.nav-size-md .owl-nav .owl-next {
	width: 40px;
	height: 40px;
}

.owl-carousel.nav-size-md .owl-nav .owl-next:before {
	top: 0;
	font-size: 12px;
	font-size: 0.75rem;
}

.owl-carousel.nav-size-md .owl-nav .owl-prev {
	width: 40px;
	height: 40px;
}

.owl-carousel.nav-size-md .owl-nav .owl-prev:before {
	top: 0;
	font-size: 12px;
	font-size: 0.75rem;
}

.owl-carousel.nav-style-1 .owl-nav .owl-next,
.owl-carousel.nav-style-1 .owl-nav .owl-prev {
	width: 20px;
	background: transparent !important;
	color: #000;
}

.owl-carousel.nav-style-1 .owl-nav .owl-next:hover, .owl-carousel.nav-style-1 .owl-nav .owl-next:active,
.owl-carousel.nav-style-1 .owl-nav .owl-prev:hover,
.owl-carousel.nav-style-1 .owl-nav .owl-prev:active {
	color: var(--grey-500);
}

.owl-carousel.nav-style-1 .owl-nav .owl-next:before, .owl-carousel.nav-style-1 .owl-nav .owl-next:after,
.owl-carousel.nav-style-1 .owl-nav .owl-prev:before,
.owl-carousel.nav-style-1 .owl-nav .owl-prev:after {
	font-size: inherit;
}

.owl-carousel.nav-style-2 .owl-nav .owl-next,
.owl-carousel.nav-style-2 .owl-nav .owl-prev {
	background: transparent !important;
}

.owl-carousel.nav-style-2 .owl-nav .owl-next:before,
.owl-carousel.nav-style-2 .owl-nav .owl-prev:before {
	content: '';
	display: block;
	position: absolute;
	top: 50%;
	left: 1px;
	width: 1.3em;
	height: 1.3em;
	border-top: 2px solid var(--grey-500);
	border-left: 2px solid var(--grey-500);
	font-size: inherit;
	transform: translate3d(0, -50%, 0) rotate(-45deg);
}

.owl-carousel.nav-style-2 .owl-nav .owl-next:after,
.owl-carousel.nav-style-2 .owl-nav .owl-prev:after {
	content: '';
	display: block;
	border-top: 3px solid var(--grey-500);
	width: 2.5em;
	position: absolute;
	top: 50%;
	font-size: inherit;
	transform: translate3d(0, -50%, 0);
}

.owl-carousel.nav-style-2 .owl-nav .owl-next {
	transform: rotate(180deg) !important;
	transform-origin: 15px 8px;
}

.owl-carousel.nav-style-2.nav-bottom.nav-inside .owl-nav .owl-next {
	transform-origin: 15px;
}

.owl-carousel.nav-style-3 .owl-nav {
	top: 25%;
}

.owl-carousel.nav-style-3 .owl-nav .owl-next,
.owl-carousel.nav-style-3 .owl-nav .owl-prev {
	width: 30px;
	background: transparent !important;
	color: var(--grey-500);
}

.owl-carousel.nav-style-3 .owl-nav .owl-next:hover, .owl-carousel.nav-style-3 .owl-nav .owl-next:active,
.owl-carousel.nav-style-3 .owl-nav .owl-prev:hover,
.owl-carousel.nav-style-3 .owl-nav .owl-prev:active {
	color: var(--grey-500);
}

.owl-carousel.nav-style-3 .owl-nav .owl-next:before, .owl-carousel.nav-style-3 .owl-nav .owl-next:after,
.owl-carousel.nav-style-3 .owl-nav .owl-prev:before,
.owl-carousel.nav-style-3 .owl-nav .owl-prev:after {
	font-size: 1.5em;
}

.owl-carousel.nav-style-4 .owl-nav .owl-prev {
	left: 75px;
}

@media (max-width: 991px) {
	.owl-carousel.nav-style-4 .owl-nav .owl-prev {
		left: 40px;
	}
}

@media (max-width: 767px) {
	.owl-carousel.nav-style-4 .owl-nav .owl-prev {
		left: 13px;
	}
}

.owl-carousel.nav-style-4 .owl-nav .owl-next {
	right: 75px;
}

@media (max-width: 991px) {
	.owl-carousel.nav-style-4 .owl-nav .owl-next {
		right: 40px;
	}
}

@media (max-width: 767px) {
	.owl-carousel.nav-style-4 .owl-nav .owl-next {
		right: 13px;
	}
}

.owl-carousel.nav-style-4 .owl-nav .owl-prev, .owl-carousel.nav-style-4 .owl-nav .owl-next {
	background: var(--light);
	font-size: 11.2px;
	font-size: 0.7rem;
	width: 40px;
	height: 40px;
	color: #000;
	border-radius: 100%;
	box-shadow: 0px 0px 40px -10px rgba(0, 0, 0, 0.3);
}

.owl-carousel.nav-style-4 .owl-nav .owl-prev:hover, .owl-carousel.nav-style-4 .owl-nav .owl-next:hover {
	color: var(--light);
}

.owl-carousel.nav-style-4.nav-style-4-pos-2 .owl-nav .owl-prev {
	left: 0px;
}

.owl-carousel.nav-style-4.nav-style-4-pos-2 .owl-nav .owl-next {
	right: 0px;
}

.owl-carousel.nav-style-diamond .owl-nav .owl-prev, .owl-carousel.nav-style-diamond .owl-nav .owl-next {
	transform: rotate(45deg);
	transform-origin: 100% 0%;
}

.owl-carousel.nav-style-diamond .owl-nav .owl-prev:before, .owl-carousel.nav-style-diamond .owl-nav .owl-next:before {
	display: block;
	transform: rotate(-45deg);
	transform-origin: 60% 50%;
}

.owl-carousel.nav-style-diamond .owl-nav .owl-next:before {
	transform-origin: 50%;
}

.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-prev, .owl-carousel.nav-svg-arrows-1 .owl-nav .owl-next {
	width: 35px;
	height: 35px;
}

.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-prev:before, .owl-carousel.nav-svg-arrows-1 .owl-nav .owl-next:before {
	content: none;
}

.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-prev svg, .owl-carousel.nav-svg-arrows-1 .owl-nav .owl-next svg {
	width: 2em;
}

.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-prev svg polygon, .owl-carousel.nav-svg-arrows-1 .owl-nav .owl-next svg polygon {
	fill: #FFF;
	stroke: #FFF;
}

.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-prev svg {
	transform: rotate(180deg);
}

.owl-carousel.nav-arrows-1 .owl-nav .owl-prev, .owl-carousel.nav-arrows-1 .owl-nav .owl-next {
	width: 35px;
	height: 35px;
	font-size: 19.2px;
	font-size: 1.2rem;
	background: transparent;
}

.owl-carousel.nav-arrows-1 .owl-nav .owl-next:before {
	content: '\f061';
	font-size: inherit;
}

.owl-carousel.nav-arrows-1 .owl-nav .owl-prev:before {
	content: '\f060';
	font-size: inherit;
}

.owl-carousel.nav-arrows-2 .owl-nav .owl-prev, .owl-carousel.nav-arrows-2 .owl-nav .owl-next {
	width: 35px;
	height: 35px;
	font-size: 19.2px;
	font-size: 1.2rem;
	background: transparent;
}

.owl-carousel.nav-arrows-2 .owl-nav .owl-next:before {
	content: '\f101';
	font-size: inherit;
}

.owl-carousel.nav-arrows-2 .owl-nav .owl-prev:before {
	content: '\f100';
	font-size: inherit;
}

.owl-carousel.nav-arrows-thin .owl-nav .owl-prev:before, .owl-carousel.nav-arrows-thin .owl-nav .owl-next:before {
	font-family: simple-line-icons;
	speak: none;
	font-style: normal;
	font-weight: 700;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
}

.owl-carousel.nav-arrows-thin .owl-nav .owl-next:before {
	content: "\e606";
	font-size: inherit;
}

.owl-carousel.nav-arrows-thin .owl-nav .owl-prev:before {
	content: "\e605";
	font-size: inherit;
}

.owl-carousel.nav-center-images-only .owl-nav {
	top: 37%;
}

.owl-carousel.nav-center-outside .owl-nav {
	width: calc(100% + 90px);
	left: 49.9%;
	transform: translate3d(-50%, 0, 0);
}

.owl-carousel.full-width .owl-nav button[class*="owl-"], .owl-carousel.full-width .owl-nav button[class*="owl-"]:hover, .owl-carousel.big-nav .owl-nav button[class*="owl-"], .owl-carousel.big-nav .owl-nav button[class*="owl-"]:hover {
	height: auto;
	padding: 20px 0 !important;
}

.owl-carousel.full-width .owl-nav button.owl-prev, .owl-carousel.big-nav .owl-nav button.owl-prev {
	border-radius: 0 4px 4px 0;
}

.owl-carousel.full-width .owl-nav button.owl-next, .owl-carousel.big-nav .owl-nav button.owl-next {
	border-radius: 4px 0 0 4px;
}

.owl-carousel.nav-squared .owl-nav button[class*="owl-"] {
	border-radius: 0;
}

.owl-carousel.nav-rounded .owl-nav button[class*="owl-"] {
	border-radius: 50%;
}

.owl-carousel.nav-sm .owl-nav button.owl-prev,
.owl-carousel.nav-sm .owl-nav button.owl-next {
	width: 30px !important;
	height: 30px !important;
}

.owl-carousel.nav-md .owl-nav button.owl-prev,
.owl-carousel.nav-md .owl-nav button.owl-next {
	width: 40px;
	height: 40px;
}

.owl-carousel.nav-lg .owl-nav button.owl-prev,
.owl-carousel.nav-lg .owl-nav button.owl-next {
	width: 45px;
	height: 60px;
}

.owl-carousel.nav-lg.rounded-nav .owl-nav button.owl-prev,
.owl-carousel.nav-lg.rounded-nav .owl-nav button.owl-next {
	width: 55px;
	height: 55px;
}

.owl-carousel.nav-lg.rounded-nav .owl-nav button.owl-prev:before,
.owl-carousel.nav-lg.rounded-nav .owl-nav button.owl-next:before {
	font-size: 14.4px;
	font-size: 0.9rem;
}

.owl-carousel.nav-lg.rounded-nav .owl-nav button.owl-prev:before {
	top: -1px;
	left: -1px;
}

.owl-carousel.nav-lg.rounded-nav .owl-nav button.owl-next:before {
	top: -1px;
	left: 1px;
}

.owl-carousel.nav-font-size-sm .owl-nav button.owl-prev,
.owl-carousel.nav-font-size-sm .owl-nav button.owl-next {
	font-size: 10px;
}

.owl-carousel.nav-font-size-sm .owl-nav button.owl-prev:before,
.owl-carousel.nav-font-size-sm .owl-nav button.owl-next:before {
	font-size: inherit;
}

.owl-carousel.nav-font-size-md .owl-nav button.owl-prev,
.owl-carousel.nav-font-size-md .owl-nav button.owl-next {
	font-size: 14px;
}

.owl-carousel.nav-font-size-md .owl-nav button.owl-prev:before,
.owl-carousel.nav-font-size-md .owl-nav button.owl-next:before {
	font-size: inherit;
}

.owl-carousel.nav-font-size-lg .owl-nav button.owl-prev,
.owl-carousel.nav-font-size-lg .owl-nav button.owl-next {
	font-size: 19px;
}

.owl-carousel.nav-font-size-lg .owl-nav button.owl-prev:before,
.owl-carousel.nav-font-size-lg .owl-nav button.owl-next:before {
	font-size: inherit;
	left: 2px;
}

.owl-carousel.nav-font-size-xl .owl-nav button.owl-prev,
.owl-carousel.nav-font-size-xl .owl-nav button.owl-next {
	font-size: 25px;
}

.owl-carousel.nav-font-size-xl .owl-nav button.owl-prev:before,
.owl-carousel.nav-font-size-xl .owl-nav button.owl-next:before {
	font-size: inherit;
	left: 2px;
}

.owl-carousel.nav-transparent .owl-nav button[class*="owl-"] {
	background-color: transparent !important;
	color: var(--dark) !important;
}

.owl-carousel.nav-transparent .owl-nav button[class*="owl-"]:hover {
	background-color: transparent !important;
	border-color: transparent !important;
}

.owl-carousel.nav-transparent .owl-nav button[class*="owl-"]:active {
	background-color: transparent !important;
	border-color: transparent !important;
}

.owl-carousel.nav-borders .owl-nav button[class*="owl-"] {
	border-color: var(--dark-rgba-10) !important;
}

.owl-carousel.nav-borders .owl-nav button[class*="owl-"]:hover {
	border-color: var(--dark-rgba-10) !important;
}

.owl-carousel.nav-borders .owl-nav button[class*="owl-"]:active {
	border-color: var(--dark-rgba-30) !important;
}

.owl-carousel.nav-borders-light .owl-nav button[class*="owl-"] {
	border-color: var(--light-rgba-20) !important;
}

.owl-carousel.nav-borders-light .owl-nav button[class*="owl-"]:hover {
	border-color: var(--light-rgba-20) !important;
}

.owl-carousel.nav-borders-light .owl-nav button[class*="owl-"]:active {
	border-color: var(--light-rgba-30) !important;
}

.owl-carousel.nav-arrow-light .owl-nav .owl-next:before, .owl-carousel.nav-arrow-light .owl-nav .owl-next:after,
.owl-carousel.nav-arrow-light .owl-nav .owl-prev:before,
.owl-carousel.nav-arrow-light .owl-nav .owl-prev:after {
	color: var(--light) !important;
}

.owl-carousel.nav-light:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav button[class*="owl-"] {
	background-color: var(--grey-100) !important;
	border-color: var(--grey-100) !important;
	color: var(--dark) !important;
}

.owl-carousel.nav-light:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav button[class*="owl-"]:hover {
	background-color: var(--light) !important;
	border-color: var(--light) !important;
}

.owl-carousel.nav-light:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav button[class*="owl-"]:active {
	background-color: var(--grey-200) !important;
	border-color: var(--grey-200) !important;
}

.owl-carousel.nav-light:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-with-transparency .owl-nav button[class*="owl-"] {
	width: 35px;
	height: 45px;
	background-color: var(--dark-rgba-10) !important;
	border-color: transparent !important;
}

.owl-carousel.nav-light:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-svg-arrows-1 .owl-nav button[class*="owl-"] svg polygon {
	fill: var(--dark) !important;
	stroke: var(--dark) !important;
}

.owl-carousel.nav-light.nav-style-1 .owl-nav .owl-next,
.owl-carousel.nav-light.nav-style-1 .owl-nav .owl-prev {
	color: var(--light) !important;
}

.owl-carousel.nav-light.nav-style-2 .owl-nav .owl-next:before, .owl-carousel.nav-light.nav-style-2 .owl-nav .owl-next:after,
.owl-carousel.nav-light.nav-style-2 .owl-nav .owl-prev:before,
.owl-carousel.nav-light.nav-style-2 .owl-nav .owl-prev:after {
	border-color: var(--light) !important;
}

.owl-carousel.nav-light.nav-style-3 .owl-nav .owl-next,
.owl-carousel.nav-light.nav-style-3 .owl-nav .owl-prev {
	color: var(--light) !important;
}

.owl-carousel.nav-light.nav-svg-arrows-1 .owl-nav .owl-next svg polygon,
.owl-carousel.nav-light.nav-svg-arrows-1 .owl-nav .owl-prev svg polygon {
	fill: #FFF !important;
	stroke: #FFF !important;
}

.owl-carousel.nav-light.nav-arrows-1 .owl-nav .owl-next,
.owl-carousel.nav-light.nav-arrows-1 .owl-nav .owl-prev {
	color: var(--light) !important;
}

.owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav .owl-next, .owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav .owl-prev {
	background-color: var(--dark--100) !important;
	border-color: var(--dark--100) var(--dark--100) var(--dark--100) !important;
	color: var(--light) !important;
}

.owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav .owl-next:hover, .owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav .owl-prev:hover {
	background-color: var(--dark--100) !important;
	border-color: var(--dark--100) !important;
}

.owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav .owl-next:active, .owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1) .owl-nav .owl-prev:active {
	background-color: var(--dark) !important;
	border-color: var(--dark) !important;
}

.owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-with-transparency .owl-nav .owl-next, .owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-with-transparency .owl-nav .owl-prev {
	width: 35px;
	height: 45px;
	background-color: rgba(var(--dark--100), 0.4) !important;
	border-color: transparent !important;
}

.owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-with-transparency .owl-nav .owl-next:hover, .owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-with-transparency .owl-nav .owl-prev:hover {
	background-color: rgba(var(--dark--100), 1) !important;
}

.owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-with-transparency.nav-style-diamond .owl-nav .owl-next, .owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-with-transparency.nav-style-diamond .owl-nav .owl-prev {
	width: 40px;
	height: 40px;
}

.owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-svg-arrows-1 .owl-nav .owl-next svg polygon, .owl-carousel.nav-dark:not(.nav-style-1):not(.nav-style-2):not(.nav-style-3):not(.show-nav-title):not(.nav-arrows-1).nav-svg-arrows-1 .owl-nav .owl-prev svg polygon {
	fill: #FFF !important;
	stroke: #FFF !important;
}

.owl-carousel.nav-dark.nav-style-1 .owl-nav .owl-next,
.owl-carousel.nav-dark.nav-style-1 .owl-nav .owl-prev {
	color: var(--dark) !important;
}

.owl-carousel.nav-dark.nav-style-2 .owl-nav .owl-next:before, .owl-carousel.nav-dark.nav-style-2 .owl-nav .owl-next:after,
.owl-carousel.nav-dark.nav-style-2 .owl-nav .owl-prev:before,
.owl-carousel.nav-dark.nav-style-2 .owl-nav .owl-prev:after {
	border-color: var(--dark) !important;
}

.owl-carousel.nav-dark.nav-style-3 .owl-nav .owl-next,
.owl-carousel.nav-dark.nav-style-3 .owl-nav .owl-prev {
	color: var(--dark) !important;
}

.owl-carousel.nav-dark.nav-svg-arrows-1 .owl-nav .owl-next svg polygon,
.owl-carousel.nav-dark.nav-svg-arrows-1 .owl-nav .owl-prev svg polygon {
	fill: var(--dark) !important;
	stroke: var(--dark) !important;
}

.owl-carousel.nav-dark.nav-arrows-1 .owl-nav .owl-next,
.owl-carousel.nav-dark.nav-arrows-1 .owl-nav .owl-prev {
	color: var(--dark) !important;
}

.owl-carousel .owl-dots .owl-dot {
	outline: 0;
}

.owl-carousel .owl-dots .owl-dot span {
	width: 8px;
	height: 8px;
	margin: 5px 4px;
}

.owl-carousel.dots-inside .owl-dots {
	position: absolute;
	bottom: 2px;
	right: 10px;
	margin-top: 0;
}

.owl-carousel.dots-title .owl-dots {
	position: absolute;
	margin-top: 0 !important;
	top: -51px;
	left: 155px;
}

.owl-carousel.dots-title .owl-dots .owl-dot span {
	width: 8px;
	height: 8px;
	margin: 5px 4px;
}

.owl-carousel.dots-title.dots-title-pos-2 .owl-dots {
	left: 235px;
}

.owl-carousel.dots-light .owl-dots .owl-dot span {
	background: rgba(255, 255, 255, 0.6);
}

.owl-carousel.dots-light .owl-dots .owl-dot.active span, .owl-carousel.dots-light .owl-dots .owl-dot:hover span {
	background: #FFF !important;
}

.owl-carousel.dots-dark .owl-dots .owl-dot span {
	background: rgba(33, 33, 33, 0.6);
}

.owl-carousel.dots-dark .owl-dots .owl-dot.active span, .owl-carousel.dots-dark .owl-dots .owl-dot:hover span {
	background: #212121 !important;
}

.owl-carousel.dots-morphing .owl-dots .owl-dot span {
	width: 20px;
	height: 6px;
	transition: ease width 300ms;
}

.owl-carousel.dots-morphing .owl-dots .owl-dot.active span, .owl-carousel.dots-morphing .owl-dots .owl-dot:hover span {
	width: 40px;
}

.owl-carousel.dots-modern .owl-dots {
	display: flex;
	align-items: center;
	justify-content: center;
}

.owl-carousel.dots-modern .owl-dots .owl-dot {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 2px;
}

.owl-carousel.dots-modern .owl-dots .owl-dot span {
	width: 4px;
	height: 4px;
	transition: ease all 300ms 300ms;
}

.owl-carousel.dots-modern .owl-dots .owl-dot.active span {
	transition: ease all 300ms;
	transform: scale(2);
}

.owl-carousel.dots-modern.dots-modern-lg .owl-dots .owl-dot {
	margin: 0 3px;
}

.owl-carousel.dots-modern.dots-modern-lg .owl-dots .owl-dot span {
	width: 6px;
	height: 6px;
}

.owl-carousel.dots-orientation-portrait .owl-dots {
	display: flex;
	flex-direction: column;
	margin-left: 15px !important;
	margin-right: 15px !important;
}

.owl-carousel.dots-align-left .owl-dots {
	text-align: left;
	justify-content: flex-start;
}

.owl-carousel.dots-align-right .owl-dots {
	text-align: left;
}

.owl-carousel.dots-horizontal-center .owl-dots {
	left: 0;
	right: 0;
	width: 100%;
}

.owl-carousel.dots-vertical-center .owl-dots {
	top: 50%;
	bottom: auto;
	margin: 0;
	transform: translate3d(0, -50%, 0);
}

@media (max-width: 575px) {
	.owl-carousel.show-dots-xs .owl-dots {
		opacity: 1 !important;
		visibility: visible !important;
	}
}

@media (min-width: 576px) and (max-width: 767px) {
	.owl-carousel.show-dots-sm .owl-dots {
		opacity: 1 !important;
		visibility: visible !important;
	}
}

@media (min-width: 768px) and (max-width: 991px) {
	.owl-carousel.show-dots-md .owl-dots {
		opacity: 1 !important;
		visibility: visible !important;
	}
}

.owl-carousel.show-dots-hover .owl-dots {
	opacity: 0;
	visibility: hidden;
	transition: ease opacity 300ms;
}

.owl-carousel.show-dots-hover:hover .owl-dots {
	opacity: 1;
	visibility: visible;
}

.owl-carousel.carousel-shadow-1 {
	position: relative;
}

.owl-carousel.carousel-shadow-1:before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 65%;
	height: 0px;
	box-shadow: 0 0 110px 180px rgba(0, 0, 0, 0.04);
	transform: translate3d(-50%, -50%, 0);
	z-index: 0;
}

.owl-carousel.carousel-shadow-1.carousel-shadow-1-bold:before {
	box-shadow: 0 0 110px 230px rgba(0, 0, 0, 0.04);
}

.owl-carousel .img-thumbnail.img-thumbnail-hover-icon {
	display: block;
}

.owl-carousel.carousel-right-side-nav {
	width: calc( 100% - 55px);
}

.owl-carousel.carousel-right-side-nav .owl-nav .owl-next {
	width: 55px;
	transform: translate3d(100%, -50%, 0);
}

.owl-carousel.carousel-bottom-inside-shadow .owl-stage-outer:after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	height: 35%;
	width: 100%;
	background-image: linear-gradient(360deg, var(--grey-500) 0%, transparent 100%);
}

.owl-carousel [class*="opacity-"]:not([class*="opacity-hover"]) {
	transition: ease opacity 300ms;
}

.owl-carousel [class*="opacity-"]:not([class*="opacity-hover"]):hover {
	opacity: 1 !important;
}

@media (min-width: 576px) {
	.owl-carousel.carousel-sync-style-1 {
		position: absolute;
		top: 50%;
		left: -30px;
		max-width: 355px;
		transform: translate3d(0, -50%, 0);
	}
}

.owl-carousel-spaced {
	margin-left: -5px;
}

.owl-carousel-spaced .owl-item > div {
	margin: 5px;
}

.owl-carousel.testimonials img {
	display: inline-block;
	height: 70px;
	width: 70px;
}

@media (max-width: 575px) {
	.owl-carousel-spaced {
		margin-left: 0;
	}
}

.carousel-half-full-width-wrapper > .owl-carousel {
	width: 100%;
}

@media (min-width: 576px) {
	.carousel-half-full-width-wrapper > .owl-carousel {
		width: calc( 100vw - ( ( 100vw - 540px ) / 2 ));
	}
}

@media (min-width: 768px) {
	.carousel-half-full-width-wrapper > .owl-carousel {
		width: calc( 100vw - ( ( 100vw - 720px ) / 2 ));
	}
}

@media (min-width: 992px) {
	.carousel-half-full-width-wrapper > .owl-carousel {
		width: calc( 100vw - ( ( 100vw - 960px ) / 2 ));
	}
}

@media (min-width: 1200px) {
	.carousel-half-full-width-wrapper > .owl-carousel {
		width: calc( 100vw - ( ( 100vw - 1140px ) / 2 ));
	}
}

.carousel-half-full-width-wrapper > .owl-carousel .owl-stage-outer {
	margin-bottom: 20px;
}

.carousel-half-full-width-wrapper.carousel-half-full-width-left {
	direction: rtl;
}

.carousel-half-full-width-wrapper.carousel-half-full-width-left > .owl-carousel .owl-nav {
	display: flex;
}

.carousel-half-full-width-wrapper.carousel-half-full-width-left > .owl-carousel .owl-nav .owl-prev {
	order: 2;
}

.carousel-half-full-width-wrapper.carousel-half-full-width-left > .owl-carousel .owl-nav .owl-next {
	order: 1;
}

.carousel-areas {
	background: #f2f2f2;
	background: linear-gradient(to bottom, #f2f2f2 0%, rgba(33, 37, 41, 0.5) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f2f2f2', endColorstr='var(--dark)', GradientType=0 );
	margin-bottom: -10px;
	padding: 8px 8px 0 8px;
	border-radius: 6px 6px 0 0;
	box-shadow: 0px 0px 50px 20px rgba(0, 0, 0, 0.07);
}

.carousel-areas .owl-carousel {
	box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
}

.carousel-areas .owl-carousel .owl-nav button.owl-prev {
	left: -55px;
}

.carousel-areas .owl-carousel .owl-nav button.owl-next {
	right: -55px;
}

.carousel-areas .owl-carousel:first-child img {
	border-radius: 6px 6px 0 0;
}

@media (max-width: 991px) {
	.carousel-areas .owl-carousel .owl-nav {
		display: none;
	}
}

.owl-carousel.carousel-center-active-item .owl-item {
	opacity: 0.2;
	transition: ease opacity 300ms;
}

.owl-carousel.carousel-center-active-item .owl-item.current {
	opacity: 1 !important;
}

.owl-carousel.carousel-center-active-item.carousel-center-active-item-style-2 .owl-item {
	opacity: 0.7;
}

.owl-carousel.carousel-center-active-item-2 .owl-stage-outer {
	overflow: visible;
}

.owl-carousel.carousel-center-active-item-2 .owl-item > div {
	width: 66.6666%;
	margin-left: auto;
	padding: 48px;
	padding: 3rem;
	background: var(--light);
	border-radius: 7px;
	box-shadow: 0px 0px 70px -40px rgba(0, 0, 0, 0.2);
}

.owl-carousel.carousel-center-active-item-2 .owl-item.active > div {
	margin-right: auto;
}

.owl-carousel.carousel-center-active-item-2 .owl-item.active > div * {
	color: var(--light) !important;
}

.owl-carousel.carousel-center-active-item-2 .owl-item.active + .owl-item > div {
	margin-right: auto;
	margin-left: 0;
}

.owl-carousel.carousel-center-active-item-3 .owl-stage-outer {
	overflow: visible;
}

.owl-carousel.carousel-center-active-item-3 .owl-item > div {
	width: 100%;
	margin-left: auto;
	transition: ease opacity 300ms;
}

.owl-carousel.carousel-center-active-item-3 .owl-item.active > div {
	margin-right: auto;
}

.owl-carousel.carousel-center-active-item-3 .owl-item.active + .owl-item > div {
	margin-right: auto;
	margin-left: 0;
}

.owl-carousel-light.owl-carousel-light-init-fadeIn {
	transition: ease opacity 300ms;
}

.owl-carousel-light .owl-stage-outer,
.owl-carousel-light .owl-stage {
	height: 100%;
}

.owl-carousel-light .owl-item {
	display: none;
	visibility: hidden;
	opacity: 0;
	position: absolute !important;
	width: 100%;
	height: 100%;
	top: 0;
	transition: ease opacity 300ms;
}

.owl-carousel-light .owl-item.active {
	display: block;
	visibility: visible;
	opacity: 1;
}

.owl-carousel-light .owl-dots .owl-dot span {
	margin: 5px 2px;
}

.horizontal-scroller {
	padding: 32px 0;
	padding: 2rem 0;
	height: 100vh;
	position: relative;
}

.horizontal-scroller-scroll {
	position: relative;
	overflow: hidden;
	padding: 32px;
	padding: 2rem;
}

.horizontal-scroller-images {
	height: 100%;
	display: flex;
	align-items: center;
}

.horizontal-scroller-item {
	height: 100%;
	display: flex;
	justify-content: center;
	flex: 0 0 auto;
	padding: 0 32px;
	padding: 0 2rem;
}

.horizontal-scroller-image {
	-o-object-fit: fill;
	object-fit: fill;
	margin: 0 auto;
	max-height: 80vh;
	padding-top: 10vh;
}

/* Carousel - Skin */
.owl-carousel .owl-dots .owl-dot.active span,
.owl-carousel .owl-dots .owl-dot:hover span {
	background-color: var(--primary-100);
}

.owl-carousel.show-nav-title .owl-nav button[class*="owl-"],
.owl-carousel.show-nav-title .owl-nav button[class*="owl-"]:hover,
.owl-carousel.show-nav-title .owl-nav button[class*="owl-"].hover {
	color: var(--primary);
}

.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"] {
	background-color: var(--primary);
	border-color: var(--primary) var(--primary) var(--primary-300);
	color: var(--primary-inverse);
}

.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"]:hover, .owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"].hover {
	background-color: var(--primary--100);
	border-color: var(--primary--300) var(--primary--300) var(--primary);
}

.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"]:active, .owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"].active {
	background-color: var(--primary-300);
	background-image: none;
	border-color: var(--primary-300) var(--primary-300) var(--primary-300);
}

.owl-carousel.nav-with-transparency:not(.nav-style-1):not(.show-nav-title):not(.nav-arrows-1) .owl-nav button[class*="owl-"] {
	background-color: var(--primary-rgba-35);
}

.owl-carousel.nav-style-1 .owl-nav .owl-next,
.owl-carousel.nav-style-1 .owl-nav .owl-prev {
	color: var(--primary) !important;
}

.owl-carousel.nav-style-2 .owl-nav .owl-next:before, .owl-carousel.nav-style-2 .owl-nav .owl-next:after,
.owl-carousel.nav-style-2 .owl-nav .owl-prev:before,
.owl-carousel.nav-style-2 .owl-nav .owl-prev:after {
	border-color: var(--primary);
}

.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-prev svg polygon, .owl-carousel.nav-svg-arrows-1 .owl-nav .owl-next svg polygon {
	fill: var(--primary);
	stroke: var(--primary);
}

.owl-carousel.nav-arrows-1 .owl-nav .owl-prev, .owl-carousel.nav-arrows-1 .owl-nav .owl-next {
	color: var(--primary);
}

/* Carousel Center Active Item 2 - Skin */
.owl-carousel.carousel-center-active-item-2 .owl-item.active > div {
	background: var(--primary);
}

/* Bottom Inside Shadow - Skin */
.owl-carousel.carousel-bottom-inside-shadow .owl-stage-outer:after {
	background-image: linear-gradient(360deg, var(--primary) 0%, transparent 100%);
}

/* Cascading Images */
.cascading-images-wrapper {
	display: inline-block;
	padding: 12% 20%;
}

.cascading-images-wrapper .cascading-images {
	transform: translate3d(0, -35%, 0);
}

.cascading-images-wrapper .cascading-images img {
	max-width: 100%;
}

/* Countdown */
.countdown {
	display: flex;
	text-align: center;
}

.countdown > span {
	flex-basis: 25%;
	max-width: 25%;
	width: 25%;
	font-size: 16px;
	font-size: 1rem;
	line-height: 1.5;
	color: var(--default);
}

.countdown > span > span {
	display: block;
	font-size: 48px;
	font-size: 3rem;
	line-height: 1;
	color: var(--dark);
}

.countdown.countdown-light > span {
	color: rgba(255, 255, 255, 0.6);
}

.countdown.countdown-light > span > span {
	color: var(--light);
}

.countdown.countdown-light.countdown-borders > span {
	border-color: var(--light);
}

.countdown.countdown-borders {
	margin-right: -15px;
	margin-left: -15px;
}

.countdown.countdown-borders > span {
	border-width: 2px;
	border-style: solid;
	border-color: var(--dark);
	margin-right: 15px;
	margin-left: 15px;
	padding: 24px;
	padding: 1.5rem;
}

@media (max-width: 767px) {
	.countdown {
		flex-wrap: wrap;
	}

	.countdown > span {
		flex-basis: calc(50% - 15px);
		max-width: calc(50% - 15px);
		width: calc(50% - 15px);
		margin-bottom: 30px;
	}

	.countdown > span:nth-child(3), .countdown > span:nth-child(4) {
		margin-bottom: 0;
	}

	.countdown.countdown-borders {
		justify-content: space-between;
		margin-right: 0;
		margin-left: 0;
	}

	.countdown.countdown-borders > span {
		margin-right: 0;
		margin-left: 0;
	}
}

@media (max-width: 576px) {
	.countdown > span {
		flex-basis: 100%;
		max-width: 100%;
		width: 100%;
		margin-bottom: 50px;
	}

	.countdown > span:nth-child(3) {
		margin-bottom: 50px;
	}

	.countdown.countdown-borders > span {
		margin-bottom: 30px;
	}

	.countdown.countdown-borders > span:nth-child(3) {
		margin-bottom: 30px;
	}
}

/* Countdown - Skin */
.countdown.countdown-primary > span > span {
	color: var(--primary);
}

.countdown.countdown-secondary > span > span {
	color: var(--secondary);
}

.countdown.countdown-tertiary > span > span {
	color: var(--tertiary);
}

.countdown.countdown-quaternary > span > span {
	color: var(--quaternary);
}

.countdown.countdown-dark > span > span {
	color: var(--dark);
}

.countdown.countdown-light > span > span {
	color: var(--light);
}

/* Counters */
.counters .counter {
	text-align: center;
}

.counters [class*="fa-"], .counters .icons {
	display: block;
	font-size: 35px;
	line-height: 35px;
	margin-bottom: 5px;
}

.counters strong {
	display: block;
	font-weight: bold;
	font-size: 50px;
	line-height: 50px;
}

.counters label {
	display: block;
	font-weight: 100;
	font-size: 20px;
	line-height: 20px;
}

.counters .counter-side-icon [class*="fa-"], .counters .counter-side-icon .icons, .counters .counter-side-icon strong {
	display: inline-block;
}

.counters .counter-with-unit strong {
	display: inline-block;
}

.counters .counter-with-unit.counter-unit-on-top {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	align-items: flex-start;
}

.counters .counter-with-unit.counter-unit-on-top .unit {
	font-size: 32px;
	font-size: 2rem;
	line-height: 43.2px;
	line-height: 2.7rem;
	margin-left: 4.8px;
	margin-left: 0.3rem;
}

.counters .counter-with-unit.counter-unit-on-top label {
	width: 100%;
	margin-top: 11.2px;
	margin-top: 0.7rem;
}

.counters .counter-with-unit.counter-unit-on-bottom {
	align-items: flex-end;
}

.counters.with-borders .counter {
	border-radius: 6px;
	border-top: 1px solid #DFDFDF;
	border-bottom: 1px solid #DFDFDF;
	border-left: 1px solid #ECECEC;
	border-right: 1px solid #ECECEC;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.04);
	padding: 25px;
}

.counters.counters-sm [class*="fa-"], .counters.counters-sm .icons {
	font-size: 25px;
	line-height: 25px;
}

.counters.counters-sm strong {
	font-size: 35px;
	line-height: 35px;
}

.counters.counters-sm label {
	font-size: 15px;
	line-height: 15px;
}

.counters.counters-lg [class*="fa-"] {
	font-size: 45px;
	line-height: 45px;
}

.counters.counters-lg strong {
	font-size: 60px;
	line-height: 60px;
}

.counters.counters-lg label {
	font-size: 25px;
	line-height: 25px;
}

.counters.counters-text-light .counter {
	color: var(--light) !important;
}

.counters.counters-text-light .counter [class*="fa-"], .counters.counters-text-light .counter .icons, .counters.counters-text-light .counter strong, .counters.counters-text-light .counter label {
	color: var(--light) !important;
}

.counters.counters-text-dark .counter {
	color: var(--dark--200) !important;
}

.counters.counters-text-dark .counter [class*="fa-"], .counters.counters-text-dark .counter .icons, .counters.counters-text-dark .counter strong, .counters.counters-text-dark .counter label {
	color: var(--dark--200) !important;
}

/* Counters - Skin */
.counters .counter-primary [class*="fa-"], .counters .counter-primary .icons, .counters .counter-primary strong {
	color: var(--primary);
}

.counters .counter-secondary [class*="fa-"], .counters .counter-secondary .icons, .counters .counter-secondary strong {
	color: var(--secondary);
}

.counters .counter-tertiary [class*="fa-"], .counters .counter-tertiary .icons, .counters .counter-tertiary strong {
	color: var(--tertiary);
}

.counters .counter-quaternary [class*="fa-"], .counters .counter-quaternary .icons, .counters .counter-quaternary strong {
	color: var(--quaternary);
}

.counters .counter-dark [class*="fa-"], .counters .counter-dark .icons, .counters .counter-dark strong {
	color: var(--dark);
}

.counters .counter-light [class*="fa-"], .counters .counter-light .icons, .counters .counter-light strong {
	color: var(--light);
}

/* Dividers */
hr {
	background: rgba(0, 0, 0, 0.06);
	border: 0;
	height: 1px;
	margin: 22px 0;
	opacity: 1;
}

hr.short {
	margin: 11px 0;
}

hr.tall {
	margin: 44px 0;
}

hr.taller {
	margin: 66px 0;
}

hr.gratient {
	background: none;
	background-image: linear-gradient(to left, transparent, rgba(0, 0, 0, 0.06), transparent);
}

hr.gradient-to-right {
	background: none;
	background-image: linear-gradient(to left, rgba(0, 0, 0, 0.06), transparent);
}

hr.gradient-to-left {
	background: none;
	background-image: linear-gradient(to left, transparent, rgba(0, 0, 0, 0.06));
}

hr.solid {
	background: rgba(0, 0, 0, 0.06);
}

hr.light {
	background: rgba(255, 255, 255, 0.15);
}

hr.invisible {
	background: none;
}

.divider {
	border: 0;
	height: 1px;
	margin: 44px auto;
	background: rgba(0, 0, 0, 0.06);
	text-align: center;
	position: relative;
	clear: both;
}

.divider.taller {
	margin: 66px auto;
}

.divider [class*="fa-"], .divider .icons {
	text-align: center;
	background: var(--light);
	border-radius: 50px;
	color: #a9a9a9;
	display: inline-block;
	height: 50px;
	line-height: 50px;
	position: absolute;
	text-align: center;
	width: 50px;
	font-size: 20px;
	margin: 0 auto 0 -25px;
	top: -25px;
	left: 50%;
	z-index: 1;
}

.divider.divider-solid {
	background: rgba(0, 0, 0, 0.06);
}

.divider.divider-icon-sm [class*="fa-"], .divider.divider-icon-sm .icons {
	width: 30px;
	height: 30px;
	line-height: 30px;
	top: -15px;
	font-size: 15px;
	margin-left: -16px;
}

.divider.divider-icon-lg {
	margin: 86px 0;
}

.divider.divider-icon-lg [class*="fa-"], .divider.divider-icon-lg .icons {
	width: 60px;
	height: 60px;
	line-height: 60px;
	top: -30px;
	font-size: 30px;
	margin-left: -30px;
}

.divider.divider-icon-lg.taller {
	margin: 100px auto;
}

.divider.divider-xs {
	width: 35%;
}

.divider.divider-sm {
	width: 67%;
}

.divider.divider-left [class*="fa-"], .divider.divider-left .icons {
	left: 0;
	margin-left: 0;
	margin-right: 0;
	right: auto;
}

.divider.divider-right [class*="fa-"], .divider.divider-right .icons {
	left: auto;
	margin-left: 0;
	margin-right: 0;
	right: 0;
}

.divider.divider-style-2 [class*="fa-"], .divider.divider-style-2 .icons {
	background: var(--grey-100);
	color: #a9a9a9;
}

.divider.divider-style-3 [class*="fa-"], .divider.divider-style-3 .icons {
	border: 1px solid #CECECE;
}

.divider.divider-style-4 [class*="fa-"], .divider.divider-style-4 .icons {
	border: 1px solid #CECECE;
}

.divider.divider-style-4 [class*="fa-"]:after, .divider.divider-style-4 .icons:after {
	border: 3px solid var(--grey-100);
	border-radius: 50%;
	box-sizing: content-box;
	content: "";
	display: block;
	height: 100%;
	left: -4px;
	padding: 1px;
	position: absolute;
	top: -4px;
	width: 100%;
}

.divider.divider-small {
	margin: 22px auto;
	background: transparent;
}

.divider.divider-small hr {
	width: 50px;
	height: 3px;
	background: #555;
}

.divider.divider-small.divider-small-center {
	text-align: center;
}

.divider.divider-small.divider-small-center hr {
	margin-left: auto;
	margin-right: auto;
}

.divider.divider-small.divider-small-right {
	text-align: right;
}

.divider.divider-small.divider-small-right hr {
	margin-left: auto;
}

.divider.divider-small.divider-light hr {
	background: #DDD;
}

.divider.divider-small.divider-small-sm hr {
	height: 1px;
}

.divider.divider-small.divider-small-lg hr {
	height: 6px;
}

.divider.divider-small.tall {
	margin: 34px 0;
}

.divider.divider-small.taller {
	margin: 46px 0;
}

.divider .divider-small-text {
	background: var(--light);
	color: #999;
	display: inline-block;
	text-align: center;
	font-size: 0.8em;
	height: 30px;
	line-height: 30px;
	padding: 0 30px;
	text-transform: uppercase;
	margin: 0;
	position: relative;
	top: -15px;
}

hr.dashed, .divider.dashed {
	background: none;
	position: relative;
}

hr.dashed:after, .divider.dashed:after {
	border-top: 1px dashed rgba(0, 0, 0, 0.06);
	content: "";
	display: block;
	height: 0;
	position: absolute;
	top: 50%;
	margin-top: -1px;
	width: 100%;
}

hr.pattern, .divider.pattern {
	background: none;
	position: relative;
}

hr.pattern:after, .divider.pattern:after {
	background: transparent url(../img/patterns/worn_dots.png) repeat 0 0;
	content: "";
	display: block;
	height: 15px;
	position: absolute;
	top: 50%;
	margin-top: -7px;
	width: 100%;
}

hr.pattern.pattern-2:after, .divider.pattern.pattern-2:after {
	background-image: url(../img/patterns/az_subtle.png);
}

@keyframes dividerProgress25 {
	from {
		width: 0;
	}

	to {
		width: 25%;
	}
}

.dividerProgress25 {
	animation-name: dividerProgress25;
}

/* Divider Borders */
.divider-top-border {
	border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.divider-bottom-border {
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.divider-left-border {
	border-left: 1px solid rgba(0, 0, 0, 0.06);
}

.divider-right-border {
	border-right: 1px solid rgba(0, 0, 0, 0.06);
}

/* Dividers - Skin */
.divider.divider-primary [class*="fa-"], .divider.divider-primary .icons {
	color: var(--primary);
}

.divider.divider-primary.divider-small hr {
	background: var(--primary);
}

.divider.divider-secondary [class*="fa-"], .divider.divider-secondary .icons {
	color: var(--secondary);
}

.divider.divider-secondary.divider-small hr {
	background: var(--secondary);
}

.divider.divider-tertiary [class*="fa-"], .divider.divider-tertiary .icons {
	color: var(--tertiary);
}

.divider.divider-tertiary.divider-small hr {
	background: var(--tertiary);
}

.divider.divider-quaternary [class*="fa-"], .divider.divider-quaternary .icons {
	color: var(--quaternary);
}

.divider.divider-quaternary.divider-small hr {
	background: var(--quaternary);
}

.divider.divider-dark [class*="fa-"], .divider.divider-dark .icons {
	color: var(--dark);
}

.divider.divider-dark.divider-small hr {
	background: var(--dark);
}

.divider.divider-light [class*="fa-"], .divider.divider-light .icons {
	color: var(--light);
}

.divider.divider-light.divider-small hr {
	background: var(--light);
}

.divider.divider-style-2.divider-primary [class*="fa-"], .divider.divider-style-2.divider-primary .icons {
	background: var(--primary);
	color: var(--primary-inverse);
}

.divider.divider-style-2.divider-secondary [class*="fa-"], .divider.divider-style-2.divider-secondary .icons {
	background: var(--secondary);
	color: var(--secondary-inverse);
}

.divider.divider-style-2.divider-tertiary [class*="fa-"], .divider.divider-style-2.divider-tertiary .icons {
	background: var(--tertiary);
	color: var(--tertiary-inverse);
}

.divider.divider-style-2.divider-quaternary [class*="fa-"], .divider.divider-style-2.divider-quaternary .icons {
	background: var(--quaternary);
	color: var(--quaternary-inverse);
}

.divider.divider-style-2.divider-dark [class*="fa-"], .divider.divider-style-2.divider-dark .icons {
	background: var(--dark);
	color: var(--dark-inverse);
}

.divider.divider-style-2.divider-light [class*="fa-"], .divider.divider-style-2.divider-light .icons {
	background: var(--light);
	color: var(--light-inverse);
}

.divider.divider-style-3.divider-primary [class*="fa-"], .divider.divider-style-3.divider-primary .icons {
	border-color: var(--primary);
}

.divider.divider-style-3.divider-secondary [class*="fa-"], .divider.divider-style-3.divider-secondary .icons {
	border-color: var(--secondary);
}

.divider.divider-style-3.divider-tertiary [class*="fa-"], .divider.divider-style-3.divider-tertiary .icons {
	border-color: var(--tertiary);
}

.divider.divider-style-3.divider-quaternary [class*="fa-"], .divider.divider-style-3.divider-quaternary .icons {
	border-color: var(--quaternary);
}

.divider.divider-style-3.divider-dark [class*="fa-"], .divider.divider-style-3.divider-dark .icons {
	border-color: var(--dark);
}

.divider.divider-style-3.divider-light [class*="fa-"], .divider.divider-style-3.divider-light .icons {
	border-color: var(--light);
}

/* Dropdowns */
.dropdown.dropdown-style-1 .dropdown-toggle {
	display: flex;
	align-items: center;
	padding: 16px 17.6px;
	padding: 1rem 1.1rem;
	background: var(--light);
	border-radius: 0;
	border-right: 1px solid #ededed;
	min-width: 280px;
	font-weight: bold;
	text-decoration: none;
}

.dropdown.dropdown-style-1 .dropdown-toggle:after {
	content: '\f107';
	position: absolute;
	right: 17.6px;
	right: 1.1rem;
	top: 50%;
	font-family: 'Font Awesome 6 Free';
	font-weight: bold;
	transform: translate3d(0, -50%, 0);
	border: 0;
	margin: 0;
	width: 10px;
	height: 10px;
	line-height: 10px;
}

.dropdown.dropdown-style-1 .dropdown-toggle > i {
	display: none;
}

.dropdown.dropdown-style-1 .dropdown-menu {
	width: 100%;
	border-radius: 0;
	margin-top: -1px;
	border-color: #ededed;
}

.dropdown.dropdown-style-1 .dropdown-menu .dropdown-item {
	font-size: 14px;
	padding-right: 17.6px;
	padding-right: 1.1rem;
	padding-left: 17.6px;
	padding-left: 1.1rem;
}

.dropdown.dropdown-corner-left-fix .dropdown-menu {
	margin-left: -5px;
}

.dropdown-menu.dropdown-menu-arrow-centered:before {
	right: auto;
	left: 50%;
	transform: translate3d(-50%, -6px, 0);
}

.dropdown-menu.dropdown-menu-arrow-centered-style-2 {
	border: 0;
	margin-top: -2px;
	margin-left: -2px;
	padding: 0;
	box-shadow: 0 10px 30px 10px rgba(0, 0, 0, 0.05);
	background: var(--light);
	padding: 3px;
	min-width: 180px;
	z-index: 10000;
	margin-right: 0;
	margin-top: 1px;
	transition: opacity .2s ease-out;
	border-radius: 0;
}

.dropdown-item:not(.btn):hover {
	text-decoration: none;
}

/* Dropdown - Skin */
.dropdown-menu.dropdown-menu-arrow-centered a:focus, .dropdown-menu.dropdown-menu-arrow-centered a:active {
	background-color: var(--primary);
}

/* Icon Featured */
.icon-featured {
	display: inline-block;
	font-size: 0;
	margin: 15px;
	width: 110px;
	height: 110px;
	border-radius: 50%;
	text-align: center;
	position: relative;
	z-index: 1;
	color: var(--light);
	font-size: 2.3em;
	line-height: 110px;
}

.icon-featured:after {
	pointer-events: none;
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	content: '';
	box-sizing: content-box;
}

.icon-featured.icon-featured-lg {
	width: 180px !important;
	height: 180px !important;
	line-height: 180px !important;
}

/* Featured Box */
.featured-box {
	background: var(--light);
	box-sizing: border-box;
	border-bottom: 1px solid #DFDFDF;
	border-left: 1px solid #ECECEC;
	border-radius: var(--border-radius2x);
	border-right: 1px solid #ECECEC;
	box-shadow: 0 2px 4px 0px rgba(0, 0, 0, 0.05);
	margin-bottom: 20px;
	margin-left: auto;
	margin-right: auto;
	margin-top: 20px;
	min-height: 100px;
	position: relative;
	text-align: center;
	z-index: 1;
}

.featured-box h4 {
	font-size: 1.3em;
	font-weight: 400;
	letter-spacing: -0.7px;
	margin-top: 5px;
	margin-bottom: 5px;
}

.featured-box .box-content {
	border-radius: var(--border-radius2x);
	border-top: 1px solid rgba(0, 0, 0, 0.06);
	border-top-width: 4px;
	padding: 28.8px;
	padding: 1.8rem;
	position: relative;
}

.featured-box .box-content:not(.box-content-border-0) {
	top: -1px;
	border-top-width: 4px;
}

.featured-box .box-content.box-content-border-0 {
	border-top: 1px solid rgba(0, 0, 0, 0.06) !important;
	border-bottom: 0 !important;
}

.featured-box .box-content.box-content-border-bottom {
	top: 1px;
}

.featured-box .box-content-border-bottom {
	border-top: 1px solid rgba(0, 0, 0, 0.06) !important;
	border-bottom: 4px solid rgba(0, 0, 0, 0.06);
}

.featured-box.border-radius {
	border-radius: var(--border-radius2x) !important;
}

.featured-box.border-radius.box-shadow-1:before {
	border-radius: var(--border-radius2x) !important;
}

/* Featured Box Left */
.featured-box-text-left {
	text-align: left;
}

/* Featured Boxes - Flat */
.featured-boxes-flat .featured-box {
	box-shadow: none;
	margin: 10px 0;
}

.featured-boxes-flat .featured-box .box-content {
	background: var(--light);
	margin-top: 65px;
}

.featured-boxes-flat .featured-box .icon-featured {
	display: inline-block;
	font-size: 2em;
	height: 90px;
	line-height: 90px;
	padding: 0;
	width: 90px;
	margin: -100px 0 -15px;
	position: relative;
	top: -40px;
}

/* Featured Boxes - Style 2 */
.featured-boxes-style-2 .featured-box {
	background: none;
	border: 0;
	margin: 10px 0;
	box-shadow: none;
}

.featured-boxes-style-2 .featured-box .box-content {
	border: 0;
	padding-top: 0;
	padding-bottom: 0;
}

.featured-boxes-style-2 .featured-box .icon-featured {
	display: inline-block;
	font-size: 1.8em;
	height: 75px;
	line-height: 75px;
	padding: 0;
	width: 75px;
	margin-top: 0;
}

/* Featured Boxes - Style 3 */
.featured-boxes-style-3 .featured-box {
	margin: 10px 0;
}

.featured-boxes-style-3 .featured-box .icon-featured {
	display: inline-block;
	font-size: 1.8em;
	height: 75px;
	line-height: 75px;
	padding: 0;
	width: 75px;
	margin-top: -37px;
	top: -37px;
	background: var(--light);
	border: 2px solid rgba(0, 0, 0, 0.06);
	color: rgba(0, 0, 0, 0.06);
}

.featured-boxes-style-3:not(.featured-boxes-flat) .featured-box {
	background: none;
	border: 0;
	box-shadow: none;
}

.featured-boxes-style-3:not(.featured-boxes-flat) .featured-box .box-content {
	border: 0;
	padding-top: 0;
	padding-bottom: 0;
}

/* Featured Boxes - Style 4 */
.featured-boxes-style-4 .featured-box {
	background: none;
	border: 0;
	margin: 10px 0;
	box-shadow: none;
}

.featured-boxes-style-4 .featured-box .box-content {
	border: 0;
	padding-top: 0;
	padding-bottom: 0;
}

.featured-boxes-style-4 .featured-box .icon-featured {
	display: inline-block;
	font-size: 35px;
	height: 45px;
	line-height: 45px;
	padding: 0;
	width: 45px;
	margin-top: 0;
	margin-bottom: 10px;
	background: transparent;
	color: rgba(0, 0, 0, 0.06);
	border-radius: 0;
}

/* Featured Boxes - Style 5, 6, 7 and 9 */
.featured-boxes-style-5 .featured-box, .featured-boxes-style-6 .featured-box, .featured-boxes-style-7 .featured-box, .featured-boxes-style-9 .featured-box {
	background: none;
	border: 0;
	box-shadow: none;
	margin: 10px 0;
}

.featured-boxes-style-5 .featured-box .box-content, .featured-boxes-style-6 .featured-box .box-content, .featured-boxes-style-7 .featured-box .box-content, .featured-boxes-style-9 .featured-box .box-content {
	border: 0;
	padding-top: 0;
	padding-bottom: 0;
}

.featured-boxes-style-5 .featured-box .box-content h4, .featured-boxes-style-6 .featured-box .box-content h4, .featured-boxes-style-7 .featured-box .box-content h4, .featured-boxes-style-9 .featured-box .box-content h4 {
	color: var(--dark--200);
}

.featured-boxes-style-5 .featured-box .icon-featured, .featured-boxes-style-6 .featured-box .icon-featured, .featured-boxes-style-7 .featured-box .icon-featured, .featured-boxes-style-9 .featured-box .icon-featured {
	display: inline-block;
	font-size: 2em;
	height: 75px;
	padding: 0;
	width: 75px;
	margin-top: 0;
	margin-bottom: 10px;
	background: var(--light);
	border: 1px solid #dfdfdf;
	color: var(--default);
	line-height: 73px;
}

/* Featured Boxes - Style 6 */
.featured-boxes-style-6 .featured-box .icon-featured {
	border: 1px solid #cecece;
	color: var(--default);
}

.featured-boxes-style-6 .featured-box .icon-featured:after {
	border: 5px solid var(--grey-100);
	border-radius: 50%;
	box-sizing: content-box;
	content: "";
	display: block;
	height: 100%;
	left: -6px;
	padding: 1px;
	position: absolute;
	top: -6px;
	width: 100%;
}

/* Featured Boxes - Style 7 */
.featured-boxes-style-7 .featured-box .icon-featured {
	border: 1px solid #dfdfdf;
	color: var(--default);
}

/* Featured Boxes - Style 8 */
.featured-boxes-style-8 .featured-box .icon-featured {
	display: inline-block;
	font-size: 2em;
	height: 75px;
	padding: 0;
	width: 75px;
	margin: -15px -15px 0 0;
	background: var(--light);
	line-height: 75px;
	border: 0;
	color: var(--default);
}

.featured-boxes-style-8 .featured-box .icon-featured:after {
	display: none;
}

/* Featured Boxes - Style 9 */
.featured-boxes-style-9 .featured-box .icon-featured {
	background: var(--light);
	box-shadow: 0px 0px 32px -7px rgba(0, 0, 0, 0.1);
	border: 0;
	width: 100px;
	height: 100px;
	line-height: 100px;
	transition-duration: .4s;
}

/* Featured Boxes Modern */
.featured-boxes-modern-style-1 .featured-box {
	position: relative;
	overflow: hidden;
	border-radius: 0;
	height: auto !important;
	margin: 0 !important;
}

.featured-boxes-modern-style-1 .featured-box .featured-box-background {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	transition: cubic-bezier(0.39, 0.66, 0.42, 0.99) all 2s;
	transform: scale(1);
}

.featured-boxes-modern-style-1 .featured-box .box-content {
	padding: 64px;
	padding: 4rem;
	border: none;
	z-index: 3;
}

.featured-boxes-modern-style-1 .featured-box .box-content .icon-featured {
	color: var(--light);
	background: transparent;
	width: auto;
	height: auto;
	line-height: 1.4;
	font-size: 46.4px;
	font-size: 2.9rem;
	margin: 0 0 16px;
	margin: 0 0 1rem;
}

.featured-boxes-modern-style-1 .featured-box .box-content h2 {
	color: var(--light);
	font-weight: 600;
	font-size: 19.2px;
	font-size: 1.2rem;
	line-height: 1.3;
	margin-bottom: 11.2px;
	margin-bottom: 0.7rem;
}

.featured-boxes-modern-style-1 .featured-box .box-content p {
	color: #999;
	margin-bottom: 25.6px;
	margin-bottom: 1.6rem;
}

.featured-boxes-modern-style-1 .featured-box:hover .featured-box-background {
	transform: scale(1.1);
}

@media (max-width: 575px) {
	.featured-boxes-modern-style-1 .featured-box .box-content {
		padding: 2rem 1.5rem;
	}
}

.featured-boxes-modern-style-2 .featured-box {
	position: relative;
	background: #6c6ef1;
	background: linear-gradient(135deg, #6c6ef1 0%, #e2498e 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6c6ef1', endColorstr='#e2498e', GradientType=1 );
	border-radius: 0;
	margin: 0 !important;
	overflow: hidden;
}

.featured-boxes-modern-style-2 .featured-box:before {
	content: '';
	position: absolute;
	top: 8px;
	right: 8px;
	bottom: 8px;
	left: 8px;
	background: var(--light);
}

.featured-boxes-modern-style-2 .featured-box .box-content {
	padding: 64px;
	padding: 4rem;
	border: none;
}

.featured-boxes-modern-style-2 .featured-box .box-content .icon-featured {
	color: var(--light);
	background: transparent;
	width: auto;
	height: auto;
	line-height: 1.4;
	font-size: 46.4px;
	font-size: 2.9rem;
	margin: 0 0 16px;
	margin: 0 0 1rem;
}

.featured-boxes-modern-style-2 .featured-box .box-content h2 {
	color: var(--dark);
	font-weight: 700;
	font-size: 19.2px;
	font-size: 1.2rem;
	line-height: 1.3;
	margin-bottom: 11.2px;
	margin-bottom: 0.7rem;
}

.featured-boxes-modern-style-2 .featured-box .box-content p {
	color: #999;
	margin-bottom: 25.6px;
	margin-bottom: 1.6rem;
}

@media (max-width: 575px) {
	.featured-boxes-modern-style-2 .featured-box .box-content {
		padding: 2rem 1.5rem;
	}
}

.featured-boxes-modern-style-2-hover-only .featured-box:before {
	top: 1px;
	right: 1px;
	bottom: 1px;
	left: 1px;
	transition: cubic-bezier(0.39, 0.66, 0.42, 0.99) all 200ms;
}

.featured-boxes-modern-style-2-hover-only:not(:hover) .featured-box {
	background: rgba(0, 0, 0, 0.06) !important;
	box-shadow: none;
	border: 0;
}

.featured-boxes-modern-style-2-hover-only:not(:hover) .featured-box-no-borders {
	background: none !important;
}

.featured-boxes-modern-style-2-hover-only:not(:hover) .featured-box-box-shadow {
	box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.1);
}

.featured-boxes-modern-style-2-hover-only:hover .featured-box:before {
	top: 8px;
	right: 8px;
	bottom: 8px;
	left: 8px;
}

/* Featured Boxes */
.featured-boxes .featured-box {
	margin-bottom: 24px;
	margin-bottom: 1.5rem;
	margin-top: 24px;
	margin-top: 1.5rem;
}

/* Effects */
.featured-box-effect-1 .icon-featured:after {
	top: -7px;
	left: -7px;
	padding: 7px;
	box-shadow: 0 0 0 3px #FFF;
	transition: transform 0.2s, opacity 0.2s;
	transform: scale(1);
	opacity: 0;
}

.featured-box-effect-1:hover .icon-featured:after {
	transform: scale(0.8);
	opacity: 1;
}

.featured-box-effect-2 .icon-featured:after {
	top: -7px;
	left: -7px;
	padding: 7px;
	box-shadow: 0 0 0 3px #FFF;
	transition: transform 0.2s, opacity 0.2s;
	transform: scale(0.8);
	opacity: 0;
}

.featured-box-effect-2:hover .icon-featured:after {
	transform: scale(0.95);
	opacity: 1;
}

.featured-box-effect-3 .icon-featured:after {
	top: 0;
	left: 0;
	box-shadow: 0 0 0 10px #FFF;
	transition: transform 0.2s, opacity 0.2s;
	transform: scale(0.9);
	opacity: 0;
}

.featured-box-effect-3:hover .icon-featured {
	color: var(--light) !important;
}

.featured-box-effect-3:hover .icon-featured:after {
	transform: scale(1);
	opacity: 0.8;
}

.featured-box-effect-4 .icon-featured {
	transition: transform 0.2s, transform 0.2s;
	transform: scale(1);
}

.featured-box-effect-4:hover .icon-featured {
	transform: scale(1.15);
}

.featured-box-effect-5 .icon-featured {
	overflow: hidden;
	transition: all 0.3s;
}

.featured-box-effect-5:hover .icon-featured {
	animation: toRightFromLeft 0.3s forwards;
}

@keyframes toRightFromLeft {
	49% {
		transform: translate(100%);
	}

	50% {
		opacity: 0;
		transform: translate(-100%);
	}

	51% {
		opacity: 1;
	}
}

.featured-box-effect-6 .icon-featured:after {
	transition: all 0.2s, transform 0.2s;
}

.featured-box-effect-6:hover .box-content .icon-featured:after {
	transform: scale(1.1);
}

.featured-box-effect-7 .icon-featured:after {
	opacity: 0;
	box-shadow: 3px 3px #d5d5d5;
	transform: rotate(-90deg);
	transition: opacity 0.2s, transform 0.2s;
	left: 0;
	top: -1px;
}

.featured-box-effect-7:hover .icon-featured:after {
	opacity: 1;
	transform: rotate(0deg);
}

.featured-box-effect-7 .icon-featured:before {
	transform: scale(0.8);
	opacity: 0.7;
	transition: transform 0.2s, opacity 0.2s;
}

.featured-box-effect-7:hover .icon-featured:before {
	transform: scale(1);
	opacity: 1;
}

/* Feature Box */
.feature-box {
	display: flex;
}

.feature-box .feature-box-icon {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	flex: 1 0 auto;
	background: rgba(0, 0, 0, 0.06);
	border-radius: 100%;
	color: var(--light);
	font-size: 16px;
	font-size: 1rem;
	position: relative;
	top: 7px;
	text-align: center;
	padding: 0.8em;
	height: 1px;
	width: 2.7em;
	height: 2.7em;
}

.feature-box .feature-box-info {
	flex: 1 1 100%;
	padding-left: 15px;
}

.feature-box.align-items-center .feature-box-icon {
	top: 0;
}

/* Style 2 */
.feature-box.feature-box-style-2 h4 {
	color: var(--dark);
}

.feature-box.feature-box-style-2 .feature-box-icon {
	background: transparent;
	top: -3px;
}

.feature-box.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-2 .feature-box-icon .icons {
	font-size: 1.7em;
}

.feature-box.feature-box-style-2.align-items-center .feature-box-icon {
	top: 0;
}

/* Style 3 */
.feature-box.feature-box-style-3 h4 {
	color: var(--dark);
}

.feature-box.feature-box-style-3 .feature-box-icon {
	border: 1px solid rgba(0, 0, 0, 0.06);
	background: transparent;
	line-height: 32px;
}

.feature-box.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-3 .feature-box-icon .icons {
	color: rgba(0, 0, 0, 0.06);
}

/* Style 4 */
.feature-box.feature-box-style-4 {
	flex-wrap: wrap;
}

.feature-box.feature-box-style-4 h4 {
	color: var(--dark);
}

.feature-box.feature-box-style-4 .feature-box-icon {
	flex-basis: 100%;
	justify-content: flex-start;
	background: transparent;
	padding: 0 0 19.2px 0;
	padding: 0 0 1.2rem 0;
}

.feature-box.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-4 .feature-box-icon .icons {
	font-size: 2.35em;
	width: auto;
	height: auto;
}

.feature-box.feature-box-style-4 .feature-box-info {
	clear: both;
	padding-left: 0;
}

/* Style 5 */
.feature-box.feature-box-style-5 h4 {
	color: var(--dark);
}

.feature-box.feature-box-style-5 .feature-box-icon {
	background: transparent;
	top: 0;
	padding: 1em 1.4em;
	min-width: 5em;
}

.feature-box.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-5 .feature-box-icon .icons {
	font-size: 2.75em;
}

.feature-box.feature-box-style-5.align-items-center .feature-box-icon {
	top: 0;
}

/* Style 6 */
.feature-box.feature-box-style-6 h4 {
	color: var(--dark);
}

.feature-box.feature-box-style-6 .feature-box-icon {
	border: 1px solid #cecece;
	background: transparent;
	line-height: 32px;
}

.feature-box.feature-box-style-6 .feature-box-icon:after {
	border: 3px solid var(--grey-100);
	border-radius: 50%;
	box-sizing: content-box;
	content: "";
	display: block;
	height: 100%;
	left: -4px;
	padding: 1px;
	position: absolute;
	top: -4px;
	width: 100%;
}

.feature-box.feature-box-style-6 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-6 .feature-box-icon .icons {
	color: #a9a9a9;
}

/* Feature Box Icon Size */
.feature-box .feature-box-icon.feature-box-icon-lg {
	width: 3.7em;
	height: 3.7em;
}

.feature-box .feature-box-icon.feature-box-icon-xl {
	width: 4.5em;
	height: 4.5em;
}

/* Reverse All Resolutions */
.feature-box.reverse-allres {
	text-align: right;
	flex-direction: row-reverse;
}

.feature-box.reverse-allres .feature-box-info {
	padding-right: 15px;
	padding-left: 0;
}

.feature-box.reverse-allres.feature-box-style-4 .feature-box-icon {
	justify-content: flex-end;
}

.feature-box.reverse-allres.feature-box-style-4 .feature-box-info {
	padding-right: 0;
}

/* Reverse */
@media (min-width: 992px) {
	.feature-box.reverse {
		text-align: right;
		flex-direction: row-reverse;
	}

	.feature-box.reverse .feature-box-info {
		padding-right: 15px;
		padding-left: 0;
	}

	.feature-box.reverse.feature-box-style-4 .feature-box-icon {
		justify-content: flex-end;
	}

	.feature-box.reverse.feature-box-style-4 .feature-box-info {
		padding-right: 0;
	}
}

/* Full Width */
.featured-boxes-full .featured-box-full {
	text-align: center;
	padding: 55px;
}

.featured-boxes-full .featured-box-full [class*="fa-"], .featured-boxes-full .featured-box-full .icons, .featured-boxes-full .featured-box-full h1, .featured-boxes-full .featured-box-full h2, .featured-boxes-full .featured-box-full h3, .featured-boxes-full .featured-box-full h4, .featured-boxes-full .featured-box-full h5, .featured-boxes-full .featured-box-full h6, .featured-boxes-full .featured-box-full p, .featured-boxes-full .featured-box-full a {
	padding: 0;
	margin: 0;
}

.featured-boxes-full .featured-box-full:not(.featured-box-full-light) [class*="fa-"], .featured-boxes-full .featured-box-full:not(.featured-box-full-light) .icons, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) h1, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) h2, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) h3, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) h4, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) h5, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) h6, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) p, .featured-boxes-full .featured-box-full:not(.featured-box-full-light) a {
	color: var(--light);
}

.featured-boxes-full .featured-box-full .icons, .featured-boxes-full .featured-box-full [class*="fa-"] {
	font-size: 3.9em;
	margin-bottom: 15px;
}

.featured-boxes-full .featured-box-full p {
	padding-top: 12px;
	opacity: 0.8;
}

.featured-boxes-full .featured-box-full.featured-box-full-light {
	border: 1px solid rgba(0, 0, 0, 0.06);
}

.featured-boxes-full .featured-box-full.featured-box-full-light:not(:last-child) {
	border-right: 0;
}

.featured-boxes-full .featured-box-full.featured-box-full-light:first-child {
	border-left: 0;
}

/* Icons - Skin */
.icon-featured {
	background-color: var(--primary);
}

/* Featured Box - - Skin */
.featured-box-primary .icon-featured {
	background-color: var(--primary);
}

.featured-box-primary h4 {
	color: var(--primary);
}

.featured-box-primary .box-content {
	border-top-color: var(--primary);
}

.featured-box-primary .box-content-border-bottom {
	border-bottom-color: var(--primary);
}

.featured-box-secondary .icon-featured {
	background-color: var(--secondary);
}

.featured-box-secondary h4 {
	color: var(--secondary);
}

.featured-box-secondary .box-content {
	border-top-color: var(--secondary);
}

.featured-box-secondary .box-content-border-bottom {
	border-bottom-color: var(--secondary);
}

.featured-box-tertiary .icon-featured {
	background-color: var(--tertiary);
}

.featured-box-tertiary h4 {
	color: var(--tertiary);
}

.featured-box-tertiary .box-content {
	border-top-color: var(--tertiary);
}

.featured-box-tertiary .box-content-border-bottom {
	border-bottom-color: var(--tertiary);
}

.featured-box-quaternary .icon-featured {
	background-color: var(--quaternary);
}

.featured-box-quaternary h4 {
	color: var(--quaternary);
}

.featured-box-quaternary .box-content {
	border-top-color: var(--quaternary);
}

.featured-box-quaternary .box-content-border-bottom {
	border-bottom-color: var(--quaternary);
}

.featured-box-dark .icon-featured {
	background-color: var(--dark);
}

.featured-box-dark h4 {
	color: var(--dark);
}

.featured-box-dark .box-content {
	border-top-color: var(--dark);
}

.featured-box-dark .box-content-border-bottom {
	border-bottom-color: var(--dark);
}

.featured-box-light .icon-featured {
	background-color: var(--light);
}

.featured-box-light h4 {
	color: var(--light);
}

.featured-box-light .box-content {
	border-top-color: var(--light);
}

.featured-box-light .box-content-border-bottom {
	border-bottom-color: var(--light);
}

.featured-boxes-style-3 .featured-box.featured-box-primary .icon-featured {
	border-color: var(--primary);
	color: var(--primary);
}

.featured-boxes-style-3 .featured-box.featured-box-secondary .icon-featured {
	border-color: var(--secondary);
	color: var(--secondary);
}

.featured-boxes-style-3 .featured-box.featured-box-tertiary .icon-featured {
	border-color: var(--tertiary);
	color: var(--tertiary);
}

.featured-boxes-style-3 .featured-box.featured-box-quaternary .icon-featured {
	border-color: var(--quaternary);
	color: var(--quaternary);
}

.featured-boxes-style-3 .featured-box.featured-box-dark .icon-featured {
	border-color: var(--dark);
	color: var(--dark);
}

.featured-boxes-style-3 .featured-box.featured-box-light .icon-featured {
	border-color: var(--light);
	color: var(--light);
}

.featured-boxes-style-4 .featured-box.featured-box-primary .icon-featured {
	border-color: var(--primary);
	color: var(--primary);
}

.featured-boxes-style-4 .featured-box.featured-box-secondary .icon-featured {
	border-color: var(--secondary);
	color: var(--secondary);
}

.featured-boxes-style-4 .featured-box.featured-box-tertiary .icon-featured {
	border-color: var(--tertiary);
	color: var(--tertiary);
}

.featured-boxes-style-4 .featured-box.featured-box-quaternary .icon-featured {
	border-color: var(--quaternary);
	color: var(--quaternary);
}

.featured-boxes-style-4 .featured-box.featured-box-dark .icon-featured {
	border-color: var(--dark);
	color: var(--dark);
}

.featured-boxes-style-4 .featured-box.featured-box-light .icon-featured {
	border-color: var(--light);
	color: var(--light);
}

.featured-boxes-style-5 .featured-box.featured-box-primary .icon-featured {
	color: var(--primary);
}

.featured-boxes-style-5 .featured-box.featured-box-secondary .icon-featured {
	color: var(--secondary);
}

.featured-boxes-style-5 .featured-box.featured-box-tertiary .icon-featured {
	color: var(--tertiary);
}

.featured-boxes-style-5 .featured-box.featured-box-quaternary .icon-featured {
	color: var(--quaternary);
}

.featured-boxes-style-5 .featured-box.featured-box-dark .icon-featured {
	color: var(--dark);
}

.featured-boxes-style-5 .featured-box.featured-box-light .icon-featured {
	color: var(--light);
}

.featured-boxes-style-6 .featured-box.featured-box-primary .icon-featured {
	color: var(--primary);
}

.featured-boxes-style-6 .featured-box.featured-box-secondary .icon-featured {
	color: var(--secondary);
}

.featured-boxes-style-6 .featured-box.featured-box-tertiary .icon-featured {
	color: var(--tertiary);
}

.featured-boxes-style-6 .featured-box.featured-box-quaternary .icon-featured {
	color: var(--quaternary);
}

.featured-boxes-style-6 .featured-box.featured-box-dark .icon-featured {
	color: var(--dark);
}

.featured-boxes-style-6 .featured-box.featured-box-light .icon-featured {
	color: var(--light);
}

.featured-boxes-style-8 .featured-box.featured-box-primary .icon-featured {
	color: var(--primary);
}

.featured-boxes-style-8 .featured-box.featured-box-secondary .icon-featured {
	color: var(--secondary);
}

.featured-boxes-style-8 .featured-box.featured-box-tertiary .icon-featured {
	color: var(--tertiary);
}

.featured-boxes-style-8 .featured-box.featured-box-quaternary .icon-featured {
	color: var(--quaternary);
}

.featured-boxes-style-8 .featured-box.featured-box-dark .icon-featured {
	color: var(--dark);
}

.featured-boxes-style-8 .featured-box.featured-box-light .icon-featured {
	color: var(--light);
}

/* Featured Box Modern - Gradient - Skin */
.featured-boxes-modern-style-primary .featured-box {
	background: var(--primary);
	background: linear-gradient(135deg, var(--primary) 0%, var(--quaternary) 100%);
}

.featured-boxes-modern-style-secondary .featured-box {
	background: var(--secondary);
	background: linear-gradient(135deg, var(--secondary) 0%, var(--quaternary) 100%);
}

.featured-boxes-modern-style-tertiary .featured-box {
	background: var(--tertiary);
	background: linear-gradient(135deg, var(--tertiary) 0%, var(--quaternary) 100%);
}

/* Featured Box - Effects - Skin */
.featured-box-effect-2.featured-box-primary .icon-featured:after {
	box-shadow: 0 0 0 3px var(--primary);
}

.featured-box-effect-2.featured-box-secondary .icon-featured:after {
	box-shadow: 0 0 0 3px var(--secondary);
}

.featured-box-effect-2.featured-box-tertiary .icon-featured:after {
	box-shadow: 0 0 0 3px var(--tertiary);
}

.featured-box-effect-2.featured-box-quaternary .icon-featured:after {
	box-shadow: 0 0 0 3px var(--quaternary);
}

.featured-box-effect-2.featured-box-dark .icon-featured:after {
	box-shadow: 0 0 0 3px var(--dark);
}

.featured-box-effect-2.featured-box-light .icon-featured:after {
	box-shadow: 0 0 0 3px var(--light);
}

.featured-box-effect-3.featured-box-primary .icon-featured:after {
	box-shadow: 0 0 0 10px var(--primary);
}

.featured-box-effect-3.featured-box-primary:hover .icon-featured {
	background: var(--primary) !important;
}

.featured-box-effect-3.featured-box-secondary .icon-featured:after {
	box-shadow: 0 0 0 10px var(--secondary);
}

.featured-box-effect-3.featured-box-secondary:hover .icon-featured {
	background: var(--secondary) !important;
}

.featured-box-effect-3.featured-box-tertiary .icon-featured:after {
	box-shadow: 0 0 0 10px var(--tertiary);
}

.featured-box-effect-3.featured-box-tertiary:hover .icon-featured {
	background: var(--tertiary) !important;
}

.featured-box-effect-3.featured-box-quaternary .icon-featured:after {
	box-shadow: 0 0 0 10px var(--quaternary);
}

.featured-box-effect-3.featured-box-quaternary:hover .icon-featured {
	background: var(--quaternary) !important;
}

.featured-box-effect-3.featured-box-dark .icon-featured:after {
	box-shadow: 0 0 0 10px var(--dark);
}

.featured-box-effect-3.featured-box-dark:hover .icon-featured {
	background: var(--dark) !important;
}

.featured-box-effect-3.featured-box-light .icon-featured:after {
	box-shadow: 0 0 0 10px var(--light);
}

.featured-box-effect-3.featured-box-light:hover .icon-featured {
	background: var(--light) !important;
}

/* Feature Box - Skin */
.feature-box .feature-box-icon {
	background-color: var(--primary);
}

.feature-box-primary .feature-box-icon {
	background-color: var(--primary);
}

.feature-box-secondary .feature-box-icon {
	background-color: var(--secondary);
}

.feature-box-tertiary .feature-box-icon {
	background-color: var(--tertiary);
}

.feature-box-quaternary .feature-box-icon {
	background-color: var(--quaternary);
}

.feature-box-dark .feature-box-icon {
	background-color: var(--dark);
}

.feature-box-light .feature-box-icon {
	background-color: var(--light);
}

.feature-box.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-2 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-primary.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box-primary.feature-box-style-2 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-secondary.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box-secondary.feature-box-style-2 .feature-box-icon .icons {
	color: var(--secondary);
}

.feature-box-tertiary.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box-tertiary.feature-box-style-2 .feature-box-icon .icons {
	color: var(--tertiary);
}

.feature-box-quaternary.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box-quaternary.feature-box-style-2 .feature-box-icon .icons {
	color: var(--quaternary);
}

.feature-box-dark.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box-dark.feature-box-style-2 .feature-box-icon .icons {
	color: var(--dark);
}

.feature-box-light.feature-box-style-2 .feature-box-icon [class*="fa-"], .feature-box-light.feature-box-style-2 .feature-box-icon .icons {
	color: var(--light);
}

.feature-box.feature-box-style-3 .feature-box-icon {
	border-color: var(--primary);
}

.feature-box.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-3 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-primary.feature-box-style-3 .feature-box-icon {
	border-color: var(--primary);
}

.feature-box-primary.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box-primary.feature-box-style-3 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-secondary.feature-box-style-3 .feature-box-icon {
	border-color: var(--secondary);
}

.feature-box-secondary.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box-secondary.feature-box-style-3 .feature-box-icon .icons {
	color: var(--secondary);
}

.feature-box-tertiary.feature-box-style-3 .feature-box-icon {
	border-color: var(--tertiary);
}

.feature-box-tertiary.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box-tertiary.feature-box-style-3 .feature-box-icon .icons {
	color: var(--tertiary);
}

.feature-box-quaternary.feature-box-style-3 .feature-box-icon {
	border-color: var(--quaternary);
}

.feature-box-quaternary.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box-quaternary.feature-box-style-3 .feature-box-icon .icons {
	color: var(--quaternary);
}

.feature-box-dark.feature-box-style-3 .feature-box-icon {
	border-color: var(--dark);
}

.feature-box-dark.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box-dark.feature-box-style-3 .feature-box-icon .icons {
	color: var(--dark);
}

.feature-box-light.feature-box-style-3 .feature-box-icon {
	border-color: var(--light);
}

.feature-box-light.feature-box-style-3 .feature-box-icon [class*="fa-"], .feature-box-light.feature-box-style-3 .feature-box-icon .icons {
	color: var(--light);
}

.feature-box.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-4 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-primary.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box-primary.feature-box-style-4 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-secondary.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box-secondary.feature-box-style-4 .feature-box-icon .icons {
	color: var(--secondary);
}

.feature-box-tertiary.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box-tertiary.feature-box-style-4 .feature-box-icon .icons {
	color: var(--tertiary);
}

.feature-box-quaternary.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box-quaternary.feature-box-style-4 .feature-box-icon .icons {
	color: var(--quaternary);
}

.feature-box-dark.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box-dark.feature-box-style-4 .feature-box-icon .icons {
	color: var(--dark);
}

.feature-box-light.feature-box-style-4 .feature-box-icon [class*="fa-"], .feature-box-light.feature-box-style-4 .feature-box-icon .icons {
	color: var(--light);
}

.feature-box.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box.feature-box-style-5 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-primary.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box-primary.feature-box-style-5 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-secondary.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box-secondary.feature-box-style-5 .feature-box-icon .icons {
	color: var(--secondary);
}

.feature-box-tertiary.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box-tertiary.feature-box-style-5 .feature-box-icon .icons {
	color: var(--tertiary);
}

.feature-box-quaternary.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box-quaternary.feature-box-style-5 .feature-box-icon .icons {
	color: var(--quaternary);
}

.feature-box-dark.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box-dark.feature-box-style-5 .feature-box-icon .icons {
	color: var(--dark);
}

.feature-box-light.feature-box-style-5 .feature-box-icon [class*="fa-"], .feature-box-light.feature-box-style-5 .feature-box-icon .icons {
	color: var(--light);
}

.feature-box-primary.feature-box-style-6 .feature-box-icon [class*="fa-"], .feature-box-primary.feature-box-style-6 .feature-box-icon .icons {
	color: var(--primary);
}

.feature-box-secondary.feature-box-style-6 .feature-box-icon [class*="fa-"], .feature-box-secondary.feature-box-style-6 .feature-box-icon .icons {
	color: var(--secondary);
}

.feature-box-tertiary.feature-box-style-6 .feature-box-icon [class*="fa-"], .feature-box-tertiary.feature-box-style-6 .feature-box-icon .icons {
	color: var(--tertiary);
}

.feature-box-quaternary.feature-box-style-6 .feature-box-icon [class*="fa-"], .feature-box-quaternary.feature-box-style-6 .feature-box-icon .icons {
	color: var(--quaternary);
}

.feature-box-dark.feature-box-style-6 .feature-box-icon [class*="fa-"], .feature-box-dark.feature-box-style-6 .feature-box-icon .icons {
	color: var(--dark);
}

.feature-box-light.feature-box-style-6 .feature-box-icon [class*="fa-"], .feature-box-light.feature-box-style-6 .feature-box-icon .icons {
	color: var(--light);
}

.featured-boxes-full .featured-box-full-primary {
	background-color: var(--primary);
	color: var(--primary-inverse);
}

.featured-boxes-full .featured-box-full-secondary {
	background-color: var(--secondary);
	color: var(--secondary-inverse);
}

.featured-boxes-full .featured-box-full-tertiary {
	background-color: var(--tertiary);
	color: var(--tertiary-inverse);
}

.featured-boxes-full .featured-box-full-quaternary {
	background-color: var(--quaternary);
	color: var(--quaternary-inverse);
}

.featured-boxes-full .featured-box-full-dark {
	background-color: var(--dark);
	color: var(--dark-inverse);
}

.featured-boxes-full .featured-box-full-light {
	background-color: var(--light);
	color: var(--light-inverse);
}

.flag {
	width: 16px;
	height: 11px;
	background: url(../img/flags.png) no-repeat;
}

.flag.flag-ad {
	background-position: -16px 0;
}

.flag.flag-ae {
	background-position: -32px 0;
}

.flag.flag-af {
	background-position: -48px 0;
}

.flag.flag-ag {
	background-position: -64px 0;
}

.flag.flag-ai {
	background-position: -80px 0;
}

.flag.flag-al {
	background-position: -96px 0;
}

.flag.flag-am {
	background-position: -112px 0;
}

.flag.flag-an {
	background-position: -128px 0;
}

.flag.flag-ao {
	background-position: -144px 0;
}

.flag.flag-ar {
	background-position: -160px 0;
}

.flag.flag-as {
	background-position: -176px 0;
}

.flag.flag-at {
	background-position: -192px 0;
}

.flag.flag-au {
	background-position: -208px 0;
}

.flag.flag-aw {
	background-position: -224px 0;
}

.flag.flag-az {
	background-position: -240px 0;
}

.flag.flag-ba {
	background-position: 0 -11px;
}

.flag.flag-bb {
	background-position: -16px -11px;
}

.flag.flag-bd {
	background-position: -32px -11px;
}

.flag.flag-be {
	background-position: -48px -11px;
}

.flag.flag-bf {
	background-position: -64px -11px;
}

.flag.flag-bg {
	background-position: -80px -11px;
}

.flag.flag-bh {
	background-position: -96px -11px;
}

.flag.flag-bi {
	background-position: -112px -11px;
}

.flag.flag-bj {
	background-position: -128px -11px;
}

.flag.flag-bm {
	background-position: -144px -11px;
}

.flag.flag-bn {
	background-position: -160px -11px;
}

.flag.flag-bo {
	background-position: -176px -11px;
}

.flag.flag-br {
	background-position: -192px -11px;
}

.flag.flag-bs {
	background-position: -208px -11px;
}

.flag.flag-bt {
	background-position: -224px -11px;
}

.flag.flag-bv {
	background-position: -240px -11px;
}

.flag.flag-bw {
	background-position: 0 -22px;
}

.flag.flag-by {
	background-position: -16px -22px;
}

.flag.flag-bz {
	background-position: -32px -22px;
}

.flag.flag-ca {
	background-position: -48px -22px;
}

.flag.flag-catalonia {
	background-position: -64px -22px;
}

.flag.flag-cd {
	background-position: -80px -22px;
}

.flag.flag-cf {
	background-position: -96px -22px;
}

.flag.flag-cg {
	background-position: -112px -22px;
}

.flag.flag-ch {
	background-position: -128px -22px;
}

.flag.flag-ci {
	background-position: -144px -22px;
}

.flag.flag-ck {
	background-position: -160px -22px;
}

.flag.flag-cl {
	background-position: -176px -22px;
}

.flag.flag-cm {
	background-position: -192px -22px;
}

.flag.flag-cn {
	background-position: -208px -22px;
}

.flag.flag-co {
	background-position: -224px -22px;
}

.flag.flag-cr {
	background-position: -240px -22px;
}

.flag.flag-cu {
	background-position: 0 -33px;
}

.flag.flag-cv {
	background-position: -16px -33px;
}

.flag.flag-cw {
	background-position: -32px -33px;
}

.flag.flag-cy {
	background-position: -48px -33px;
}

.flag.flag-cz {
	background-position: -64px -33px;
}

.flag.flag-de {
	background-position: -80px -33px;
}

.flag.flag-dj {
	background-position: -96px -33px;
}

.flag.flag-dk {
	background-position: -112px -33px;
}

.flag.flag-dm {
	background-position: -128px -33px;
}

.flag.flag-do {
	background-position: -144px -33px;
}

.flag.flag-dz {
	background-position: -160px -33px;
}

.flag.flag-ec {
	background-position: -176px -33px;
}

.flag.flag-ee {
	background-position: -192px -33px;
}

.flag.flag-eg {
	background-position: -208px -33px;
}

.flag.flag-eh {
	background-position: -224px -33px;
}

.flag.flag-england {
	background-position: -240px -33px;
}

.flag.flag-er {
	background-position: 0 -44px;
}

.flag.flag-es {
	background-position: -16px -44px;
}

.flag.flag-et {
	background-position: -32px -44px;
}

.flag.flag-eu {
	background-position: -48px -44px;
}

.flag.flag-fi {
	background-position: -64px -44px;
}

.flag.flag-fj {
	background-position: -80px -44px;
}

.flag.flag-fk {
	background-position: -96px -44px;
}

.flag.flag-fm {
	background-position: -112px -44px;
}

.flag.flag-fo {
	background-position: -128px -44px;
}

.flag.flag-fr {
	background-position: -144px -44px;
}

.flag.flag-ga {
	background-position: -160px -44px;
}

.flag.flag-gb {
	background-position: -176px -44px;
}

.flag.flag-gd {
	background-position: -192px -44px;
}

.flag.flag-ge {
	background-position: -208px -44px;
}

.flag.flag-gf {
	background-position: -224px -44px;
}

.flag.flag-gg {
	background-position: -240px -44px;
}

.flag.flag-gh {
	background-position: 0 -55px;
}

.flag.flag-gi {
	background-position: -16px -55px;
}

.flag.flag-gl {
	background-position: -32px -55px;
}

.flag.flag-gm {
	background-position: -48px -55px;
}

.flag.flag-gn {
	background-position: -64px -55px;
}

.flag.flag-gp {
	background-position: -80px -55px;
}

.flag.flag-gq {
	background-position: -96px -55px;
}

.flag.flag-gr {
	background-position: -112px -55px;
}

.flag.flag-gs {
	background-position: -128px -55px;
}

.flag.flag-gt {
	background-position: -144px -55px;
}

.flag.flag-gu {
	background-position: -160px -55px;
}

.flag.flag-gw {
	background-position: -176px -55px;
}

.flag.flag-gy {
	background-position: -192px -55px;
}

.flag.flag-hk {
	background-position: -208px -55px;
}

.flag.flag-hm {
	background-position: -224px -55px;
}

.flag.flag-hn {
	background-position: -240px -55px;
}

.flag.flag-hr {
	background-position: 0 -66px;
}

.flag.flag-ht {
	background-position: -16px -66px;
}

.flag.flag-hu {
	background-position: -32px -66px;
}

.flag.flag-ic {
	background-position: -48px -66px;
}

.flag.flag-id {
	background-position: -64px -66px;
}

.flag.flag-ie {
	background-position: -80px -66px;
}

.flag.flag-il {
	background-position: -96px -66px;
}

.flag.flag-im {
	background-position: -112px -66px;
}

.flag.flag-in {
	background-position: -128px -66px;
}

.flag.flag-io {
	background-position: -144px -66px;
}

.flag.flag-iq {
	background-position: -160px -66px;
}

.flag.flag-ir {
	background-position: -176px -66px;
}

.flag.flag-is {
	background-position: -192px -66px;
}

.flag.flag-it {
	background-position: -208px -66px;
}

.flag.flag-je {
	background-position: -224px -66px;
}

.flag.flag-jm {
	background-position: -240px -66px;
}

.flag.flag-jo {
	background-position: 0 -77px;
}

.flag.flag-jp {
	background-position: -16px -77px;
}

.flag.flag-ke {
	background-position: -32px -77px;
}

.flag.flag-kg {
	background-position: -48px -77px;
}

.flag.flag-kh {
	background-position: -64px -77px;
}

.flag.flag-ki {
	background-position: -80px -77px;
}

.flag.flag-km {
	background-position: -96px -77px;
}

.flag.flag-kn {
	background-position: -112px -77px;
}

.flag.flag-kp {
	background-position: -128px -77px;
}

.flag.flag-kr {
	background-position: -144px -77px;
}

.flag.flag-kurdistan {
	background-position: -160px -77px;
}

.flag.flag-kw {
	background-position: -176px -77px;
}

.flag.flag-ky {
	background-position: -192px -77px;
}

.flag.flag-kz {
	background-position: -208px -77px;
}

.flag.flag-la {
	background-position: -224px -77px;
}

.flag.flag-lb {
	background-position: -240px -77px;
}

.flag.flag-lc {
	background-position: 0 -88px;
}

.flag.flag-li {
	background-position: -16px -88px;
}

.flag.flag-lk {
	background-position: -32px -88px;
}

.flag.flag-lr {
	background-position: -48px -88px;
}

.flag.flag-ls {
	background-position: -64px -88px;
}

.flag.flag-lt {
	background-position: -80px -88px;
}

.flag.flag-lu {
	background-position: -96px -88px;
}

.flag.flag-lv {
	background-position: -112px -88px;
}

.flag.flag-ly {
	background-position: -128px -88px;
}

.flag.flag-ma {
	background-position: -144px -88px;
}

.flag.flag-mc {
	background-position: -160px -88px;
}

.flag.flag-md {
	background-position: -176px -88px;
}

.flag.flag-me {
	background-position: -192px -88px;
}

.flag.flag-mg {
	background-position: -208px -88px;
}

.flag.flag-mh {
	background-position: -224px -88px;
}

.flag.flag-mk {
	background-position: -240px -88px;
}

.flag.flag-ml {
	background-position: 0 -99px;
}

.flag.flag-mm {
	background-position: -16px -99px;
}

.flag.flag-mn {
	background-position: -32px -99px;
}

.flag.flag-mo {
	background-position: -48px -99px;
}

.flag.flag-mp {
	background-position: -64px -99px;
}

.flag.flag-mq {
	background-position: -80px -99px;
}

.flag.flag-mr {
	background-position: -96px -99px;
}

.flag.flag-ms {
	background-position: -112px -99px;
}

.flag.flag-mt {
	background-position: -128px -99px;
}

.flag.flag-mu {
	background-position: -144px -99px;
}

.flag.flag-mv {
	background-position: -160px -99px;
}

.flag.flag-mw {
	background-position: -176px -99px;
}

.flag.flag-mx {
	background-position: -192px -99px;
}

.flag.flag-my {
	background-position: -208px -99px;
}

.flag.flag-mz {
	background-position: -224px -99px;
}

.flag.flag-na {
	background-position: -240px -99px;
}

.flag.flag-nc {
	background-position: 0 -110px;
}

.flag.flag-ne {
	background-position: -16px -110px;
}

.flag.flag-nf {
	background-position: -32px -110px;
}

.flag.flag-ng {
	background-position: -48px -110px;
}

.flag.flag-ni {
	background-position: -64px -110px;
}

.flag.flag-nl {
	background-position: -80px -110px;
}

.flag.flag-no {
	background-position: -96px -110px;
}

.flag.flag-np {
	background-position: -112px -110px;
}

.flag.flag-nr {
	background-position: -128px -110px;
}

.flag.flag-nu {
	background-position: -144px -110px;
}

.flag.flag-nz {
	background-position: -160px -110px;
}

.flag.flag-om {
	background-position: -176px -110px;
}

.flag.flag-pa {
	background-position: -192px -110px;
}

.flag.flag-pe {
	background-position: -208px -110px;
}

.flag.flag-pf {
	background-position: -224px -110px;
}

.flag.flag-pg {
	background-position: -240px -110px;
}

.flag.flag-ph {
	background-position: 0 -121px;
}

.flag.flag-pk {
	background-position: -16px -121px;
}

.flag.flag-pl {
	background-position: -32px -121px;
}

.flag.flag-pm {
	background-position: -48px -121px;
}

.flag.flag-pn {
	background-position: -64px -121px;
}

.flag.flag-pr {
	background-position: -80px -121px;
}

.flag.flag-ps {
	background-position: -96px -121px;
}

.flag.flag-pt {
	background-position: -112px -121px;
}

.flag.flag-pw {
	background-position: -128px -121px;
}

.flag.flag-py {
	background-position: -144px -121px;
}

.flag.flag-qa {
	background-position: -160px -121px;
}

.flag.flag-re {
	background-position: -176px -121px;
}

.flag.flag-ro {
	background-position: -192px -121px;
}

.flag.flag-rs {
	background-position: -208px -121px;
}

.flag.flag-ru {
	background-position: -224px -121px;
}

.flag.flag-rw {
	background-position: -240px -121px;
}

.flag.flag-sa {
	background-position: 0 -132px;
}

.flag.flag-sb {
	background-position: -16px -132px;
}

.flag.flag-sc {
	background-position: -32px -132px;
}

.flag.flag-scotland {
	background-position: -48px -132px;
}

.flag.flag-sd {
	background-position: -64px -132px;
}

.flag.flag-se {
	background-position: -80px -132px;
}

.flag.flag-sg {
	background-position: -96px -132px;
}

.flag.flag-sh {
	background-position: -112px -132px;
}

.flag.flag-si {
	background-position: -128px -132px;
}

.flag.flag-sk {
	background-position: -144px -132px;
}

.flag.flag-sl {
	background-position: -160px -132px;
}

.flag.flag-sm {
	background-position: -176px -132px;
}

.flag.flag-sn {
	background-position: -192px -132px;
}

.flag.flag-so {
	background-position: -208px -132px;
}

.flag.flag-somaliland {
	background-position: -224px -132px;
}

.flag.flag-sr {
	background-position: -240px -132px;
}

.flag.flag-ss {
	background-position: 0 -143px;
}

.flag.flag-st {
	background-position: -16px -143px;
}

.flag.flag-sv {
	background-position: -32px -143px;
}

.flag.flag-sx {
	background-position: -48px -143px;
}

.flag.flag-sy {
	background-position: -64px -143px;
}

.flag.flag-sz {
	background-position: -80px -143px;
}

.flag.flag-tc {
	background-position: -96px -143px;
}

.flag.flag-td {
	background-position: -112px -143px;
}

.flag.flag-tf {
	background-position: -128px -143px;
}

.flag.flag-tg {
	background-position: -144px -143px;
}

.flag.flag-th {
	background-position: -160px -143px;
}

.flag.flag-tibet {
	background-position: -176px -143px;
}

.flag.flag-tj {
	background-position: -192px -143px;
}

.flag.flag-tk {
	background-position: -208px -143px;
}

.flag.flag-tl {
	background-position: -224px -143px;
}

.flag.flag-tm {
	background-position: -240px -143px;
}

.flag.flag-tn {
	background-position: 0 -154px;
}

.flag.flag-to {
	background-position: -16px -154px;
}

.flag.flag-tr {
	background-position: -32px -154px;
}

.flag.flag-tt {
	background-position: -48px -154px;
}

.flag.flag-tv {
	background-position: -64px -154px;
}

.flag.flag-tw {
	background-position: -80px -154px;
}

.flag.flag-tz {
	background-position: -96px -154px;
}

.flag.flag-ua {
	background-position: -112px -154px;
}

.flag.flag-ug {
	background-position: -128px -154px;
}

.flag.flag-um {
	background-position: -144px -154px;
}

.flag.flag-us {
	background-position: -160px -154px;
}

.flag.flag-uy {
	background-position: -176px -154px;
}

.flag.flag-uz {
	background-position: -192px -154px;
}

.flag.flag-va {
	background-position: -208px -154px;
}

.flag.flag-vc {
	background-position: -224px -154px;
}

.flag.flag-ve {
	background-position: -240px -154px;
}

.flag.flag-vg {
	background-position: 0 -165px;
}

.flag.flag-vi {
	background-position: -16px -165px;
}

.flag.flag-vn {
	background-position: -32px -165px;
}

.flag.flag-vu {
	background-position: -48px -165px;
}

.flag.flag-wales {
	background-position: -64px -165px;
}

.flag.flag-wf {
	background-position: -80px -165px;
}

.flag.flag-ws {
	background-position: -96px -165px;
}

.flag.flag-xk {
	background-position: -112px -165px;
}

.flag.flag-ye {
	background-position: -128px -165px;
}

.flag.flag-yt {
	background-position: -144px -165px;
}

.flag.flag-za {
	background-position: -160px -165px;
}

.flag.flag-zanzibar {
	background-position: -176px -165px;
}

.flag.flag-zm {
	background-position: -192px -165px;
}

.flag.flag-zw {
	background-position: -208px -165px;
}

/* Forms */
form label {
	font-weight: normal;
}

textarea {
	resize: vertical;
}

select {
	border: 1px solid #E5E7E9;
	border-radius: 6px;
	outline: none;
}

.label {
	font-weight: normal;
}

label.required:after {
	content: "*";
	font-size: 0.8em;
	margin: 0.3em;
	position: relative;
	top: -2px;
}

.form-group {
	margin-bottom: 16px;
	margin-bottom: 1rem;
}

.form-group:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}

.form-control {
	border-color: rgba(0, 0, 0, 0.09);
}

.form-control:not(.form-control-lg) {
	font-size: 12px;
	font-size: 0.75rem;
	line-height: 1.3;
}

.form-control:not(.form-control-sm):not(.form-control-lg) {
	font-size: 13.6px;
	font-size: 0.85rem;
	line-height: 1.85;
}

.form-control.form-control-focused {
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
	border-color: var(--grey-500);
}

.form-control:focus {
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
	border-color: var(--grey-500);
}

.form-control.error {
	border-color: #a94442;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.form-control.error:focus {
	border-color: #843534;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}

.form-control-icon.is-invalid,
.was-validated .form-control-icon:invalid,
.form-control.is-valid:focus,
.was-validated .form-control:valid:focus {
	background-image: none !important;
}

.form-select.form-select-icon-light {
	background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>");
}

.form-switch.form-switch-sm {
	margin-bottom: 8px;
	margin-bottom: 0.5rem;
}

.form-switch.form-switch-sm .form-check-input {
	height: 16px;
	height: 1rem;
	width: calc(1rem + 0.75rem);
	border-radius: 2rem;
}

.form-switch.form-switch-md {
	margin-bottom: 16px;
	margin-bottom: 1rem;
}

.form-switch.form-switch-md .form-check-input {
	height: 24px;
	height: 1.5rem;
	width: calc(2rem + 0.75rem);
	border-radius: 3rem;
}

.form-switch.form-switch-lg {
	margin-bottom: 24px;
	margin-bottom: 1.5rem;
}

.form-switch.form-switch-lg .form-check-input {
	height: 32px;
	height: 2rem;
	width: calc(3rem + 0.75rem);
	border-radius: 4rem;
}

.form-switch.form-switch-xl {
	margin-bottom: 32px;
	margin-bottom: 2rem;
}

.form-switch.form-switch-xl .form-check-input {
	height: 40px;
	height: 2.5rem;
	width: calc(4rem + 0.75rem);
	border-radius: 5rem;
}

.form-switch .form-check-input {
	box-shadow: none !important;
}

.form-switch .form-check-input:focus {
	border: 1px solid rgba(0, 0, 0, 0.25);
	background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='rgba%280, 0, 0, 0.25%29'/></svg>") !important;
}

.form-switch .form-check-input:checked {
	background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='white'/></svg>") !important;
}

.page-link {
	border-color: rgba(0, 0, 0, 0.06);
}

.input-group .form-control {
	height: auto;
}

.input-group-rounded .form-control {
	border-radius: 2rem 0rem 0rem 2rem;
	border: 0;
	padding-left: 16px;
	padding-left: 1rem;
}

.input-group-rounded .btn {
	border-color: rgba(0, 0, 0, 0.09);
	border-radius: 0rem 2rem 2rem 0rem;
	font-size: 11.2px;
	font-size: 0.7rem;
	padding: 13.92px;
	padding: 0.87rem;
	border-top: 0;
	border-bottom: 0;
	border-right: 0;
	z-index: 3;
}

/* Form - iOS Override */
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
textarea {
	-webkit-appearance: none;
}

.form-control::-webkit-input-placeholder,
input[type="text"]::-webkit-input-placeholder,
input[type="password"]::-webkit-input-placeholder,
input[type="datetime"]::-webkit-input-placeholder,
input[type="datetime-local"]::-webkit-input-placeholder,
input[type="date"]::-webkit-input-placeholder,
input[type="month"]::-webkit-input-placeholder,
input[type="time"]::-webkit-input-placeholder,
input[type="week"]::-webkit-input-placeholder,
input[type="number"]::-webkit-input-placeholder,
input[type="email"]::-webkit-input-placeholder,
input[type="url"]::-webkit-input-placeholder,
input[type="search"]::-webkit-input-placeholder,
input[type="tel"]::-webkit-input-placeholder,
input[type="color"]::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
	color: #bdbdbd;
}

.form-control::-moz-placeholder,
input[type="text"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="datetime"]::-moz-placeholder,
input[type="datetime-local"]::-moz-placeholder,
input[type="date"]::-moz-placeholder,
input[type="month"]::-moz-placeholder,
input[type="time"]::-moz-placeholder,
input[type="week"]::-moz-placeholder,
input[type="number"]::-moz-placeholder,
input[type="email"]::-moz-placeholder,
input[type="url"]::-moz-placeholder,
input[type="search"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
input[type="color"]::-moz-placeholder,
textarea::-moz-placeholder {
	color: #bdbdbd;
}

.form-control:-ms-input-placeholder,
input[type="text"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="datetime"]:-ms-input-placeholder,
input[type="datetime-local"]:-ms-input-placeholder,
input[type="date"]:-ms-input-placeholder,
input[type="month"]:-ms-input-placeholder,
input[type="time"]:-ms-input-placeholder,
input[type="week"]:-ms-input-placeholder,
input[type="number"]:-ms-input-placeholder,
input[type="email"]:-ms-input-placeholder,
input[type="url"]:-ms-input-placeholder,
input[type="search"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
input[type="color"]:-ms-input-placeholder,
textarea:-ms-input-placeholder {
	color: #bdbdbd;
}

/* Placeholder Light */
.form-placeholders-light .form-control::-webkit-input-placeholder,
.form-placeholders-light input[type="text"]::-webkit-input-placeholder,
.form-placeholders-light input[type="password"]::-webkit-input-placeholder,
.form-placeholders-light input[type="datetime"]::-webkit-input-placeholder,
.form-placeholders-light input[type="datetime-local"]::-webkit-input-placeholder,
.form-placeholders-light input[type="date"]::-webkit-input-placeholder,
.form-placeholders-light input[type="month"]::-webkit-input-placeholder,
.form-placeholders-light input[type="time"]::-webkit-input-placeholder,
.form-placeholders-light input[type="week"]::-webkit-input-placeholder,
.form-placeholders-light input[type="number"]::-webkit-input-placeholder,
.form-placeholders-light input[type="email"]::-webkit-input-placeholder,
.form-placeholders-light input[type="url"]::-webkit-input-placeholder,
.form-placeholders-light input[type="search"]::-webkit-input-placeholder,
.form-placeholders-light input[type="tel"]::-webkit-input-placeholder,
.form-placeholders-light input[type="color"]::-webkit-input-placeholder,
.form-placeholders-light textarea::-webkit-input-placeholder {
	color: #FFF;
}

.form-placeholders-light .form-control::-moz-placeholder,
.form-placeholders-light input[type="text"]::-moz-placeholder,
.form-placeholders-light input[type="password"]::-moz-placeholder,
.form-placeholders-light input[type="datetime"]::-moz-placeholder,
.form-placeholders-light input[type="datetime-local"]::-moz-placeholder,
.form-placeholders-light input[type="date"]::-moz-placeholder,
.form-placeholders-light input[type="month"]::-moz-placeholder,
.form-placeholders-light input[type="time"]::-moz-placeholder,
.form-placeholders-light input[type="week"]::-moz-placeholder,
.form-placeholders-light input[type="number"]::-moz-placeholder,
.form-placeholders-light input[type="email"]::-moz-placeholder,
.form-placeholders-light input[type="url"]::-moz-placeholder,
.form-placeholders-light input[type="search"]::-moz-placeholder,
.form-placeholders-light input[type="tel"]::-moz-placeholder,
.form-placeholders-light input[type="color"]::-moz-placeholder,
.form-placeholders-light textarea::-moz-placeholder {
	color: #FFF;
}

.form-placeholders-light .form-control:-ms-input-placeholder,
.form-placeholders-light input[type="text"]:-ms-input-placeholder,
.form-placeholders-light input[type="password"]:-ms-input-placeholder,
.form-placeholders-light input[type="datetime"]:-ms-input-placeholder,
.form-placeholders-light input[type="datetime-local"]:-ms-input-placeholder,
.form-placeholders-light input[type="date"]:-ms-input-placeholder,
.form-placeholders-light input[type="month"]:-ms-input-placeholder,
.form-placeholders-light input[type="time"]:-ms-input-placeholder,
.form-placeholders-light input[type="week"]:-ms-input-placeholder,
.form-placeholders-light input[type="number"]:-ms-input-placeholder,
.form-placeholders-light input[type="email"]:-ms-input-placeholder,
.form-placeholders-light input[type="url"]:-ms-input-placeholder,
.form-placeholders-light input[type="search"]:-ms-input-placeholder,
.form-placeholders-light input[type="tel"]:-ms-input-placeholder,
.form-placeholders-light input[type="color"]:-ms-input-placeholder,
.form-placeholders-light textarea:-ms-input-placeholder {
	color: #FFF;
}

/* Form - Bootstrap Override */
.btn-lg,
.btn-group-lg > .btn {
	line-height: 1.334;
}

.input-group-btn .btn {
	padding-top: 8px;
	padding-top: 0.5rem;
	padding-bottom: 8px;
	padding-bottom: 0.5rem;
}

select.form-control-sm, select.form-control-lg {
	line-height: 1;
	height: auto !important;
}

.custom-select-1 {
	position: relative;
}

.custom-select-1 select {
	line-height: 1;
	font-size: inherit;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

.custom-select-1 select::-ms-expand {
	display: none;
}

.custom-select-1.custom-select-1-icon-light select {
	background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>");
}

/* Custom Checkbox */
.custom-checkbox-1 {
	position: relative;
	padding: 0 0 0 25px;
	margin-bottom: 7px;
	margin-top: 0;
}

.custom-checkbox-1.form-check .form-check-input {
	margin-left: 0;
}

.custom-checkbox-1.form-check .form-check-label {
	padding-left: 0;
}

.custom-checkbox-1.checkbox-inline {
	display: inline-block;
	vertical-align: middle;
}

.form-group .custom-checkbox-1.checkbox-inline {
	margin-top: 7px;
	padding-top: 0;
}

.custom-checkbox-1:last-child, .custom-checkbox-1:last-of-type {
	margin-bottom: 0;
}

.custom-checkbox-1 input[type="checkbox"] {
	opacity: 0;
	position: absolute;
	top: 50%;
	left: 3px;
	margin: -6px 0 0 0;
	z-index: 2;
	cursor: pointer;
}

.custom-checkbox-1 input[type="checkbox"]:checked + label:after {
	position: absolute;
	display: inline-block;
	font-family: 'Font Awesome 6 Free';
	content: '\F00C';
	top: 50%;
	left: 4px;
	margin-top: -5px;
	font-size: 11px;
	font-weight: 900;
	line-height: 1;
	width: 16px;
	height: 16px;
	color: var(--dark--200);
}

.custom-checkbox-1 input[type="checkbox"]:disabled {
	cursor: not-allowed;
}

.custom-checkbox-1 input[type="checkbox"]:disabled:checked + label:after {
	color: #999;
}

.custom-checkbox-1 input[type="checkbox"]:disabled + label {
	cursor: not-allowed;
}

.custom-checkbox-1 input[type="checkbox"]:disabled + label:before {
	background-color: #eee;
}

.custom-checkbox-1 label {
	cursor: pointer;
	margin-bottom: 0;
	text-align: left;
	line-height: 1.5;
}

.custom-checkbox-1 label:before {
	content: '';
	position: absolute;
	top: 50%;
	left: 0;
	margin-top: -9px;
	width: 19px;
	height: 18px;
	display: inline-block;
	border-radius: 2px;
	border: 1px solid var(--grey-500);
	background-color: var(--light);
}

.custom-checkbox-1 label + label.error {
	display: block;
}

.custom-checkbox-1.checkbox-custom-transparent label:before {
	background-color: transparent;
}

.custom-checkbox-1.checkbox-custom-transparent input[type="checkbox"]:checked + label:after {
	color: var(--light);
}

.custom-checkbox-1.checkbox-custom-transparent input[type="checkbox"]:disabled:checked + label:after {
	color: #999;
}

/* Custom Checkbox Switch */
.custom-checkbox-switch {
	position: relative;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	outline: none;
	width: 50px;
	height: 26px;
	background-color: var(--grey-500);
	border: 1px solid #D9DADC;
	border-radius: 50px;
	cursor: pointer;
	transition: ease all 300ms;
}

.custom-checkbox-switch:after {
	content: '';
	position: absolute;
	top: 50%;
	left: 6px;
	width: 15px;
	height: 15px;
	background-color: var(--light);
	border-radius: 50%;
	transform: translate3d(0, -50%, 0);
	transition: ease left 300ms;
}

.custom-checkbox-switch:checked {
	border-color: var(--grey-500);
}

.custom-checkbox-switch:checked:after {
	left: 27px;
}

.bootstrap-timepicker-widget input {
	border: 0;
}

.bootstrap-timepicker-widget table td input {
	width: 40px;
}

.fileupload .btn {
	line-height: 20px;
	margin-left: -5px;
}

/* Forms Validations */
label.valid {
	display: inline-block;
	text-indent: -9999px;
}

label.error {
	color: #c10000;
	font-size: 0.9em;
	line-height: 18px;
	padding: 5px 0 0;
}

label.error#captcha-error {
	margin-top: 15px;
}

.form-errors-light .form-control.error {
	border-color: var(--light);
}

.form-errors-light label.error {
	color: var(--light);
}

/* Search Cancel Button */
input[type="search" i]::-webkit-search-cancel-button {
	margin-right: -7px !important;
}

/* Simple Search */
.simple-search .form-control {
	border-radius: 1.3rem 0 0 1.3rem;
	border-color: #EEE;
	border-right: 0;
}

.simple-search .form-control:focus {
	box-shadow: none;
}

.simple-search .btn {
	border: 0;
	box-shadow: none !important;
	border-radius: 0 1.3rem 1.3rem 0;
	border: 1px solid #EEE;
	border-left: 0;
}

/* Search With Select */
.search-with-select .mobile-search-toggle-btn {
	display: none;
}

.search-with-select .search-form-wrapper > .form-control {
	background-color: #f4f4f4;
	border-radius: 1.3rem 0 0 1.3rem;
	border: 0;
	height: auto;
	padding: 9.6px 22.4px;
	padding: 0.6rem 1.4rem;
	font-size: 12.8px !important;
	font-size: 0.8rem !important;
	color: #8d8d8d;
}

.search-with-select .search-form-wrapper > .form-control::-webkit-input-placeholder {
	color: #8d8d8d;
}

.search-with-select .search-form-wrapper > .form-control::-moz-placeholder {
	color: #8d8d8d;
}

.search-with-select .search-form-wrapper > .form-control:-ms-input-placeholder {
	color: #8d8d8d;
}

.search-with-select .search-form-wrapper > .form-control:focus {
	box-shadow: none;
}

.search-with-select .search-form-wrapper .search-form-select-wrapper {
	display: flex;
	background-color: #f4f4f4;
	border-radius: 0 1.3rem 1.3rem 0;
}

.search-with-select .search-form-wrapper .search-form-select-wrapper .custom-select-1:before {
	color: #8d8d8d;
}

.search-with-select .search-form-wrapper .search-form-select-wrapper .custom-select-1 select {
	background-color: transparent;
	border: 1px solid #FFF;
	border-top-width: 0;
	border-bottom-width: 0;
	border-radius: 0;
	color: #8d8d8d;
	height: 100%;
	z-index: 1;
}

.search-with-select .search-form-wrapper .search-form-select-wrapper .custom-select-1 select:focus {
	box-shadow: none;
}

.search-with-select .search-form-wrapper .search-form-select-wrapper .btn {
	background: transparent !important;
	border: 0;
	box-shadow: none !important;
}

.search-with-select .search-form-wrapper .search-form-select-wrapper .btn .header-nav-top-icon {
	font-size: 16px;
	font-size: 1rem;
	top: 2px;
	right: 2px;
	color: var(--dark);
	font-weight: bold;
}

@media (max-width: 991px) {
	.search-with-select {
		position: relative;
		float: right;
	}

	.search-with-select .mobile-search-toggle-btn {
		display: block;
		text-decoration: none !important;
	}

	.search-with-select .mobile-search-toggle-btn.open + .search-form-wrapper {
		display: flex;
		visibility: visible;
		opacity: 1;
	}

	.search-with-select .mobile-search-toggle-btn > i {
		position: relative;
		top: 2px;
		font-size: 1.9rem;
	}

	.search-with-select .search-form-wrapper {
		display: none;
		visibility: hidden;
		opacity: 0;
		position: absolute;
		top: 36px;
		left: 50%;
		transform: translate3d(-50%, 0, 0);
		width: 300px;
		background-color: #d8d8d8;
		padding: 0.2rem;
		border-radius: 28px;
		transition: ease opacity 300ms;
	}

	.search-with-select .search-form-wrapper:before {
		content: '';
		position: absolute;
		bottom: 100%;
		left: 50%;
		border-bottom: 6px solid #d8d8d8;
		border-left: 6px solid transparent;
		border-right: 6px solid transparent;
		transform: translate3d(-50%, 0, 0);
	}
}

/* Form Styles */
.form-style-2 .form-group {
	margin-bottom: 11.2px;
	margin-bottom: 0.7rem;
}

.form-style-2 .form-control {
	padding: 11.2px 16px;
	padding: 0.7rem 1rem;
	border: 0;
}

.form-style-2 .form-control::-webkit-input-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-2 .form-control::-moz-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-2 .form-control:-ms-input-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-2 .form-control:-moz-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-2 .form-control:not(textarea) {
	height: calc(2.2em + .75rem + 2px);
}

.form-style-2 .form-control.error {
	border-width: 2px;
}

.form-style-3 .form-group {
	margin-bottom: 17.6px;
	margin-bottom: 1.1rem;
}

.form-style-3 .form-control {
	padding: 11.2px 16px;
	padding: 0.7rem 1rem;
	background-color: #F4F4F4;
	border: none;
}

.form-style-3 .form-control::-webkit-input-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-3 .form-control::-moz-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-3 .form-control:-ms-input-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-3 .form-control:-moz-placeholder {
	color: #202528;
	opacity: 0.5;
}

.form-style-3 .form-control.error {
	border: 2px solid #e21a1a;
}

.form-style-4 .form-control {
	background-color: transparent;
	border-top: 0;
	border-right: 0;
	border-left: 0;
	border-bottom-color: rgba(0, 0, 0, 0.4);
	color: var(--light);
	box-shadow: none !important;
	border-radius: 0;
}

.form-style-4 .form-control::-moz-placeholder {
	color: #d8d8d8;
}

.form-style-4 .form-control::placeholder {
	color: #d8d8d8;
}

.form-style-4 select.form-control {
	color: var(--light);
}

.form-style-4 select.form-control option {
	color: var(--default);
}

.form-style-4 .form-check-label {
	color: #d8d8d8;
}

.form-style-4.form-style-4-text-dark .form-control {
	color: #212121;
}

.form-style-4.form-style-4-text-dark .form-control::-moz-placeholder {
	color: var(--default);
}

.form-style-4.form-style-4-text-dark .form-control::placeholder {
	color: var(--default);
}

.form-style-4.form-style-4-text-dark .form-check-label {
	color: var(--default);
}

.form-style-4.form-style-4-border-light .form-control {
	border-color: rgba(255, 255, 255, 0.4);
}

.form-style-4.form-style-4-border-light-2 .form-control {
	border-color: rgba(255, 255, 255, 0.15);
}

.form-style-4.form-with-icons .form-control {
	padding-left: 31px;
}

.form-style-5 .form-control {
	background-color: rgba(255, 255, 255, 0.05);
	border-radius: 35px;
	height: auto;
	padding: 8px 24px;
	padding: 0.5rem 1.5rem;
	color: #bfbfbf;
}

.form-style-5 .form-control:focus {
	border-color: #676767;
}

/* Floating Labels */
.form-label-group {
	position: relative;
	margin-bottom: 16px;
	margin-bottom: 1rem;
}

.form-label-group input, .form-label-group label {
	height: 50px;
	height: 3.125rem;
	padding: 12px;
	padding: .75rem;
}

.form-label-group label {
	position: absolute;
	top: 0;
	left: 0;
	display: block;
	width: 100%;
	margin-bottom: 0;
	line-height: 1.5;
	color: #495057;
	pointer-events: none;
	cursor: text;
	border: 1px solid transparent;
	border-radius: .25rem;
	transition: all .1s ease-in-out;
}

.form-label-group input::-moz-placeholder {
	color: transparent;
}

.form-label-group input::placeholder {
	color: transparent;
}

.form-label-group input:not(:-moz-placeholder-shown) {
	padding-top: 20px;
	padding-top: 1.25rem;
	padding-bottom: 4px;
	padding-bottom: .25rem;
}

.form-label-group input:not(:placeholder-shown) {
	padding-top: 20px;
	padding-top: 1.25rem;
	padding-bottom: 4px;
	padding-bottom: .25rem;
}

.form-label-group input:not(:-moz-placeholder-shown) ~ label {
	padding-top: 4px;
	padding-top: .25rem;
	padding-bottom: 4px;
	padding-bottom: .25rem;
	font-size: 12px;
	color: var(--default);
}

.form-label-group input:not(:placeholder-shown) ~ label {
	padding-top: 4px;
	padding-top: .25rem;
	padding-bottom: 4px;
	padding-bottom: .25rem;
	font-size: 12px;
	color: var(--default);
}

/* Fallback for Edge
-------------------------------------------------- */
@supports (-ms-ime-align: auto) {
	.form-label-group {
		display: flex;
		flex-direction: column-reverse;
	}

	.form-label-group label {
		position: static;
	}

	.form-label-group input::-ms-input-placeholder {
		color: var(--default);
	}
}

/* Form Squared Borders */
.form-squared-borders .form-control {
	border-radius: 0;
}

/* Form With Icons */
.form-with-icons .form-control {
	padding-left: 41px;
}

/* Form Fields Rounded */
.form-fields-rounded .form-control {
	padding-left: 18px;
	padding-right: 18px;
	border-radius: 35px;
}

.form-fields-rounded textarea.form-control {
	border-radius: 22px;
}

/* Form With Shadow */
.form-with-shadow .form-control {
	box-shadow: 0px 0px 17px -4px rgba(0, 0, 0, 0.1) !important;
}

/* Form Fieds Size - Global class to increase size of form controls */
.form-fields-size-sm .form-control {
	padding: 0 15px;
	height: 40px;
}

.form-fields-size-sm textarea.form-control {
	padding: 12px 15px;
	height: auto;
}

.form-fields-size-md .form-control {
	padding: 0 23px;
	height: 54px;
}

.form-fields-size-md textarea.form-control {
	padding: 18px 23px;
	height: auto;
}

.form-fields-size-lg .form-control {
	padding: 0 25px;
	height: 63px;
}

.form-fields-size-lg textarea.form-control {
	padding: 23px 25px;
	height: auto;
}

/* Form Fieds Font Size - Global class to increase font size of form controls */
.form-fields-font-size-sm .form-control:not(.form-control-sm):not(.form-control-lg) {
	font-size: 12px;
	font-size: 0.75rem;
}

.form-fields-font-size-md .form-control:not(.form-control-sm):not(.form-control-lg) {
	font-size: 15.2px;
	font-size: 0.95rem;
}

.form-fields-font-size-lg .form-control:not(.form-control-sm):not(.form-control-lg) {
	font-size: 18.4px;
	font-size: 1.15rem;
}

/* Custom Checkbox - Skin */
.custom-control-input:checked ~ .custom-control-label::before,
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
	background-color: var(--primary);
}

/* Custom Checkbox Switch - Skin */
.custom-checkbox-switch:checked {
	background-color: var(--primary);
	border-color: var(--primary);
}

/* Form Control - Skin */
.form-control.bg-primary, .form-control.bg-color-primary {
	color: var(--primary-inverse);
	border-color: var(--primary);
}

.form-control.bg-primary::-webkit-input-placeholder, .form-control.bg-color-primary::-webkit-input-placeholder {
	color: var(--primary-inverse);
}

.form-control.bg-primary::-moz-placeholder, .form-control.bg-color-primary::-moz-placeholder {
	color: var(--primary-inverse);
}

.form-control.bg-primary:-ms-input-placeholder, .form-control.bg-color-primary:-ms-input-placeholder {
	color: var(--primary-inverse);
}

.form-control.bg-primary:-moz-placeholder, .form-control.bg-color-primary:-moz-placeholder {
	color: var(--primary-inverse);
}

.form-control .testimonial-primary blockquote {
	background: var(--primary--100);
}

.form-control .testimonial-primary .testimonial-arrow-down {
	border-top-color: var(--primary--100);
}

.form-control .testimonial-primary.testimonial-style-7 {
	background: var(--primary--100);
}

.form-control .testimonial-quotes-primary blockquote:before, .form-control .testimonial-quotes-primary blockquote:after {
	color: var(--primary) !important;
}

.form-control.bg-secondary, .form-control.bg-color-secondary {
	color: var(--secondary-inverse);
	border-color: var(--secondary);
}

.form-control.bg-secondary::-webkit-input-placeholder, .form-control.bg-color-secondary::-webkit-input-placeholder {
	color: var(--secondary-inverse);
}

.form-control.bg-secondary::-moz-placeholder, .form-control.bg-color-secondary::-moz-placeholder {
	color: var(--secondary-inverse);
}

.form-control.bg-secondary:-ms-input-placeholder, .form-control.bg-color-secondary:-ms-input-placeholder {
	color: var(--secondary-inverse);
}

.form-control.bg-secondary:-moz-placeholder, .form-control.bg-color-secondary:-moz-placeholder {
	color: var(--secondary-inverse);
}

.form-control .testimonial-secondary blockquote {
	background: var(--secondary--100);
}

.form-control .testimonial-secondary .testimonial-arrow-down {
	border-top-color: var(--secondary--100);
}

.form-control .testimonial-secondary.testimonial-style-7 {
	background: var(--secondary--100);
}

.form-control .testimonial-quotes-secondary blockquote:before, .form-control .testimonial-quotes-secondary blockquote:after {
	color: var(--primary) !important;
}

.form-control.bg-tertiary, .form-control.bg-color-tertiary {
	color: var(--tertiary-inverse);
	border-color: var(--tertiary);
}

.form-control.bg-tertiary::-webkit-input-placeholder, .form-control.bg-color-tertiary::-webkit-input-placeholder {
	color: var(--tertiary-inverse);
}

.form-control.bg-tertiary::-moz-placeholder, .form-control.bg-color-tertiary::-moz-placeholder {
	color: var(--tertiary-inverse);
}

.form-control.bg-tertiary:-ms-input-placeholder, .form-control.bg-color-tertiary:-ms-input-placeholder {
	color: var(--tertiary-inverse);
}

.form-control.bg-tertiary:-moz-placeholder, .form-control.bg-color-tertiary:-moz-placeholder {
	color: var(--tertiary-inverse);
}

.form-control .testimonial-tertiary blockquote {
	background: var(--tertiary--100);
}

.form-control .testimonial-tertiary .testimonial-arrow-down {
	border-top-color: var(--tertiary--100);
}

.form-control .testimonial-tertiary.testimonial-style-7 {
	background: var(--tertiary--100);
}

.form-control .testimonial-quotes-tertiary blockquote:before, .form-control .testimonial-quotes-tertiary blockquote:after {
	color: var(--primary) !important;
}

.form-control.bg-quaternary, .form-control.bg-color-quaternary {
	color: var(--quaternary-inverse);
	border-color: var(--quaternary);
}

.form-control.bg-quaternary::-webkit-input-placeholder, .form-control.bg-color-quaternary::-webkit-input-placeholder {
	color: var(--quaternary-inverse);
}

.form-control.bg-quaternary::-moz-placeholder, .form-control.bg-color-quaternary::-moz-placeholder {
	color: var(--quaternary-inverse);
}

.form-control.bg-quaternary:-ms-input-placeholder, .form-control.bg-color-quaternary:-ms-input-placeholder {
	color: var(--quaternary-inverse);
}

.form-control.bg-quaternary:-moz-placeholder, .form-control.bg-color-quaternary:-moz-placeholder {
	color: var(--quaternary-inverse);
}

.form-control .testimonial-quaternary blockquote {
	background: var(--quaternary--100);
}

.form-control .testimonial-quaternary .testimonial-arrow-down {
	border-top-color: var(--quaternary--100);
}

.form-control .testimonial-quaternary.testimonial-style-7 {
	background: var(--quaternary--100);
}

.form-control .testimonial-quotes-quaternary blockquote:before, .form-control .testimonial-quotes-quaternary blockquote:after {
	color: var(--primary) !important;
}

.form-control.bg-dark, .form-control.bg-color-dark {
	color: var(--dark-inverse);
	border-color: var(--dark);
}

.form-control.bg-dark::-webkit-input-placeholder, .form-control.bg-color-dark::-webkit-input-placeholder {
	color: var(--dark-inverse);
}

.form-control.bg-dark::-moz-placeholder, .form-control.bg-color-dark::-moz-placeholder {
	color: var(--dark-inverse);
}

.form-control.bg-dark:-ms-input-placeholder, .form-control.bg-color-dark:-ms-input-placeholder {
	color: var(--dark-inverse);
}

.form-control.bg-dark:-moz-placeholder, .form-control.bg-color-dark:-moz-placeholder {
	color: var(--dark-inverse);
}

.form-control .testimonial-dark blockquote {
	background: var(--dark--100);
}

.form-control .testimonial-dark .testimonial-arrow-down {
	border-top-color: var(--dark--100);
}

.form-control .testimonial-dark.testimonial-style-7 {
	background: var(--dark--100);
}

.form-control .testimonial-quotes-dark blockquote:before, .form-control .testimonial-quotes-dark blockquote:after {
	color: var(--primary) !important;
}

.form-control.bg-light, .form-control.bg-color-light {
	color: var(--light-inverse);
	border-color: var(--light);
}

.form-control.bg-light::-webkit-input-placeholder, .form-control.bg-color-light::-webkit-input-placeholder {
	color: var(--light-inverse);
}

.form-control.bg-light::-moz-placeholder, .form-control.bg-color-light::-moz-placeholder {
	color: var(--light-inverse);
}

.form-control.bg-light:-ms-input-placeholder, .form-control.bg-color-light:-ms-input-placeholder {
	color: var(--light-inverse);
}

.form-control.bg-light:-moz-placeholder, .form-control.bg-color-light:-moz-placeholder {
	color: var(--light-inverse);
}

.form-control .testimonial-light blockquote {
	background: var(--light--100);
}

.form-control .testimonial-light .testimonial-arrow-down {
	border-top-color: var(--light--100);
}

.form-control .testimonial-light.testimonial-style-7 {
	background: var(--light--100);
}

.form-control .testimonial-quotes-light blockquote:before, .form-control .testimonial-quotes-light blockquote:after {
	color: var(--primary) !important;
}

/* Form Range - Skin */
.form-range::-webkit-slider-thumb {
	background-color: var(--primary);
}

.form-range::-webkit-slider-thumb:active {
	background-color: var(--primary-rgba-35);
}

.form-range:focus::-webkit-slider-thumb {
	box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem var(--primary-rgba-35);
}

/* Form Check - Skin */
.form-switch .form-check-input:checked {
	background-color: var(--primary);
	border-color: var(--primary) !important;
}

/* GDPR */
[data-plugin-gdpr-wrapper] {
	opacity: 0;
	transition: ease opacity 300ms;
}

[data-plugin-gdpr-wrapper].show {
	opacity: 1;
}

.gdpr-bar {
	position: fixed;
	width: 100%;
	bottom: 0;
	left: 0;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	background: rgba(0, 0, 0, 0.9);
	padding: 16px 24px;
	padding: 1rem 1.5rem;
	z-index: 1050;
	opacity: 0;
	visibility: hidden;
	transform: translate3d(0, 100%, 0);
	transition: ease opacity 300ms, ease transform 300ms;
}

.gdpr-bar .gdpr-bar-actions {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 15px;
}

.gdpr-bar .gdpr-bar-actions .gdpr-agree-trigger {
	margin-left: 32px;
	margin-left: 2rem;
}

.gdpr-bar.show {
	opacity: 1;
	visibility: visible;
	transform: translate3d(0, 0, 0);
}

.gdpr-bar.removing {
	opacity: 0;
	transform: translate3d(0, 100%, 0);
}

@media (min-width: 1200px) {
	.gdpr-bar {
		flex-direction: row;
		align-items: center;
	}

	.gdpr-bar .gdpr-bar-actions {
		justify-content: flex-end;
		margin-top: 0;
	}
}

.gdpr-preferences-popup {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 20px;
	background: rgba(0, 0, 0, 0.8);
	z-index: 1051;
	opacity: 0;
	visibility: hidden;
	transition: ease opacity 300ms;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content {
	max-width: 1000px;
	background: var(--light);
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-close-popup {
	position: absolute;
	top: 25px;
	right: 25px;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body {
	max-height: 60vh;
	overflow-y: auto;
	padding: 40px;
	padding: 2.5rem;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item {
	display: flex;
	flex-wrap: wrap;
	font-size: 12.8px;
	font-size: 0.8rem;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item .gdpr-cookie-item-title {
	width: 100%;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item .gdpr-cookie-item-description {
	width: 70%;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item .gdpr-cookie-item-action {
	width: 30%;
	text-align: right;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item + .gdpr-cookie-item {
	margin-top: 10px;
}

.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24px 40px;
	padding: 1.5rem 2.5rem;
	background: #f4f4f4;
}

.gdpr-preferences-popup.show {
	opacity: 1;
	visibility: visible;
}

@media (min-width: 992px) {
	.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item {
		flex-wrap: nowrap;
	}

	.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item .gdpr-cookie-item-title {
		width: 20%;
	}

	.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item .gdpr-cookie-item-description {
		width: 60%;
	}

	.gdpr-preferences-popup .gdpr-preferences-popup-content .gdpr-preferences-popup-content-body .gdpr-cookie-item .gdpr-cookie-item-action {
		width: 20%;
	}
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
	color: var(--dark);
	font-weight: 200;
	letter-spacing: -.05em;
	margin: 0;
	-webkit-font-smoothing: antialiased;
}

h1 {
	font-size: 2.6em;
	line-height: 44px;
	margin: 0 0 32px 0;
}

h2 {
	font-size: 2.2em;
	font-weight: 300;
	line-height: 42px;
	margin: 0 0 32px 0;
}

h3 {
	font-size: 1.8em;
	font-weight: 400;
	line-height: 24px;
	margin-bottom: 33px;
	text-transform: uppercase;
}

h4 {
	font-size: 1.4em;
	font-weight: 600;
	line-height: 27px;
	margin: 0 0 14px 0;
}

h5 {
	font-size: 1em;
	font-weight: 600;
	line-height: 18px;
	margin: 0 0 14px 0;
	text-transform: uppercase;
}

h6 {
	color: var(--dark--200);
	font-size: 1em;
	font-weight: 400;
	line-height: 18px;
	margin: 0 0 14px 0;
}

h1.big {
	font-size: 4.2em;
	line-height: 54px;
}

h1.small {
	font-size: 2.2em;
	line-height: 42px;
	font-weight: 600;
}

h1.short {
	margin-bottom: 15px;
}

h2.short {
	margin-bottom: 15px;
}

h3.short,
h4.short,
h5.short,
h6.short {
	margin-bottom: 9px;
}

h1.shorter,
h2.shorter,
h3.shorter,
h4.shorter,
h5.shorter,
h6.shorter {
	margin-bottom: 0;
}

h1.tall,
h2.tall,
h3.tall,
h4.tall,
h5.tall,
h6.tall {
	margin-bottom: 33px;
}

h1.taller,
h2.taller,
h3.taller,
h4.taller,
h5.taller,
h6.taller {
	margin-bottom: 44px;
}

h1.spaced,
h2.spaced,
h3.spaced,
h4.spaced,
h5.spaced,
h6.spaced {
	margin-top: 22px;
}

h1.more-spaced,
h2.more-spaced,
h3.more-spaced,
h4.more-spaced,
h5.more-spaced,
h6.more-spaced {
	margin-top: 44px;
}

h1.dark,
h2.dark,
h3.dark,
h4.dark,
h5.dark {
	color: var(--dark);
}

h1.light,
h2.light,
h3.light,
h4.light,
h5.light,
h6.light {
	color: var(--light);
}

h1.text-shadow,
h2.text-shadow,
h3.text-shadow,
h4.text-shadow,
h5.text-shadow,
h6.text-shadow {
	text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.2);
}

/* Heading */
.heading.heading-border {
	margin-bottom: 25px;
}

.heading.heading-border h1, .heading.heading-border h2, .heading.heading-border h3, .heading.heading-border h4, .heading.heading-border h5, .heading.heading-border h6 {
	margin: 0;
	padding: 0;
}

.heading.heading-bottom-border h1 {
	border-bottom: 5px solid #eaeaea;
	padding-bottom: 10px;
}

.heading.heading-bottom-border h2, .heading.heading-bottom-border h3 {
	border-bottom: 2px solid #eaeaea;
	padding-bottom: 10px;
}

.heading.heading-bottom-border h4, .heading.heading-bottom-border h5, .heading.heading-bottom-border h6 {
	border-bottom: 1px solid #eaeaea;
	padding-bottom: 5px;
}

.heading.heading-bottom-double-border h1, .heading.heading-bottom-double-border h2, .heading.heading-bottom-double-border h3 {
	border-bottom: 3px double #eaeaea;
	padding-bottom: 10px;
}

.heading.heading-bottom-double-border h4, .heading.heading-bottom-double-border h5, .heading.heading-bottom-double-border h6 {
	border-bottom: 3px double #eaeaea;
	padding-bottom: 5px;
}

.heading.heading-middle-border {
	position: relative;
}

.heading.heading-middle-border:before {
	border-top: 1px solid #eaeaea;
	content: "";
	height: 0;
	left: auto;
	position: absolute;
	right: 0;
	top: 50%;
	width: 100%;
}

.heading.heading-middle-border h1, .heading.heading-middle-border h2, .heading.heading-middle-border h3, .heading.heading-middle-border h4, .heading.heading-middle-border h5, .heading.heading-middle-border h6 {
	position: relative;
	background: var(--light);
	display: inline-block;
}

.heading.heading-middle-border h1 {
	padding-right: 15px;
}

.heading.heading-middle-border h2, .heading.heading-middle-border h3 {
	padding-right: 10px;
}

.heading.heading-middle-border h4, .heading.heading-middle-border h5, .heading.heading-middle-border h6 {
	padding-right: 5px;
}

.heading.heading-middle-border-reverse {
	text-align: right;
}

.heading.heading-middle-border-reverse h1, .heading.heading-middle-border-reverse h2, .heading.heading-middle-border-reverse h3, .heading.heading-middle-border-reverse h4, .heading.heading-middle-border-reverse h5, .heading.heading-middle-border-reverse h6 {
	padding-right: 0;
}

.heading.heading-middle-border-reverse h1 {
	padding-left: 15px;
}

.heading.heading-middle-border-reverse h2, .heading.heading-middle-border-reverse h3 {
	padding-left: 10px;
}

.heading.heading-middle-border-reverse h4, .heading.heading-middle-border-reverse h5, .heading.heading-middle-border-reverse h6 {
	padding-left: 5px;
}

.heading.heading-middle-border-center {
	text-align: center;
}

.heading.heading-middle-border-center h1 {
	padding-left: 15px;
	padding-right: 15px;
}

.heading.heading-middle-border-center h2, .heading.heading-middle-border-center h3 {
	padding-left: 10px;
	padding-right: 10px;
}

.heading.heading-middle-border-center h4, .heading.heading-middle-border-center h5, .heading.heading-middle-border-center h6 {
	padding-left: 5px;
	padding-right: 5px;
}

.heading.heading-border-xs:before {
	border-top-width: 1px;
}

.heading.heading-border-sm:before {
	border-top-width: 2px;
}

.heading.heading-border-lg:before {
	border-top-width: 3px;
}

.heading.heading-border-xl:before {
	border-top-width: 5px;
}

/* Modal Titles */
.modal-header h3 {
	color: var(--dark--200);
	text-transform: none;
}

@media (max-width: 575px) {
	h2 {
		line-height: 40px;
	}
}

@media (max-width: 767px) {
	h1.big {
		font-size: 3.2em;
		line-height: 42px;
	}
}

/* Highlight */
.highlight {
	position: relative;
	background-image: linear-gradient(90deg, var(--light-rgba-0) 50%, var(--grey-500) 0);
	background-size: 200%;
	background-position: 0;
	padding-right: 8px;
	padding-right: .5rem;
	padding-left: 8px;
	padding-left: .5rem;
	transition: background-position .8s;
}

.highlight.highlight-bg-opacity {
	background-image: linear-gradient(90deg, var(--light-rgba-0) 50%, rgba(var(--grey-500), 0.2) 0);
}

.highlight.highlight-change-text-color {
	color: var(--light);
}

.highlight.highlight-animated.highlight-animated-start {
	background-position: -100%;
}

/* Highlight Text - Skin */
.highlight-primary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--primary) 0);
}

.highlight-primary.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--primary-rgba-35) 0);
}

.highlight-primary.highlight-change-text-color {
	color: var(--primary-inverse);
}

.highlight-secondary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--secondary) 0);
}

.highlight-secondary.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--secondary-rgba-35) 0);
}

.highlight-secondary.highlight-change-text-color {
	color: var(--secondary-inverse);
}

.highlight-tertiary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--tertiary) 0);
}

.highlight-tertiary.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--tertiary-rgba-35) 0);
}

.highlight-tertiary.highlight-change-text-color {
	color: var(--tertiary-inverse);
}

.highlight-quaternary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--quaternary) 0);
}

.highlight-quaternary.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--quaternary-rgba-35) 0);
}

.highlight-quaternary.highlight-change-text-color {
	color: var(--quaternary-inverse);
}

.highlight-dark {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--dark) 0);
}

.highlight-dark.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--dark-rgba-35) 0);
}

.highlight-dark.highlight-change-text-color {
	color: var(--dark-inverse);
}

.highlight-light {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--light) 0);
}

.highlight-light.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, var(--light-rgba-35) 0);
}

.highlight-light.highlight-change-text-color {
	color: var(--light-inverse);
}

/* Icons */
.featured-icon {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	background: var(--grey-500);
	border-radius: 100%;
	color: var(--light);
	height: 2.5em;
	line-height: 2.5em;
	margin-right: 10px;
	position: relative;
	text-align: center;
	top: 5px;
	width: 2.5em;
}

.featured-icon i {
	color: var(--light);
	font-size: 0.9em;
}

.featured-icon.featured-icon-style-2 {
	width: 5em;
	height: 5em;
	line-height: 5em;
}

.featured-icon.featured-icon-hover-effect-1 {
	transition: ease transform 300ms;
}

.featured-icon.featured-icon-hover-effect-1:hover {
	transform: scale(1.1);
}

/* Font Awesome List */
.sample-icon-list {
	margin-top: 22px;
}

.sample-icon-list .sample-icon a {
	display: block;
	color: #222222;
	line-height: 36px;
	height: 36px;
	padding-left: 10px;
	border-radius: var(--border-radius);
	font-size: 11px;
}

.sample-icon-list .sample-icon a [class*="fa-"], .sample-icon-list .sample-icon a .icons {
	width: 32px;
	font-size: 14px;
	display: inline-block;
	text-align: right;
	margin-right: 10px;
}

.sample-icon-list .sample-icon a:hover {
	color: var(--light);
	text-decoration: none;
}

.sample-icon-list .sample-icon a:hover [class*="fa-"], .sample-icon-list .sample-icon a:hover .icons {
	font-size: 28px;
	vertical-align: -6px;
}

/* Font Awesome - Skin */
.sample-icon-list .sample-icon a:hover {
	background-color: var(--primary);
}

/* Simple Line Icons List */
#lineIcons .sample-icon-list .sample-icon a {
	line-height: 39px;
}

/* Porto Icons */
i.pi {
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	font-style: normal;
	font-size: 14px;
}

i.pi.pi-porto-brand:before {
	content: '\f111';
	font-family: 'Font Awesome 6 Free';
	font-weight: bold;
	font-size: 0.51em;
	color: var(--light);
	padding: 1.2em;
}

i.pi.pi-porto-brand:after {
	content: '';
	width: 2em;
	height: 2em;
	border: 0.5em solid #FFF;
	border-radius: 100%;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate3d(-50%, -50%, 0);
}

i.pi.pi-close {
	padding: 0.6em;
	transform: rotate(45deg);
}

i.pi.pi-close:before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate3d(-50%, -50%, 0);
	border-right: 1px solid #FFF;
	height: 1em;
}

i.pi.pi-close:after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate3d(-50%, -50%, 0);
	border-bottom: 1px solid #FFF;
	width: 1em;
}

/* Area Expanded Change */
.icon-aria-expanded-change[aria-expanded=true] .fa-chevron-down {
	display: none;
}

.icon-aria-expanded-change[aria-expanded=false] .fa-chevron-up {
	display: none;
}

/* Icon Animated */
[data-icon] {
	opacity: 0;
}

.icon-animated {
	opacity: 0;
}

/* Elements Shadow Icon */
.elements-list-shadow-icon {
	font-size: 64px;
	font-size: 4rem;
	position: absolute;
	top: -18px;
	opacity: 0.08;
	right: -18px;
}

/* Custom Image Icons */
.icon-bg {
	display: inline-block;
	background: url(../img/icons/icons.png) no-repeat;
	width: 80px;
	height: 80px;
}

.icon-1 {
	background-position: -10px -10px;
}

.icon-2 {
	background-position: -110px -10px;
}

.icon-3 {
	background-position: -210px -10px;
}

.icon-plugin-1 {
	background-position: 0 100%;
	width: 115px;
	height: 115px;
}

.icon-plugin-2 {
	background-position: -115px 100%;
	width: 115px;
	height: 115px;
}

.icon-feature-1 {
	background-position: -20px -110px;
}

.icon-feature-2 {
	background-position: -120px -110px;
}

.icon-feature-3 {
	background-position: -220px -110px;
}

.icon-feature-4 {
	background-position: -320px -110px;
}

.icon-feature-5 {
	background-position: -420px -110px;
}

.icon-feature-6 {
	background-position: -520px -110px;
}

.icon-feature-7 {
	background-position: -620px -110px;
}

.icon-feature-8 {
	background-position: -720px -110px;
}

.icon-feature-9 {
	background-position: -820px -110px;
}

.icon-feature-10 {
	background-position: -920px -110px;
}

.icon-feature-11 {
	background-position: -20px -210px;
}

.icon-feature-12 {
	background-position: -120px -210px;
}

.icon-feature-13 {
	background-position: -220px -210px;
}

.icon-feature-14 {
	background-position: -320px -210px;
}

.icon-feature-15 {
	background-position: -420px -210px;
}

.icon-feature-16 {
	background-position: -520px -210px;
}

.icon-feature-17 {
	background-position: -624px -210px;
}

.icon-feature-18 {
	background-position: -720px -210px;
}

.icon-feature-19 {
	background-position: -820px -210px;
}

.icon-menu-1 {
	background-position: -310px -10px;
}

.icon-menu-2 {
	background-position: -410px -10px;
}

.icon-menu-3 {
	background-position: -510px -10px;
}

.icon-menu-4 {
	background-position: -610px -10px;
}

/* Thumbnails */
.img-thumbnail {
	border-radius: var(--border-radius);
	position: relative;
}

.img-thumbnail img {
	border-radius: var(--border-radius);
}

.img-thumbnail .zoom {
	background: var(--grey-500);
	border-radius: 100%;
	bottom: 8px;
	color: var(--light);
	display: block;
	height: 30px;
	padding: 6px;
	position: absolute;
	right: 8px;
	text-align: center;
	width: 30px;
}

.img-thumbnail .zoom i {
	font-size: 10px;
	left: 0px;
	position: relative;
	top: -6px;
}

.img-thumbnail.img-thumbnail-hover-icon {
	position: relative;
}

.img-thumbnail.img-thumbnail-hover-icon img {
	position: relative;
	z-index: 1;
}

.img-thumbnail.img-thumbnail-hover-icon:before {
	transition: all 0.2s;
	background: rgba(0, 0, 0, 0.65);
	border-radius: var(--border-radius);
	content: "";
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	opacity: 0;
	z-index: 2;
}

.img-thumbnail.img-thumbnail-hover-icon:after {
	transition: all 0.2s;
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	color: var(--light);
	content: "\f067";
	font-size: 18px;
	height: 30px;
	position: absolute;
	top: 30%;
	margin-top: -15px;
	display: block;
	text-align: center;
	width: 100%;
	opacity: 0;
	z-index: 3;
}

.img-thumbnail.img-thumbnail-hover-icon:hover:before {
	opacity: 1;
}

.img-thumbnail.img-thumbnail-hover-icon:hover:after {
	top: 50%;
	opacity: 1;
}

.img-thumbnail a.img-thumbnail-hover-icon {
	display: inline-block;
}

.img-thumbnail.img-thumbnail-no-borders {
	border: none;
	padding: 0;
}

/* Image Gallery */
ul.image-gallery {
	list-style: none;
	margin: 0;
	padding: 0;
}

ul.image-gallery .image-gallery-item {
	margin-bottom: 35px;
}

/* Thumbnail Gallery */
ul.thumbnail-gallery {
	list-style: none;
	margin: 10px 0;
	padding: 0;
}

ul.thumbnail-gallery li {
	display: inline-block;
	margin: 10px 10px 0 0;
	padding: 0;
}

@media (max-width: 991px) {
	.thumbnail {
		border: 0;
		text-align: center;
	}
}

/* Masonry */
.masonry .masonry-item:not(.no-default-style) {
	width: 25%;
}

.masonry .masonry-item:not(.no-default-style).w2 {
	width: 50%;
}

.masonry .masonry-item:not(.no-default-style) img {
	border-radius: 0 !important;
}

/* Masonry Loading */
.masonry-loader {
	height: auto;
	overflow: visible;
	position: relative;
}

.masonry-loader:after {
	transition: all 0.5s;
	content: ' ';
	background: var(--light);
	bottom: 0;
	left: 0;
	position: absolute;
	right: 0;
	top: 0;
	z-index: 1;
	opacity: 0;
}

.masonry-loader .bounce-loader {
	display: none;
}

.masonry-loader.masonry-loader-showing {
	height: 200px;
	overflow: hidden;
}

.masonry-loader.masonry-loader-showing:after {
	opacity: 1;
}

.masonry-loader.masonry-loader-showing .bounce-loader {
	display: block;
}

.masonry-loader.masonry-loader-loaded:after {
	display: none;
}

/* Mockup */
@media (min-width: 992px) {
	.mockup-landing-page {
		margin-top: -120px;
	}
}

/* Hover Effect 3d */
.hover-effect-3d {
	transform-style: flat !important;
}

.hover-effect-3d .thumb-info {
	will-change: box-shadow;
	transition: box-shadow 300ms;
}

.hover-effect-3d .thumb-info:hover {
	box-shadow: 0px 17px 20px -15px var(--dark-rgba-70);
}

.hover-effect-3d .thumb-info .thumb-info-title {
	transition: ease-out opacity .2s !important;
}

.hover-effect-3d .thumb-info .thumb-info-action {
	transition: ease-out opacity .2s !important;
}

.hover-effect-3d .thumb-info .thumb-info-action .thumb-info-action-icon {
	pointer-events: none;
}

.hover-effect-3d .thumb-info.thumb-info-centered-info .thumb-info-title {
	top: 47%;
}

/* Profile Image */
.profile-image-outer-container {
	margin-top: auto;
	margin-bottom: auto;
	border-radius: 50%;
	position: relative;
	cursor: pointer;
}

.profile-image-outer-container .profile-image-input {
	position: absolute;
	top: 0;
	height: 100%;
	opacity: 0;
	z-index: 3;
	cursor: pointer;
}

.profile-image-outer-container .profile-image-inner-container {
	border-radius: 50%;
	padding: 5px;
}

.profile-image-outer-container .profile-image-inner-container img {
	height: 200px;
	width: 200px;
	border-radius: 50%;
	border: 5px solid white;
}

.profile-image-outer-container .profile-image-inner-container .profile-image-button {
	position: absolute;
	z-index: 2;
	bottom: 15px;
	right: 15px;
	width: 40px;
	height: 40px;
	text-align: center;
	border-radius: 50%;
	line-height: 38px;
	transition: ease all 0.1s;
}

.profile-image-outer-container:hover .profile-image-button {
	transform: scale(1.1);
}

/* Lazy */
.lazy-bg {
	background-image: url(img/lazy.png);
}

/* Thumb Gallery */
.thumb-gallery-wrapper .thumb-gallery-thumbs .owl-item {
	border: 2px solid var(--grey-100);
	transition: ease all 300ms;
}

.thumb-gallery-wrapper .thumb-gallery-thumbs .owl-item:hover, .thumb-gallery-wrapper .thumb-gallery-thumbs .owl-item.selected {
	border: 2px solid #212121;
}

/* Arrows */
button.mfp-arrow {
	display: flex;
	align-items: center;
	justify-content: center;
	top: 50%;
	left: 0;
	border-radius: var(--border-radius);
	background: var(--dark);
	width: 35px;
	height: 100px;
	color: var(--light);
	margin: 0 !important;
	transform: translate3d(0, -50%, 0);
}

button.mfp-arrow:before {
	content: none;
}

button.mfp-arrow:after {
	content: "\f053";
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	border: none !important;
}

button.mfp-arrow.mfp-arrow-left {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}

button.mfp-arrow.mfp-arrow-left:after {
	margin-left: 0;
	margin-top: 0;
	position: relative;
	top: auto;
	left: -2px;
	width: auto;
	height: auto;
}

button.mfp-arrow.mfp-arrow-right {
	left: auto;
	right: 0;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
}

button.mfp-arrow.mfp-arrow-right:after {
	content: "\f054";
	margin-left: 0;
	margin-top: 0;
	position: relative;
	top: auto;
	left: auto;
	right: -2px;
	width: auto;
	height: auto;
}

/* Close */
.mfp-close,
.mfp-close-btn-in .mfp-close {
	font-family: "Poppins", Arial, sans-serif;
	font-weight: 600;
	font-size: 22px;
	color: #838383;
}

/* No Margins */
.mfp-no-margins img.mfp-img {
	padding: 0;
}

.mfp-no-margins .mfp-figure:after {
	top: 0;
	bottom: 0;
}

.mfp-no-margins .mfp-container {
	padding: 0;
}

/* Zoom */
.mfp-with-zoom .mfp-container, .mfp-with-zoom.mfp-bg {
	opacity: 0.001;
	transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
	opacity: 1;
}

.mfp-with-zoom.mfp-ready.mfp-bg {
	opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container, .mfp-with-zoom.mfp-removing.mfp-bg {
	opacity: 0;
}

/* Animnate */
.my-mfp-zoom-in .zoom-anim-dialog {
	opacity: 0;
	transition: all 0.2s ease-in-out;
	transform: scale(0.8);
}

.my-mfp-zoom-in.mfp-ready .zoom-anim-dialog {
	opacity: 1;
	transform: scale(1);
}

.my-mfp-zoom-in.mfp-removing .zoom-anim-dialog {
	transform: scale(0.8);
	opacity: 0;
}

.my-mfp-zoom-in.mfp-bg {
	opacity: 0.001;
	transition: opacity 0.3s ease-out;
}

.my-mfp-zoom-in.mfp-ready.mfp-bg {
	opacity: 0.8;
}

.my-mfp-zoom-in.mfp-removing.mfp-bg {
	opacity: 0;
}

.my-mfp-slide-bottom .zoom-anim-dialog {
	opacity: 0;
	transition: all 0.2s ease-out;
	transform: translateY(-20px) perspective(600px) rotateX(10deg);
}

.my-mfp-slide-bottom.mfp-ready .zoom-anim-dialog {
	opacity: 1;
	transform: translateY(0) perspective(600px) rotateX(0);
}

.my-mfp-slide-bottom.mfp-removing .zoom-anim-dialog {
	opacity: 0;
	transform: translateY(-10px) perspective(600px) rotateX(10deg);
}

.my-mfp-slide-bottom.mfp-bg {
	opacity: 0.01;
	transition: opacity 0.3s ease-out;
}

.my-mfp-slide-bottom.mfp-ready.mfp-bg {
	opacity: 0.8;
}

.my-mfp-slide-bottom.mfp-removing.mfp-bg {
	opacity: 0;
}

/* Fade */
.mfp-fade.mfp-bg {
	opacity: 0;
	transition: all 0.15s ease-out;
}

.mfp-fade.mfp-bg.mfp-ready {
	opacity: 0.8;
}

.mfp-fade.mfp-bg.mfp-removing {
	opacity: 0;
}

.mfp-fade.mfp-wrap .mfp-content {
	opacity: 0;
	transition: all 0.15s ease-out;
}

.mfp-fade.mfp-wrap.mfp-ready .mfp-content {
	opacity: 1;
}

.mfp-fade.mfp-wrap.mfp-removing .mfp-content {
	opacity: 0;
}

/* Dialog */
.dialog {
	background: white;
	padding: 20px 30px;
	text-align: left;
	margin: 40px auto;
	position: relative;
	max-width: 600px;
}

.dialog.dialog-xs {
	max-width: 200px;
}

.dialog.dialog-sm {
	max-width: 400px;
}

.dialog.dialog-md {
	max-width: 600px;
}

.dialog.dialog-lg {
	max-width: 900px;
}

.dialog.mfp-close-out .mfp-close {
	top: -50px;
	right: -15px;
	color: var(--light);
}

/* White Popup Block */
.white-popup-block {
	background: var(--light);
	padding: 20px 30px;
	text-align: left;
	max-width: 600px;
	margin: 40px auto;
	position: relative;
}

.white-popup-block.white-popup-block-xs {
	max-width: 200px;
}

.white-popup-block.white-popup-block-sm {
	max-width: 400px;
}

.white-popup-block.white-popup-block-md {
	max-width: 600px;
}

.white-popup-block.white-popup-block-lg {
	max-width: 900px;
}

/* Lightbox Opened */
html.lightbox-opened.sticky-header-active #header .header-body {
	padding-right: 17px;
}

/* Modal Opened */
.modal-open {
	padding-right: 0 !important;
}

/* List */
.list li {
	margin-bottom: 13px;
}

.list.pull-left li {
	text-align: left;
}

.list.list-icons {
	list-style: none;
	padding-left: 0;
	padding-right: 0;
}

.list.list-icons li {
	position: relative;
	padding-left: 25px;
}

.list.list-icons li > [class*="fa-"]:first-child,
.list.list-icons li > .icons:first-child, .list.list-icons li a:first-child > [class*="fa-"]:first-child,
.list.list-icons li a:first-child > .icons:first-child {
	position: absolute;
	left: 0;
	top: 5px;
}

.list.list-icons.list-icons-style-2 li {
	padding-top: 5px;
	padding-left: 36px;
}

.list.list-icons.list-icons-style-2 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-2 li > .icons:first-child, .list.list-icons.list-icons-style-2 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-2 li a:first-child > .icons:first-child {
	border-width: 1px;
	border-style: solid;
	border-radius: 50%;
	width: 25px;
	height: 25px;
	text-align: center;
	padding: 4px;
	font-size: 0.8em;
	line-height: 1.3;
}

.list.list-icons.list-icons-style-3 li {
	padding-top: 5px;
	padding-left: 36px;
}

.list.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-3 li > .icons:first-child, .list.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	color: var(--light);
	border-radius: 50%;
	width: 25px;
	height: 25px;
	text-align: center;
	padding: 5px;
	font-size: 0.8em;
	line-height: 1.3;
}

.list.list-icons.list-icons-sm li {
	padding-left: 13px;
	margin-bottom: 5px;
}

.list.list-icons.list-icons-sm li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-sm li > .icons:first-child, .list.list-icons.list-icons-sm li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-sm li a:first-child > .icons:first-child {
	font-size: 0.8em;
	top: 7px;
}

.list.list-icons.list-icons-sm.list-icons-style-2 li {
	padding-left: 32px;
}

.list.list-icons.list-icons-sm.list-icons-style-2 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-sm.list-icons-style-2 li > .icons:first-child, .list.list-icons.list-icons-sm.list-icons-style-2 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-sm.list-icons-style-2 li a:first-child > .icons:first-child {
	width: 20px;
	height: 20px;
	padding: 3px;
}

.list.list-icons.list-icons-sm.list-icons-style-3 li {
	padding-left: 32px;
}

.list.list-icons.list-icons-sm.list-icons-style-3 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-sm.list-icons-style-3 li > .icons:first-child, .list.list-icons.list-icons-sm.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-sm.list-icons-style-3 li a:first-child > .icons:first-child {
	width: 20px;
	height: 20px;
	padding: 3px;
}

.list.list-icons.list-icons-lg li {
	padding-top: 5px;
	padding-left: 27px;
}

.list.list-icons.list-icons-lg li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-lg li > .icons:first-child, .list.list-icons.list-icons-lg li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-lg li a:first-child > .icons:first-child {
	font-size: 1.1em;
	line-height: 1.7;
}

.list.list-icons.list-icons-lg.list-icons-style-2 li {
	padding-top: 8px;
	padding-left: 42px;
}

.list.list-icons.list-icons-lg.list-icons-style-2 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-lg.list-icons-style-2 li > .icons:first-child, .list.list-icons.list-icons-lg.list-icons-style-2 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-lg.list-icons-style-2 li a:first-child > .icons:first-child {
	width: 30px;
	height: 30px;
	padding: 4px;
}

.list.list-icons.list-icons-lg.list-icons-style-3 li {
	padding-top: 10px;
	padding-left: 46px;
}

.list.list-icons.list-icons-lg.list-icons-style-3 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-lg.list-icons-style-3 li > .icons:first-child, .list.list-icons.list-icons-lg.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-lg.list-icons-style-3 li a:first-child > .icons:first-child {
	width: 36px;
	height: 36px;
	padding: 4px;
}

.list.list-icons.list-icons-reverse li {
	text-align: right;
	padding-right: 25px;
	padding-left: 0;
}

.list.list-icons.list-icons-reverse li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-reverse li > .icons:first-child, .list.list-icons.list-icons-reverse li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-reverse li a:first-child > .icons:first-child {
	position: absolute;
	left: auto;
	right: 0;
	top: 5px;
}

.list.list-icons.list-icons-reverse.list-icons-style-2 li {
	padding-right: 36px;
}

.list.list-icons.list-icons-reverse.list-icons-style-3 li {
	padding-right: 36px;
}

.list.list-icons.list-icons-reverse.list-icons-sm li {
	padding-left: 0;
	padding-right: 13px;
}

.list.list-icons.list-icons-reverse.list-icons-sm.list-icons-style-2 li {
	padding-right: 32px;
}

.list.list-icons.list-icons-reverse.list-icons-sm.list-icons-style-3 li {
	padding-right: 32px;
}

.list.list-icons.list-icons-reverse.list-icons-lg li {
	padding-left: 0;
	padding-right: 32px;
}

.list.list-icons.list-icons-reverse.list-icons-lg.list-icons-style-2 li {
	padding-right: 42px;
}

.list.list-icons.list-icons-reverse.list-icons-lg.list-icons-style-3 li {
	padding-right: 42px;
}

.list.list-borders li {
	border-bottom: 1px solid #eaeaea;
	margin-top: 9px;
	margin-bottom: 0;
	padding-bottom: 9px;
}

.list.list-borders li:last-child {
	border-bottom: 0;
}

.list.list-side-borders {
	border-left: 1px solid #eaeaea;
	padding-left: 10px;
}

.list.list-ordened {
	counter-reset: custom-counter;
	list-style: none;
}

.list.list-ordened li {
	position: relative;
	padding-left: 22px;
}

.list.list-ordened li:before {
	position: absolute;
	top: 0;
	left: 0;
	content: counter(custom-counter);
	counter-increment: custom-counter;
	font-weight: bold;
}

.list.list-ordened.list-ordened-style-2 li, .list.list-ordened.list-ordened-style-3 li {
	padding-left: 32px;
}

.list.list-ordened.list-ordened-style-2 li:before, .list.list-ordened.list-ordened-style-3 li:before {
	border-radius: 100%;
	border-style: solid;
	border-width: 1px;
	min-width: 26px;
	padding: 0;
	text-align: center;
}

.list.list-ordened.list-ordened-style-3 li:before {
	color: var(--light);
	border: 0;
}

.list.list-unstyled {
	padding-left: 0;
	padding-right: 0;
}

.list-unstyled {
	padding-left: 0;
	padding-right: 0;
}

dl dd {
	margin-bottom: 15px;
}

/* Sitemap */
ul.sitemap > li > a {
	font-weight: bold;
}

ul.sitemap.list li {
	margin: 0 0 3px 0;
}

ul.sitemap.list ul {
	margin-bottom: 5px;
	margin-top: 5px;
}

/* Lists - Skin */
.list.list-icons li > [class*="fa-"]:first-child,
.list.list-icons li > .icons:first-child, .list.list-icons li a:first-child > [class*="fa-"]:first-child,
.list.list-icons li a:first-child > .icons:first-child {
	color: var(--primary);
	border-color: var(--primary);
}

.list.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-3 li > .icons:first-child, .list.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: var(--primary);
}

.list.list-ordened li:before {
	color: var(--primary);
	border-color: var(--primary);
}

.list.list-ordened.list-ordened-style-3 li:before {
	background-color: var(--primary);
}

.list-primary.list-icons li > [class*="fa-"]:first-child,
.list-primary.list-icons li > .icons:first-child, .list-primary.list-icons li a:first-child > [class*="fa-"]:first-child,
.list-primary.list-icons li a:first-child > .icons:first-child {
	color: var(--primary);
	border-color: var(--primary);
}

.list-primary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list-primary.list-icons.list-icons-style-3 li > .icons:first-child, .list-primary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list-primary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: var(--primary);
	color: var(--primary-inverse);
}

.list-primary.list-ordened li:before {
	color: var(--primary);
}

.list-primary.list-ordened.list-ordened-style-3 li:before {
	background-color: var(--primary);
	color: var(--primary-inverse);
}

.list-secondary.list-icons li > [class*="fa-"]:first-child,
.list-secondary.list-icons li > .icons:first-child, .list-secondary.list-icons li a:first-child > [class*="fa-"]:first-child,
.list-secondary.list-icons li a:first-child > .icons:first-child {
	color: var(--secondary);
	border-color: var(--secondary);
}

.list-secondary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list-secondary.list-icons.list-icons-style-3 li > .icons:first-child, .list-secondary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list-secondary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: var(--secondary);
	color: var(--secondary-inverse);
}

.list-secondary.list-ordened li:before {
	color: var(--secondary);
}

.list-secondary.list-ordened.list-ordened-style-3 li:before {
	background-color: var(--secondary);
	color: var(--secondary-inverse);
}

.list-tertiary.list-icons li > [class*="fa-"]:first-child,
.list-tertiary.list-icons li > .icons:first-child, .list-tertiary.list-icons li a:first-child > [class*="fa-"]:first-child,
.list-tertiary.list-icons li a:first-child > .icons:first-child {
	color: var(--tertiary);
	border-color: var(--tertiary);
}

.list-tertiary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list-tertiary.list-icons.list-icons-style-3 li > .icons:first-child, .list-tertiary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list-tertiary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: var(--tertiary);
	color: var(--tertiary-inverse);
}

.list-tertiary.list-ordened li:before {
	color: var(--tertiary);
}

.list-tertiary.list-ordened.list-ordened-style-3 li:before {
	background-color: var(--tertiary);
	color: var(--tertiary-inverse);
}

.list-quaternary.list-icons li > [class*="fa-"]:first-child,
.list-quaternary.list-icons li > .icons:first-child, .list-quaternary.list-icons li a:first-child > [class*="fa-"]:first-child,
.list-quaternary.list-icons li a:first-child > .icons:first-child {
	color: var(--quaternary);
	border-color: var(--quaternary);
}

.list-quaternary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list-quaternary.list-icons.list-icons-style-3 li > .icons:first-child, .list-quaternary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list-quaternary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: var(--quaternary);
	color: var(--quaternary-inverse);
}

.list-quaternary.list-ordened li:before {
	color: var(--quaternary);
}

.list-quaternary.list-ordened.list-ordened-style-3 li:before {
	background-color: var(--quaternary);
	color: var(--quaternary-inverse);
}

.list-dark.list-icons li > [class*="fa-"]:first-child,
.list-dark.list-icons li > .icons:first-child, .list-dark.list-icons li a:first-child > [class*="fa-"]:first-child,
.list-dark.list-icons li a:first-child > .icons:first-child {
	color: var(--dark);
	border-color: var(--dark);
}

.list-dark.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list-dark.list-icons.list-icons-style-3 li > .icons:first-child, .list-dark.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list-dark.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: var(--dark);
	color: var(--dark-inverse);
}

.list-dark.list-ordened li:before {
	color: var(--dark);
}

.list-dark.list-ordened.list-ordened-style-3 li:before {
	background-color: var(--dark);
	color: var(--dark-inverse);
}

.list-light.list-icons li > [class*="fa-"]:first-child,
.list-light.list-icons li > .icons:first-child, .list-light.list-icons li a:first-child > [class*="fa-"]:first-child,
.list-light.list-icons li a:first-child > .icons:first-child {
	color: var(--light);
	border-color: var(--light);
}

.list-light.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list-light.list-icons.list-icons-style-3 li > .icons:first-child, .list-light.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list-light.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: var(--light);
	color: var(--light-inverse);
}

.list-light.list-ordened li:before {
	color: var(--light);
}

.list-light.list-ordened.list-ordened-style-3 li:before {
	background-color: var(--light);
	color: var(--light-inverse);
}

/* Loading Overlay */
.loading-overlay-showing {
	overflow: hidden;
}

.loading-overlay-showing > .loading-overlay {
	opacity: 1;
	visibility: visible;
}

.loading-overlay {
	transition: visibility 0s ease-in-out 0.5s, opacity 0.5s ease-in-out;
	bottom: 0;
	left: 0;
	position: absolute;
	opacity: 0;
	right: 0;
	top: 0;
	visibility: hidden;
	background: #FFF !important;
}

body > .loading-overlay {
	position: fixed;
	z-index: 999999;
}

/* Bounce Loading */
.bounce-loader {
	transition: all 0.2s;
	margin: -9px 0 0 -35px;
	text-align: center;
	width: 70px;
	left: 50%;
	position: absolute;
	top: 50%;
	z-index: 10000;
}

.bounce-loader .bounce1,
.bounce-loader .bounce2,
.bounce-loader .bounce3 {
	animation: 1.4s ease-in-out 0s normal both infinite bouncedelay;
	background-color: var(--grey-500);
	border-radius: 100%;
	box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.15);
	display: inline-block;
	height: 18px;
	width: 18px;
}

.bounce-loader .bounce1 {
	animation-delay: -0.32s;
}

.bounce-loader .bounce2 {
	animation-delay: -0.16s;
}

@keyframes bouncedelay {
	0%, 80%, 100% {
		transform: scale(0);
	}

	40% {
		transform: scale(1);
	}
}

html.safari.iphone .bounce-loader .bounce1,
html.safari.iphone .bounce-loader .bounce2,
html.safari.iphone .bounce-loader .bounce3 {
	animation: 1.4s ease-in-out infinite bouncedelay;
}

/* Loading Overlay Percentage */
.loading-overlay.loading-overlay-percentage {
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-overlay.loading-overlay-percentage .page-loader-progress-wrapper {
	display: flex;
	font-size: 65.6px;
	font-size: 4.1rem;
	font-weight: bold;
	letter-spacing: -1.5px;
	color: #212121;
}

.loading-overlay.loading-overlay-percentage.loading-overlay-percentage-effect-2 .loading-overlay-background-layer {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	width: 0px;
	background: #f4f4f4;
	z-index: 0;
	transition: ease width 300ms;
}

.loading-overlay.loading-overlay-percentage.loading-overlay-percentage-effect-2 .page-loader-progress-wrapper {
	position: relative;
	z-index: 1;
}

/* Cubes Effect */
.cssload-thecube {
	width: 40px;
	height: 40px;
	margin: 0 auto;
	margin-top: -20px;
	position: relative;
	transform: rotateZ(45deg);
}

.cssload-thecube .cssload-cube {
	position: relative;
	float: left;
	width: 50%;
	height: 50%;
	transform: rotateZ(45deg);
	transform: scale(1.1);
}

.cssload-thecube .cssload-cube:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: var(--grey-500);
	animation: cssload-fold-thecube 1.68s infinite linear both;
	transform-origin: 100% 100%;
}

.cssload-thecube .cssload-c2 {
	transform: scale(1.1) rotateZ(90deg);
}

.cssload-thecube .cssload-c2:before {
	animation-delay: 0.205s;
}

.cssload-thecube .cssload-c3 {
	transform: scale(1.1) rotateZ(180deg);
}

.cssload-thecube .cssload-c3:before {
	animation-delay: 0.42s;
}

.cssload-thecube .cssload-c4 {
	transform: scale(1.1) rotateZ(270deg);
}

.cssload-thecube .cssload-c4:before {
	animation-delay: 0.625s;
}

@keyframes cssload-fold-thecube {
	0%, 10% {
		transform: perspective(129px) rotateX(-180deg);
		opacity: 0;
	}

	25%,
		75% {
		transform: perspective(129px) rotateX(0deg);
		opacity: 1;
	}

	90%,
		100% {
		transform: perspective(129px) rotateY(180deg);
		opacity: 0;
	}
}

/* Cube Progress Effect */
.cssload-cube-progress {
	display: block;
	margin: 0 auto;
	width: 29px;
	height: 29px;
	position: relative;
	border: 4px solid var(--grey-500);
	animation: cssload-cube-progress 2.3s infinite ease;
}

.cssload-cube-progress-inner {
	vertical-align: top;
	display: inline-block;
	width: 100%;
	background-color: var(--grey-500);
	animation: cssload-cube-progress-inner 2.3s infinite ease-in;
}

@keyframes cssload-cube-progress {
	0% {
		transform: rotate(0deg);
	}

	25% {
		transform: rotate(180deg);
	}

	50% {
		transform: rotate(180deg);
	}

	75% {
		transform: rotate(360deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes cssload-cube-progress-inner {
	0% {
		height: 0%;
	}

	25% {
		height: 0%;
	}

	50% {
		height: 100%;
	}

	75% {
		height: 100%;
	}

	100% {
		height: 0%;
	}
}

/* Float Rings Effect */
.cssload-float-rings-loader {
	position: relative;
	left: calc(50% - 15px);
	width: 40px;
	height: 40px;
	border-radius: 50%;
	perspective: 380px;
}

.cssload-float-rings-inner {
	position: absolute;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	border-radius: 50%;
}

.cssload-float-rings-inner.cssload-one {
	left: 0%;
	top: 0%;
	animation: cssload-rotate-one 1.15s linear infinite;
	border-bottom: 1px solid var(--grey-500);
}

.cssload-float-rings-inner.cssload-two {
	right: 0%;
	top: 0%;
	animation: cssload-rotate-two 1.15s linear infinite;
	border-right: 1px solid var(--grey-500);
}

.cssload-float-rings-inner.cssload-three {
	right: 0%;
	bottom: 0%;
	animation: cssload-rotate-three 1.15s linear infinite;
	border-top: 1px solid var(--grey-500);
}

@keyframes cssload-rotate-one {
	0% {
		transform: rotateX(35deg) rotateY(-45deg) rotateZ(0deg);
	}

	100% {
		transform: rotateX(35deg) rotateY(-45deg) rotateZ(360deg);
	}
}

@keyframes cssload-rotate-two {
	0% {
		transform: rotateX(50deg) rotateY(10deg) rotateZ(0deg);
	}

	100% {
		transform: rotateX(50deg) rotateY(10deg) rotateZ(360deg);
	}
}

@keyframes cssload-rotate-three {
	0% {
		transform: rotateX(35deg) rotateY(55deg) rotateZ(0deg);
	}

	100% {
		transform: rotateX(35deg) rotateY(55deg) rotateZ(360deg);
	}
}

/* Float Bars Effect */
.cssload-float-bars-container {
	margin: -70px auto 0 auto;
	max-width: 545px;
	transform: rotate(90deg);
}

.cssload-float-bars-container * {
	box-sizing: border-box;
}

.cssload-float-bars-container ul li {
	list-style: none;
}

.cssload-float-bars-flex-container {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: space-around;
}

.cssload-float-bars-flex-container li {
	padding: 10px;
	height: 97px;
	width: 97px;
	margin: 29px 19px;
	position: relative;
	text-align: center;
}

.cssload-float-bars-loading,
.cssload-float-bars-loading:after,
.cssload-float-bars-loading:before {
	display: inline-block;
	position: relative;
	width: 5px;
	height: 49px;
	background: var(--grey-500);
	margin-top: 5px;
	border-radius: 975px;
	animation: cssload-upDown2 1.15s ease infinite;
	animation-direction: alternate;
	animation-delay: 0.29s;
}

.cssload-float-bars-loading:after,
.cssload-float-bars-loading:before {
	position: absolute;
	content: '';
	animation: cssload-upDown 1.15s ease infinite;
	animation-direction: alternate;
}

.cssload-float-bars-loading:before {
	left: -10px;
}

.cssload-float-bars-loading:after {
	left: 10px;
	animation-delay: 0.58s;
}

@keyframes cssload-upDown {
	from {
		transform: translateY(19px);
	}

	to {
		transform: translateY(-19px);
	}
}

@keyframes cssload-upDown2 {
	from {
		transform: translateY(29px);
	}

	to {
		transform: translateY(-19px);
	}
}

/* Speeding Wheel */
.cssload-speeding-wheel-container {
	width: 100%;
	height: 49px;
	text-align: center;
}

.cssload-speeding-wheel {
	width: 49px;
	height: 49px;
	margin: 0 auto;
	border: 3px solid var(--grey-500);
	border-radius: 50%;
	border-left-color: transparent;
	border-right-color: transparent;
	animation: cssload-spin 575ms infinite linear;
}

@keyframes cssload-spin {
	100% {
		transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

/* Zenith Effect */
.cssload-zenith-container {
	width: 100%;
	height: 49px;
	text-align: center;
}

.cssload-zenith {
	width: 49px;
	height: 49px;
	margin: 0 auto;
	border-radius: 50%;
	border-top-color: transparent;
	border-left-color: transparent;
	border-right-color: transparent;
	box-shadow: 3px 3px 1px var(--grey-500);
	animation: cssload-spin 690ms infinite linear;
}

@keyframes cssload-spin {
	100% {
		transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

/* Spinning Square Effect */
.cssload-spinning-square-loading {
	position: relative;
	width: 30px;
	height: 30px;
	background: var(--grey-500);
	display: block;
	margin: auto;
	animation: 1.15s cssload-spin infinite;
}

.cssload-spinning-square-loading:before, .cssload-spinning-square-loading:after {
	content: "";
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	animation: 0.58s infinite alternate linear;
}

.cssload-spinning-square-loading:before {
	background: #999;
	animation-name: cssload-blur1;
}

.cssload-spinning-square-loading:after {
	background: white;
	animation-name: cssload-blur2;
}

@keyframes cssload-spin {
	to {
		transform: rotate(360deg);
	}
}

@keyframes cssload-blur1 {
	to {
		transform: rotate(-30deg);
	}
}

@keyframes cssload-blur2 {
	to {
		transform: rotate(-15deg);
	}
}

/* Pulse */
.wrapper-pulse {
	margin-top: -60px;
}

.cssload-pulse-loader {
	width: 49px;
	height: 49px;
	border-radius: 50%;
	margin: 3em;
	display: inline-block;
	position: relative;
	vertical-align: middle;
	background: black;
}

.cssload-pulse-loader,
.cssload-pulse-loader:before,
.cssload-pulse-loader:after {
	animation: 1.15s infinite ease-in-out;
}

.cssload-pulse-loader:before,
.cssload-pulse-loader:after {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	position: absolute;
	top: 0;
	left: 0;
}

.cssload-pulse-loader {
	animation-name: cssload-pulse-loader;
}

@keyframes cssload-pulse-loader {
	from {
		transform: scale(0);
		opacity: 1;
	}

	to {
		transform: scale(1);
		opacity: 0;
	}
}

/* Loading Overlay - Skin */
/* Cubes - Skin */
.cssload-thecube .cssload-cube:before {
	background-color: var(--primary);
}

/* CubeProgress - Skin */
.cssload-cube-progress {
	border-color: var(--primary);
}

.cssload-cube-progress-inner {
	background-color: var(--primary);
}

/* Float Rings - Skin */
.cssload-float-rings-inner.cssload-one {
	border-bottom-color: var(--primary);
}

.cssload-float-rings-inner.cssload-two {
	border-right-color: var(--primary);
}

.cssload-float-rings-inner.cssload-three {
	border-top-color: var(--primary);
}

/* Float Bars - Skin */
.cssload-float-bars-loading,
.cssload-float-bars-loading:after,
.cssload-float-bars-loading:before {
	background: var(--primary);
}

/* Speeding Wheel - Skin */
.cssload-speeding-wheel {
	border-top-color: var(--primary);
	border-bottom-color: var(--primary);
}

/* Zenith - Skin */
.cssload-zenith {
	box-shadow: 3px 3px 1px var(--primary);
}

/* Spinning Square Effect - Skin */
.cssload-spinning-square-loading {
	background: var(--primary);
}

.cssload-spinning-square-loading:before {
	background: var(--secondary);
}

.cssload-spinning-square-loading:after {
	background: var(--primary-inverse);
}

/* Pulse Effect - Skin */
.cssload-pulse-loader {
	background: var(--primary);
}

/* Maps */
.google-map {
	background: #E5E3DF;
	height: 400px;
	margin: -35px 0 30px 0;
	width: 100%;
}

.google-map.small {
	height: 250px;
	border-radius: 6px;
	margin: 0;
	clear: both;
}

.google-map img {
	max-width: 9999px;
}

.google-map-borders {
	border: 1px solid #DDD;
	padding: 4px;
	border-radius: var(--border-radius);
}

/* Map Pins */
@keyframes mapPinPulse {
	from {
		transform: scale(1);
		opacity: 0.7;
	}

	to {
		transform: scale(10);
		opacity: 0;
	}
}

/* Map Info Window */
.map-info-window {
	position: absolute;
	bottom: 30px;
	left: 50%;
	font-size: 12.8px;
	font-size: 0.8rem;
	background: var(--light);
	border-radius: var(--border-radius);
	padding: 15px 20px;
	white-space: nowrap;
	transform: translate3d(-50%, 0, 0);
}

.map-info-window:after {
	content: '';
	position: absolute;
	bottom: -7px;
	left: 50%;
	transform: translate3d(-50%, 0, 0);
	width: 0;
	height: 0;
	border-top: 7px solid #FFF;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
}

.map-info-window .map-info-window-close {
	position: absolute;
	top: 3px;
	right: 3px;
}

.map-rounded-pin {
	width: 13px;
	height: 13px;
	background: var(--grey-500);
	border-radius: 100%;
}

.map-rounded-pin.map-rounded-pin-animated {
	position: relative;
}

.map-rounded-pin.map-rounded-pin-animated:before, .map-rounded-pin.map-rounded-pin-animated:after {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: inherit;
	border-radius: inherit;
	animation-name: mapPinPulse;
	animation-duration: 1.5s;
	animation-iteration-count: infinite;
	animation-delay: 1s;
}

.map-rounded-pin.map-rounded-pin-animated:after {
	animation-delay: 1.250s;
}

/* Map Pin - Skin */
.map-rounded-pin {
	background: var(--primary);
}

/* Google Map on Footer */
.google-map-footer {
	margin: 35px 0 -50px !important;
}

/* RTL Popup */
html[dir="rtl"] .gm-style .gm-style-iw-c {
	padding: 20px 20px 0 0 !important;
}

/* Pre */
pre {
	background: var(--grey-100);
	border: 1px solid #e8e8e8;
	border-radius: var(--border-radius);
	padding: 15px;
}

/* Alternative Font - Skin */
.alternative-font {
	color: var(--primary);
}

/* Blockquote - Skin */
.blockquote-primary {
	border-color: var(--primary) !important;
}

.blockquote-secondary {
	border-color: var(--secondary) !important;
}

.blockquote-tertiary {
	border-color: var(--tertiary) !important;
}

.blockquote-quaternary {
	border-color: var(--quaternary) !important;
}

.blockquote-dark {
	border-color: var(--dark) !important;
}

.blockquote-light {
	border-color: var(--light) !important;
}

/* Drop Caps - Skin */
p.drop-caps:first-letter {
	color: var(--primary);
}

p.drop-caps.drop-caps-style-2:first-letter {
	background-color: var(--primary);
}

/* Offset Anchor */
.offset-anchor {
	display: block;
	position: relative;
	visibility: hidden;
	top: -100px;
}

@media (max-width: 991px) {
	.offset-anchor {
		top: 0;
	}
}

/* Alerts */
.alert [class*="fa-"], .alert .icons {
	margin-right: 8px;
	font-size: 1.1em;
}

.alert ul {
	padding: 0;
	margin: 7px 0 0 40px;
}

.alert ul li {
	font-size: 0.9em;
}

.alert.alert-sm {
	padding: 5px 10px;
	font-size: 0.9em;
}

.alert.alert-lg {
	padding: 20px;
	font-size: 1.2em;
}

.alert.alert-dark {
	background-color: var(--dark--300);
	border-color: var(--dark--300);
	color: var(--light);
}

.alert.alert-dark .alert-link {
	color: var(--light);
}

/* Alerts - Skin */
.alert-primary {
	background-color: var(--primary);
	border-color: var(--primary-100);
	color: var(--primary-inverse);
}

.alert-primary .alert-link {
	color: var(--primary-inverse);
}

.alert-secondary {
	background-color: var(--secondary);
	border-color: var(--secondary-100);
	color: var(--secondary-inverse);
}

.alert-secondary .alert-link {
	color: var(--secondary-inverse);
}

.alert-tertiary {
	background-color: var(--tertiary);
	border-color: var(--tertiary-100);
	color: var(--tertiary-inverse);
}

.alert-tertiary .alert-link {
	color: var(--tertiary-inverse);
}

.alert-quaternary {
	background-color: var(--quaternary);
	border-color: var(--quaternary-100);
	color: var(--quaternary-inverse);
}

.alert-quaternary .alert-link {
	color: var(--quaternary-inverse);
}

.alert-dark {
	background-color: var(--dark);
	border-color: var(--dark-100);
	color: var(--dark-inverse);
}

.alert-dark .alert-link {
	color: var(--dark-inverse);
}

.alert-light {
	background-color: var(--light);
	border-color: var(--light-100);
	color: var(--light-inverse);
}

.alert-light .alert-link {
	color: var(--light-inverse);
}

/* Ratio */
.ratio.ratio-soundcloud {
	padding-bottom: 19.25%;
}

.ratio.ratio-borders {
	border: 1px solid #DDD;
	padding: 4px;
	border-radius: var(--border-radius);
}

.ratio.ratio-borders > * {
	top: 4px;
	left: 4px;
	width: calc( 100% - 8px);
	height: calc( 100% - 8px);
}

/* Alert Admin */
.alert-admin {
	margin: 25px 0;
}

.alert-admin img {
	margin: -50px 0 0;
}

.alert-admin .btn {
	margin: 0 0 -15px;
}

.alert-admin p {
	color: #444;
}

.alert-admin h4 {
	color: #111;
	font-size: 1.2em;
	font-weight: 600;
	text-transform: uppercase;
}

.alert-admin .warning {
	color: #B20000;
}

/* Not Included */
.not-included {
	color: #b7b7b7;
	display: block;
	font-size: 0.8em;
	font-style: normal;
	margin: -4px 0;
	padding: 0;
}

/* Tip */
.tip {
	display: inline-block;
	padding: 0 5px;
	background: var(--light);
	color: #111;
	text-shadow: none;
	border-radius: 3px;
	margin-left: 8px;
	position: relative;
	text-transform: uppercase;
	font-size: 10px;
	font-weight: bold;
}

.tip:before {
	right: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(255, 255, 255, 0);
	border-right-color: var(--light);
	border-width: 5px;
	margin-top: -5px;
}

.tip.skin {
	color: var(--light);
}

/* Ajax Box */
.ajax-box {
	transition: all 0.2s;
	position: relative;
}

.ajax-box .ajax-box-content {
	transition: all 0.2s;
	opacity: 1;
	overflow: hidden;
	height: auto;
}

.ajax-box .bounce-loader {
	opacity: 0;
}

.ajax-box.ajax-box-init {
	height: 0;
}

.ajax-box.ajax-box-loading {
	height: 300px;
}

.ajax-box.ajax-box-loading .ajax-box-content {
	opacity: 0;
	height: 300px;
}

.ajax-box.ajax-box-loading .bounce-loader {
	opacity: 1;
}

/* Notice Top Bar */
.notice-top-bar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	background: #d01a34;
	z-index: -1;
}

.notice-top-bar .notice-button {
	background: rgba(235, 34, 63, 0.7);
	color: var(--light);
	border-radius: 35px;
	transition: ease background 300ms;
	text-decoration: none;
}

.notice-top-bar .notice-button:hover {
	background: #eb223f;
}

.notice-top-bar .notice-top-bar-close {
	position: absolute;
	top: 7px;
	right: 7px;
}

/* Hover effetcs */
.hover-effect-1:not(.portfolio-item) {
	box-shadow: 10px 10px 74px -15px rgba(0, 0, 0, 0);
	transition: ease transform 300ms, ease box-shadow 300ms;
}

.hover-effect-1:not(.portfolio-item):hover {
	box-shadow: 10px 10px 74px -15px rgba(0, 0, 0, 0.4);
	transform: translate3d(0, -15px, 0);
}

.hover-effect-1.portfolio-item {
	transition: ease transform 300ms;
}

.hover-effect-1.portfolio-item .thumb-info,
.hover-effect-1.portfolio-item .owl-carousel {
	box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.06);
	transition: ease box-shadow 300ms;
}

.hover-effect-1.portfolio-item:hover .thumb-info,
.hover-effect-1.portfolio-item:hover .owl-carousel, .hover-effect-1.portfolio-item.show .thumb-info,
.hover-effect-1.portfolio-item.show .owl-carousel {
	box-shadow: 10px 10px 74px -15px rgba(0, 0, 0, 0.4);
}

.hover-effect-1 a {
	transition: ease color 300ms;
}

.hover-effect-2 {
	opacity: 1;
	transition: ease opacity 300ms;
}

.hover-effect-2:hover {
	opacity: 0.85;
}

.hover-effect-3 {
	opacity: 0.3;
	transition: ease opacity 300ms;
}

.hover-effect-3:hover {
	opacity: 1;
}

/* Sticky Wrapper */
.sticky-wrapper {
	position: fixed;
	width: 100% !important;
	z-index: 11;
}

.sticky-wrapper .sticky-body {
	display: flex;
	background: var(--light);
	width: 100%;
	z-index: 10;
	transition: ease background 300ms;
}

.sticky-wrapper.sticky-wrapper-transparent .sticky-body {
	background: transparent;
}

.sticky-wrapper.sticky-wrapper-effect-1 {
	position: absolute !important;
}

.sticky-wrapper.sticky-wrapper-effect-1.sticky-effect-active .sticky-body {
	position: fixed;
	background: var(--light);
	animation: headerReveal 0.4s ease-in;
}

.sticky-wrapper.sticky-wrapper-effect-1.sticky-wrapper-effect-1-dark.sticky-effect-active .sticky-body {
	background: var(--dark);
}

.sticky-wrapper.sticky-wrapper-border-bottom.sticky-effect-active .sticky-body {
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

/* Curved Border */
.curved-border {
	position: relative;
	min-height: 7.2vw;
}

.curved-border:after {
	content: '';
	background-image: url(../img/curved-border.svg);
	background-size: 100% 100%;
	background-position: center;
	position: absolute;
	left: 0;
	bottom: -2px;
	width: 100vw;
	height: 7.2vw;
	z-index: 20;
}

.curved-border.curved-border-top:after {
	top: -2px;
	bottom: auto;
	transform: rotateX(180deg);
}

/* Vertical Divider */
.vertical-divider {
	display: inline;
	border-left: 1px solid rgba(255, 255, 255, 0.1);
	border-right: 1px solid transparent;
}

/* Star Rating */
.rating-container .filled-stars {
	text-shadow: none !important;
	-webkit-text-stroke: 0 !important;
}

.rating-container .star {
	margin: 0 1px;
}

.rating-invisible {
	height: 27px;
	visibility: hidden !important;
}

.rating-xs {
	font-size: 12px;
}

/* Image Hotspots */
.image-hotspots {
	position: relative;
}

.image-hotspot {
	cursor: pointer;
	position: absolute;
}

.image-hotspot strong {
	color: var(--light);
	z-index: 5;
	font-size: 0.75em;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate3d(-50%, -50%, 0);
	font-weight: 600 !important;
}

.image-hotspot .circle {
	position: absolute;
	left: 50%;
	top: 50%;
	width: 28px;
	height: 28px;
	margin: -0.666em auto auto -0.666em;
	background: var(--grey-500);
	border-radius: 50%;
	opacity: 0.6;
	transform-origin: 50% 50%;
	transition: opacity .2s ease-in, transform .1s ease-out;
	color: white;
	font-size: 1.5em;
	padding: 0;
	text-align: center;
	line-height: 28px;
	overflow: hidden;
}

.image-hotspot .ring {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	width: 2em;
	height: 2em;
	margin: -1em auto auto -1em;
	transform-origin: 50% 50%;
	border-radius: 50%;
	border: 1px solid var(--grey);
	opacity: 0;
	animation: hotspot-pulsate 3s ease-out infinite;
}

/* Page Transition */
body[data-plugin-page-transition] {
	transition: ease opacity 300ms;
}

body.page-transition-active {
	opacity: 0 !important;
}

/* 
Image 360º Viewer
Credits: Codyhouse (https://codyhouse.co/gem/360-degrees-product-viewer)
*/
.cd-product-viewer-wrapper {
	text-align: center;
	padding: 2em 0;
}

.cd-product-viewer-wrapper > div {
	display: inline-block;
}

@media (max-width: 1199px) {
	.cd-product-viewer-wrapper > div {
		width: 100% !important;
	}
}

.cd-product-viewer-wrapper .product-viewer {
	position: relative;
	z-index: 1;
	display: inline-block;
	overflow: hidden;
}

@media (max-width: 1199px) {
	.cd-product-viewer-wrapper .product-viewer {
		width: 100% !important;
	}
}

.cd-product-viewer-wrapper img {
	display: block;
	position: relative;
	width: 100%;
	z-index: 1;
}

.cd-product-viewer-wrapper .product-sprite {
	position: absolute;
	z-index: 2;
	top: 0;
	left: 0;
	height: 100%;
	width: 1600%;
	background-size: 100%;
	background-repeat: no-repeat;
	background-position: center center;
	opacity: 0;
	transition: ease opacity 300ms;
}

.cd-product-viewer-wrapper.loaded .product-sprite {
	opacity: 1;
	cursor: ew-resize;
}

.cd-product-viewer-handle {
	position: relative;
	z-index: 2;
	width: 60%;
	max-width: 300px;
	border-radius: 50em;
	margin: 1em auto 3em;
	height: 4px;
}

.cd-product-viewer-handle .fill {
	position: absolute;
	z-index: 1;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	border-radius: inherit;
	background: var(--dark);
	transform: scaleX(0);
	transform-origin: left center;
	transition: ease transform 500ms;
}

.no-csstransitions .cd-product-viewer-handle .fill {
	display: none;
}

.loaded .cd-product-viewer-handle .fill {
	opacity: 1;
	background-image: none !important;
	transition: ease transform 300ms, ease opacity 200ms 300ms;
}

.cd-product-viewer-handle .handle {
	position: absolute;
	z-index: 2;
	display: inline-block;
	height: 44px;
	width: 44px;
	left: 0;
	top: -20px;
	background: var(--dark);
	border-radius: 50%;
	border: 2px solid var(--dark);
	text-indent: 100%;
	white-space: nowrap;
	overflow: hidden;
	color: transparent;
	transform: translateX(-50%) scale(0);
	transition: ease box-shadow 200ms;
}

.cd-product-viewer-handle .handle .bars {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 4px;
	height: 20px;
	background: var(--light);
	transform: translate3d(-50%, -50%, 0);
}

.cd-product-viewer-handle .handle .bars:before, .cd-product-viewer-handle .handle .bars:after {
	content: '';
	position: absolute;
	top: 50%;
	width: 4px;
	height: 60%;
	background: var(--light);
	transform: translate3d(0, -50%, 0);
}

.cd-product-viewer-handle .handle .bars:before {
	left: -8px;
}

.cd-product-viewer-handle .handle .bars:after {
	right: -8px;
}

.loaded .cd-product-viewer-handle .handle {
	transform: translateX(-50%) scale(1);
	animation: cd-bounce 300ms 300ms;
	animation-fill-mode: both;
	cursor: ew-resize;
}

@keyframes cd-bounce {
	0% {
		transform: translateX(-50%) scale(0);
	}

	60% {
		transform: translateX(-50%) scale(1.1);
	}

	100% {
		transform: translateX(-50%) scale(1);
	}
}

/* 360º Image Viewer - Skin */
.cd-product-viewer-handle .handle {
	background: var(--primary);
}

/* Payment CC */
.payment-cc {
	display: flex;
}

.payment-cc > i {
	font-size: 32px;
	font-size: 2rem;
	transition: ease color 300ms;
}

.payment-cc > i + i {
	margin-left: 10px;
}

.payment-cc > i:hover {
	color: var(--light);
}

.payment-cc.payment-cc-no-hover-effect > i:hover {
	color: inherit;
}

/* PORTO Big Title */
.porto-big-title {
	letter-spacing: -22px;
	font-size: 144px;
	font-size: 9rem;
	line-height: 0.8;
}

@media (max-width: 575px) {
	.porto-big-title {
		letter-spacing: -18px;
		font-size: 7rem;
	}
}

/* Read More Rendered (JS Plugin) */
.readmore-rendered {
	position: relative;
	overflow: hidden;
}

.readmore-rendered .readmore-overlay {
	position: absolute;
	bottom: 0px;
	left: 0px;
	width: 100%;
	z-index: 1;
}

.readmore-rendered .readmore-button-wrapper {
	position: absolute;
	bottom: 0px;
	left: 0px;
	width: 100%;
	z-index: 2;
}

/* Style Switcher Open Loader Button */
.style-switcher-open-loader {
	color: var(--light) !important;
	background-color: #151719;
	display: block;
	height: 40px;
	position: fixed;
	left: 0;
	top: 135px;
	width: 40px;
	font-size: 12px;
	line-height: 40px;
	color: var(--light);
	text-align: center;
	text-decoration: none;
	border-radius: 0 4px 4px 0;
	z-index: 9999;
}

.style-switcher-open-loader.style-switcher-open-loader-loading > i {
	animation-name: basicRotation;
	animation-duration: 1.5s;
	animation-iteration-count: infinite;
	animation-timing-function: linear;
}

.style-switcher-open-loader.style-switcher-open-loader-loading > i:before {
	content: "\f1ce";
}

.style-switcher-open-loader .style-switcher-tooltip {
	visibility: hidden;
	opacity: 0;
	position: absolute;
	top: 50%;
	left: calc(100% + 20px);
	width: 200px;
	background: #282d31;
	text-align: left;
	padding: 22.4px;
	padding: 1.4rem;
	border-radius: var(--border-radius);
	box-shadow: 0px 0px 30px -6px rgba(0, 0, 0, 0.2);
	transform: translate3d(-20px, -50%, 0);
	transition: ease opacity 300ms, ease transform 300ms;
}

.style-switcher-open-loader .style-switcher-tooltip:before {
	content: '';
	display: block;
	position: absolute;
	top: 50%;
	left: -10px;
	width: 0;
	height: 0;
	border-top: 10px solid transparent;
	border-bottom: 10px solid transparent;
	border-right: 10px solid #282d31;
	transform: translate3d(0, -50%, 0);
}

.style-switcher-open-loader .style-switcher-tooltip > strong {
	font-size: 13.6px;
	font-size: 0.85rem;
	font-family: "Open Sans";
	line-height: 1.3;
	display: block;
	margin-bottom: 8px;
	margin-bottom: 0.5rem;
}

.style-switcher-open-loader .style-switcher-tooltip > p {
	color: #e2e2e2;
	font-size: 12.8px;
	font-size: 0.8rem;
	line-height: 1.5;
	margin-bottom: 0;
	opacity: 0.8;
}

.style-switcher-open-loader .style-switcher-tooltip.active {
	visibility: visible;
	opacity: 1;
	transform: translate3d(0, -50%, 0);
}

@media (max-width: 767px) {
	.style-switcher-open-loader {
		display: none;
	}
}

/* Envato Frame */
.remove-envato-frame {
	background: #404040;
	border-radius: 0 0 4px 4px;
	top: 0;
	color: var(--light);
	display: block;
	height: 9px;
	padding: 10px 20px 35px;
	position: fixed;
	right: 5px;
	text-align: center;
	text-decoration: none;
	min-width: 50px;
	z-index: 10000;
	font-size: 0.8em;
	opacity: 0.7;
}

.remove-envato-frame:hover {
	opacity: 0.9;
	text-decoration: none;
	color: var(--light);
}

.remove-envato-frame:before {
	font-family: 'Font Awesome 6 Free';
	content: "\f00d" !important;
	font-weight: 900;
	display: inline-block;
	padding-right: 5px;
}

@media (max-width: 991px) {
	.remove-envato-frame {
		display: none !important;
	}
}

/* Envato Buy Redirect and Demos */
.envato-buy-redirect, .demos-redirect {
	color: var(--light) !important;
	background-color: #151719;
	display: block;
	height: 40px;
	position: fixed;
	left: 0;
	top: 176px;
	width: 40px;
	font-size: 12px;
	line-height: 40px;
	color: var(--light);
	text-align: center;
	text-decoration: none;
	border-radius: 0 4px 4px 0;
	z-index: 9998;
}

.demos-redirect {
	top: 217px;
}

.demos-redirect img {
	max-width: 16px;
	max-height: 16px;
}

@media (max-width: 767px) {
	.envato-buy-redirect, .demos-redirect {
		display: none;
	}
}

/* Video */
[data-plugin-video] {
	position: relative;
}

[data-plugin-video] > img {
	position: relative;
	z-index: 1;
}

[data-plugin-video] > video {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 0;
}

[data-plugin-video] > video.active {
	z-index: 1;
}

/* Cursor Effect */
.cursor-outer {
	position: fixed;
	left: 0;
	top: 0;
	width: 40px;
	height: 40px;
	border: 1px solid var(--grey-500);
	border-radius: 100%;
	pointer-events: none;
	z-index: 9998;
	transition: ease-out all .08s .010s;
}

.cursor-outer.cursor-outer-fit {
	left: 0 !important;
	top: 0 !important;
}

.cursor-outer.cursor-color-light {
	border-color: var(--light) !important;
}

.cursor-outer.cursor-color-dark {
	border-color: #212121 !important;
}

.cursor-inner {
	position: fixed;
	left: 17px;
	top: 17px;
	width: 6px;
	height: 6px;
	background: var(--grey-500);
	border-radius: 100%;
	pointer-events: none;
	z-index: 9999;
	transition: all .08s ease-out;
}

.cursor-inner.cursor-inner-plus {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: transparent !important;
}

.cursor-inner.cursor-inner-plus:before {
	content: '+';
	color: var(--light);
	font-size: 27.2px;
	font-size: 1.7rem;
}

.cursor-inner.cursor-color-light:not(.cursor-inner-plus) {
	background-color: var(--light) !important;
}

.cursor-inner.cursor-color-light.cursor-inner-plus:before {
	color: var(--light) !important;
}

.cursor-inner.cursor-color-dark:not(.cursor-inner-plus) {
	background-color: #212121 !important;
}

.cursor-inner.cursor-color-dark.cursor-inner-plus:before {
	color: #212121 !important;
}

.hide-mouse-cursor {
	cursor: none;
}

.hide-mouse-cursor a,
.hide-mouse-cursor button,
.hide-mouse-cursor input,
.hide-mouse-cursor textarea,
.hide-mouse-cursor .mfp-auto-cursor .mfp-content {
	cursor: none !important;
}

.cursor-effect-size-small .cursor-outer {
	width: 20px;
	height: 20px;
	left: 10px;
	top: 9px;
}

.cursor-effect-size-small .cursor-inner {
	width: 4px;
	height: 4px;
	left: 18px;
}

.cursor-effect-size-small .cursor-inner.cursor-inner-plus:before {
	font-size: 17.6px;
	font-size: 1.1rem;
}

.cursor-effect-size-big .cursor-outer {
	width: 60px;
	height: 60px;
	left: -9px;
	top: -10px;
}

.cursor-effect-size-big .cursor-inner {
	width: 8px;
	height: 8px;
}

.cursor-effect-size-big .cursor-inner.cursor-inner-plus:before {
	font-size: 41.6px;
	font-size: 2.6rem;
}

.cursor-effect-style-square .cursor-outer {
	border-radius: 0;
}

.cursor-effect-style-square .cursor-inner {
	border-radius: 0;
}

/* Cursor Effect - Skin */
.cursor-inner {
	background-color: var(--primary);
}

.cursor-inner.cursor-inner-plus:before {
	color: var(--primary);
}

.cursor-outer {
	border-color: var(--primary);
}

/* Simple Copy To Clipboard */
.copy-to-clipboard-wrapper .btn {
	position: relative;
}

.copy-to-clipboard-wrapper .btn:before {
	content: 'Copy to Clipboard';
	position: absolute;
	top: -41px;
	left: 50%;
	background: #212121;
	border-radius: var(--border-radius);
	transform: translate3d(-50%, 0, 0);
	opacity: 0;
	display: none;
	transition: ease opacity 300ms;
	white-space: nowrap;
	padding: 6px 14px;
	font-size: 12px;
	font-size: 0.75rem;
	z-index: 10;
}

.copy-to-clipboard-wrapper .btn:after {
	content: '';
	position: absolute;
	top: -12px;
	left: 50%;
	width: 0;
	height: 0;
	border-top: 7px solid #212121;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	transform: translate3d(-50%, 0, 0);
	opacity: 0;
	display: none;
	transition: ease opacity 300ms;
}

.copy-to-clipboard-wrapper .btn:hover:before, .copy-to-clipboard-wrapper .btn:hover:after, .copy-to-clipboard-wrapper .btn.copied:before, .copy-to-clipboard-wrapper .btn.copied:after {
	display: block;
	opacity: 1;
}

.copy-to-clipboard-wrapper .btn.copied:before {
	content: 'Copied!';
}

/* Particles */
.particles-wrapper {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
}

.particles-wrapper div {
	height: 100%;
}

.particle {
	position: absolute;
}

.particle.particle-dots {
	-webkit-mask-image: url(../img/particle-dot.svg);
	mask-image: url(../img/particle-dot.svg);
	z-index: 0;
	-webkit-mask-size: 18px;
}

.particle.particle-repeating-lines {
	background: repeating-linear-gradient(-55deg, var(--light-rgba-0) 0.8px, var(--primary) 1.6px, var(--primary) 3px, var(--light-rgba-0) 3.8px, var(--light-rgba-0) 10px);
}

.particle.particle-repeating-lines.bg-light {
	background: repeating-linear-gradient(-55deg, var(--light-rgba-0) 0.8px, #FFF 1.6px, #FFF 3px, var(--light-rgba-0) 3.8px, var(--light-rgba-0) 10px) !important;
}

.particle.particle-repeating-lines.bg-dark {
	background: repeating-linear-gradient(-55deg, var(--light-rgba-0) 0.8px, var(--dark) 1.6px, var(--dark) 3px, var(--light-rgba-0) 3.8px, var(--light-rgba-0) 10px) !important;
}

/* Patterns */
.patterns svg {
	position: absolute;
}

/* Progress Reading */
.progress-reading {
	z-index: 99999;
	height: 8px;
}

/* Content Switcher */
.content-switcher-wrapper {
	position: relative;
}

.content-switcher {
	transition: all 0.2s ease-in-out;
	opacity: 0;
	top: 0;
}

.content-switcher.active {
	position: absolute;
	opacity: 1;
}

.content-switcher.initialized {
	position: absolute;
}

/* reCaptcha Fix */
.grecaptcha-badge {
	bottom: 60px !important;
}

/* Inverted - Skin */
.inverted {
	background-color: var(--primary);
}

.inverted-primary {
	background-color: var(--primary);
}

.inverted-secondary {
	background-color: var(--secondary);
}

.inverted-tertiary {
	background-color: var(--tertiary);
}

.inverted-quaternary {
	background-color: var(--quaternary);
}

.inverted-dark {
	background-color: var(--dark);
}

.inverted-light {
	background-color: var(--light);
}

/* Hotspots - Skin */
.image-hotspot-primary strong {
	color: var(--primary-inverse) !important;
}

.image-hotspot-primary .circle {
	background: var(--primary) !important;
}

.image-hotspot-primary .ring {
	border-color: var(--primary-rgba-90);
}

.image-hotspot-secondary strong {
	color: var(--secondary-inverse) !important;
}

.image-hotspot-secondary .circle {
	background: var(--secondary) !important;
}

.image-hotspot-secondary .ring {
	border-color: var(--secondary-rgba-90);
}

.image-hotspot-tertiary strong {
	color: var(--tertiary-inverse) !important;
}

.image-hotspot-tertiary .circle {
	background: var(--tertiary) !important;
}

.image-hotspot-tertiary .ring {
	border-color: var(--tertiary-rgba-90);
}

.image-hotspot-quaternary strong {
	color: var(--quaternary-inverse) !important;
}

.image-hotspot-quaternary .circle {
	background: var(--quaternary) !important;
}

.image-hotspot-quaternary .ring {
	border-color: var(--quaternary-rgba-90);
}

.image-hotspot-dark strong {
	color: var(--dark-inverse) !important;
}

.image-hotspot-dark .circle {
	background: var(--dark) !important;
}

.image-hotspot-dark .ring {
	border-color: var(--dark-rgba-90);
}

.image-hotspot-light strong {
	color: var(--light-inverse) !important;
}

.image-hotspot-light .circle {
	background: var(--light) !important;
}

.image-hotspot-light .ring {
	border-color: var(--light-rgba-90);
}

/* Popover */
.popover {
	box-shadow: 0 15px 45px 15px rgba(0, 0, 0, 0.07);
}

/* Scrollable	- Skin */
.scrollable.colored-slider .scrollable-slider {
	background: var(--primary);
}

/* Vertical Lines */
.vertical-lines:not(.vertical-lines-after) {
	background-image: url(../img/vertical-lines.svg);
	background-repeat: repeat;
	background-position: 50%;
	pointer-events: none;
}

.vertical-lines.vertical-lines-after {
	position: relative;
}

.vertical-lines.vertical-lines-after:after {
	content: "";
	display: block;
	position: absolute;
	z-index: 1;
	background-image: url(../img/vertical-lines.svg);
	background-repeat: repeat;
	background-position: 50%;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

.vertical-lines.vertical-lines-light:not(.vertical-lines-after) {
	background-image: url(../img/vertical-lines-light.svg);
}

.vertical-lines.vertical-lines-light.vertical-lines-after:after {
	background-image: url(../img/vertical-lines-light.svg);
}

/* OffCanvas */
.offcanvas {
	max-width: 82vw;
}

/* Popup */
.popup-inline-content,
.mfp-content .ajax-container {
	background: none repeat scroll 0 0 #FFF;
	margin: 40px auto;
	max-width: 1170px;
	padding: 20px 30px;
	position: relative;
	text-align: left;
	display: none;
}

.mfp-content .ajax-container {
	display: block;
}

.mfp-wrap .popup-inline-content {
	display: block;
}

.modal-header .btn-close {
	text-indent: -99999px;
	box-shadow: none;
}

.modal-footer > :not(:first-child) {
	margin-left: 4px;
	margin-left: .25rem;
	margin-right: 0px;
	margin-right: 0rem;
}

.modal-footer > :not(:last-child) {
	margin-right: 4px;
	margin-right: .25rem;
	margin-left: 0px;
	margin-left: 0rem;
}

/* Nav */
.nav > li > a {
	text-decoration: none;
}

.nav > li > a:hover, .nav > li > a:focus {
	background: transparent;
	text-decoration: none;
}

/* Nav List */
ul.nav-list li {
	margin: 0;
	padding: 0;
	display: block;
	position: relative;
}

ul.nav-list li a {
	transition: background 0.1s;
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
	padding: 10px 0 10px 22px;
	display: block;
	color: #666;
	font-size: 1em;
	text-decoration: none;
	line-height: 20px;
}

ul.nav-list li a:before {
	font-family: 'Font Awesome 6 Free';
	content: "\f054";
	font-weight: 900;
	position: absolute;
	top: 10px;
	font-size: 7.2px;
	font-size: 0.45rem;
	opacity: 0.7;
	transform: translate3d(-1px, 0, 0);
	left: 12px;
}

ul.nav-list li a:hover {
	text-decoration: none;
}

ul.nav-list li a:hover:before,
ul.nav-list li a:hover i[class*="fa-chevron"] {
	animation-name: arrowLeftRight;
	animation-duration: 500ms;
	animation-iteration-count: infinite;
}

ul.nav-list li a i[class*="fa-chevron"] {
	position: relative;
	top: -1px;
	font-size: 8px;
	font-size: 0.5rem;
	opacity: 0.8;
}

ul.nav-list li > a.active {
	font-weight: bold;
}

ul.nav-list li ul {
	list-style: none;
	margin: 0 0 0 25px;
	padding: 0;
}

ul.nav-list li:last-child a {
	border-bottom: 0;
}

ul.nav-list.hide-not-active li ul {
	display: none;
}

ul.nav-list.hide-not-active li.active ul {
	display: block;
}

ul.nav-list.show-bg-active .active > a,
ul.nav-list.show-bg-active a.active {
	background-color: #f5f5f5;
}

ul.nav-list.show-bg-active .active > a:hover,
ul.nav-list.show-bg-active a.active:hover {
	background-color: #eee;
}

ul.nav-list.no-arrows li a:before {
	content: none;
}

ul.nav-list-arrows li a {
	padding-top: 12px;
	padding-bottom: 12px;
	padding-left: 0;
	padding-right: 0;
}

ul.nav-list-arrows li a:after {
	content: '';
	width: 24px;
	height: 24px;
	display: inline-flex;
	position: absolute;
	right: 0;
	left: auto;
	background: var(--dark-rgba-10);
	transform: none;
	border-radius: 50%;
}

ul.nav-list-arrows li a:before {
	width: auto;
	height: 24px;
	display: inline-flex;
	position: absolute;
	right: 8px;
	left: auto;
	font-size: 0.9em;
	transform: none;
	color: var(--dark);
	opacity: 1;
	z-index: 1;
	top: 14px;
}

ul.nav-list-arrows li a.active {
	color: var(--primary);
}

ul.nav-list-arrows li a.active:after {
	background: var(--primary);
}

ul.nav-list-arrows li a.active:before {
	color: var(--primary-inverse);
}

ul.nav-list-arrows-simple li a:after {
	display: none;
}

ul.nav-list-arrows-simple li a:before {
	content: "\f061";
}

ul.nav-list-arrows-simple li a.active:before {
	color: var(--primary);
}

ul.nav-list-simple li {
	margin: 0;
	padding: 0;
	display: block;
	position: relative;
}

ul.nav-list-simple li a {
	transition: background 0.1s;
	border-bottom: 1px solid rgba(0, 0, 0, 0.07);
	padding: 8px 0;
	display: block;
	color: #666;
	font-size: 0.9em;
	text-decoration: none;
	line-height: 20px;
	transition: ease padding 300ms;
}

ul.nav-list-simple li > a.active {
	font-weight: bold;
}

ul.nav-list-simple li:hover a {
	padding-left: 4px;
}

ul.nav-list-simple li ul {
	list-style: none;
	margin: 0 0 0 25px;
	padding: 0;
}

ul.narrow li a {
	padding: 4px 0;
}

ul.nav-pills > li > a.active {
	color: var(--light);
	background-color: var(--grey-500);
}

ul.nav-pills > li > a.active:hover, ul.nav-pills > li > a.active:focus {
	color: var(--light);
	background-color: var(--grey-500);
}

/* Nav Pills */
.nav-pills-center {
	text-align: center;
}

.nav-pills-center > li {
	display: inline-block;
	float: none;
}

.nav-pills-sm > li > a {
	padding: 7px 12px;
}

.nav-pills-xs > li > a {
	padding: 5px 10px;
	font-size: 0.9em;
}

.nav-pills .nav-link.active {
	background-color: transparent;
}

.show > .nav-pills .nav-link {
	background-color: transparent;
}

/* Nav Light */
.nav-light > li > a {
	color: var(--light);
}

/* Nav Active Style - Active Bottom Border */
.nav-active-style-1 {
	min-height: 100px;
	align-items: center;
}

.nav-active-style-1 > li {
	align-self: stretch;
}

.nav-active-style-1 > li > a {
	display: flex;
	align-items: center;
	height: 100%;
	padding: 0 !important;
	margin: 0 15px;
	border-bottom: 2px solid transparent;
	transition: ease border-color 300ms;
}

.nav-active-style-1 > li > a:hover, .nav-active-style-1 > li > a:focus, .nav-active-style-1 > li > a.active {
	border-bottom-color: var(--grey-500);
}

@media (max-width: 575px) {
	.nav-active-style-1 > li > a {
		min-height: 45px;
		justify-content: center;
	}
}

/* Nav Secondary */
.nav-secondary {
	background: #fafafa;
	margin: 0;
	padding: 20px 0;
	text-align: center;
	z-index: 99;
	width: 100% !important;
}

.nav-secondary .nav-pills > li {
	float: none;
	display: inline-block;
}

.nav-secondary .nav-pills > li a {
	font-size: 12px;
	font-style: normal;
	font-weight: 600;
	line-height: 20px;
	padding: 10px 13px;
	text-transform: uppercase;
}

.nav-secondary.sticky-active {
	border-bottom-color: #e9e9e9;
	box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Boxed */
html.boxed .nav-secondary.sticky-active {
	max-width: 1200px;
	left: auto !important;
}

/* Section Scroll - Dots Navigation */
.section-scroll-dots-navigation {
	display: flex;
	flex-direction: column;
	position: fixed;
	top: 50%;
	right: 30px;
	background: var(--light);
	border-radius: 35px;
	padding: 9.6px;
	padding: 0.6rem;
	z-index: 10;
	transform: translate3d(0, -50%, 0);
}

.section-scroll-dots-navigation > ul {
	margin: 8px 0;
	margin: 0.5rem 0;
}

.section-scroll-dots-navigation > ul:hover > li.active:hover > a > span {
	opacity: 1;
	visibility: visible;
}

.section-scroll-dots-navigation > ul:hover > li.active > a > span {
	opacity: 0;
	visibility: hidden;
}

.section-scroll-dots-navigation > ul > li {
	position: relative;
	margin-bottom: 4.8px;
	margin-bottom: 0.3rem;
}

.section-scroll-dots-navigation > ul > li:last-child {
	margin-bottom: 0;
}

.section-scroll-dots-navigation > ul > li:hover {
	background: transparent;
}

.section-scroll-dots-navigation > ul > li:hover > a > span {
	opacity: 1;
	visibility: visible;
}

.section-scroll-dots-navigation > ul > li.active > a:before {
	opacity: 1;
}

.section-scroll-dots-navigation > ul > li > a {
	position: relative;
	width: 15px;
	height: 15px;
	display: block;
	padding: 4.8px;
	padding: 0.3rem;
}

.section-scroll-dots-navigation > ul > li > a:before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate3d(-50%, -50%, 0);
	width: 8px;
	height: 8px;
	border-radius: 100%;
	opacity: 0.3;
	background: var(--grey-500);
	transition: ease opacity 300ms;
}

.section-scroll-dots-navigation > ul > li > a > span {
	position: absolute;
	top: 50%;
	right: calc(100% + 20px);
	background: var(--light);
	color: var(--dark);
	font-size: 12.8px;
	font-size: 0.8rem;
	font-weight: 800;
	padding: 3.2px 16px;
	padding: 0.2rem 1rem;
	border-radius: var(--border-radius);
	white-space: nowrap;
	opacity: 0;
	visibility: hidden;
	transform: translate3d(0, -50%, 0);
	transition: ease opacity 300ms;
}

.section-scroll-dots-navigation > ul > li > a > span:after {
	content: '';
	position: absolute;
	top: 50%;
	left: 100%;
	width: 0;
	height: 0;
	border-top: 6px solid transparent;
	border-bottom: 6px solid transparent;
	border-left: 6px solid var(--grey-100);
	transform: translate3d(0, -50%, 0);
}

.section-scroll-dots-navigation.section-scroll-dots-navigation-style-2 {
	background: transparent;
}

.section-scroll-dots-navigation.section-scroll-dots-navigation-light > ul > li > a:before {
	background: var(--light);
}

.section-scroll-dots-navigation.section-scroll-dots-navigation-light > ul > li > a > span {
	right: calc(100% + 10px);
}

@media (max-width: 767px) {
	.section-scroll-dots-navigation {
		right: 15px;
	}
}

/* Nav Colors */
.nav-color-primary nav > ul > li > a {
	color: var(--primary) !important;
}

.nav-color-primary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: var(--primary) !important;
}

.nav-color-primary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: var(--primary) !important;
}

.nav-color-secondary nav > ul > li > a {
	color: var(--secondary) !important;
}

.nav-color-secondary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: var(--secondary) !important;
}

.nav-color-secondary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: var(--secondary) !important;
}

.nav-color-tertiary nav > ul > li > a {
	color: var(--tertiary) !important;
}

.nav-color-tertiary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: var(--tertiary) !important;
}

.nav-color-tertiary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: var(--tertiary) !important;
}

.nav-color-quaternary nav > ul > li > a {
	color: var(--quaternary) !important;
}

.nav-color-quaternary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: var(--quaternary) !important;
}

.nav-color-quaternary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: var(--quaternary) !important;
}

.nav-color-dark nav > ul > li > a {
	color: var(--dark) !important;
}

.nav-color-dark:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: var(--dark) !important;
}

.nav-color-dark:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: var(--dark) !important;
}

.nav-color-light nav > ul > li > a {
	color: var(--light) !important;
}

.nav-color-light:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: var(--light) !important;
}

.nav-color-light:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: var(--light) !important;
}

.nav-pills > li.active > a,
.nav-pills .nav-link.active {
	background-color: var(--primary);
}

.nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus,
.nav-pills .nav-link.active:hover,
.nav-pills .nav-link.active:focus {
	background-color: var(--primary);
}

.nav-active-style-1 > li > a:hover, .nav-active-style-1 > li > a:focus, .nav-active-style-1 > li > a.active {
	border-bottom-color: var(--primary);
}

/* Nav Pills Colors */
.nav-pills-primary a {
	color: var(--primary);
}

.nav-pills-primary a:hover {
	color: var(--primary--100);
}

.nav-pills-primary a:focus {
	color: var(--primary--100);
}

.nav-pills-primary a:active {
	color: var(--primary-100);
}

.nav-pills-primary .nav-link.active,
.nav-pills-primary > li.active > a {
	background-color: var(--primary);
}

.nav-pills-primary .nav-link.active:hover, .nav-pills-primary .nav-link.active:focus,
.nav-pills-primary > li.active > a:hover,
.nav-pills-primary > li.active > a:focus {
	background-color: var(--primary);
}

.nav-pills-secondary a {
	color: var(--secondary);
}

.nav-pills-secondary a:hover {
	color: var(--secondary--100);
}

.nav-pills-secondary a:focus {
	color: var(--secondary--100);
}

.nav-pills-secondary a:active {
	color: var(--secondary-100);
}

.nav-pills-secondary .nav-link.active,
.nav-pills-secondary > li.active > a {
	background-color: var(--secondary);
}

.nav-pills-secondary .nav-link.active:hover, .nav-pills-secondary .nav-link.active:focus,
.nav-pills-secondary > li.active > a:hover,
.nav-pills-secondary > li.active > a:focus {
	background-color: var(--secondary);
}

.nav-pills-tertiary a {
	color: var(--tertiary);
}

.nav-pills-tertiary a:hover {
	color: var(--tertiary--100);
}

.nav-pills-tertiary a:focus {
	color: var(--tertiary--100);
}

.nav-pills-tertiary a:active {
	color: var(--tertiary-100);
}

.nav-pills-tertiary .nav-link.active,
.nav-pills-tertiary > li.active > a {
	background-color: var(--tertiary);
}

.nav-pills-tertiary .nav-link.active:hover, .nav-pills-tertiary .nav-link.active:focus,
.nav-pills-tertiary > li.active > a:hover,
.nav-pills-tertiary > li.active > a:focus {
	background-color: var(--tertiary);
}

.nav-pills-quaternary a {
	color: var(--quaternary);
}

.nav-pills-quaternary a:hover {
	color: var(--quaternary--100);
}

.nav-pills-quaternary a:focus {
	color: var(--quaternary--100);
}

.nav-pills-quaternary a:active {
	color: var(--quaternary-100);
}

.nav-pills-quaternary .nav-link.active,
.nav-pills-quaternary > li.active > a {
	background-color: var(--quaternary);
}

.nav-pills-quaternary .nav-link.active:hover, .nav-pills-quaternary .nav-link.active:focus,
.nav-pills-quaternary > li.active > a:hover,
.nav-pills-quaternary > li.active > a:focus {
	background-color: var(--quaternary);
}

.nav-pills-dark a {
	color: var(--dark);
}

.nav-pills-dark a:hover {
	color: var(--dark--100);
}

.nav-pills-dark a:focus {
	color: var(--dark--100);
}

.nav-pills-dark a:active {
	color: var(--dark-100);
}

.nav-pills-dark .nav-link.active,
.nav-pills-dark > li.active > a {
	background-color: var(--dark);
}

.nav-pills-dark .nav-link.active:hover, .nav-pills-dark .nav-link.active:focus,
.nav-pills-dark > li.active > a:hover,
.nav-pills-dark > li.active > a:focus {
	background-color: var(--dark);
}

.nav-pills-light a {
	color: var(--light);
}

.nav-pills-light a:hover {
	color: var(--light--100);
}

.nav-pills-light a:focus {
	color: var(--light--100);
}

.nav-pills-light a:active {
	color: var(--light-100);
}

.nav-pills-light .nav-link.active,
.nav-pills-light > li.active > a {
	background-color: var(--light);
}

.nav-pills-light .nav-link.active:hover, .nav-pills-light .nav-link.active:focus,
.nav-pills-light > li.active > a:hover,
.nav-pills-light > li.active > a:focus {
	background-color: var(--light);
}

/* Nav Link Colors */
.nav-link {
	color: var(--primary);
}

.nav-link:hover {
	color: var(--primary--100);
}

.nav-link:focus {
	color: var(--primary--100);
}

.nav-link:active {
	color: var(--primary-100);
}

/* Section Scroll Nav Colors */
.section-scroll-dots-navigation-colored > ul > li.active > a:before {
	background: var(--primary);
}

/* Nav OffCanvas */
.offcanvas-nav .nav ul {
	list-style: none;
}

.offcanvas-nav .nav li {
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
	position: relative;
}

.offcanvas-nav .nav li:last-child {
	border-bottom: 0;
}

.offcanvas-nav .nav li a {
	padding: 15px 10px;
	font-size: 16px;
	display: block;
	text-decoration: none;
	font-weight: 600;
	color: var(--dark);
	background-color: transparent !important;
	opacity: 1;
	transition: opacity 0.3s;
}

.offcanvas-nav .nav li a:hover {
	opacity: 0.7;
}

.offcanvas-nav .nav li a.dropdown-toggle:after {
	display: none;
}

.offcanvas-nav .nav li a.dropdown-toggle .fa-chevron-down {
	color: var(--dark);
	border-radius: 50%;
	position: absolute;
	right: 0;
	margin-top: -3px;
	width: 30px;
	display: inline-flex;
	justify-content: center;
	height: 30px;
	align-items: center;
	z-index: 1;
}

.offcanvas-nav .nav li a.dropdown-toggle .fa-chevron-down:hover {
	background: var(--primary);
	color: var(--primary-inverse);
}

.offcanvas-nav .nav li.dropdown .dropdown-menu {
	margin: 0;
	padding: 0 0 10px 20px;
	display: none;
	position: relative;
	border: 0;
	opacity: 0;
	transition: opacity 1s;
}

.offcanvas-nav .nav li.dropdown .dropdown-menu li a {
	font-size: 14px;
}

.offcanvas-nav .nav li.dropdown .dropdown-menu li .dropdown-mega-sub-title {
	font-size: 14px;
	font-weight: 600;
	opacity: 0.7;
	display: inline-block;
	padding: 10px 0;
}

.offcanvas-nav .nav li.dropdown.open .dropdown-menu {
	opacity: 1;
	display: block;
}

/* Overlays */
.overlay {
	position: relative;
}

.overlay:before {
	content: ' ';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 2;
	background: var(--dark);
	opacity: 0;
	pointer-events: none;
	transition: ease opacity 300ms 100ms;
}

.overlay:hover:before, .overlay.overlay-show:before, .overlay.show:before {
	opacity: 0.8;
}

.overlay.overlay-op-1:hover:before, .overlay.overlay-op-1.overlay-show:before, .overlay.overlay-op-1.show:before {
	opacity: 0.1;
}

.overlay.overlay-op-2:hover:before, .overlay.overlay-op-2.overlay-show:before, .overlay.overlay-op-2.show:before {
	opacity: 0.2;
}

.overlay.overlay-op-3:hover:before, .overlay.overlay-op-3.overlay-show:before, .overlay.overlay-op-3.show:before {
	opacity: 0.3;
}

.overlay.overlay-op-4:hover:before, .overlay.overlay-op-4.overlay-show:before, .overlay.overlay-op-4.show:before {
	opacity: 0.4;
}

.overlay.overlay-op-5:hover:before, .overlay.overlay-op-5.overlay-show:before, .overlay.overlay-op-5.show:before {
	opacity: 0.5;
}

.overlay.overlay-op-6:hover:before, .overlay.overlay-op-6.overlay-show:before, .overlay.overlay-op-6.show:before {
	opacity: 0.6;
}

.overlay.overlay-op-7:hover:before, .overlay.overlay-op-7.overlay-show:before, .overlay.overlay-op-7.show:before {
	opacity: 0.7;
}

.overlay.overlay-op-8:hover:before, .overlay.overlay-op-8.overlay-show:before, .overlay.overlay-op-8.show:before {
	opacity: 0.8;
}

.overlay.overlay-op-9:hover:before, .overlay.overlay-op-9.overlay-show:before, .overlay.overlay-op-9.show:before {
	opacity: 0.9;
}

.overlay.overlay-backward:hover:before, .overlay.overlay-backward.overlay-show:before, .overlay.overlay-backward.show:before {
	z-index: 0;
}

.overlay .container,
.overlay .container-fluid {
	position: relative;
	z-index: 3;
}

.overlay-light:before {
	background-color: var(--light) !important;
}

.overlay-dark:before {
	background-color: var(--dark) !important;
}

.overlay-primary:before {
	background: var(--primary) !important;
}

.overlay-secondary:before {
	background: var(--secondary) !important;
}

.overlay-tertiary:before {
	background: var(--tertiary) !important;
}

.overlay-quaternary:before {
	background: var(--quaternary) !important;
}

/* Overlay Gradient Bottom Content */
.overlay-gradient-bottom-content:before {
	opacity: 1 !important;
	background: var(--dark);
	background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 30%, var(--dark) 100%);
}

/* Overlay Gradient Top Content */
.overlay-gradient-top-content:before {
	opacity: 1 !important;
	background: var(--dark);
	background: linear-gradient(to top, rgba(0, 0, 0, 0) 30%, var(--dark) 100%);
}

/* Overlay Gradient */
.overlay-gradient:before {
	background-color: #6c6ef1;
	background-image: linear-gradient(to right, #6c6ef1 0%, #e2498e 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6c6ef1', endColorstr='#e2498e', GradientType=1 );
}

.overlay-gradient.overlay-gradient-animated:before {
	background-size: 200% 200%;
	animation-name: gradientTransition;
	animation-duration: 20000ms;
	animation-iteration-count: infinite;
}

/* Overlay - Skin */
.overlay-color-primary:not(.no-skin):before {
	background-color: var(--primary) !important;
}

.overlay-color-secondary:not(.no-skin):before {
	background-color: var(--secondary) !important;
}

.overlay-color-tertiary:not(.no-skin):before {
	background-color: var(--tertiary) !important;
}

.overlay-color-quaternary:not(.no-skin):before {
	background-color: var(--quaternary) !important;
}

.overlay-color-dark:not(.no-skin):before {
	background-color: var(--dark) !important;
}

.overlay-color-light:not(.no-skin):before {
	background-color: var(--light) !important;
}

/* Overlay Gradient - Skin */
.overlay-gradient:before {
	background-color: var(--tertiary) !important;
	background-image: linear-gradient(to right, var(--tertiary) 0%, var(--quaternary) 100%) !important;
}

/* Parallax */
.parallax {
	background-color: transparent;
	background-position: 50% 50%;
	background-repeat: repeat;
	background-attachment: fixed;
	position: relative;
	clear: both;
}

.parallax.parallax-mouse {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
}

.parallax.parallax-mouse .parallax-mouse-object {
	position: absolute;
	top: 0;
	left: 0;
	-o-object-fit: contain;
	object-fit: contain;
}

.parallax:not(.page-header):not(.match-height):not([class*="section-height-"]) {
	margin: 70px 0;
	padding: 50px 0;
}

.parallax blockquote {
	border: 0;
	padding: 0 10%;
}

.parallax blockquote i.fa-quote-left {
	font-size: 34px;
	position: relative;
	left: -5px;
}

.parallax blockquote p {
	font-size: 24px;
	line-height: 30px;
}

.parallax blockquote span {
	font-size: 16px;
	line-height: 20px;
	color: #999;
	position: relative;
	left: -5px;
}

.parallax-disabled .parallax-background {
	background-attachment: scroll !important;
	background-position: 50% 50% !important;
}

.parallax-background + .container {
	position: relative;
}

.scrollable-parallax-wrapper {
	position: relative;
	z-index: 1;
}

/* Parallax - Skin */
.parallax blockquote i.fa-quote-left {
	color: var(--primary);
}

/* Pricing Tables */
.pricing-table {
	display: flex;
	flex-wrap: wrap;
}

.pricing-table .plan {
	background: var(--light);
	border-radius: 0;
	margin-top: 15px;
	margin-bottom: 15px;
}

.pricing-table .plan .plan-price,
.pricing-table .plan .plan-features,
.pricing-table .plan .plan-footer {
	border: 1px solid #ededed;
}

.pricing-table .plan .plan-header {
	background: var(--dark);
	padding: 15.2px 16px;
	padding: 0.95rem 1rem;
}

.pricing-table .plan .plan-header h3 {
	color: var(--light);
	font-size: 12.8px;
	font-size: 0.8rem;
	font-weight: 600;
	line-height: 1;
	letter-spacing: 0;
	text-align: center;
	text-transform: uppercase;
	margin-bottom: 0;
}

.pricing-table .plan .plan-price {
	display: flex;
	flex-direction: column;
	background: var(--grey-100);
	border-top: 0;
	border-bottom: 0;
	padding: 32px 12.8px;
	padding: 2rem 0.8rem;
	text-align: center;
}

.pricing-table .plan .plan-price .price {
	display: flex;
	justify-content: center;
	align-items: flex-start;
	font-size: 43.2px;
	font-size: 2.7rem;
	font-weight: 600;
	line-height: 1;
	color: var(--dark);
}

.pricing-table .plan .plan-price .price .price-unit {
	font-size: 50%;
	line-height: 1.4;
}

.pricing-table .plan .plan-price .price-label {
	text-transform: uppercase;
	color: var(--default);
	font-size: 11.2px;
	font-size: 0.7rem;
	line-height: 1;
	margin-bottom: 0;
}

.pricing-table .plan .plan-features {
	border-top: 0;
	border-bottom: 0;
	padding: 12.8px;
	padding: 0.8rem;
	text-align: center;
}

.pricing-table .plan .plan-features ul {
	padding: 0 8px 0 8px;
	padding: 0 0.5rem 0 0.5rem;
	margin-bottom: 0;
}

.pricing-table .plan .plan-features ul li {
	border-bottom: 1px solid #ededed;
	line-height: 2.9;
	list-style: none;
	font-size: 0.9em;
}

.pricing-table .plan .plan-features ul li:last-child {
	border-bottom: 0;
}

.pricing-table .plan .plan-footer {
	border-top: 0;
	padding: 24px 12.8px 40px;
	padding: 1.5rem 0.8rem 2.5rem;
	text-align: center;
}

.pricing-table .plan.plan-featured {
	position: relative;
	box-shadow: 0px 0px 40px rgba(200, 200, 200, 0.3);
	transform: scale(1.15);
	z-index: 1;
}

.pricing-table .plan.plan-featured .plan-header h3,
.pricing-table .plan.plan-featured .plan-features ul li {
	font-size: 12.8px;
	font-size: 0.8rem;
}

@media (max-width: 767px) {
	.pricing-table .plan.plan-featured {
		transform: scale(1);
	}

	.pricing-table .plan.plan-featured .plan-header h3,
		.pricing-table .plan.plan-featured .plan-features ul li {
		font-size: 0.9rem;
	}
}

/* Pricing Block Style */
.pricing-block > .row {
	margin-right: 0;
	margin-left: 0;
}

.pricing-block > .row > div:nth-child(1) {
	padding: 27.2px;
	padding: 1.7rem;
}

.pricing-block > .row > div:nth-child(2) {
	display: flex;
	flex-direction: column;
	justify-content: center;
	padding: 27.2px;
	padding: 1.7rem;
	background: var(--grey-100);
	text-align: center;
	border-left: 1px solid #dee2e6;
	border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.pricing-block .plan-price {
	display: flex;
	flex-direction: column;
	background: var(--grey-100);
	border-top: 0;
	border-bottom: 0;
	text-align: center;
}

.pricing-block .plan-price .price {
	display: flex;
	justify-content: center;
	align-items: flex-start;
	font-size: 43.2px;
	font-size: 2.7rem;
	font-weight: 600;
	line-height: 1;
	color: var(--dark);
}

.pricing-block .plan-price .price .price-unit {
	font-size: 50%;
	line-height: 1.4;
}

.pricing-block .plan-price .price-label {
	text-transform: uppercase;
	color: var(--default);
	font-size: 11.2px;
	font-size: 0.7rem;
	line-height: 1;
	margin-bottom: 0;
}

@media (max-width: 991px) {
	.pricing-block > .row > div:nth-child(2) {
		border-left: none;
		border-top: 1px solid #dee2e6;
		border-radius: 0 0 var(--border-radius) var(--border-radius);
	}
}

/* Price Menu */
.price-menu-item .price-menu-item-details {
	display: flex;
	justify-content: space-between;
	position: relative;
}

.price-menu-item .price-menu-item-details .price-menu-item-title {
	position: relative;
	padding-bottom: 10px;
}

.price-menu-item .price-menu-item-details .price-menu-item-price {
	position: relative;
	top: -5px;
}

.price-menu-item .price-menu-item-details .price-menu-item-price strong {
	font-size: 20.8px;
	font-size: 1.3rem;
}

.price-menu-item .price-menu-item-details .price-menu-item-price strong > span {
	font-size: 0.6em;
}

.price-menu-item .price-menu-item-details .price-menu-item-line {
	position: absolute;
	width: 100%;
	bottom: 0;
	border-bottom: dashed 1px #777;
}

.price-menu-item .price-menu-item-img img {
	border-radius: 50%;
	max-width: 60px;
	height: auto;
}

.price-menu-item .price-menu-item-desc {
	position: relative;
	margin-top: 5px;
}

.price-menu-item + .price-menu-item {
	margin-top: 25px;
}

/* Pricing Tables - Skin */
.pricing-table .most-popular {
	border-color: var(--primary);
}

.pricing-table .most-popular h3 {
	background-color: var(--primary);
}

.pricing-table.princig-table-flat .plan h3 {
	background-color: var(--primary);
}

.pricing-table.princig-table-flat .plan h3 span {
	background: var(--primary);
}

/* Process */
.process {
	text-align: center;
}

.process .process-step {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	position: relative;
}

.process .process-step-circle {
	display: flex;
	justify-content: center;
	height: 90px;
	text-align: center;
	width: 90px;
	border: 2px solid var(--grey-500);
	border-radius: 50%;
	align-items: center;
	position: relative;
	background-color: transparent;
	transition: all 0.2s ease-in-out;
}

.process .process-step-content {
	padding-top: 20px;
}

.process .process-step-circle-content {
	font-weight: bold;
	color: var(--dark);
	font-size: 1.6em;
	letter-spacing: -1px;
	transition: all 0.2s ease-in-out;
}

.process .process-step:hover .process-step-circle {
	background-color: var(--grey-500);
}

.process .process-step:hover .process-step-circle .process-step-circle-content {
	color: var(--light);
}

.process .process-step:hover .process-step-circle .process-step-circle-content .icons {
	color: var(--light) !important;
}

.process.process-shapes .process-step-circle {
	position: relative;
	width: auto;
	height: auto;
	border: 0;
	margin-bottom: 19.2px;
	margin-bottom: 1.2rem;
}

.process.process-shapes .process-step-circle .process-step-circle-content {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 45px;
	height: 45px;
	background-color: var(--grey-500);
	border-radius: 100%;
	color: var(--light);
	z-index: 3;
}

.process.process-shapes .process-step-circle:before, .process.process-shapes .process-step-circle:after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 100%;
	height: 100%;
	background-color: var(--default);
	border-radius: 100%;
	opacity: 0.6;
	transform: translate3d(-50%, -50%, 0) scale(1.5);
	transition: ease transform 1s;
	z-index: 2;
}

.process.process-shapes .process-step-circle:after {
	background-color: #999;
	opacity: 0.3;
	transform: translate3d(-50%, -50%, 0) scale(2);
	z-index: 1;
}

.process.process-shapes.process-shapes-hover-animate .process-step:hover .process-step-circle:before {
	animation-name: processShapesScaleOne;
	animation-delay: 300ms;
}

.process.process-shapes.process-shapes-hover-animate .process-step:hover .process-step-circle:after {
	animation-name: processShapesScaleTwo;
}

.process.process-shapes.process-shapes-hover-animate .process-step:hover .process-step-circle:before, .process.process-shapes.process-shapes-hover-animate .process-step:hover .process-step-circle:after {
	animation-fill-mode: forwards;
	animation-iteration-count: infinite;
	animation-duration: 3s;
	animation-timing-function: linear;
}

.process.process-shapes.process-shapes-always-animate .process-step .process-step-circle:before {
	animation-name: processShapesScaleOne;
	animation-delay: 300ms;
}

.process.process-shapes.process-shapes-always-animate .process-step .process-step-circle:after {
	animation-name: processShapesScaleTwo;
}

.process.process-shapes.process-shapes-always-animate .process-step .process-step-circle:before, .process.process-shapes.process-shapes-always-animate .process-step .process-step-circle:after {
	animation-fill-mode: forwards;
	animation-iteration-count: infinite;
	animation-duration: 3s;
	animation-timing-function: linear;
}

.process.process-shapes.process-shapes-always-animate .process-step .process-step-circle.process-shapes-always-animate-delay:before {
	animation-name: processShapesScaleOne;
	animation-delay: 1000ms;
}

.process.process-shapes.process-shapes-always-animate .process-step .process-step-circle.process-shapes-always-animate-delay:after {
	animation-name: processShapesScaleTwo;
	animation-delay: 700ms;
}

@keyframes processShapesScaleOne {
	0% {
		opacity: 0.6;
		transform: translate3d(-50%, -50%, 0) scale(1.5);
	}

	25% {
		opacity: 0;
		transform: translate3d(-50%, -50%, 0) scale(1.8);
	}

	50% {
		opacity: 0;
		transform: translate3d(-50%, -50%, 0) scale(1);
	}

	100% {
		opacity: 0.6;
		transform: translate3d(-50%, -50%, 0) scale(1.5);
	}
}

@keyframes processShapesScaleTwo {
	0% {
		opacity: 0.3;
		transform: translate3d(-50%, -50%, 0) scale(2);
	}

	25% {
		opacity: 0;
		transform: translate3d(-50%, -50%, 0) scale(2.3);
	}

	50% {
		opacity: 0;
		transform: translate3d(-50%, -50%, 0) scale(1);
	}

	100% {
		opacity: 0.3;
		transform: translate3d(-50%, -50%, 0) scale(2);
	}
}

@media (min-width: 992px) {
	.process .process-step:before {
		content: '';
		height: 2px;
		display: block;
		background-color: #EDEDED;
		position: absolute;
		width: 50%;
		top: 45px;
		left: 75%;
	}

	.process .process-step:last-child:before {
		display: none;
	}

	.process.process-shapes .process-step:before {
		top: 23px;
	}
}

.process-vertical {
	text-align: left;
}

.process-vertical .process-step {
	width: 100%;
	flex-direction: row;
	justify-content: start;
	align-items: start;
	position: relative;
	padding-bottom: 60px;
}

.process-vertical .process-step:before {
	display: none;
}

.process-vertical .process-step:after {
	width: 2px;
	display: block;
	background-color: #EDEDED;
	position: absolute;
	content: '';
	height: calc(100% - 80px);
	top: 70px;
	left: 30px;
}

.process-vertical .process-step:last-child:after {
	display: none;
}

.process-vertical .process-step-circle {
	height: 60px;
	width: 60px;
	margin: 0 15px 0 0;
	min-height: 60px;
	min-width: 60px;
}

.process-vertical .process-step-content {
	padding-top: 0;
}

.process-connecting-line {
	position: relative;
	text-align: left;
}

.process-connecting-line .connecting-line {
	content: '';
	position: absolute;
	top: 25px;
	left: 15px;
	right: 15px;
	height: 3px;
	background: var(--grey-500);
}

.process-connecting-line .process-step {
	align-items: flex-start;
	padding-right: 48px;
	padding-right: 3rem;
	padding-left: 48px;
	padding-left: 3rem;
}

.process-connecting-line .process-step:before {
	content: none;
}

.process-connecting-line .process-step-circle {
	background: var(--light);
	width: 50px;
	height: 50px;
	border: 3px solid var(--grey-500);
}

/* Process - Skin */
.process .process-step-circle {
	border-color: var(--primary);
}

.process .process-step:hover .process-step-circle {
	background-color: var(--primary);
}

.process .process-step:hover .process-step-circle .process-step-circle-content {
	color: var(--primary-inverse) !important;
}

.process.process-shapes .process-step-circle .process-step-circle-content, .process.process-shapes .process-step-circle:before, .process.process-shapes .process-step-circle:after {
	background-color: var(--primary);
}

.process-connecting-line .connecting-line {
	background: var(--primary);
}

/* Scroll to Top */
html .scroll-to-top {
	transition: opacity 0.3s;
	background: #404040;
	border-radius: var(--border-radius) var(--border-radius) 0 0;
	bottom: 0;
	color: var(--light);
	display: block;
	height: 9px;
	opacity: 0;
	padding: 10px 10px 35px;
	position: fixed;
	right: 10px;
	text-align: center;
	text-decoration: none;
	min-width: 50px;
	z-index: 1040;
	font-size: 0.8em;
}

html .scroll-to-top:hover {
	opacity: 1;
}

html .scroll-to-top.visible {
	opacity: 0.75;
}

html .scroll-to-top span {
	display: inline-block;
	padding: 0 5px;
}

html.lightbox-opened .scroll-to-top {
	right: 27px;
}

@media (max-width: 991px) {
	html .scroll-to-top.hidden-mobile {
		display: none !important;
	}
}

/* Scrollable */
.scrollable {
	overflow: hidden;
	position: relative;
	width: 100%;
}

.scrollable .scrollable-content {
	bottom: 0;
	left: 0;
	overflow: hidden;
	position: absolute;
	right: 0;
	top: 0;
	padding: 0 37px 0 0;
	overflow-x: hidden;
	overflow-y: scroll;
	outline: none;
}

.scrollable .scrollable-content::-webkit-scrollbar {
	visibility: hidden;
}

.scrollable .scrollable-pane {
	bottom: 0;
	opacity: 0.01;
	position: absolute;
	right: 5px;
	top: 0;
	transition: all 0.2s ease 0s;
	width: 4px;
}

.scrollable .scrollable-slider {
	border-radius: 5px;
	background: none repeat scroll 0 0 var(--grey-500);
	margin: 0;
	position: relative;
	transition: opacity 0.2s ease 0s;
	opacity: 0;
}

.scrollable.scrollable-padding .scrollable-content {
	padding: 10px 24px 10px 10px;
}

.scrollable:hover .scrollable-slider, .scrollable.visible-slider .scrollable-slider {
	opacity: 1;
}

/* Section Featured */
section.section {
	background: var(--grey-100);
	border-top: 5px solid var(--grey-100);
	margin: 30px 0;
	padding: 50px 0;
}

section.section > .container {
	position: relative;
}

section.section.section-height-1 {
	padding: 12.307px 0;
	padding: 0.7692307692rem 0;
}

section.section.section-height-2 {
	padding: 36.923px 0;
	padding: 2.3076923077rem 0;
}

section.section.section-height-3 {
	padding: 73.846px 0;
	padding: 4.6153846154rem 0;
}

section.section.section-height-4 {
	padding: 110.769px 0;
	padding: 6.9230769231rem 0;
}

section.section.section-height-5 {
	padding: 147.692px 0;
	padding: 9.2307692308rem 0;
}

section.section.section-no-background {
	background: transparent;
	border-top-color: transparent;
}

section.section.section-center {
	text-align: center;
}

section.section.section-text-light h1, section.section.section-text-light h2, section.section.section-text-light h3, section.section.section-text-light h4, section.section.section-text-light h5, section.section.section-text-light h6 {
	color: var(--light);
}

section.section.section-text-light p {
	color: #e6e5e5;
}

section.section.section-background {
	background-repeat: repeat;
	border: 0;
}

section.section.section-background-change-anim {
	animation-name: colorTransition;
	animation-duration: 25s;
	animation-direction: alternate;
	animation-iteration-count: infinite;
	animation-timing-function: linear;
}

section.section.section-funnel {
	position: relative;
}

section.section.section-funnel .section-funnel-layer-top {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	padding: 96px 0;
	padding: 6rem 0;
	transform: translate3d(0, -50%, 0);
}

section.section.section-funnel .section-funnel-layer-bottom {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 96px 0;
	padding: 6rem 0;
	transform: translate3d(0, 50%, 0);
}

section.section.section-funnel .section-funnel-layer {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: var(--light);
}

section.section.section-funnel .section-funnel-layer:nth-child(1) {
	transform: skewY(-4.7deg);
}

section.section.section-funnel .section-funnel-layer:nth-child(2) {
	transform: skewY(4.7deg);
}

section.section.section-angled {
	position: relative;
	margin: 0;
	overflow: hidden;
}

section.section.section-angled .section-angled-layer-top {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	padding: 48px 0;
	padding: 3rem 0;
	transform: skewY(-0.7deg) translate3d(0, -50%, 0);
}

section.section.section-angled .section-angled-layer-bottom {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 48px 0;
	padding: 3rem 0;
	transform: skewY(0.7deg) translate3d(0, 50%, 0);
}

section.section.section-angled .section-angled-layer-top + .section-angled-content,
section.section.section-angled .section-angled-layer-top + .section-angled-layer-bottom + .section-angled-content {
	margin-top: 40px;
	margin-top: 2.5rem;
}

section.section.section-angled .section-angled-layer-bottom + .section-angled-content,
section.section.section-angled .section-angled-layer-bottom + .section-angled-layer-top + .section-angled-content {
	margin-bottom: 40px;
	margin-bottom: 2.5rem;
}

section.section.section-angled .section-angled-layer-increase-angle.section-angled-layer-top {
	transform: skewY(-1.7deg) translate3d(0, -50%, 0);
}

section.section.section-angled .section-angled-layer-increase-angle.section-angled-layer-bottom {
	transform: skewY(1.7deg) translate3d(0, 50%, 0);
}

section.section.section-angled .section-angled-layer-increase-angle-2.section-angled-layer-top {
	padding: 112px 0;
	padding: 7rem 0;
	transform: skewY(-4.7deg) translate3d(0, -50%, 0);
}

section.section.section-angled .section-angled-layer-increase-angle-2.section-angled-layer-bottom {
	padding: 112px 0;
	padding: 7rem 0;
	transform: skewY(4.7deg) translate3d(0, 50%, 0);
}

section.section.section-angled.section-angled-reverse .section-angled-layer-top {
	transform: skewY(0.7deg) translate3d(0, -50%, 0);
}

section.section.section-angled.section-angled-reverse .section-angled-layer-bottom {
	transform: skewY(-0.7deg) translate3d(0, 50%, 0);
}

section.section.section-angled.section-angled-reverse .section-angled-layer-increase-angle.section-angled-layer-top {
	transform: skewY(1.7deg) translate3d(0, -50%, 0);
}

section.section.section-angled.section-angled-reverse .section-angled-layer-increase-angle.section-angled-layer-bottom {
	transform: skewY(-1.7deg) translate3d(0, 50%, 0);
}

section.section.section-angled.section-angled-reverse .section-angled-layer-increase-angle-2.section-angled-layer-top {
	padding: 112px 0;
	padding: 7rem 0;
	transform: skewY(4.7deg) translate3d(0, -50%, 0);
}

section.section.section-angled.section-angled-reverse .section-angled-layer-increase-angle-2.section-angled-layer-bottom {
	padding: 112px 0;
	padding: 7rem 0;
	transform: skewY(-4.7deg) translate3d(0, 50%, 0);
}

section.section.section-angled + .section-angled {
	padding-top: 0;
}

section.section.overlay .section-angled-layer-top,
section.section.overlay .section-angled-layer-bottom {
	z-index: 99 !important;
}

section.section.section-overlay {
	position: relative;
}

section.section.section-overlay:before {
	content: "";
	display: block;
	background: var(--dark-rgba-20);
	bottom: 0;
	height: 100%;
	left: 0;
	position: absolute;
	right: 0;
	top: 0;
	width: 100%;
}

section.section.section-overlay .section-angled-layer-top,
section.section.section-overlay .section-angled-layer-bottom {
	z-index: 99 !important;
}

section.section.section-overlay-dot:before {
	background: url(../img/overlay-dot.png) repeat scroll 0 0 transparent;
}

section.section.section-overlay-opacity {
	position: relative;
}

section.section.section-overlay-opacity:before {
	content: "";
	display: block;
	background: rgba(0, 0, 0, 0.5);
	bottom: 0;
	height: 100%;
	left: 0;
	position: absolute;
	right: 0;
	top: 0;
	width: 100%;
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-1:before {
	background: var(--dark-rgba-10);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-2:before {
	background: var(--dark-rgba-20);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-3:before {
	background: var(--dark-rgba-30);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-4:before {
	background: var(--dark-rgba-40);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-5:before {
	background: var(--dark-rgba-50);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-6:before {
	background: var(--dark-rgba-60);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-7:before {
	background: var(--dark-rgba-70);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-8:before {
	background: var(--dark-rgba-80);
}

section.section.section-overlay-opacity.section-overlay-opacity-scale-9:before {
	background: var(--dark-rgba-90);
}

section.section.section-overlay-opacity-light:before {
	background: var(--light-rgba-50);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-1:before {
	background: var(--light-rgba-10);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-2:before {
	background: var(--light-rgba-20);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-3:before {
	background: var(--light-rgba-30);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-4:before {
	background: var(--light-rgba-40);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-5:before {
	background: var(--light-rgba-50);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-6:before {
	background: var(--light-rgba-60);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-7:before {
	background: var(--light-rgba-70);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-8:before {
	background: var(--light-rgba-80);
}

section.section.section-overlay-opacity-light.section-overlay-opacity-light-scale-9:before {
	background: var(--light-rgba-90);
}

section.section.section-overlay-opacity-gradient:before {
	opacity: 0.85;
}

section.section.section-video {
	background: none !important;
	border: 0;
}

section.section.section-parallax {
	background-color: transparent;
	background-position: 50% 50%;
	background-repeat: repeat;
	background-attachment: fixed;
	border: 0;
}

section.section.section-parallax.parallax-disabled {
	background-attachment: scroll !important;
	background-position: 50% 50% !important;
}

section.section.section-no-border {
	border: 0;
}

section.section.section-with-divider {
	margin: 56px 0 35px;
}

section.section.section-with-divider .divider {
	margin: -56px 0 44px;
}

section.section.section-footer {
	margin-bottom: -50px;
}

section.section.section-with-mockup {
	margin-top: 120px;
}

section.section.section-front {
	position: relative;
	z-index: 1;
}

@media (max-width: 991px) {
	.container-fluid > .row > .p-0 section.section {
		margin-bottom: 0;
		margin-top: 0;
	}

	.container-fluid > .row > .p-0:first-child section.section {
		margin-bottom: 0;
		margin-top: 30px;
	}

	.container-fluid > .row > .p-0:last-child section.section {
		margin-bottom: 30px;
		margin-top: 0;
	}
}

/* Section With Shape Divider */
@keyframes shapeDividerToLeft {
	from {
		transform: translate3d(0, 0, 0);
	}

	to {
		transform: translate3d(-200%, 0, 0);
	}
}

@keyframes shapeDividerToRight {
	from {
		transform: translate3d(0, 0, 0);
	}

	to {
		transform: translate3d(200%, 0, 0);
	}
}

.section.section-with-shape-divider {
	position: relative;
	overflow: hidden;
}

.section.section-with-shape-divider .shape-divider {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 80px;
	z-index: 1;
}

.section.section-with-shape-divider .shape-divider svg {
	position: absolute;
	top: 0;
	left: 50%;
	height: 100%;
	transform: translate3d(-50%, 0, 0);
}

@media (min-width: 1950px) {
	.section.section-with-shape-divider .shape-divider svg {
		width: 100%;
	}
}

.section.section-with-shape-divider .shape-divider.shape-divider-bottom {
	top: auto;
	bottom: 0;
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-y {
	transform: rotateY(180deg);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-x {
	transform: rotateX(180deg);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-xy {
	transform: rotateY(180deg) rotateX(180deg);
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation {
	position: absolute;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100%;
	animation-duration: 45s;
	animation-iteration-count: infinite;
	animation-timing-function: linear;
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation > svg {
	width: 100vw;
	left: 0;
	transform: none;
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right.start {
	animation-name: shapeDividerToRight;
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right > svg:nth-child(2) {
	transform: rotateY(180deg) translate3d(99.9%, 0, 0);
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right > svg:nth-child(3) {
	transform: translate3d(-199.8%, 0, 0);
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left.start {
	animation-name: shapeDividerToLeft;
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(2) {
	transform: rotateY(180deg) translate3d(-99.9%, 0, 0);
}

.section.section-with-shape-divider .shape-divider .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(3) {
	transform: translate3d(199.8%, 0, 0);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-y .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right.start, .section.section-with-shape-divider .shape-divider.shape-divider-reverse-xy .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right.start {
	animation-name: shapeDividerToLeft;
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-y .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right > svg:nth-child(2), .section.section-with-shape-divider .shape-divider.shape-divider-reverse-xy .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right > svg:nth-child(2) {
	transform: rotateY(180deg) translate3d(-99.9%, 0, 0);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-y .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right > svg:nth-child(3), .section.section-with-shape-divider .shape-divider.shape-divider-reverse-xy .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right > svg:nth-child(3) {
	transform: translate3d(199.8%, 0, 0);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-y .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left.start, .section.section-with-shape-divider .shape-divider.shape-divider-reverse-xy .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left.start {
	animation-name: shapeDividerToRight;
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-y .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(2), .section.section-with-shape-divider .shape-divider.shape-divider-reverse-xy .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(2) {
	transform: rotateY(180deg) translate3d(99.9%, 0, 0);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-y .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(3), .section.section-with-shape-divider .shape-divider.shape-divider-reverse-xy .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(3) {
	transform: translate3d(-199.8%, 0, 0);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-x .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-right.start {
	animation-name: shapeDividerToRight;
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-x .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left.start {
	animation-name: shapeDividerToLeft;
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-x .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(2) {
	transform: rotateY(180deg) translate3d(-99.9%, 0, 0);
}

.section.section-with-shape-divider .shape-divider.shape-divider-reverse-x .shape-divider-horizontal-animation.shape-divider-horizontal-animation-to-left > svg:nth-child(3) {
	transform: translate3d(199.8%, 0, 0);
}

.section.section-with-shape-divider.overlay:before {
	z-index: 1;
}

/* Sections - Skin */
section.section-primary {
	background-color: var(--primary) !important;
	border-color: var(--primary-100) !important;
}

section.section-primary h1, section.section-primary h2, section.section-primary h3, section.section-primary h4, section.section-primary h5, section.section-primary h6 {
	color: var(--primary-inverse);
}

section.section-primary p {
	color: var(--primary-inverse);
}

section.section-primary-scale-2 {
	background-color: var(--primary-300) !important;
	border-color: var(--primary-300) !important;
}

section.section-primary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: var(--primary-300);
}

section.section-secondary {
	background-color: var(--secondary) !important;
	border-color: var(--secondary-100) !important;
}

section.section-secondary h1, section.section-secondary h2, section.section-secondary h3, section.section-secondary h4, section.section-secondary h5, section.section-secondary h6 {
	color: var(--secondary-inverse);
}

section.section-secondary p {
	color: var(--secondary-inverse);
}

section.section-secondary-scale-2 {
	background-color: var(--secondary-300) !important;
	border-color: var(--secondary-300) !important;
}

section.section-secondary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: var(--secondary-300);
}

section.section-tertiary {
	background-color: var(--tertiary) !important;
	border-color: var(--tertiary-100) !important;
}

section.section-tertiary h1, section.section-tertiary h2, section.section-tertiary h3, section.section-tertiary h4, section.section-tertiary h5, section.section-tertiary h6 {
	color: var(--tertiary-inverse);
}

section.section-tertiary p {
	color: var(--tertiary-inverse);
}

section.section-tertiary-scale-2 {
	background-color: var(--tertiary-300) !important;
	border-color: var(--tertiary-300) !important;
}

section.section-tertiary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: var(--tertiary-300);
}

section.section-quaternary {
	background-color: var(--quaternary) !important;
	border-color: var(--quaternary-100) !important;
}

section.section-quaternary h1, section.section-quaternary h2, section.section-quaternary h3, section.section-quaternary h4, section.section-quaternary h5, section.section-quaternary h6 {
	color: var(--quaternary-inverse);
}

section.section-quaternary p {
	color: var(--quaternary-inverse);
}

section.section-quaternary-scale-2 {
	background-color: var(--quaternary-300) !important;
	border-color: var(--quaternary-300) !important;
}

section.section-quaternary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: var(--quaternary-300);
}

section.section-dark {
	background-color: var(--dark) !important;
	border-color: var(--dark-100) !important;
}

section.section-dark h1, section.section-dark h2, section.section-dark h3, section.section-dark h4, section.section-dark h5, section.section-dark h6 {
	color: var(--dark-inverse);
}

section.section-dark p {
	color: var(--dark-inverse);
}

section.section-dark-scale-2 {
	background-color: var(--dark-300) !important;
	border-color: var(--dark-300) !important;
}

section.section-dark-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: var(--dark-300);
}

section.section-light {
	background-color: var(--light) !important;
	border-color: var(--light-100) !important;
}

section.section-light h1, section.section-light h2, section.section-light h3, section.section-light h4, section.section-light h5, section.section-light h6 {
	color: var(--light-inverse);
}

section.section-light p {
	color: var(--light-inverse);
}

section.section-light-scale-2 {
	background-color: var(--light-300) !important;
	border-color: var(--light-300) !important;
}

section.section-light-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: var(--light-300);
}

section.section.section-background-half-primary-half-secondary {
	background: linear-gradient(to right, var(--primary) 50%, var(--secondary) 50%);
}

section.section.section-overlay-opacity-gradient:before {
	background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%);
}

/* Side Panel */
html.side-panel .body:before {
	content: '';
	display: block;
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: var(--dark-rgba-50);
	opacity: 0;
	visibility: hidden;
	z-index: 9998;
	transition: ease opacity 300ms;
}

html.side-panel .side-panel-wrapper {
	position: fixed;
	top: 0;
	left: 0;
	width: 300px;
	height: 100vh;
	background: var(--light);
	padding: 32px;
	padding: 2rem;
	box-shadow: 0px 0px 30px -13px rgba(0, 0, 0, 0.2);
	overflow-y: auto;
	transform: translate3d(-100%, 0, 0);
	transition: ease transform 300ms;
	z-index: 9999;
}

html.side-panel .side-panel-wrapper .side-panel-close {
	position: absolute;
	top: 0;
	right: 0;
}

html.side-panel.side-panel-push .body {
	transform: translate3d(0, 0, 0);
	transition: ease transform 300ms;
}

html.side-panel.side-panel-right .side-panel-wrapper {
	left: auto;
	right: 0;
	transform: translate3d(100%, 0, 0);
}

html.side-panel.side-panel-without-overlay .body:before {
	content: none;
}

html.side-panel.side-panel-open .body:before {
	opacity: 1;
	visibility: visible;
}

html.side-panel.side-panel-open .side-panel-wrapper {
	transform: translate3d(0, 0, 0);
}

html.side-panel.side-panel-open.side-panel-push .body {
	transform: translate3d(300px, 0, 0);
}

html.side-panel.side-panel-open.side-panel-push.side-panel-right .body {
	transform: translate3d(-300px, 0, 0);
}

/* Home Slider - Revolution Slider */
.slider-container {
	background: #151719;
	height: 500px;
	overflow: hidden;
	width: 100%;
	direction: ltr;
}

.slider-container .tparrows {
	border-radius: var(--border-radius);
}

.slider-container .slider-single-slide .tparrows {
	display: none;
}

.slider-container .top-label {
	color: var(--light);
	font-size: 24px;
	font-weight: 300;
}

.slider-container .main-label {
	color: var(--light);
	font-size: 62px;
	line-height: 62px;
	font-weight: 800;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

.slider-container .featured-label {
	color: var(--light);
	font-size: 52px;
	line-height: 52px;
	font-weight: 800;
	text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.15);
}

.slider-container .bottom-label {
	color: var(--light);
	font-size: 20px;
	line-height: 20px;
	font-weight: 300;
}

.slider-container .blackboard-text {
	color: #BAB9BE;
	font-size: 46px;
	line-height: 46px;
	font-family: "Shadows Into Light", cursive;
	text-transform: uppercase;
}

.slider-container .btn.btn-lg.btn-slider-action {
	font-size: 22px;
	font-weight: 600;
	line-height: 20px;
	padding: 19.2px 28.8px;
	padding: 1.2rem 1.8rem;
}

.slider-container .top-label-dark, .slider-container .main-label-dark, .slider-container .bottom-label-dark {
	color: var(--dark--200);
}

.slider-container .tp-caption-dark a {
	color: var(--dark--200);
}

.slider-container .tp-caption-dark a:hover {
	color: var(--dark--200);
}

.slider-container .tp-caption-overlay {
	background: #000;
	padding: 10px;
}

.slider-container .tp-caption-overlay-opacity {
	background: rgba(0, 0, 0, 0.4);
	padding: 10px;
}

.slider-container .tp-opacity-overlay {
	background: #000;
	opacity: 0.75;
}

.slider-container .tp-opacity-overlay-light {
	background: var(--light);
	opacity: 0.75;
}

.slider-container.transparent {
	background-color: transparent;
}

.slider-container.light {
	background-color: #EAEAEA;
}

.slider-container.light .tp-bannershadow {
	opacity: 0.05;
	top: 0;
	bottom: auto;
	z-index: 1000;
}

.slider-container.light .featured-label, .slider-container.light .top-label, .slider-container.light .main-label, .slider-container.light .bottom-label {
	color: var(--dark--200);
}

.slider-container.light .tp-caption a {
	color: var(--dark--200);
}

.slider-container.light .tp-caption a:hover {
	color: var(--dark--200);
}

/* Revolution Slider Typewriter Addon */
.tp-caption[data-typewriter]:after {
	transform: translateX(-9px);
}

/* Slider With Overlay */
.rev_slider li.slide-overlay .slotholder:after {
	width: 100%;
	height: 100%;
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	pointer-events: none;
	opacity: 0.9;
	background: var(--dark);
	z-index: 2;
}

.rev_slider li.slide-overlay.slide-overlay-light .slotholder:after {
	background: var(--light);
}

.rev_slider li.slide-overlay.slide-overlay-gradient .slotholder:after {
	background-color: #6c6ef1;
	background-image: linear-gradient(to right, #6c6ef1 0%, #e2498e 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6c6ef1', endColorstr='#e2498e', GradientType=1 );
}

.rev_slider li.slide-overlay.slide-overlay-gradient.slide-overlay-gradient-animated .slotholder:after {
	background-size: 200% 200%;
	animation-name: gradientTransition;
	animation-duration: 20000ms;
	animation-iteration-count: infinite;
}

.rev_slider li.slide-overlay.slide-overlay-level-0 .slotholder:after {
	opacity: 0.0;
}

.rev_slider li.slide-overlay.slide-overlay-level-1 .slotholder:after {
	opacity: 0.1;
}

.rev_slider li.slide-overlay.slide-overlay-level-2 .slotholder:after {
	opacity: 0.2;
}

.rev_slider li.slide-overlay.slide-overlay-level-3 .slotholder:after {
	opacity: 0.3;
}

.rev_slider li.slide-overlay.slide-overlay-level-4 .slotholder:after {
	opacity: 0.4;
}

.rev_slider li.slide-overlay.slide-overlay-level-5 .slotholder:after {
	opacity: 0.5;
}

.rev_slider li.slide-overlay.slide-overlay-level-6 .slotholder:after {
	opacity: 0.6;
}

.rev_slider li.slide-overlay.slide-overlay-level-7 .slotholder:after {
	opacity: 0.7;
}

.rev_slider li.slide-overlay.slide-overlay-level-8 .slotholder:after {
	opacity: 0.8;
}

.rev_slider li.slide-overlay.slide-overlay-level-9 .slotholder:after {
	opacity: 0.9;
}

.rev_slider li.slide-overlay.slide-overlay-level-10 .slotholder:after {
	opacity: 0.10;
}

/* Slider Scroll Button */
.slider-scroll-button {
	position: relative;
	width: 57px;
	height: 57px;
	border-radius: 100%;
	text-indent: -9999px;
}

.slider-scroll-button:before {
	content: '';
	position: absolute;
	top: 50%;
	left: 49%;
	width: 23px;
	height: 34px;
	border: 1px solid #FFF;
	border-radius: 15px;
	transform: translate3d(-50%, -50%, 0);
}

.slider-scroll-button:after {
	content: '';
	width: 6px;
	height: 10px;
	border-radius: 10px;
	border: 1px solid #FFF;
	position: absolute;
	top: 19px;
	left: 49%;
	transform: translate3d(-50%, 0, 0);
	animation-name: sliderScrollButton;
	animation-duration: 2s;
	animation-iteration-count: infinite;
}

.slider-scroll-button.slider-scroll-button-dark:before {
	border-color: var(--dark);
}

.slider-scroll-button.slider-scroll-button-dark:after {
	border-color: var(--dark);
}

/* Arrows */
.tparrows.tparrows-carousel {
	display: inline-block;
	position: absolute;
	width: 30px;
	height: auto;
	padding: 20px 0;
	background: var(--primary);
}

.tparrows.tparrows-carousel.tp-leftarrow {
	right: auto !important;
	left: -30px !important;
	border-radius: 0 4px 4px 0;
}

.tparrows.tparrows-carousel.tp-leftarrow:before {
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	content: "\f053";
	position: relative;
	left: -1px;
	top: 1px;
}

.tparrows.tparrows-carousel.tp-rightarrow {
	right: -60px !important;
	left: auto !important;
	border-radius: 4px 0 0 4px;
}

.tparrows.tparrows-carousel.tp-rightarrow:before {
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	content: "\f054";
	position: relative;
	left: 1px;
	top: 1px;
}

.tparrows.tparrows-carousel:hover {
	background: var(--primary--300);
}

.tparrows.arrows-style-1 {
	background: transparent !important;
}

.tparrows.arrows-style-2 {
	background: transparent !important;
}

.tparrows.arrows-style-2.tp-leftarrow:before, .tparrows.arrows-style-2.tp-rightarrow:before {
	content: '';
	display: block;
	position: absolute;
	left: 1.5px;
	top: 50%;
	width: 0.8em;
	height: 0.8em;
	border-top: 1px solid #FFF;
	border-left: 1px solid #FFF;
	transition: ease opacity 400ms;
	font-size: inherit;
	opacity: 0.7;
}

.tparrows.arrows-style-2.tp-leftarrow:after, .tparrows.arrows-style-2.tp-rightarrow:after {
	content: '';
	display: block;
	border-top: 1px solid #FFF;
	transition: ease opacity 400ms;
	width: 1.5em;
	position: absolute;
	top: 50%;
	font-size: inherit;
	transform: translate3d(0, -50%, 0);
	opacity: 0.7;
}

.tparrows.arrows-style-2.tp-leftarrow:hover:before, .tparrows.arrows-style-2.tp-rightarrow:hover:before {
	opacity: 1;
}

.tparrows.arrows-style-2.tp-leftarrow:hover:after, .tparrows.arrows-style-2.tp-rightarrow:hover:after {
	opacity: 1;
}

.tparrows.arrows-style-2.tp-leftarrow:before {
	left: 0.5px;
	transform: translate3d(0, -50%, 0) rotate(-45deg);
}

.tparrows.arrows-style-2.tp-rightarrow:before {
	left: 9.5px;
	transform: translate3d(0, -50%, 0) rotate(135deg);
}

@media (max-width: 768px) {
	.tparrows.arrows-style-2 {
		display: none;
	}
}

.tparrows.arrows-dark:before {
	color: #111111 !important;
}

.tparrows.arrows-big:before {
	font-size: 25px;
}

.tparrows.arrows-rounded {
	border-radius: 100%;
}

/* Bullets */
.tp-bullets.bullets-style-1 .tp-bullet {
	width: 8px;
	height: 8px;
	border-radius: 100%;
}

.tp-bullets.bullets-style-2 .tp-bullet {
	width: 8px;
	height: 8px;
	border-radius: 100%;
	background: #78888D;
}

/* Embed Border */
.rev_slider embed,
.rev_slider iframe,
.rev_slider object,
.rev_slider video {
	border: 0 !important;
}

/* Defaults */
.hesperiden-custom {
	padding: 10px 0 !important;
}

.hesperiden-custom .tp-tab {
	border: 0 !important;
}

/* Slider With Overlay */
.slider-with-overlay {
	position: relative;
}

.slider-with-overlay .slider-container {
	z-index: auto;
}

/* Slider Contact Form */
.slider-contact-form {
	margin: 45px 0;
}

.slider-contact-form .featured-box {
	text-align: left;
}

.slider-contact-form label.error {
	display: none !important;
}

.slider-contact-form .alert {
	padding: 6px;
	text-align: center;
}

.slider-contact-form textarea {
	resize: none;
}

@media (min-width: 992px) {
	.slider-contact-form {
		margin: 0;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		z-index: 99;
		width: 100%;
	}
}

/* Boxed */
html.boxed .slider-container {
	width: 100% !important;
	left: auto !important;
}

/* Side Header - Full Screen Slider Height On Mobile */
@media (max-width: 991px) {
	html.side-header .slider-container.fullscreen-slider,
		html.side-header .slider-container.fullscreen-slider + .tp-fullwidth-forcer {
		height: calc(100vh - 84px) !important;
	}
}

/* Slider - Skin */
.slide-overlay-primary .slotholder:after {
	background-color: var(--primary) !important;
}

.slider-container .tp-caption-overlay-primary {
	background: var(--primary);
}

.slider-container .tp-caption-overlay-opacity-primary {
	background: var(--primary-rgba-35);
}

.tp-bullets.bullets-color-primary .tp-bullet:hover, .tp-bullets.bullets-color-primary .tp-bullet.selected {
	background: var(--primary);
}

.slide-overlay-secondary .slotholder:after {
	background-color: var(--secondary) !important;
}

.slider-container .tp-caption-overlay-secondary {
	background: var(--secondary);
}

.slider-container .tp-caption-overlay-opacity-secondary {
	background: var(--secondary-rgba-35);
}

.tp-bullets.bullets-color-secondary .tp-bullet:hover, .tp-bullets.bullets-color-secondary .tp-bullet.selected {
	background: var(--secondary);
}

.slide-overlay-tertiary .slotholder:after {
	background-color: var(--tertiary) !important;
}

.slider-container .tp-caption-overlay-tertiary {
	background: var(--tertiary);
}

.slider-container .tp-caption-overlay-opacity-tertiary {
	background: var(--tertiary-rgba-35);
}

.tp-bullets.bullets-color-tertiary .tp-bullet:hover, .tp-bullets.bullets-color-tertiary .tp-bullet.selected {
	background: var(--tertiary);
}

.slide-overlay-quaternary .slotholder:after {
	background-color: var(--quaternary) !important;
}

.slider-container .tp-caption-overlay-quaternary {
	background: var(--quaternary);
}

.slider-container .tp-caption-overlay-opacity-quaternary {
	background: var(--quaternary-rgba-35);
}

.tp-bullets.bullets-color-quaternary .tp-bullet:hover, .tp-bullets.bullets-color-quaternary .tp-bullet.selected {
	background: var(--quaternary);
}

.slide-overlay-dark .slotholder:after {
	background-color: var(--dark) !important;
}

.slider-container .tp-caption-overlay-dark {
	background: var(--dark);
}

.slider-container .tp-caption-overlay-opacity-dark {
	background: var(--dark-rgba-35);
}

.tp-bullets.bullets-color-dark .tp-bullet:hover, .tp-bullets.bullets-color-dark .tp-bullet.selected {
	background: var(--dark);
}

.slide-overlay-light .slotholder:after {
	background-color: var(--light) !important;
}

.slider-container .tp-caption-overlay-light {
	background: var(--light);
}

.slider-container .tp-caption-overlay-opacity-light {
	background: var(--light-rgba-35);
}

.tp-bullets.bullets-color-light .tp-bullet:hover, .tp-bullets.bullets-color-light .tp-bullet.selected {
	background: var(--light);
}

.slide-overlay.slide-overlay-gradient .slotholder:after {
	background-color: var(--tertiary) !important;
	background-image: linear-gradient(to right, var(--tertiary) 0%, var(--quaternary) 100%) !important;
}

/* Social Icons */
.social-icons {
	margin: 0;
	padding: 0;
	width: auto;
}

.social-icons li {
	display: inline-block;
	margin: -1px 1px 0 0;
	padding: 0;
	border-radius: 100%;
	overflow: visible;
}

.social-icons li a {
	transition: all 0.2s ease;
	border-radius: 100%;
	display: block;
	height: 28px;
	line-height: 28px;
	width: 28px;
	text-align: center;
	color: var(--dark--200) !important;
	text-decoration: none;
	font-size: 12.8px;
	font-size: 0.8rem;
}

.social-icons li:hover a {
	background: #151719;
	color: var(--light) !important;
}

.social-icons li:hover.social-icons-x a {
	background: #1aa9e1 !important;
}

.social-icons li:hover.social-icons-facebook a {
	background: #3b5a9a !important;
}

.social-icons li:hover.social-icons-linkedin a {
	background: #0073b2 !important;
}

.social-icons li:hover.social-icons-rss a {
	background: #ff8201 !important;
}

.social-icons li:hover.social-icons-googleplus a {
	background: #dd4b39 !important;
}

.social-icons li:hover.social-icons-pinterest a {
	background: #cc2127 !important;
}

.social-icons li:hover.social-icons-youtube a {
	background: #c3191e !important;
}

.social-icons li:hover.social-icons-instagram a {
	background: #7c4a3a !important;
}

.social-icons li:hover.social-icons-skype a {
	background: #00b0f6 !important;
}

.social-icons li:hover.social-icons-email a {
	background: #dd4b39 !important;
}

.social-icons li:hover.social-icons-vk a {
	background: #6383a8 !important;
}

.social-icons li:hover.social-icons-xing a {
	background: #1a7576 !important;
}

.social-icons li:hover.social-icons-tumblr a {
	background: #304e6c !important;
}

.social-icons li:hover.social-icons-reddit a {
	background: #ff4107 !important;
}

.social-icons li:hover.social-icons-whatsapp a {
	background: #25d366 !important;
}

.social-icons.social-icons-dark li a {
	background: #181818;
}

.social-icons.social-icons-dark li a i {
	color: var(--light);
}

.social-icons.social-icons-dark-2 li a {
	background: #272a2e;
}

.social-icons.social-icons-dark-2 li a i {
	color: var(--light);
}

.social-icons.social-icons-transparent li {
	box-shadow: none;
}

.social-icons.social-icons-transparent li a {
	background: transparent;
}

.social-icons.social-icons-icon-gray li a {
	color: var(--default) !important;
}

.social-icons.social-icons-icon-gray li:hover a {
	color: var(--light) !important;
}

.social-icons.social-icons-icon-light li a i {
	color: var(--light);
}

.social-icons.social-icons-icon-light.social-icons-clean li a i {
	color: var(--light) !important;
}

.social-icons.social-icons-medium li a, .social-icons.social-icons-md li a {
	height: 33px;
	line-height: 33px;
	width: 33px;
	font-size: 14.4px;
	font-size: 0.9rem;
}

.social-icons.social-icons-big li a, .social-icons.social-icons-lg li a {
	height: 48px;
	line-height: 50px;
	width: 48px;
}

@media (max-width: 400px) {
	.social-icons.social-icons-big li a, .social-icons.social-icons-lg li a {
		height: 38px;
		line-height: 40px;
		width: 38px;
	}
}

.social-icons.social-icons-opacity-light li a {
	background: rgba(255, 255, 255, 0.04);
}

.social-icons.social-icons-clean-with-border li a {
	background: transparent;
	color: var(--light) !important;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-icons.social-icons-clean-with-border.social-icons-clean-with-border-border-grey li a {
	border: 1px solid #e7e7e7;
}

.social-icons.social-icons-clean-with-border.social-icons-clean-with-border-icon-dark li a {
	color: #212121 !important;
}

.social-icons.social-icons-clean-with-border.social-icons-clean-with-border-icon-dark li:hover a {
	color: var(--light) !important;
}

.social-icons:not(.social-icons-opacity-light):not(.social-icons-transparent):not(.social-icons-clean):not(.social-icons-clean-with-border):not(.social-icons-dark):not(.social-icons-dark-2):not(.custom-social-icons) li {
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.2);
}

.social-icons:not(.social-icons-opacity-light):not(.social-icons-transparent):not(.social-icons-clean):not(.social-icons-clean-with-border):not(.social-icons-dark):not(.social-icons-dark-2):not(.custom-social-icons) li a {
	background: var(--light);
}

.social-icons-dark.social-icons-clean li a,
.social-icons-dark-2.social-icons-clean li a {
	background: transparent;
	color: var(--light) !important;
}

/* Sort Source Wrapper */
.sort-source-wrapper {
	padding: 8px 0;
	position: relative;
}

.sort-source-wrapper .nav {
	margin: -100px 0 5px;
}

.sort-source-wrapper .nav > li > a {
	color: var(--light);
	background: transparent;
}

.sort-source-wrapper .nav > li > a:hover, .sort-source-wrapper .nav > li > a:focus {
	background: transparent;
}

.sort-source-wrapper .nav > li.active > a {
	background: transparent;
}

.sort-source-wrapper .nav > li.active > a:hover, .sort-source-wrapper .nav > li.active > a:focus {
	background: transparent;
}

/* Sort Source */
.sort-source {
	clear: both;
}

.sort-source.d-none {
	display: block !important;
	width: 0;
	height: 0;
	overflow: hidden;
}

.sort-source > li > a {
	padding: 6px 12px;
	text-decoration: none;
}

.sort-source.sort-source-style-2 {
	margin: -10px auto;
}

.sort-source.sort-source-style-2 > li {
	float: none;
	display: inline-block;
}

.sort-source.sort-source-style-2 > li > a, .sort-source.sort-source-style-2 > li > a:focus, .sort-source.sort-source-style-2 > li > a:hover {
	background: transparent;
	color: var(--light);
	display: inline-block;
	border-radius: 0;
	margin: 0;
	padding: 18px 28px;
	position: relative;
	text-decoration: none;
}

.sort-source.sort-source-style-2 > li > a:after {
	transition: margin 0.3s;
	width: 0;
	height: 0;
	border-left: 20px solid transparent;
	border-right: 20px solid transparent;
	border-top: 15px solid var(--grey-500);
	content: " ";
	display: block;
	position: absolute;
	left: 50%;
	margin-left: -20px;
	margin-top: 0;
	opacity: 0;
}

.sort-source.sort-source-style-2 > li.active > a, .sort-source.sort-source-style-2 > li.active > a:focus, .sort-source.sort-source-style-2 > li.active > a:hover {
	background: transparent;
}

.sort-source.sort-source-style-2 > li.active > a:after {
	opacity: 1;
	margin-top: 18px;
}

/* Sort Source Style 2 */
@media (max-width: 991px) {
	.sort-source.sort-source-style-2 > li {
		display: block;
	}

	.sort-source.sort-source-style-2 > li.active > a:after {
		display: none !important;
	}
}

/* Sort Source Style 3 */
.sort-source.sort-source-style-3 > li > a {
	color: var(--default);
}

.sort-source.sort-source-style-3 > li.active > a {
	background: transparent !important;
	border-bottom: 3px solid var(--grey-500);
	border-radius: 0;
}

/* Sort Destination */
.sort-destination {
	overflow: hidden;
}

.sort-destination .isotope-item {
	min-width: 1px;
}

.sort-destination.full-width {
	position: relative;
}

.sort-destination.full-width .isotope-item {
	width: 20%;
	float: left;
}

.sort-destination.full-width .isotope-item.w2 {
	width: 40%;
}

.sort-destination.full-width .isotope-item .img-thumbnail, .sort-destination.full-width .isotope-item a, .sort-destination.full-width .isotope-item .thumb-info img {
	border-radius: 0;
}

.sort-destination.full-width-spaced {
	margin-top: 50px;
	margin-left: 20px;
	margin-right: 20px;
}

.sort-destination.full-width-spaced .portfolio-item {
	margin-left: 20px;
	margin-right: 20px;
}

.sort-destination.sort-destination-items-hardware-acc .isotope-item {
	transform: translate3d(0, 0, 0);
}

html.gecko .sort-destination.sort-destination-margin > [class*="col-"] {
	margin: -1px;
}

.sort-source-wrapper + .sort-destination.full-width {
	margin: 0 0 -81px;
	top: -51px;
}

.sort-source-wrapper + .sort-destination-loader .full-width {
	margin: 0 0 -81px;
	top: -51px;
}

/* Sort Loading */
.sort-destination-loader {
	height: auto;
	overflow: hidden;
	position: relative;
}

.sort-destination-loader:after {
	transition: opacity 0.3s;
	content: ' ';
	background: var(--light);
	bottom: 0;
	left: 0;
	position: absolute;
	right: 0;
	top: 0;
	z-index: 1;
	opacity: 0;
}

.sort-destination-loader .bounce-loader {
	display: none;
}

.sort-destination-loader.sort-destination-loader-showing {
	max-height: 200px;
}

.sort-destination-loader.sort-destination-loader-showing:after {
	opacity: 1;
}

.sort-destination-loader.sort-destination-loader-showing .bounce-loader {
	display: block;
}

.sort-destination-loader.loader-showing-dark:after {
	background: #181818;
}

.sort-destination-loader.sort-destination-loader-loaded {
	overflow: visible;
}

.sort-destination-loader.sort-destination-loader-loaded:after {
	display: none;
}

.sort-destination-loader-full-width {
	overflow: visible;
}

.sort-destination-loader-full-width:after {
	bottom: -81px;
	top: -51px;
}

.sort-destination-loader-full-width.sort-destination-loader-showing {
	overflow: hidden;
	height: 600px;
}

.sort-destination-loader-lg.sort-destination-loader-showing {
	height: 600px;
}

.portfolio-load-more-loader.portfolio-load-more-loader-showing .bounce-loader {
	display: block !important;
}

@media (min-width: 992px) {
	.col-md-6.isotope-item:nth-child(2n+1) {
		clear: both;
	}

	.col-md-4.isotope-item:nth-child(3n+1) {
		clear: both;
	}

	.col-md-3.isotope-item:nth-child(4n+1) {
		clear: both;
	}

	.col-md-1-5.isotope-item:nth-child(5n+1) {
		clear: both;
	}

	.col-md-2.isotope-item:nth-child(6n+1) {
		clear: both;
	}

	.sort-destination.full-width .isotope-item:nth-child(5n+1) {
		clear: both;
	}
}

@media (max-width: 991px) {
	.sort-destination.isotope {
		overflow: visible !important;
		height: auto !important;
	}

	.sort-destination.isotope.full-width {
		overflow: hidden !important;
	}

	.isotope-hidden {
		display: none !important;
	}
}

@media (max-width: 767px) {
	.isotope-item {
		position: static !important;
		text-align: center;
		width: 100%;
		margin-left: auto;
		margin-right: auto;
		transform: none !important;
	}

	.isotope-item.product {
		position: relative !important;
	}

	.sort-source-wrapper {
		background: #151719;
		clear: both;
		margin-top: -35px;
	}

	.sort-source-wrapper .nav {
		margin: 0;
	}

	.sort-destination {
		height: auto !important;
	}

	.sort-destination.full-width,
		.sort-source-wrapper + .sort-destination.full-width {
		max-width: none;
		margin: 0 0 -30px;
		top: 0;
	}

	.sort-destination.full-width .isotope-item,
		.sort-source-wrapper + .sort-destination.full-width .isotope-item {
		max-width: 100%;
	}

	.sort-source-wrapper + .sort-destination-loader .full-width {
		margin: 0 0 -30px;
		top: 0;
	}
}

@media (max-width: 575px) {
	.sort-destination.full-width .isotope-item,
		.sort-source-wrapper + .sort-destination.full-width .isotope-item {
		position: static !important;
		clear: both;
		float: none;
		width: auto;
	}
}

/* Sort Colors */
.sort-source-wrapper .nav > li.active > a {
	color: var(--primary);
}

.sort-source-wrapper .nav > li.active > a:hover, .sort-source-wrapper .nav > li.active > a:focus {
	color: var(--primary);
}

.sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: var(--primary);
}

.sort-source.sort-source-style-3 > li.active > a {
	border-bottom-color: var(--primary) !important;
	color: var(--primary) !important;
}

/* Clip SVG */
.clip-svg {
	width: 0;
	height: 0;
}

/* SVG Colors */
.svg-fill-color-primary {
	fill: var(--primary) !important;
}

.svg-fill-color-primary svg path, .svg-fill-color-primary svg rect, .svg-fill-color-primary svg line, .svg-fill-color-primary svg polyline, .svg-fill-color-primary svg polygon {
	fill: var(--primary) !important;
}

.svg-fill-color-hover-primary:hover {
	fill: var(--primary) !important;
}

.svg-fill-color-hover-primary svg:hover path, .svg-fill-color-hover-primary svg:hover rect, .svg-fill-color-hover-primary svg:hover line, .svg-fill-color-hover-primary svg:hover polyline, .svg-fill-color-hover-primary svg:hover polygon {
	fill: var(--primary) !important;
}

.svg-stroke-color-primary {
	stroke: var(--primary) !important;
}

.svg-stroke-color-primary svg path, .svg-stroke-color-primary svg rect, .svg-stroke-color-primary svg line, .svg-stroke-color-primary svg polyline, .svg-stroke-color-primary svg polygon {
	stroke: var(--primary) !important;
}

.svg-stroke-color-hover-primary:hover {
	stroke: var(--primary) !important;
}

.svg-stroke-color-hover-primary svg:hover path, .svg-stroke-color-hover-primary svg:hover rect, .svg-stroke-color-hover-primary svg:hover line, .svg-stroke-color-hover-primary svg:hover polyline, .svg-stroke-color-hover-primary svg:hover polygon {
	stroke: var(--primary) !important;
}

.svg-fill-color-secondary {
	fill: var(--secondary) !important;
}

.svg-fill-color-secondary svg path, .svg-fill-color-secondary svg rect, .svg-fill-color-secondary svg line, .svg-fill-color-secondary svg polyline, .svg-fill-color-secondary svg polygon {
	fill: var(--secondary) !important;
}

.svg-fill-color-hover-secondary:hover {
	fill: var(--secondary) !important;
}

.svg-fill-color-hover-secondary svg:hover path, .svg-fill-color-hover-secondary svg:hover rect, .svg-fill-color-hover-secondary svg:hover line, .svg-fill-color-hover-secondary svg:hover polyline, .svg-fill-color-hover-secondary svg:hover polygon {
	fill: var(--secondary) !important;
}

.svg-stroke-color-secondary {
	stroke: var(--secondary) !important;
}

.svg-stroke-color-secondary svg path, .svg-stroke-color-secondary svg rect, .svg-stroke-color-secondary svg line, .svg-stroke-color-secondary svg polyline, .svg-stroke-color-secondary svg polygon {
	stroke: var(--secondary) !important;
}

.svg-stroke-color-hover-secondary:hover {
	stroke: var(--secondary) !important;
}

.svg-stroke-color-hover-secondary svg:hover path, .svg-stroke-color-hover-secondary svg:hover rect, .svg-stroke-color-hover-secondary svg:hover line, .svg-stroke-color-hover-secondary svg:hover polyline, .svg-stroke-color-hover-secondary svg:hover polygon {
	stroke: var(--secondary) !important;
}

.svg-fill-color-tertiary {
	fill: var(--tertiary) !important;
}

.svg-fill-color-tertiary svg path, .svg-fill-color-tertiary svg rect, .svg-fill-color-tertiary svg line, .svg-fill-color-tertiary svg polyline, .svg-fill-color-tertiary svg polygon {
	fill: var(--tertiary) !important;
}

.svg-fill-color-hover-tertiary:hover {
	fill: var(--tertiary) !important;
}

.svg-fill-color-hover-tertiary svg:hover path, .svg-fill-color-hover-tertiary svg:hover rect, .svg-fill-color-hover-tertiary svg:hover line, .svg-fill-color-hover-tertiary svg:hover polyline, .svg-fill-color-hover-tertiary svg:hover polygon {
	fill: var(--tertiary) !important;
}

.svg-stroke-color-tertiary {
	stroke: var(--tertiary) !important;
}

.svg-stroke-color-tertiary svg path, .svg-stroke-color-tertiary svg rect, .svg-stroke-color-tertiary svg line, .svg-stroke-color-tertiary svg polyline, .svg-stroke-color-tertiary svg polygon {
	stroke: var(--tertiary) !important;
}

.svg-stroke-color-hover-tertiary:hover {
	stroke: var(--tertiary) !important;
}

.svg-stroke-color-hover-tertiary svg:hover path, .svg-stroke-color-hover-tertiary svg:hover rect, .svg-stroke-color-hover-tertiary svg:hover line, .svg-stroke-color-hover-tertiary svg:hover polyline, .svg-stroke-color-hover-tertiary svg:hover polygon {
	stroke: var(--tertiary) !important;
}

.svg-fill-color-quaternary {
	fill: var(--quaternary) !important;
}

.svg-fill-color-quaternary svg path, .svg-fill-color-quaternary svg rect, .svg-fill-color-quaternary svg line, .svg-fill-color-quaternary svg polyline, .svg-fill-color-quaternary svg polygon {
	fill: var(--quaternary) !important;
}

.svg-fill-color-hover-quaternary:hover {
	fill: var(--quaternary) !important;
}

.svg-fill-color-hover-quaternary svg:hover path, .svg-fill-color-hover-quaternary svg:hover rect, .svg-fill-color-hover-quaternary svg:hover line, .svg-fill-color-hover-quaternary svg:hover polyline, .svg-fill-color-hover-quaternary svg:hover polygon {
	fill: var(--quaternary) !important;
}

.svg-stroke-color-quaternary {
	stroke: var(--quaternary) !important;
}

.svg-stroke-color-quaternary svg path, .svg-stroke-color-quaternary svg rect, .svg-stroke-color-quaternary svg line, .svg-stroke-color-quaternary svg polyline, .svg-stroke-color-quaternary svg polygon {
	stroke: var(--quaternary) !important;
}

.svg-stroke-color-hover-quaternary:hover {
	stroke: var(--quaternary) !important;
}

.svg-stroke-color-hover-quaternary svg:hover path, .svg-stroke-color-hover-quaternary svg:hover rect, .svg-stroke-color-hover-quaternary svg:hover line, .svg-stroke-color-hover-quaternary svg:hover polyline, .svg-stroke-color-hover-quaternary svg:hover polygon {
	stroke: var(--quaternary) !important;
}

.svg-fill-color-dark {
	fill: var(--dark) !important;
}

.svg-fill-color-dark svg path, .svg-fill-color-dark svg rect, .svg-fill-color-dark svg line, .svg-fill-color-dark svg polyline, .svg-fill-color-dark svg polygon {
	fill: var(--dark) !important;
}

.svg-fill-color-hover-dark:hover {
	fill: var(--dark) !important;
}

.svg-fill-color-hover-dark svg:hover path, .svg-fill-color-hover-dark svg:hover rect, .svg-fill-color-hover-dark svg:hover line, .svg-fill-color-hover-dark svg:hover polyline, .svg-fill-color-hover-dark svg:hover polygon {
	fill: var(--dark) !important;
}

.svg-stroke-color-dark {
	stroke: var(--dark) !important;
}

.svg-stroke-color-dark svg path, .svg-stroke-color-dark svg rect, .svg-stroke-color-dark svg line, .svg-stroke-color-dark svg polyline, .svg-stroke-color-dark svg polygon {
	stroke: var(--dark) !important;
}

.svg-stroke-color-hover-dark:hover {
	stroke: var(--dark) !important;
}

.svg-stroke-color-hover-dark svg:hover path, .svg-stroke-color-hover-dark svg:hover rect, .svg-stroke-color-hover-dark svg:hover line, .svg-stroke-color-hover-dark svg:hover polyline, .svg-stroke-color-hover-dark svg:hover polygon {
	stroke: var(--dark) !important;
}

.svg-fill-color-light {
	fill: var(--light) !important;
}

.svg-fill-color-light svg path, .svg-fill-color-light svg rect, .svg-fill-color-light svg line, .svg-fill-color-light svg polyline, .svg-fill-color-light svg polygon {
	fill: var(--light) !important;
}

.svg-fill-color-hover-light:hover {
	fill: var(--light) !important;
}

.svg-fill-color-hover-light svg:hover path, .svg-fill-color-hover-light svg:hover rect, .svg-fill-color-hover-light svg:hover line, .svg-fill-color-hover-light svg:hover polyline, .svg-fill-color-hover-light svg:hover polygon {
	fill: var(--light) !important;
}

.svg-stroke-color-light {
	stroke: var(--light) !important;
}

.svg-stroke-color-light svg path, .svg-stroke-color-light svg rect, .svg-stroke-color-light svg line, .svg-stroke-color-light svg polyline, .svg-stroke-color-light svg polygon {
	stroke: var(--light) !important;
}

.svg-stroke-color-hover-light:hover {
	stroke: var(--light) !important;
}

.svg-stroke-color-hover-light svg:hover path, .svg-stroke-color-hover-light svg:hover rect, .svg-stroke-color-hover-light svg:hover line, .svg-stroke-color-hover-light svg:hover polyline, .svg-stroke-color-hover-light svg:hover polygon {
	stroke: var(--light) !important;
}

/* SVG Animations */
.svg-animation-effect-1-hover:hover svg path, .svg-animation-effect-1-hover:hover svg polygon, .svg-animation-effect-1-hover:hover svg polyline, .svg-animation-effect-1-hover:hover svg rect {
	stroke: var(--primary);
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg path, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg polygon, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg polyline, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg rect {
	stroke: var(--primary);
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg path, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg polygon, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg polyline, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg rect {
	stroke: var(--secondary);
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg path, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg polygon, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg polyline, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg rect {
	stroke: var(--tertiary);
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg path, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg polygon, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg polyline, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg rect {
	stroke: var(--quaternary);
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg path, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg polygon, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg polyline, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg rect {
	stroke: var(--dark);
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg path, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg polygon, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg polyline, .svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg rect {
	stroke: var(--light);
}

/* Tables */
.table td, .table th {
	border-color: rgba(0, 0, 0, 0.06);
}

.table > :not(:first-child) {
	border-top-color: rgba(0, 0, 0, 0.06);
}

/* Tabs */
.tabs {
	border-radius: 4px;
	margin-bottom: 20px;
}

.nav-tabs {
	margin: 0;
	padding: 0;
	border-bottom-color: #EEE;
}

.nav-tabs li:last-child .nav-link {
	margin-right: 0;
}

.nav-tabs li .nav-link {
	border-radius: 5px 5px 0 0;
	font-size: 13px;
	font-weight: 600;
	letter-spacing: -0.2px;
	padding: 11.2px 24px;
	padding: .7rem 1.5rem;
	transition: all .2s;
	margin-right: 1px;
}

.nav-tabs li .nav-link, .nav-tabs li .nav-link:hover {
	background: var(--grey-100);
	border-bottom: none;
	border-left: 1px solid #EEE;
	border-right: 1px solid #EEE;
	border-top: 3px solid #EEE;
	color: var(--grey-500);
}

.nav-tabs li .nav-link:hover {
	border-bottom-color: transparent;
	border-top: 3px solid var(--grey-500);
	box-shadow: none;
}

.nav-tabs li .nav-link:active, .nav-tabs li .nav-link:hover, .nav-tabs li .nav-link:focus {
	background: var(--light);
	border-left-color: #EEE;
	border-right-color: #EEE;
	border-top: 3px solid var(--grey-500);
	color: var(--grey-500);
}

.tab-content {
	border-radius: 0 0 4px 4px;
	box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.04);
	background-color: var(--light);
	border: 1px solid #EEE;
	border-top: 0;
	padding: 15px;
}

/* Right Aligned */
.nav-tabs.nav-right {
	text-align: right;
}

/* Bottom Tabs */
.tabs.tabs-bottom {
	margin: 0 0 20px 0;
	padding: 0;
}

.tabs.tabs-bottom .tab-content {
	border-radius: 4px 4px 0 0;
	border-bottom: 0;
	border-top: 1px solid #EEE;
}

.tabs.tabs-bottom .nav-tabs {
	border-bottom: none;
	border-top: 1px solid #EEE;
}

.tabs.tabs-bottom .nav-tabs li {
	margin-bottom: 0;
	margin-top: -1px;
}

.tabs.tabs-bottom .nav-tabs li:last-child .nav-link {
	margin-right: 0;
}

.tabs.tabs-bottom .nav-tabs li .nav-link {
	border-radius: 0 0 5px 5px;
	font-size: 13px;
	margin-right: 1px;
	border-bottom: 3px solid var(--grey-500);
	border-top-color: transparent !important;
}

.tabs.tabs-bottom .nav-tabs li .nav-link.active, .tabs.tabs-bottom .nav-tabs li .nav-link.active:hover, .tabs.tabs-bottom .nav-tabs li .nav-link.active:focus {
	border-bottom: 3px solid var(--grey-500);
	border-top-color: transparent !important;
}

.tabs.tabs-bottom .nav-tabs li .nav-link, .tabs.tabs-bottom .nav-tabs li .nav-link:hover {
	border-bottom: 3px solid #EEE;
	border-top: 1px solid #EEE;
}

.tabs.tabs-bottom .nav-tabs li .nav-link:hover {
	border-bottom: 3px solid var(--grey-500);
	border-top: 1px solid #EEE;
}

/* Vertical */
.tabs-vertical {
	display: table;
	width: 100%;
	padding: 0;
	border-top: 1px solid #EEE;
}

.tabs-vertical .tab-content {
	display: table-cell;
	vertical-align: top;
}

.tabs-vertical .nav-tabs {
	border-bottom: none;
	display: table-cell;
	height: 100%;
	float: none;
	padding: 0;
	vertical-align: top;
	width: 25%;
}

.tabs-vertical .nav-tabs > li {
	display: block;
}

.tabs-vertical .nav-tabs > li .nav-link {
	border-radius: 0;
	display: block;
	padding-top: 10px;
}

.tabs-vertical .nav-tabs > li .nav-link, .tabs-vertical .nav-tabs > li .nav-link:hover, .tabs-vertical .nav-tabs > li .nav-link:focus {
	border-bottom: none;
	border-top: none;
}

.tabs-vertical .nav-tabs > li .nav-link.active, .tabs-vertical .nav-tabs > li .nav-link.active:hover, .tabs-vertical .nav-tabs > li .nav-link.active:focus {
	border-top: none;
}

/* Vertical - Left Side */
.tabs-left {
	padding: 0;
}

.tabs-left .tab-content {
	border-radius: 0 5px 5px 5px;
	border-left: none;
}

.tabs-left .nav-tabs > li {
	margin-right: -1px;
}

.tabs-left .nav-tabs > li:first-child .nav-link {
	border-radius: 5px 0 0 0;
}

.tabs-left .nav-tabs > li:last-child .nav-link {
	border-radius: 0 0 0 5px;
	border-bottom: 1px solid #eee;
}

.tabs-left .nav-tabs > li .nav-link {
	border-right: 1px solid #EEE;
	border-left: 3px solid #EEE;
	margin-right: 1px;
	margin-left: -3px;
}

.tabs-left .nav-tabs > li .nav-link:hover {
	border-left-color: var(--grey-500);
}

.tabs-left .nav-tabs > li .nav-link.active, .tabs-left .nav-tabs > li .nav-link.active:hover, .tabs-left .nav-tabs > li .nav-link.active:focus {
	border-left: 3px solid var(--grey-500);
	border-right-color: var(--light);
}

/* Vertical - Right Side */
.tabs-right {
	padding: 0;
}

.tabs-right .tab-content {
	border-radius: 5px 0 5px 5px;
	border-right: none;
}

.tabs-right .nav-tabs > li {
	margin-left: -1px;
}

.tabs-right .nav-tabs > li:first-child .nav-link {
	border-radius: 0 5px 0 0;
}

.tabs-right .nav-tabs > li:last-child .nav-link {
	border-radius: 0 0 5px 0;
	border-bottom: 1px solid #eee;
}

.tabs-right .nav-tabs > li .nav-link {
	border-right: 3px solid #EEE;
	border-left: 1px solid #EEE;
	margin-right: 1px;
	margin-left: 1px;
}

.tabs-right .nav-tabs > li .nav-link:hover {
	border-right-color: var(--grey-500);
}

.tabs-right .nav-tabs > li .nav-link.active, .tabs-right .nav-tabs > li .nav-link.active:hover, .tabs-right .nav-tabs > li .nav-link.active:focus {
	border-right: 3px solid var(--grey-500);
	border-left: 1px solid #FFF;
}

/* Justified */
.nav-tabs.nav-justified {
	padding: 0;
	margin-bottom: -1px;
}

.nav-tabs.nav-justified li {
	margin-bottom: 0;
}

.nav-tabs.nav-justified li:first-child .nav-link,
.nav-tabs.nav-justified li:first-child .nav-link:hover {
	border-radius: 5px 0 0 0;
}

.nav-tabs.nav-justified li:last-child .nav-link,
.nav-tabs.nav-justified li:last-child .nav-link:hover {
	border-radius: 0 5px 0 0;
}

.nav-tabs.nav-justified li .nav-link {
	border-bottom: 1px solid #DDD;
	border-radius: 0;
	margin-right: 0;
}

.nav-tabs.nav-justified li .nav-link:hover, .nav-tabs.nav-justified li .nav-link:focus {
	border-bottom: 1px solid #DDD;
}

.nav-tabs.nav-justified li .nav-link.active {
	border-bottom: 1px solid #FFF;
}

.nav-tabs.nav-justified li .nav-link.active, .nav-tabs.nav-justified li .nav-link.active:hover, .nav-tabs.nav-justified li .nav-link.active:focus {
	background: var(--light);
	border-left-color: #EEE;
	border-right-color: #EEE;
	border-top-width: 3px;
}

.nav-tabs.nav-justified li .nav-link.active, .nav-tabs.nav-justified li .nav-link.active:hover, .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-width: 3px;
}

.nav-tabs.nav-justified li .nav-link.active:hover {
	border-bottom: 1px solid #FFF;
}

/* Bottom Tabs with Justified Nav */
.tabs.tabs-bottom {
	padding: 0;
}

.tabs.tabs-bottom .nav.nav-tabs.nav-justified {
	border-top: none;
}

.tabs.tabs-bottom .nav.nav-tabs.nav-justified li .nav-link {
	margin-right: 0;
	border-top-color: transparent;
}

.tabs.tabs-bottom .nav.nav-tabs.nav-justified li .nav-link.active, .tabs.tabs-bottom .nav.nav-tabs.nav-justified li .nav-link.active:hover, .tabs.tabs-bottom .nav.nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: transparent;
}

.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:first-child .nav-link {
	border-radius: 0 0 0 5px;
}

.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:last-child .nav-link {
	margin-right: 0;
	border-radius: 0 0 5px 0;
}

/* Center */
.tabs-center .nav-tabs {
	margin: 0 auto;
	text-align: center;
}

/* Navigation */
.tabs-navigation {
	border-radius: 5px;
}

.tabs-navigation .nav-tabs > li {
	margin-bottom: 1px;
}

.tabs-navigation .nav-tabs > li .nav-link {
	color: var(--default);
	padding: 18px;
	line-height: 100%;
	position: relative;
}

.tabs-navigation .nav-tabs > li .nav-link:before {
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	font-size: 10px;
	content: "\f054";
	position: absolute;
	right: 15px;
	top: 50%;
	transform: translateY(-50%);
}

.tabs-navigation .nav-tabs > li .nav-link [class*="fa-"], .tabs-navigation .nav-tabs > li .nav-link .icons {
	margin-right: 4px;
}

.tabs-navigation .nav-tabs li:first-child .nav-link {
	border-radius: 5px 5px 0 0;
}

.tabs-navigation .nav-tabs li .nav-link.active, .tabs-navigation .nav-tabs li .nav-link.active:hover, .tabs-navigation .nav-tabs li .nav-link.active:focus {
	border-right-color: #eee;
}

.tabs-navigation .nav-tabs li:last-child .nav-link {
	border-radius: 0 0 5px 5px;
}

.tabs-navigation-simple {
	border: 0 !important;
	border-radius: 0 !important;
}

.tabs-navigation-simple .nav-tabs {
	border: 0 !important;
	border-radius: 0 !important;
}

.tabs-navigation-simple .nav-tabs .nav-link, .tabs-navigation-simple .nav-tabs .nav-link:hover {
	background: transparent !important;
	border-radius: 0 !important;
	border-bottom: 1px solid rgba(0, 0, 0, 0.06) !important;
	border-left: 0 !important;
	padding-left: 0 !important;
}

.tabs-navigation-simple .nav-tabs li:last-child .nav-link, .tabs-navigation-simple .nav-tabs li:last-child .nav-link:hover {
	border-bottom: 0 !important;
}

.tab-pane-navigation {
	display: none;
}

.tab-pane-navigation.active {
	display: block;
}

/* Simple */
.tabs.tabs-simple .nav-tabs {
	justify-content: center;
	border: 0;
	margin-bottom: 10px;
}

.tabs.tabs-simple .nav-tabs > li {
	margin-left: 0;
	margin-bottom: 20px;
}

.tabs.tabs-simple .nav-tabs > li .nav-link, .tabs.tabs-simple .nav-tabs > li .nav-link:hover, .tabs.tabs-simple .nav-tabs > li .nav-link:focus {
	padding: 15px 30px;
	background: none;
	border: 0;
	border-bottom: 3px solid #eee;
	border-radius: 0;
	color: var(--default);
	font-size: 16px;
}

.tabs.tabs-simple .tab-content {
	background: transparent;
	border: 0;
	padding: 0;
	margin: 0;
	box-shadow: none;
}

.tabs.tabs-simple.tabs-simple-full-width-line .nav-tabs {
	border-bottom: 3px solid #eee;
}

.tabs.tabs-simple.tabs-simple-full-width-line .nav-tabs > li {
	margin-bottom: -3px;
}

@media (max-width: 575px) {
	.tabs .nav.nav-tabs.nav-justified li {
		display: block;
		margin-bottom: -5px;
	}

	.tabs .nav.nav-tabs.nav-justified li .nav-link {
		border-top-width: 3px !important;
		border-bottom-width: 0 !important;
	}

	.tabs .nav.nav-tabs.nav-justified li:first-child .nav-link,
		.tabs .nav.nav-tabs.nav-justified li:first-child .nav-link:hover {
		border-radius: 5px 5px 0 0;
	}

	.tabs .nav.nav-tabs.nav-justified li:last-child .nav-link,
		.tabs .nav.nav-tabs.nav-justified li:last-child .nav-link:hover {
		border-radius: 0;
	}

	.tabs.tabs-bottom .nav.nav-tabs.nav-justified li {
		margin-bottom: 0;
		margin-top: -5px;
	}

	.tabs.tabs-bottom .nav.nav-tabs.nav-justified li .nav-link {
		border-bottom-width: 3px !important;
		border-top-width: 0 !important;
	}

	.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:first-child .nav-link,
		.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:first-child .nav-link:hover {
		border-radius: 0;
	}

	.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:last-child .nav-link,
		.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:last-child .nav-link:hover {
		border-radius: 0 0 5px 5px;
	}
}

.responsive-tabs-container .tab-pane {
	margin-bottom: 15px;
}

.responsive-tabs-container .accordion-link {
	display: none;
	margin-bottom: 10px;
	padding: 10px 15px;
	background-color: var(--grey-100);
	border-radius: 3px;
	border: 1px solid #ebebeb;
}

@media (max-width: 767px) {
	.responsive-tabs-container.accordion-xs .nav-tabs {
		display: none;
	}

	.responsive-tabs-container.accordion-xs .accordion-link {
		display: block;
	}
}

@media (min-width: 768px) and (max-width: 991px) {
	.responsive-tabs-container.accordion-sm .nav-tabs {
		display: none;
	}

	.responsive-tabs-container.accordion-sm .accordion-link {
		display: block;
	}
}

@media (min-width: 992px) and (max-width: 1199px) {
	.responsive-tabs-container.accordion-md .nav-tabs {
		display: none;
	}

	.responsive-tabs-container.accordion-md .accordion-link {
		display: block;
	}
}

@media (min-width: 1200px) {
	.responsive-tabs-container.accordion-lg .nav-tabs {
		display: none;
	}

	.responsive-tabs-container.accordion-lg .accordion-link {
		display: block;
	}
}

/* Tabs - Skin */
html body .tabs .nav-tabs .nav-link, html body .tabs .nav-tabs .nav-link:hover,
html body .tabs .nav-tabs .nav-link.active,
html.dark body .tabs .nav-tabs .nav-link,
html.dark body .tabs .nav-tabs .nav-link:hover,
html.dark body .tabs .nav-tabs .nav-link.active {
	color: var(--primary);
}

html body .tabs .nav-tabs .nav-link:hover,
html body .tabs .nav-tabs .nav-link:focus,
html body .tabs .nav-tabs .nav-link.active,
html body .tabs .nav-tabs.nav-justified .nav-link.active,
html body .tabs .nav-tabs.nav-justified .nav-link:hover,
html body .tabs .nav-tabs.nav-justified .nav-link:focus,
html.dark body .tabs .nav-tabs .nav-link:hover,
html.dark body .tabs .nav-tabs .nav-link:focus,
html.dark body .tabs .nav-tabs .nav-link.active,
html.dark body .tabs .nav-tabs.nav-justified .nav-link.active,
html.dark body .tabs .nav-tabs.nav-justified .nav-link:hover,
html.dark body .tabs .nav-tabs.nav-justified .nav-link:focus {
	border-top-color: var(--primary);
}

html body .tabs.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs.tabs-bottom .nav-tabs .nav-link.active,
html body .tabs.tabs-bottom .nav-tabs .nav-link.active:hover,
html body .tabs.tabs-bottom .nav-tabs .nav-link.active:focus,
html.dark body .tabs.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs.tabs-bottom .nav-tabs .nav-link.active,
html.dark body .tabs.tabs-bottom .nav-tabs .nav-link.active:hover,
html.dark body .tabs.tabs-bottom .nav-tabs .nav-link.active:focus {
	border-bottom-color: var(--primary);
}

html body .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link:hover,
html body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active,
html body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:hover,
html body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:focus,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link:hover,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:hover,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:focus {
	border-left-color: var(--primary);
}

html body .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link:hover,
html body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active,
html body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:hover,
html body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:focus,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link:hover,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:hover,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:focus {
	border-right-color: var(--primary);
}

html body .tabs.tabs-simple .nav-tabs > li .nav-link.active,
html body .tabs.tabs-simple .nav-tabs > li .nav-link.active:focus,
html body .tabs.tabs-simple .nav-tabs > li .nav-link:hover,
html body .tabs.tabs-simple .nav-tabs > li .nav-link.active:hover,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link.active,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link.active:focus,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link:hover,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link.active:hover {
	border-top-color: var(--primary);
	color: var(--primary);
}

html body .tabs-primary .nav-tabs li .nav-link, html body .tabs-primary .nav-tabs li .nav-link:hover, html body .tabs-primary .nav-tabs.nav-justified li .nav-link, html body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs li .nav-link,
html.dark body .tabs-primary .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover {
	color: var(--primary);
}

html body .tabs-primary .nav-tabs li .nav-link:hover, html body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: var(--primary);
}

html body .tabs-primary .nav-tabs li .nav-link.active, html body .tabs-primary .nav-tabs li .nav-link.active:hover, html body .tabs-primary .nav-tabs li .nav-link.active:focus, html body .tabs-primary .nav-tabs.nav-justified li .nav-link.active, html body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-primary .nav-tabs li .nav-link.active,
html.dark body .tabs-primary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-primary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: var(--primary);
	color: var(--primary);
}

html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link:hover, html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active, html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:hover, html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:focus, html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active, html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: var(--primary);
}

html body .tabs-primary.tabs-simple .nav-tabs li .nav-link:hover, html body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active, html body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:hover, html body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:focus, html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active, html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: var(--primary) !important;
	border-bottom-color: var(--primary);
}

html body .tabs-primary.tabs-vertical.tabs-left li .nav-link:hover, html body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active, html body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:hover, html body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: var(--primary);
}

html body .tabs-primary.tabs-vertical.tabs-right li .nav-link:hover, html body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active, html body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:hover, html body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: var(--primary);
}

html body .tabs-secondary .nav-tabs li .nav-link, html body .tabs-secondary .nav-tabs li .nav-link:hover, html body .tabs-secondary .nav-tabs.nav-justified li .nav-link, html body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs li .nav-link,
html.dark body .tabs-secondary .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover {
	color: var(--secondary);
}

html body .tabs-secondary .nav-tabs li .nav-link:hover, html body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: var(--secondary);
}

html body .tabs-secondary .nav-tabs li .nav-link.active, html body .tabs-secondary .nav-tabs li .nav-link.active:hover, html body .tabs-secondary .nav-tabs li .nav-link.active:focus, html body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active, html body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-secondary .nav-tabs li .nav-link.active,
html.dark body .tabs-secondary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-secondary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: var(--secondary);
	color: var(--secondary);
}

html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link:hover, html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active, html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:hover, html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:focus, html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active, html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: var(--secondary);
}

html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link:hover, html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active, html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:hover, html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:focus, html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active, html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: var(--secondary) !important;
	border-bottom-color: var(--secondary);
}

html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link:hover, html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active, html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:hover, html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: var(--secondary);
}

html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link:hover, html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active, html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:hover, html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: var(--secondary);
}

html body .tabs-tertiary .nav-tabs li .nav-link, html body .tabs-tertiary .nav-tabs li .nav-link:hover, html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link, html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs li .nav-link,
html.dark body .tabs-tertiary .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover {
	color: var(--tertiary);
}

html body .tabs-tertiary .nav-tabs li .nav-link:hover, html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: var(--tertiary);
}

html body .tabs-tertiary .nav-tabs li .nav-link.active, html body .tabs-tertiary .nav-tabs li .nav-link.active:hover, html body .tabs-tertiary .nav-tabs li .nav-link.active:focus, html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active, html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-tertiary .nav-tabs li .nav-link.active,
html.dark body .tabs-tertiary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-tertiary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: var(--tertiary);
	color: var(--tertiary);
}

html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link:hover, html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active, html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:hover, html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:focus, html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active, html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: var(--tertiary);
}

html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link:hover, html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active, html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:hover, html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:focus, html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active, html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: var(--tertiary) !important;
	border-bottom-color: var(--tertiary);
}

html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link:hover, html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active, html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:hover, html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: var(--tertiary);
}

html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link:hover, html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active, html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:hover, html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: var(--tertiary);
}

html body .tabs-quaternary .nav-tabs li .nav-link, html body .tabs-quaternary .nav-tabs li .nav-link:hover, html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link, html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs li .nav-link,
html.dark body .tabs-quaternary .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover {
	color: var(--quaternary);
}

html body .tabs-quaternary .nav-tabs li .nav-link:hover, html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: var(--quaternary);
}

html body .tabs-quaternary .nav-tabs li .nav-link.active, html body .tabs-quaternary .nav-tabs li .nav-link.active:hover, html body .tabs-quaternary .nav-tabs li .nav-link.active:focus, html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active, html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-quaternary .nav-tabs li .nav-link.active,
html.dark body .tabs-quaternary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-quaternary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: var(--quaternary);
	color: var(--quaternary);
}

html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link:hover, html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active, html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:hover, html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:focus, html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active, html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: var(--quaternary);
}

html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link:hover, html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active, html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:hover, html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:focus, html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active, html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: var(--quaternary) !important;
	border-bottom-color: var(--quaternary);
}

html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link:hover, html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active, html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:hover, html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: var(--quaternary);
}

html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link:hover, html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active, html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:hover, html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: var(--quaternary);
}

html body .tabs-dark .nav-tabs li .nav-link, html body .tabs-dark .nav-tabs li .nav-link:hover, html body .tabs-dark .nav-tabs.nav-justified li .nav-link, html body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs li .nav-link,
html.dark body .tabs-dark .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover {
	color: var(--dark);
}

html body .tabs-dark .nav-tabs li .nav-link:hover, html body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: var(--dark);
}

html body .tabs-dark .nav-tabs li .nav-link.active, html body .tabs-dark .nav-tabs li .nav-link.active:hover, html body .tabs-dark .nav-tabs li .nav-link.active:focus, html body .tabs-dark .nav-tabs.nav-justified li .nav-link.active, html body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-dark .nav-tabs li .nav-link.active,
html.dark body .tabs-dark .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-dark .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: var(--dark);
	color: var(--dark);
}

html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link:hover, html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active, html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:hover, html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:focus, html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active, html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: var(--dark);
}

html body .tabs-dark.tabs-simple .nav-tabs li .nav-link:hover, html body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active, html body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:hover, html body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:focus, html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active, html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: var(--dark) !important;
	border-bottom-color: var(--dark);
}

html body .tabs-dark.tabs-vertical.tabs-left li .nav-link:hover, html body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active, html body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:hover, html body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: var(--dark);
}

html body .tabs-dark.tabs-vertical.tabs-right li .nav-link:hover, html body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active, html body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:hover, html body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: var(--dark);
}

html body .tabs-light .nav-tabs li .nav-link, html body .tabs-light .nav-tabs li .nav-link:hover, html body .tabs-light .nav-tabs.nav-justified li .nav-link, html body .tabs-light .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light .nav-tabs li .nav-link,
html.dark body .tabs-light .nav-tabs li .nav-link:hover,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link:hover {
	color: var(--light);
}

html body .tabs-light .nav-tabs li .nav-link:hover, html body .tabs-light .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light .nav-tabs li .nav-link:hover,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: var(--light);
}

html body .tabs-light .nav-tabs li .nav-link.active, html body .tabs-light .nav-tabs li .nav-link.active:hover, html body .tabs-light .nav-tabs li .nav-link.active:focus, html body .tabs-light .nav-tabs.nav-justified li .nav-link.active, html body .tabs-light .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-light .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-light .nav-tabs li .nav-link.active,
html.dark body .tabs-light .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-light .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: var(--light);
	color: var(--light);
}

html body .tabs-light.tabs-bottom .nav-tabs li .nav-link:hover, html body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active, html body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:hover, html body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:focus, html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active, html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: var(--light);
}

html body .tabs-light.tabs-simple .nav-tabs li .nav-link:hover, html body .tabs-light.tabs-simple .nav-tabs li .nav-link.active, html body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:hover, html body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:focus, html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link:hover, html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active, html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover, html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: var(--light) !important;
	border-bottom-color: var(--light);
}

html body .tabs-light.tabs-vertical.tabs-left li .nav-link:hover, html body .tabs-light.tabs-vertical.tabs-left li .nav-link.active, html body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:hover, html body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: var(--light);
}

html body .tabs-light.tabs-vertical.tabs-right li .nav-link:hover, html body .tabs-light.tabs-vertical.tabs-right li .nav-link.active, html body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:hover, html body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: var(--light);
}

html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link:hover html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link.active, html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link.active:hover, html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link.active:focus {
	border-right-color: var(--primary);
	border-left-color: transparent;
}

html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link:hover html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link.active, html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link.active:hover, html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link.active:focus {
	border-right-color: transparent;
	border-left-color: var(--primary);
}

/* Testimonials */
.testimonial {
	margin-bottom: 20px;
}

.testimonial blockquote {
	background: var(--grey-500);
	border-radius: 5px;
	border: 0;
	color: #666;
	font-family: 'Playfair Display', serif;
	margin: 0;
	padding: 40px 64px 40px 72px;
	padding: 2.5rem 4rem 2.5rem 4.5rem;
	position: relative;
}

.testimonial blockquote:before {
	left: 22px;
	top: 21px;
	color: var(--light);
	content: "“";
	font-size: 85px;
	font-style: normal;
	line-height: 1;
	position: absolute;
}

.testimonial blockquote p {
	color: var(--light);
	font-family: 'Playfair Display', serif;
	font-size: 1.2em;
}

.testimonial .testimonial-arrow-down {
	border-left: 11px solid transparent;
	border-right: 11px solid transparent;
	border-top: 8px solid var(--grey-500);
	height: 0;
	margin: 0 0 0 45px;
	width: 0;
}

.testimonial .testimonial-author {
	display: flex;
	margin: 12px 0 0 0;
}

.testimonial .testimonial-author .testimonial-author-thumbnail {
	width: auto;
}

.testimonial .testimonial-author img {
	max-width: 25px;
}

.testimonial .testimonial-author strong {
	color: #111;
	display: block;
	margin-bottom: 2px;
	font-size: 16px;
	font-size: 1rem;
}

.testimonial .testimonial-author span {
	color: #666;
	display: block;
	font-size: 12.8px;
	font-size: 0.8rem;
	font-weight: 600;
}

.testimonial .testimonial-author p {
	color: #999;
	text-align: left;
	line-height: 1.2;
	margin-left: 9.6px;
	margin-left: 0.6rem;
}

/* Style 2 */
.testimonial.testimonial-style-2 {
	text-align: center;
}

.testimonial.testimonial-style-2 blockquote {
	background: transparent !important;
	border-radius: var(--border-radius);
	padding: 22px 20px;
}

.testimonial.testimonial-style-2 blockquote:before, .testimonial.testimonial-style-2 blockquote:after {
	display: none;
}

.testimonial.testimonial-style-2 blockquote p {
	color: var(--default);
}

.testimonial.testimonial-style-2 .testimonial-arrow-down {
	display: none;
}

.testimonial.testimonial-style-2 .testimonial-author {
	padding: 0;
	margin: 6px 0 0;
	flex-direction: column;
}

.testimonial.testimonial-style-2 .testimonial-author img {
	margin: 0 auto 15px;
	max-width: 60px;
	max-height: 60px;
}

.testimonial.testimonial-style-2 .testimonial-author p {
	text-align: center;
	padding: 0;
	margin: 0;
}

/* Style 3 */
.testimonial.testimonial-style-3 blockquote {
	background: var(--grey-100) !important;
	border-radius: var(--border-radius);
	padding: 12px 30px;
}

.testimonial.testimonial-style-3 blockquote:before {
	top: 5px;
	left: 9px;
	color: var(--default);
	font-size: 35px;
}

.testimonial.testimonial-style-3 blockquote:after {
	color: var(--default);
	font-size: 35px;
	bottom: -5px;
}

.testimonial.testimonial-style-3 blockquote p {
	color: var(--default);
	font-family: "Poppins", Arial, sans-serif;
	font-size: 1em;
	font-style: normal;
}

.testimonial.testimonial-style-3 .testimonial-author {
	margin-left: 8px;
	align-items: center;
}

.testimonial.testimonial-style-3 .testimonial-author img {
	max-width: 55px;
}

.testimonial.testimonial-style-3 .testimonial-author p {
	margin-bottom: 0;
}

.testimonial.testimonial-style-3 .testimonial-arrow-down {
	margin-left: 20px;
	border-top: 10px solid var(--grey-100) !important;
}

.testimonial.testimonial-style-3.testimonial-style-3-light blockquote {
	background: #FFF !important;
}

.testimonial.testimonial-style-3.testimonial-style-3-light .testimonial-arrow-down {
	border-top: 10px solid #FFF !important;
}

/* Style 4 */
.testimonial.testimonial-style-4 {
	border-radius: 6px;
	padding: 8px;
	border-top: 1px solid #DFDFDF;
	border-bottom: 1px solid #DFDFDF;
	border-left: 1px solid #ECECEC;
	border-right: 1px solid #ECECEC;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.04);
}

.testimonial.testimonial-style-4 blockquote {
	background: transparent !important;
	border-radius: var(--border-radius);
	padding: 12px 30px;
}

.testimonial.testimonial-style-4 blockquote:before {
	top: 5px;
	left: 9px;
	color: var(--default);
	font-size: 35px;
}

.testimonial.testimonial-style-4 blockquote:after {
	color: var(--default);
	font-size: 35px;
	bottom: -5px;
}

.testimonial.testimonial-style-4 blockquote p {
	color: var(--default);
	font-family: "Poppins", Arial, sans-serif;
	font-size: 1em;
	font-style: normal;
}

.testimonial.testimonial-style-4 .testimonial-arrow-down {
	display: none;
}

.testimonial.testimonial-style-4 .testimonial-author {
	margin-left: 30px;
	align-items: center;
}

.testimonial.testimonial-style-4 .testimonial-author img {
	max-width: 55px;
}

.testimonial.testimonial-style-4 .testimonial-author p {
	margin-bottom: 0;
}

.testimonial.testimonial-style-4.testimonial-no-borders {
	border: 0;
	box-shadow: none;
}

/* Style 5 */
.testimonial.testimonial-style-5 {
	text-align: center;
}

.testimonial.testimonial-style-5 blockquote {
	background: transparent !important;
	border-radius: var(--border-radius);
	padding: 6px 20px;
}

.testimonial.testimonial-style-5 blockquote:before, .testimonial.testimonial-style-5 blockquote:after {
	display: none;
}

.testimonial.testimonial-style-5 blockquote p {
	color: var(--default);
	font-family: "Poppins", Arial, sans-serif;
	font-size: 1.2em;
	font-style: normal;
}

.testimonial.testimonial-style-5 .testimonial-arrow-down {
	display: none;
}

.testimonial.testimonial-style-5 .testimonial-author {
	display: block;
	padding: 6px 0 0;
	margin: 12px 0 0;
	border-top: 1px solid var(--grey-100);
	justify-content: center;
}

.testimonial.testimonial-style-5 .testimonial-author img {
	margin: 0 auto 10px;
	max-width: 55px;
}

.testimonial.testimonial-style-5 .testimonial-author p {
	text-align: center;
	padding: 0;
	margin: 0;
}

/* Style 6 */
.testimonial.testimonial-style-6 {
	text-align: center;
}

.testimonial.testimonial-style-6 blockquote {
	background: transparent !important;
	border-radius: var(--border-radius);
	border: 0;
	padding: 0 10%;
}

.testimonial.testimonial-style-6 blockquote:before, .testimonial.testimonial-style-6 blockquote:after {
	display: none;
}

.testimonial.testimonial-style-6 blockquote i.fa-quote-left {
	font-size: 34px;
	position: relative;
	left: -5px;
}

.testimonial.testimonial-style-6 blockquote p {
	color: var(--default);
	font-size: 24px;
	line-height: 30px;
}

.testimonial.testimonial-style-6 blockquote span {
	font-size: 16px;
	line-height: 20px;
	color: #999;
	position: relative;
	left: -5px;
}

.testimonial.testimonial-style-6 .testimonial-arrow-down {
	display: none;
}

.testimonial.testimonial-style-6 .testimonial-author {
	padding: 0;
	margin: 6px 0 0;
	justify-content: center;
}

.testimonial.testimonial-style-6 .testimonial-author img {
	margin: 0 auto 10px;
}

.testimonial.testimonial-style-6 .testimonial-author p {
	text-align: center;
	padding: 0;
	margin: 0;
}

/* Style 7 */
.testimonial.testimonial-style-7 {
	text-align: center;
	background-color: var(--grey-500);
	padding: 20px;
}

.testimonial.testimonial-style-7 blockquote {
	padding: 30px;
}

.testimonial.testimonial-style-7 blockquote:before {
	color: var(--light);
	display: block !important;
	left: 50%;
	top: -10px;
	font-size: 80px;
	font-style: normal;
	line-height: 1;
	position: absolute;
	transform: translate3d(-50%, 0, 0);
}

.testimonial.testimonial-style-7 blockquote:after {
	display: none;
}

.testimonial.testimonial-style-7 .testimonial-arrow-down {
	display: none;
}

.testimonial.testimonial-style-7 .testimonial-author {
	padding: 0;
	margin: 6px 0 0;
	justify-content: center;
}

.testimonial.testimonial-style-7 .testimonial-author img {
	margin: 0 auto 10px;
}

.testimonial.testimonial-style-7 .testimonial-author p {
	text-align: center;
	padding: 0;
	margin: 0;
}

/* With Quotes */
.testimonial.testimonial-with-quotes blockquote:before {
	color: var(--default);
	display: block !important;
	left: 10px;
	top: 0;
	content: "“";
	font-size: 80px;
	font-style: normal;
	line-height: 1;
	position: absolute;
}

.testimonial.testimonial-with-quotes blockquote:after {
	color: var(--default);
	display: block !important;
	right: 10px;
	font-size: 80px;
	font-style: normal;
	line-height: 1;
	position: absolute;
	bottom: -0.5em;
	content: "”";
}

.testimonial.testimonial-with-quotes blockquote p {
	padding: 0 40px;
}

.testimonial.testimonial-with-quotes.testimonial-remove-left-quote blockquote:before {
	content: none;
}

.testimonial.testimonial-with-quotes.testimonial-remove-right-quote blockquote:after {
	content: none;
}

.testimonial.testimonial-with-quotes.testimonial-quotes-light blockquote:before, .testimonial.testimonial-with-quotes.testimonial-quotes-light blockquote:after {
	color: var(--light) !important;
}

.testimonial.testimonial-with-quotes.testimonial-quotes-dark blockquote:before, .testimonial.testimonial-with-quotes.testimonial-quotes-dark blockquote:after {
	color: var(--dark) !important;
}

@media (max-width: 767px) {
	.testimonial.testimonial-with-quotes.testimonial-with-quotes-mobile-remove blockquote:before, .testimonial.testimonial-with-quotes.testimonial-with-quotes-mobile-remove blockquote:after {
		content: none;
	}
}

/* Transparent Background */
.testimonial.testimonial-trasnparent-background blockquote {
	background: transparent !important;
}

/* Alternarive Font */
.testimonial.testimonial-alternarive-font blockquote p {
	font-family: 'Playfair Display', serif;
	font-style: italic;
	font-size: 1.2em;
}

/* Light */
.testimonial.testimonial-light blockquote:before, .testimonial.testimonial-light blockquote:after, .testimonial.testimonial-light blockquote p {
	color: var(--light);
}

.testimonial.testimonial-light blockquote.blockquote-default:before, .testimonial.testimonial-light blockquote.blockquote-default:after, .testimonial.testimonial-light blockquote.blockquote-default p {
	color: var(--default) !important;
}

.testimonial.testimonial-light .testimonial-author span, .testimonial.testimonial-light .testimonial-author strong {
	color: var(--light);
}

.section-text-light .testimonial.testimonial blockquote:before, .section-text-light .testimonial.testimonial blockquote:after, .section-text-light .testimonial.testimonial blockquote p {
	opacity: 0.9;
	color: var(--light);
}

.section-text-light .testimonial.testimonial .testimonial-author span, .section-text-light .testimonial.testimonial .testimonial-author strong {
	opacity: 0.6;
	color: var(--light);
}

/* Testimonial - Skin */
.testimonial blockquote {
	background: var(--primary);
}

.testimonial .testimonial-arrow-down {
	border-top-color: var(--primary);
}

.testimonial-primary blockquote {
	background: var(--primary);
}

.testimonial-primary .testimonial-arrow-down {
	border-top-color: var(--primary);
}

.testimonial-primary.testimonial-style-7 {
	background: var(--primary);
}

.testimonial-quotes-primary blockquote:before, .testimonial-quotes-primary blockquote:after {
	color: var(--primary) !important;
}

.testimonial-secondary blockquote {
	background: var(--secondary);
}

.testimonial-secondary .testimonial-arrow-down {
	border-top-color: var(--secondary);
}

.testimonial-secondary.testimonial-style-7 {
	background: var(--secondary);
}

.testimonial-quotes-secondary blockquote:before, .testimonial-quotes-secondary blockquote:after {
	color: var(--primary) !important;
}

.testimonial-tertiary blockquote {
	background: var(--tertiary);
}

.testimonial-tertiary .testimonial-arrow-down {
	border-top-color: var(--tertiary);
}

.testimonial-tertiary.testimonial-style-7 {
	background: var(--tertiary);
}

.testimonial-quotes-tertiary blockquote:before, .testimonial-quotes-tertiary blockquote:after {
	color: var(--primary) !important;
}

.testimonial-quaternary blockquote {
	background: var(--quaternary);
}

.testimonial-quaternary .testimonial-arrow-down {
	border-top-color: var(--quaternary);
}

.testimonial-quaternary.testimonial-style-7 {
	background: var(--quaternary);
}

.testimonial-quotes-quaternary blockquote:before, .testimonial-quotes-quaternary blockquote:after {
	color: var(--primary) !important;
}

.testimonial-dark blockquote {
	background: var(--dark);
}

.testimonial-dark .testimonial-arrow-down {
	border-top-color: var(--dark);
}

.testimonial-dark.testimonial-style-7 {
	background: var(--dark);
}

.testimonial-quotes-dark blockquote:before, .testimonial-quotes-dark blockquote:after {
	color: var(--primary) !important;
}

.testimonial-light blockquote {
	background: var(--light);
}

.testimonial-light .testimonial-arrow-down {
	border-top-color: var(--light);
}

.testimonial-light.testimonial-style-7 {
	background: var(--light);
}

.testimonial-quotes-light blockquote:before, .testimonial-quotes-light blockquote:after {
	color: var(--primary) !important;
}

/* Thumb Info */
.thumb-info {
	display: block;
	position: relative;
	text-decoration: none;
	max-width: 100%;
	background-color: var(--light);
	border-radius: var(--border-radius);
	overflow: hidden;
	backface-visibility: hidden;
	transform: translate3d(0, 0, 0);
}

.thumb-info .thumb-info-wrapper {
	backface-visibility: hidden;
	transform: translate3d(0, 0, 0);
	border-radius: var(--border-radius);
	margin: 0;
	overflow: hidden;
	display: block;
	position: relative;
}

.thumb-info .thumb-info-wrapper.thumb-info-wrapper-demos {
	max-height: 330px;
}

.thumb-info .thumb-info-wrapper:after, .thumb-info .thumb-info-wrapper .thumb-info-wrapper-overlay:before {
	content: "";
	background: var(--dark-rgba-80);
	transition: all 0.3s;
	border-radius: var(--border-radius);
	bottom: -4px;
	color: var(--light);
	left: -4px;
	position: absolute;
	right: -4px;
	top: -4px;
	display: block;
	opacity: 0;
	z-index: 1;
}

.thumb-info .thumb-info-wrapper .thumb-info-wrapper-overlay {
	position: absolute;
	width: 100%;
	height: 100%;
	left: auto;
	top: auto;
}

.thumb-info .thumb-info-wrapper .thumb-info-wrapper-overlay:before {
	opacity: 1;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	border-radius: 0;
}

.thumb-info .thumb-info-wrapper.thumb-info-wrapper-link {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.thumb-info .thumb-info-wrapper.thumb-info-wrapper-link > a {
	display: block;
	padding-top: 72%;
	text-indent: -9999px;
}

.thumb-info.thumb-info-wrapper-direction-aware .thumb-info-wrapper:after {
	display: none !important;
}

.thumb-info .thumb-info-wrapper-opacity-0:after {
	opacity: 0.0;
}

.thumb-info .thumb-info-wrapper-opacity-1:after {
	opacity: 0.1;
}

.thumb-info .thumb-info-wrapper-opacity-2:after {
	opacity: 0.2;
}

.thumb-info .thumb-info-wrapper-opacity-3:after {
	opacity: 0.3;
}

.thumb-info .thumb-info-wrapper-opacity-4:after {
	opacity: 0.4;
}

.thumb-info .thumb-info-wrapper-opacity-5:after {
	opacity: 0.5;
}

.thumb-info .thumb-info-wrapper-opacity-6:after {
	opacity: 0.6;
}

.thumb-info .thumb-info-wrapper-opacity-7:after {
	opacity: 0.7;
}

.thumb-info .thumb-info-wrapper-opacity-8:after {
	opacity: 0.8;
}

.thumb-info .thumb-info-wrapper-opacity-9:after {
	opacity: 0.9;
}

.thumb-info .thumb-info-action-icon {
	transition: all 0.3s;
	background: var(--grey-500);
	border-radius: 0 0 0 4px;
	display: inline-block;
	font-size: 25px;
	height: 50px;
	line-height: 50px;
	position: absolute;
	right: -100px;
	text-align: center;
	top: -100px;
	width: 50px;
	z-index: 2;
}

.thumb-info .thumb-info-action-icon i {
	font-size: 14px;
	right: 50%;
	position: absolute;
	top: 50%;
	color: var(--light);
	transform: translate(50%, -50%);
	margin: -2px -1px 0 0px;
}

.thumb-info .thumb-info-inner {
	transition: all 0.3s;
	display: block;
}

.thumb-info .thumb-info-inner em {
	display: block;
	font-size: 0.8em;
	font-style: normal;
	font-weight: normal;
}

.thumb-info .thumb-info-title {
	transition: all 0.3s;
	background: var(--dark-rgba-80);
	color: var(--light);
	font-weight: 600;
	left: 0;
	letter-spacing: -.05em;
	position: absolute;
	z-index: 2;
	max-width: 90%;
	font-size: 17px;
	padding: 13px 21px 2px;
	bottom: 13%;
}

.thumb-info .thumb-info-title.thumb-info-title-vert-align {
	bottom: auto;
	top: 50%;
	transform: translateY(-50%);
}

.thumb-info .thumb-info-title.thumb-info-title-top {
	bottom: auto;
	top: 13%;
}

.thumb-info .thumb-info-type {
	background-color: var(--grey-500);
	border-radius: 2px;
	display: inline-block;
	float: left;
	font-size: 0.6em;
	font-weight: 600;
	letter-spacing: 0;
	margin: 8px -2px -15px 0px;
	padding: 2px 12px;
	text-transform: uppercase;
	z-index: 2;
	line-height: 2.3;
}

.thumb-info .owl-carousel {
	z-index: auto;
}

.thumb-info .owl-carousel .owl-dots {
	z-index: 2;
}

.thumb-info img {
	transition: all 0.3s ease;
	border-radius: 3px;
	position: relative;
	width: 100%;
}

.thumb-info:hover .thumb-info-wrapper:after {
	opacity: 1;
}

.thumb-info:hover .thumb-info-action-icon {
	right: 0;
	top: 0;
}

.thumb-info:hover .thumb-info-title {
	background: var(--dark-rgba-90);
}

.thumb-info:hover img {
	transform: scale(1.1, 1.1);
}

.thumb-info.thumb-info-no-zoom img {
	transition: none;
}

.thumb-info.thumb-info-no-zoom:hover img {
	transform: scale(1, 1);
}

.thumb-info.thumb-info-lighten .thumb-info-wrapper:after {
	background: rgba(23, 23, 23, 0.25);
}

.thumb-info.thumb-info-hide-wrapper-bg .thumb-info-wrapper:after {
	display: none;
}

.thumb-info.thumb-info-centered-icons .thumb-info-action {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 50%;
	left: 0;
	right: auto;
	bottom: auto;
	width: 100%;
	height: 45px;
	text-align: center;
	opacity: 0;
	transition: transform .2s ease-out, opacity .2s ease-out;
	transform: translate3d(0, -70%, 0);
	z-index: 2;
}

.thumb-info.thumb-info-centered-icons .thumb-info-action a {
	text-decoration: none;
}

.thumb-info.thumb-info-centered-icons .thumb-info-action-icon {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	position: relative;
	left: auto;
	right: auto;
	top: auto;
	bottom: auto;
	width: 35px;
	height: 35px;
	font-size: 14px;
	border-radius: 50%;
	margin-right: 5px;
	transition: all 0.1s ease;
	z-index: 2;
}

.thumb-info.thumb-info-centered-icons .thumb-info-action-icon i {
	margin: 0;
}

.thumb-info.thumb-info-centered-icons .thumb-info-action-icon:hover {
	transform: scale(1.15, 1.15);
}

.thumb-info:hover.thumb-info-centered-icons .thumb-info-action {
	opacity: 1;
	transform: translate3d(0, -50%, 0);
}

.thumb-info:hover.thumb-info-centered-icons .thumb-info-action-icon {
	position: relative;
}

.thumb-info.thumb-info-centered-info .thumb-info-title {
	background: transparent;
	left: 5%;
	width: 90%;
	transition: transform .2s ease-out, opacity .2s ease-out;
	transform: translate(0%, -50%);
	top: 50%;
	bottom: auto;
	opacity: 0;
	text-align: center;
	padding: 0;
	position: absolute;
}

.thumb-info.thumb-info-centered-info .thumb-info-type {
	float: none;
}

.thumb-info.thumb-info-centered-info:hover .thumb-info-title {
	opacity: 1;
	transform: translate(0%, -50%);
}

.thumb-info.thumb-info-bottom-info .thumb-info-title {
	background: var(--light);
	left: 0;
	width: 100%;
	max-width: 100%;
	transition: transform .2s ease-out, opacity .2s ease-out;
	transform: translate3d(0, 100%, 0);
	top: auto;
	bottom: 0;
	opacity: 0;
	padding: 15px;
	text-shadow: none;
	color: var(--default);
}

.thumb-info.thumb-info-bottom-info .thumb-info-type {
	float: none;
	background: none;
	padding: 0;
	margin: 0;
}

.thumb-info.thumb-info-bottom-info:hover .thumb-info-title {
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.thumb-info.thumb-info-bottom-info:hover img {
	transform: scale(1.1);
}

.thumb-info.thumb-info-show-button-hover .btn {
	opacity: 0;
}

.thumb-info.thumb-info-show-button-hover .btn, .thumb-info.thumb-info-show-button-hover .thumb-info-type {
	transition: opacity .3s ease-out;
}

.thumb-info.thumb-info-show-button-hover .thumb-info-title {
	transform: translate3d(0, 35px, 0);
	transition: transform .3s ease-out;
	bottom: 0;
}

.thumb-info.thumb-info-show-button-hover:hover .btn {
	opacity: 1;
}

.thumb-info.thumb-info-show-button-hover:hover .thumb-info-type {
	opacity: 0;
}

.thumb-info.thumb-info-show-button-hover:hover .thumb-info-title {
	transform: translate3d(0, -20px, 0);
}

.thumb-info.thumb-info-show-hidden-content-hover {
	display: block;
}

.thumb-info.thumb-info-show-hidden-content-hover .thumb-info-content-inner {
	display: block;
	transform: perspective(500px) translate3d(0, 0, 0px);
	transition: transform .3s ease-out;
}

.thumb-info.thumb-info-show-hidden-content-hover .thumb-info-content-inner .thumb-info-content-inner-hidden {
	transition: all .3s ease-out;
	opacity: 0;
}

.thumb-info.thumb-info-show-hidden-content-hover:hover .thumb-info-content-inner {
	transform: translate3d(0, -50px, 0);
}

.thumb-info.thumb-info-show-hidden-content-hover:hover .thumb-info-content-inner .thumb-info-content-inner-hidden {
	opacity: 1;
}

.thumb-info.thumb-info-bottom-info-linear .thumb-info-title {
	background: linear-gradient(to top, rgba(255, 255, 255, 0.8) 0, transparent 100%) repeat scroll 0 0 rgba(255, 255, 255, 0);
}

.thumb-info.thumb-info-bottom-info-dark .thumb-info-title {
	background: var(--dark);
	color: var(--light);
}

.thumb-info.thumb-info-bottom-info-dark-linear .thumb-info-title {
	background: linear-gradient(to top, var(--dark-rgba-80) 0, transparent 100%) repeat scroll 0 0 rgba(0, 0, 0, 0);
}

.thumb-info.thumb-info-bottom-info-show-more img {
	transform: none !important;
}

.thumb-info.thumb-info-bottom-info-show-more .thumb-info-title {
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.thumb-info.thumb-info-bottom-info-show-more .thumb-info-show-more-content {
	transition: ease max-height 800ms;
	overflow: hidden;
	max-height: 0;
	display: block;
}

.thumb-info.thumb-info-bottom-info-show-more:hover .thumb-info-show-more-content {
	max-height: 300px;
}

.thumb-info.thumb-info-push-hor img {
	transform: translatex(-18px);
	width: calc(100% + 19px);
	max-width: none;
}

.thumb-info.thumb-info-push-hor:hover img {
	transform: translatex(0);
}

.thumb-info.thumb-info-block .thumb-info-action-icon {
	top: 15px;
	right: 15px;
	opacity: 0;
	transition: all 0.2s ease-in-out;
}

.thumb-info.thumb-info-block .thumb-info-wrapper:before {
	content: "";
	background: rgba(255, 255, 255, 0.9);
	transition: all 0.3s;
	border-radius: 0;
	bottom: 16px;
	bottom: 1rem;
	color: var(--light);
	left: 16px;
	left: 1rem;
	position: absolute;
	right: 16px;
	right: 1rem;
	top: 16px;
	top: 1rem;
	display: block;
	opacity: 0;
	z-index: 2;
}

.thumb-info.thumb-info-block:hover .thumb-info-wrapper:before {
	opacity: 1;
}

.thumb-info.thumb-info-block:hover .thumb-info-action-icon {
	opacity: 1;
}

.thumb-info.thumb-info-block.thumb-info-block-dark .thumb-info-wrapper:before {
	background: var(--dark-rgba-10);
}

.thumb-info.thumb-info-hide-info-hover .thumb-info-wrapper:after {
	opacity: 0.65;
}

.thumb-info.thumb-info-hide-info-hover .thumb-info-title {
	opacity: 1;
	top: 50%;
}

.thumb-info.thumb-info-hide-info-hover .thumb-info-type {
	float: none;
}

.thumb-info.thumb-info-hide-info-hover:hover .thumb-info-wrapper:after {
	opacity: 0.1;
}

.thumb-info.thumb-info-hide-info-hover:hover .thumb-info-title {
	opacity: 0;
}

.thumb-info.thumb-info-slide-info-hover .thumb-info-wrapper:after {
	opacity: 0.65;
}

.thumb-info.thumb-info-slide-info-hover .thumb-info-wrapper-no-opacity:after {
	opacity: 1;
}

.thumb-info.thumb-info-slide-info-hover .thumb-info-title {
	opacity: 1;
	top: 0;
	height: 100%;
	top: 0;
	transform: none !important;
}

.thumb-info.thumb-info-slide-info-hover .thumb-info-type {
	float: none;
}

.thumb-info.thumb-info-slide-info-hover .thumb-info-slide-info-hover-1,
.thumb-info.thumb-info-slide-info-hover .thumb-info-slide-info-hover-2 {
	transition: cubic-bezier(0.55, 0, 0.1, 1) top 500ms;
	position: absolute;
	left: 0;
	width: 100%;
	text-align: center;
	top: 50%;
	transform: translate(0%, -50%);
}

.thumb-info.thumb-info-slide-info-hover .thumb-info-slide-info-hover-2 {
	top: 150%;
}

.thumb-info.thumb-info-slide-info-hover:hover .thumb-info-slide-info-hover-1 {
	top: -50%;
}

.thumb-info.thumb-info-slide-info-hover:hover .thumb-info-slide-info-hover-2 {
	top: 50%;
}

.thumb-info.thumb-info-show-image-hover img {
	opacity: 0;
}

.thumb-info.thumb-info-show-image-hover .thumb-info-title {
	background: transparent;
	color: var(--dark--200);
}

.thumb-info.thumb-info-show-image-hover .thumb-info-inner {
	transition: cubic-bezier(0.55, 0, 0.1, 1) top 500ms;
}

.thumb-info.thumb-info-show-image-hover:hover img {
	opacity: 1;
}

.thumb-info.thumb-info-show-image-hover:hover .thumb-info-title {
	color: var(--light);
}

.thumb-info.thumb-info-no-borders, .thumb-info.thumb-info-no-borders img {
	border: 0;
	padding: 0;
	margin: 0;
	border-radius: 0;
}

.thumb-info.thumb-info-no-borders .thumb-info-wrapper {
	border: 0;
	padding: 0;
	margin: 0;
	border-radius: 0;
}

.thumb-info.thumb-info-no-borders .thumb-info-wrapper:after {
	border-radius: 0;
	bottom: 0;
	left: 0;
	right: 0;
	top: 0;
}

.thumb-info.thumb-info-no-borders.thumb-info-no-borders-rounded,
.thumb-info.thumb-info-no-borders.thumb-info-no-borders-rounded img,
.thumb-info.thumb-info-no-borders.thumb-info-no-borders-rounded .thumb-info-wrapper,
.thumb-info.thumb-info-no-borders.thumb-info-no-borders-rounded .thumb-info-wrapper:after {
	border-radius: var(--border-radius);
}

.thumb-info.thumb-info-preview .thumb-info-wrapper:after {
	display: none;
}

.thumb-info.thumb-info-preview .thumb-info-image {
	min-height: 232px;
	display: block;
	background-position: center top;
	background-repeat: no-repeat;
	background-size: 100% auto;
	position: relative;
	transition: background-position 0.8s linear 0s;
}

.thumb-info.thumb-info-preview:hover .thumb-info-image {
	transition: background-position 2.5s linear 0s;
	background-position: center bottom;
}

.thumb-info.thumb-info-preview.thumb-info-preview-short:hover .thumb-info-image {
	transition: background-position 1s linear 0s;
}

.thumb-info.thumb-info-preview.thumb-info-preview-long:hover .thumb-info-image {
	transition: background-position 5s linear 0s;
}

.thumb-info.thumb-info-slow-image-zoom-hover img {
	transition: transform 2s;
	will-change: transform;
	transform: scale(1, 1);
}

.thumb-info.thumb-info-slow-image-zoom-hover:hover img {
	transform: scale(1.5, 1.5);
}

.thumb-info.thumb-info-no-overlay .thumb-info-wrapper:after {
	content: none;
}

.thumb-info.thumb-info-no-bg {
	background: transparent;
}

.thumb-info.thumb-info-swap-content .thumb-info-swap-content-wrapper > * {
	transition: cubic-bezier(0.55, 0, 0.1, 1) all 300ms;
}

.thumb-info.thumb-info-swap-content .thumb-info-swap-content-wrapper > :nth-child(1) {
	max-width: 100%;
	text-align: center;
	bottom: 0%;
	margin: 0;
	position: absolute;
	width: 100%;
	left: 0;
}

.thumb-info.thumb-info-swap-content .thumb-info-swap-content-wrapper > :nth-child(2) {
	display: inline-block;
	transform: translateY(120%);
	opacity: 0;
}

.thumb-info.thumb-info-swap-content:hover .thumb-info-swap-content-wrapper > :nth-child(1) {
	transform: translateY(-120%);
	opacity: 0;
}

.thumb-info.thumb-info-swap-content:hover .thumb-info-swap-content-wrapper > :nth-child(2) {
	transform: none;
	opacity: 1;
}

/* Thumb Info Ribbon */
.thumb-info-ribbon {
	background: #999;
	position: absolute;
	margin: -16px 0 0 0;
	padding: 5px 13px 6px;
	right: 15px;
	z-index: 1;
}

.thumb-info-ribbon:before {
	border-right: 10px solid #646464;
	border-top: 16px solid transparent;
	content: "";
	display: block;
	height: 0;
	left: -10px;
	position: absolute;
	top: 0;
	width: 7px;
}

.thumb-info-ribbon span {
	color: var(--light);
	font-size: 1.1em;
	font-family: "Shadows Into Light", cursive;
}

/* Thumb Info - Full Width */
.full-width .thumb-info img {
	border-radius: 0 !important;
}

/* Thumb Info Caption	*/
.thumb-info-caption {
	padding: 10px 0;
}

.thumb-info-caption .thumb-info-caption-text, .thumb-info-caption p {
	font-size: 0.9em;
	margin: 0;
	padding: 15px 0;
	display: block;
}

/* Thumb Info Side Image	*/
.thumb-info-side-image {
	display: flex;
}

.thumb-info-side-image .thumb-info-side-image-wrapper {
	padding: 0;
	margin-right: 20px;
}

.thumb-info-side-image-right {
	display: flex;
}

.thumb-info-side-image-right .thumb-info-side-image-wrapper {
	margin-left: 20px;
	margin-right: 0;
}

/* Thumb Info Social Icons */
.thumb-info-social-icons {
	margin: 0;
	padding: 0;
	display: block;
}

.thumb-info-social-icons a {
	background: var(--grey-500);
	border-radius: 25px;
	display: inline-block;
	height: 30px;
	line-height: 30px;
	text-align: center;
	width: 30px;
}

.thumb-info-social-icons a:hover {
	text-decoration: none;
}

.thumb-info-social-icons a span {
	display: none;
}

.thumb-info-social-icons a i {
	color: var(--light);
	font-size: 0.9em;
	font-weight: normal;
}

/* Thumb Info Floating Caption */
.thumb-info-floating-caption-title {
	background: var(--dark-rgba-80);
	color: var(--light);
	font-weight: 600;
	letter-spacing: -.05em;
	font-size: 17px;
	padding: 13px 21px;
}

.thumb-info-floating-caption-title .thumb-info-floating-caption-type {
	position: absolute;
	background-color: var(--grey-500);
	border-radius: 2px;
	display: block;
	font-size: 0.6em;
	font-weight: 600;
	letter-spacing: 0;
	margin: 8px -2px -15px 0px;
	padding: 2px 12px;
	text-transform: uppercase;
	line-height: 2.3;
}

@media (max-width: 991px) {
	.thumb-info .thumb-info-title {
		font-size: 14px;
	}

	.thumb-info .thumb-info-more {
		font-size: 11px;
	}
}

.thumb-info-container-full-img {
	overflow: hidden;
	position: relative;
}

.thumb-info-container-full-img .thumb-info-container-full-img-large {
	opacity: 0;
	transform: scale(1.025);
	transition: all 0.85s cubic-bezier(0.23, 1, 0.32, 1);
	transition-property: opacity, transform;
	display: flex;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-size: cover;
	background-position: 50% 0;
}

.thumb-info-container-full-img .thumb-info-container-full-img-large.active {
	opacity: 1 !important;
}

.thumb-info-container-full-img .thumb-info-wrapper img {
	transition: all 0.85s cubic-bezier(0.23, 1, 0.32, 1);
	transition-property: opacity, transform;
}

.thumb-info-container-full-img .owl-stage-outer:hover .thumb-info-wrapper img {
	opacity: 0;
}

/* Thumb - Skin */
.thumb-info .thumb-info-type,
.thumb-info .thumb-info-action-icon,
.thumb-info-social-icons a,
.thumbnail .zoom,
.img-thumbnail .zoom,
.thumb-info-ribbon {
	background-color: var(--primary);
}

.thumb-info .thumb-info-action-icon-primary {
	background-color: var(--primary);
}

.thumb-info .thumb-info-action-icon-primary i {
	color: var(--primary-inverse);
}

.thumb-info .thumb-info-action-icon-secondary {
	background-color: var(--secondary);
}

.thumb-info .thumb-info-action-icon-secondary i {
	color: var(--secondary-inverse);
}

.thumb-info .thumb-info-action-icon-tertiary {
	background-color: var(--tertiary);
}

.thumb-info .thumb-info-action-icon-tertiary i {
	color: var(--tertiary-inverse);
}

.thumb-info .thumb-info-action-icon-quaternary {
	background-color: var(--quaternary);
}

.thumb-info .thumb-info-action-icon-quaternary i {
	color: var(--quaternary-inverse);
}

.thumb-info .thumb-info-action-icon-dark {
	background-color: var(--dark);
}

.thumb-info .thumb-info-action-icon-dark i {
	color: var(--dark-inverse);
}

.thumb-info .thumb-info-action-icon-light {
	background-color: var(--light);
}

.thumb-info .thumb-info-action-icon-light i {
	color: var(--light-inverse);
}

.thumb-info-ribbon:before {
	border-right-color: var(--primary-300);
	border-left-color: var(--primary-300);
}

.thumb-info.thumb-info-block-primary .thumb-info-wrapper:before {
	background: var(--primary-rgba-90);
}

/* Thumb Info Floating Caption - Skin */
.thumb-info-floating-caption-title .thumb-info-floating-caption-type {
	background-color: var(--primary);
}

/* Timeline */
section.timeline {
	width: 100%;
	margin: 50px 0 0;
	position: relative;
	float: left;
	padding-bottom: 120px;
}

section.timeline:before {
	background: #505050;
	background: linear-gradient(to bottom, rgba(80, 80, 80, 0) 0%, #505050 8%, #505050 92%, rgba(80, 80, 80, 0) 100%);
	content: "";
	display: block;
	height: 100%;
	left: 50%;
	margin-left: -2px;
	position: absolute;
	top: -60px;
	width: 3px;
	z-index: 0;
	filter: alpha(opacity=35);
	opacity: 0.35;
}

section.timeline .timeline-body {
	position: relative;
	z-index: 1 !important;
}

section.timeline .timeline-date {
	box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.04);
	position: relative;
	display: block;
	clear: both;
	width: 200px;
	height: 45px;
	padding: 5px;
	border: 1px solid #E5E5E5;
	margin: 0 auto 10px;
	text-shadow: 0 1px 1px #fff;
	border-radius: var(--border-radius);
	background: var(--light);
	z-index: 1;
}

section.timeline .timeline-date h3 {
	display: block;
	text-align: center;
	color: #757575;
	font-size: 0.9em;
	line-height: 32px;
}

section.timeline .timeline-number {
	position: relative;
	display: block;
	clear: both;
	width: 55px;
	height: 55px;
	line-height: 55px;
	margin: 0 auto 10px;
	border-radius: 100%;
	background: var(--primary);
	z-index: 1;
}

section.timeline .timeline-number h3 {
	line-height: 55px;
	font-weight: bold;
	text-align: center;
}

section.timeline .timeline-title {
	background: var(--grey-100);
	padding: 12px;
}

section.timeline .timeline-title h4 {
	padding: 0;
	margin: 0;
	color: #151719;
	font-size: 1.4em;
}

section.timeline .timeline-title a {
	color: #151719;
}

section.timeline .timeline-title .timeline-title-tags {
	color: #B1B1B1;
	font-size: 0.9em;
}

section.timeline .timeline-title .timeline-title-tags a {
	color: #B1B1B1;
	font-size: 0.9em;
}

section.timeline .timeline-box {
	box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.04);
	border: 1px solid #E5E5E5;
	border-radius: var(--border-radius);
	margin: 20px 10px 10px 10px;
	padding: 10px;
	width: 45.7%;
	float: left;
	z-index: 1;
	margin-bottom: 30px;
	background: var(--light);
}

section.timeline .timeline-box.left {
	clear: both;
}

section.timeline .timeline-box.left:before {
	background: none repeat scroll 0 0 #E5E5E5;
	border-radius: 50%;
	box-shadow: 0 0 0 3px #FFF, 0 0 0 6px #E5E5E5;
	content: "";
	display: block;
	height: 7px;
	margin-right: -5px;
	position: absolute;
	left: 50%;
	margin-top: 22px;
	width: 8px;
	transform: translate3d(-50%, 0, 0);
	z-index: 1;
}

section.timeline .timeline-box.left .timeline-box-arrow {
	transform: rotate(45deg);
	content: '';
	display: block;
	background: var(--light);
	border-right: 1px solid #E5E5E5;
	border-top: 1px solid #E5E5E5;
	float: right;
	height: 14px;
	margin-top: 17px;
	margin-right: -18px;
	width: 14px;
	z-index: 0;
}

section.timeline .timeline-box.right {
	clear: right;
	float: right;
	right: -1px;
	margin-top: 40px;
}

section.timeline .timeline-box.right:before {
	background: none repeat scroll 0 0 #E5E5E5;
	border-radius: 50%;
	box-shadow: 0 0 0 3px #FFF, 0 0 0 6px #E5E5E5;
	content: "";
	display: block;
	height: 7px;
	position: absolute;
	left: 50%;
	margin-top: 32px;
	width: 8px;
	transform: translate3d(-50%, 0, 0);
	z-index: 1;
}

section.timeline .timeline-box.right .timeline-box-arrow {
	transform: rotate(45deg);
	content: '';
	display: block;
	background: var(--light);
	border-left: 1px solid #E5E5E5;
	border-bottom: 1px solid #E5E5E5;
	float: left;
	height: 14px;
	margin-top: 28px;
	margin-left: -18px;
	width: 14px;
	z-index: 0;
}

section.timeline .timeline-box .owl-carousel {
	border-radius: 0;
	padding: 0;
	border: 0;
	box-shadow: none;
}

@media (max-width: 991px) {
	section.timeline .timeline-box {
		float: none;
		clear: both;
		right: auto;
		left: auto;
		margin: 25px auto;
		max-width: 70%;
		width: auto;
	}

	section.timeline .timeline-box.right {
		float: none;
		clear: both;
		right: auto;
		left: auto;
		margin: 25px auto;
		max-width: 70%;
		width: auto;
	}

	section.timeline .timeline-box.left:after, section.timeline .timeline-box.right:after, section.timeline .timeline-box.left:before, section.timeline .timeline-box.right:before {
		display: none;
	}
}

@media (max-width: 767px) {
	section.timeline {
		width: 100%;
		margin: 0;
	}
}

@media (max-width: 575px) {
	section.timeline {
		width: auto;
		float: none;
		margin-left: 0;
		margin-right: 0;
	}

	section.timeline .timeline-box {
		margin: 15px auto;
		max-width: 90%;
	}

	section.timeline .timeline-box.right {
		margin: 15px auto;
		max-width: 90%;
	}

	article.post-large {
		margin-left: 0;
	}

	article.post-large .post-image, article.post-large .post-date {
		margin-left: 0;
	}
}

/* Timeline - Skin */
section.timeline .timeline-box.left:before,
section.timeline .timeline-box.right:before {
	background: var(--primary);
	box-shadow: 0 0 0 3px #FFF, 0 0 0 6px var(--primary);
}

/* History - Skin */
ul.history li .featured-box .box-content {
	border-top-color: var(--primary);
}

/* Toggles */
.toggle {
	margin: 10px 0 20px;
	position: relative;
	clear: both;
}

.toggle .toggle {
	margin: 10px 0 0;
}

.toggle > input {
	cursor: pointer;
	filter: alpha(opacity=0);
	height: 45px;
	margin: 0;
	opacity: 0;
	position: absolute;
	width: 100%;
	z-index: 2;
}

.toggle > label,
.toggle > .toggle-title {
	transition: all .15s ease-out;
	background: var(--grey-100);
	border-left: 3px solid var(--grey-500);
	border-radius: 5px;
	color: var(--grey-500);
	display: block;
	min-height: 20px;
	padding: 12px 20px 12px 15px;
	position: relative;
	cursor: pointer;
	font-weight: 600;
	letter-spacing: -0.2px;
	text-decoration: none !important;
}

.toggle > label:-moz-selection,
.toggle > .toggle-title:-moz-selection {
	background: none;
}

.toggle > label i.fa-minus,
.toggle > .toggle-title i.fa-minus {
	display: none;
}

.toggle > label i.fa-plus,
.toggle > .toggle-title i.fa-plus {
	display: inline;
}

.toggle > label:before,
.toggle > .toggle-title:before {
	content: " ";
	position: absolute;
	top: calc(50% + 1px);
	right: 14px;
	border-color: var(--grey-500);
	border-top: 1px solid;
	border-right: 1px solid;
	width: 8px;
	height: 8px;
	backface-visibility: hidden;
	transition: transform 0.3s, top 0.3s;
	transform: rotate(-45deg) translate3d(0, -50%, 0);
	transform-origin: 35%;
}

.toggle > label:hover,
.toggle > .toggle-title:hover {
	background: #f5f5f5;
}

.toggle > label + p,
.toggle > .toggle-title + p {
	color: #999;
	display: block;
	overflow: hidden;
	padding-left: 30px;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 25px;
}

.toggle > label i,
.toggle > .toggle-title i {
	font-size: 0.7em;
	margin-right: 8px;
	position: relative;
	top: -1px;
}

.toggle > .toggle-content {
	display: none;
}

.toggle > .toggle-content > p {
	margin-bottom: 0;
	padding: 10px 0;
}

.toggle.active i.fa-minus {
	display: inline;
}

.toggle.active i.fa-plus {
	display: none;
}

.toggle.active > label,
.toggle.active > .toggle-title {
	background: var(--grey-100);
	border-color: var(--grey-500);
}

.toggle.active > label:before,
.toggle.active > .toggle-title:before {
	top: calc(50% - 8px);
	transform: rotate(135deg);
}

.toggle.active > p {
	white-space: normal;
}

.toggle > p.preview-active {
	height: auto;
	white-space: normal;
}

.toggle.toggle-sm .toggle > label,
.toggle.toggle-sm .toggle > .toggle-title {
	font-size: 0.9em;
	padding: 6px 20px 6px 10px;
}

.toggle.toggle-sm .toggle > .toggle-content > p {
	font-size: 0.9em;
}

.toggle.toggle-lg .toggle > label,
.toggle.toggle-lg .toggle > .toggle-title {
	font-size: 1.1em;
	padding: 16px 20px 16px 20px;
}

.toggle.toggle-lg .toggle > .toggle-content > p {
	font-size: 1.1em;
}

.toggle.toggle-simple .toggle > label,
.toggle.toggle-simple .toggle > .toggle-title {
	border-radius: 0;
	border: 0;
	background: transparent;
	color: var(--default);
	padding-left: 32px;
	margin-bottom: -10px;
	font-size: 1em;
	font-weight: 600;
	color: var(--dark);
}

.toggle.toggle-simple .toggle > label:after,
.toggle.toggle-simple .toggle > .toggle-title:after {
	content: '';
	position: absolute;
	left: 0;
	display: block;
	width: 24px;
	height: 24px;
	background: var(--grey-500);
	top: 25px;
	margin-top: -12px;
	border-radius: 3px;
	z-index: 1;
}

.toggle.toggle-simple .toggle > label:before,
.toggle.toggle-simple .toggle > .toggle-title:before {
	border: 0;
	left: 9px;
	right: auto;
	margin-top: 0;
	top: 12px;
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	content: "\f067";
	z-index: 2;
	color: var(--light);
	font-size: 7px;
	width: auto;
	height: auto;
	transform: none;
}

.toggle.toggle-simple .toggle.active > label,
.toggle.toggle-simple .toggle.active > .toggle-title {
	background: transparent;
	color: var(--dark);
}

.toggle.toggle-simple .toggle.active > label:before,
.toggle.toggle-simple .toggle.active > .toggle-title:before {
	content: "\f068";
}

.toggle.toggle-minimal .toggle {
	margin: 0;
}

.toggle.toggle-minimal .toggle > label,
.toggle.toggle-minimal .toggle > .toggle-title {
	background: transparent;
	color: var(--default);
	border-left: 0;
	border-bottom: 1px solid var(--grey-500);
	border-radius: 0;
	margin: 0;
	padding: 12px 20px 12px 0;
	font-weight: 600;
}

.toggle.toggle-minimal .toggle.active > label,
.toggle.toggle-minimal .toggle.active > .toggle-title {
	background: transparent;
	border-bottom-color: var(--grey-500);
}

.toggle.toggle-faqs .toggle {
	border-bottom: 1px solid var(--grey-300);
	margin-bottom: 10px;
	padding-bottom: 10px;
}

.toggle.toggle-faqs .toggle:last-child {
	border-bottom: 0;
}

.toggle.toggle-faqs .toggle > label,
.toggle.toggle-faqs .toggle > .toggle-title {
	border: 0;
	padding-right: 35px;
}

.toggle.toggle-faqs .toggle > label:before,
.toggle.toggle-faqs .toggle > .toggle-title:before {
	font-family: 'Font Awesome 6 Free';
	content: "\2b";
	border: 0;
	transform: none;
	top: 50%;
	right: 0px;
	width: auto;
	height: auto;
	transform: translateY(-50%);
}

.toggle.toggle-faqs .toggle.active > label,
.toggle.toggle-faqs .toggle.active > .toggle-title {
	border: 0;
}

.toggle.toggle-faqs .toggle.active > label:before,
.toggle.toggle-faqs .toggle.active > .toggle-title:before {
	content: "\f068";
}

.toggle.toggle-faqs .toggle > .toggle-content > p {
	padding-top: 5px;
}

/* Toggle - Skin */
.toggle-primary .toggle label,
.toggle-primary .toggle .toggle-title {
	color: var(--primary);
	border-left-color: var(--primary);
	border-right-color: var(--primary);
}

.toggle-primary .toggle.active > label,
.toggle-primary .toggle.active > .toggle-title {
	background-color: var(--primary);
	border-color: var(--primary);
	color: var(--primary-inverse);
}

.toggle-primary.toggle-simple .toggle > label:after,
.toggle-primary.toggle-simple .toggle > .toggle-title:after {
	background-color: var(--primary);
}

.toggle-primary.toggle-minimal .toggle.active > label,
.toggle-primary.toggle-minimal .toggle.active > .toggle-title {
	color: var(--primary);
}

.toggle-secondary .toggle label,
.toggle-secondary .toggle .toggle-title {
	color: var(--secondary);
	border-left-color: var(--secondary);
	border-right-color: var(--secondary);
}

.toggle-secondary .toggle.active > label,
.toggle-secondary .toggle.active > .toggle-title {
	background-color: var(--secondary);
	border-color: var(--secondary);
	color: var(--secondary-inverse);
}

.toggle-secondary.toggle-simple .toggle > label:after,
.toggle-secondary.toggle-simple .toggle > .toggle-title:after {
	background-color: var(--secondary);
}

.toggle-secondary.toggle-minimal .toggle.active > label,
.toggle-secondary.toggle-minimal .toggle.active > .toggle-title {
	color: var(--secondary);
}

.toggle-tertiary .toggle label,
.toggle-tertiary .toggle .toggle-title {
	color: var(--tertiary);
	border-left-color: var(--tertiary);
	border-right-color: var(--tertiary);
}

.toggle-tertiary .toggle.active > label,
.toggle-tertiary .toggle.active > .toggle-title {
	background-color: var(--tertiary);
	border-color: var(--tertiary);
	color: var(--tertiary-inverse);
}

.toggle-tertiary.toggle-simple .toggle > label:after,
.toggle-tertiary.toggle-simple .toggle > .toggle-title:after {
	background-color: var(--tertiary);
}

.toggle-tertiary.toggle-minimal .toggle.active > label,
.toggle-tertiary.toggle-minimal .toggle.active > .toggle-title {
	color: var(--tertiary);
}

.toggle-quaternary .toggle label,
.toggle-quaternary .toggle .toggle-title {
	color: var(--quaternary);
	border-left-color: var(--quaternary);
	border-right-color: var(--quaternary);
}

.toggle-quaternary .toggle.active > label,
.toggle-quaternary .toggle.active > .toggle-title {
	background-color: var(--quaternary);
	border-color: var(--quaternary);
	color: var(--quaternary-inverse);
}

.toggle-quaternary.toggle-simple .toggle > label:after,
.toggle-quaternary.toggle-simple .toggle > .toggle-title:after {
	background-color: var(--quaternary);
}

.toggle-quaternary.toggle-minimal .toggle.active > label,
.toggle-quaternary.toggle-minimal .toggle.active > .toggle-title {
	color: var(--quaternary);
}

.toggle-dark .toggle label,
.toggle-dark .toggle .toggle-title {
	color: var(--dark);
	border-left-color: var(--dark);
	border-right-color: var(--dark);
}

.toggle-dark .toggle.active > label,
.toggle-dark .toggle.active > .toggle-title {
	background-color: var(--dark);
	border-color: var(--dark);
	color: var(--dark-inverse);
}

.toggle-dark.toggle-simple .toggle > label:after,
.toggle-dark.toggle-simple .toggle > .toggle-title:after {
	background-color: var(--dark);
}

.toggle-dark.toggle-minimal .toggle.active > label,
.toggle-dark.toggle-minimal .toggle.active > .toggle-title {
	color: var(--dark);
}

.toggle-light .toggle label,
.toggle-light .toggle .toggle-title {
	color: var(--light);
	border-left-color: var(--light);
	border-right-color: var(--light);
}

.toggle-light .toggle.active > label,
.toggle-light .toggle.active > .toggle-title {
	background-color: var(--light);
	border-color: var(--light);
	color: var(--light-inverse);
}

.toggle-light.toggle-simple .toggle > label:after,
.toggle-light.toggle-simple .toggle > .toggle-title:after {
	background-color: var(--light);
}

.toggle-light.toggle-minimal .toggle.active > label,
.toggle-light.toggle-minimal .toggle.active > .toggle-title {
	color: var(--light);
}

/* Blockquotes */
blockquote {
	border-left: 5px solid #eee;
	margin: 0 0 16px 0;
	margin: 0 0 1rem 0;
	padding: 8px 16px;
	padding: 0.5rem 1rem;
	font-size: 1em;
}

blockquote.with-borders {
	border-radius: 6px;
	border-top: 1px solid #DFDFDF;
	border-bottom: 1px solid #DFDFDF;
	border-left: 1px solid #ECECEC;
	border-right: 1px solid #ECECEC;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.04);
	padding: 20.8px;
	padding: 1.3rem;
}

blockquote.blockquote-reverse {
	text-align: right;
	border-right: 5px solid #eee;
	border-left: 0px !important;
}

/* Small */
small, .small {
	font-size: 70%;
}

/* Paragraphs */
p {
	color: var(--default);
	line-height: 26px;
	margin: 0 0 20px;
}

p.featured {
	font-size: 1.6em;
	line-height: 1.5em;
}

p.lead {
	line-height: 28.8px;
	line-height: 1.8rem;
}

p .alternative-font {
	display: inline-block;
	margin-top: -15px;
	position: relative;
	top: 3px;
	margin-bottom: -6px;
}

/* Lead */
.lead {
	letter-spacing: -.05em;
	font-size: 19.2px;
	font-size: 1.2rem;
}

.lead-2 {
	font-size: 17.92px;
	font-size: 1.12rem;
}

/* Links */
a {
	color: var(--primary);
}

a:hover {
	color: var(--primary--100);
}

a:focus {
	color: var(--primary--100);
}

a:active {
	color: var(--primary-100);
}

/* Drop Caps */
p.drop-caps:first-letter {
	float: left;
	font-size: 75px;
	line-height: 60px;
	padding: 4px;
	margin-right: 5px;
	margin-top: 5px;
	font-family: Georgia;
}

p.drop-caps.drop-caps-style-2:first-letter {
	background-color: var(--primary);
	color: var(--light);
	padding: 6px;
	margin-right: 5px;
	border-radius: var(--border-radius);
}

/* Default Font Style */
.default-font {
	font-family: "Poppins", Arial, sans-serif !important;
}

/* Alternative Font Style */
.alternative-font {
	font-family: "Shadows Into Light", cursive !important;
	font-size: 1.6em;
}

.alternative-font-2 {
	font-family: "Oswald", sans-serif !important;
}

.alternative-font-3 {
	font-family: 'Handlee', cursive !important;
}

.alternative-font-4 {
	font-family: 'Poppins', Arial, sans-serif !important;
}

.alternative-font-5 {
	font-family: 'Open Sans', Arial, sans-serif !important;
}

.alternative-font-6 {
	font-family: 'Montserrat', Arial, sans-serif !important;
}

.alternative-font-7 {
	font-family: 'Jost', sans-serif !important;
}

.alternative-font-8 {
	font-family: 'Cormorant', serif !important;
}

/* Gradient Text */
.gradient-text-color {
	-webkit-background-clip: text !important;
	-webkit-text-fill-color: transparent !important;
}

.no-backgroundcliptext .gradient-text-color {
	background: transparent !important;
}

/* Small Sup style */
.sup-text-small {
	font-size: 0.5em;
	font-weight: 500;
	top: -.7em;
}

/* Text Color Default */
.text-default,
.text-color-default {
	color: var(--default) !important;
}

/* Marquee */
.marquee {
	overflow: hidden;
	white-space: nowrap;
}

/* Mark */
.mark.mark-gradient, .mark.mark-color {
	position: relative;
	z-index: 0;
	display: inline-flex;
	background-color: transparent;
}

.mark.mark-gradient:before, .mark.mark-color:before {
	content: '';
	height: 30%;
	width: 104%;
	bottom: 15%;
	position: absolute;
	left: -2%;
	z-index: -1;
}

.mark.mark-pos-2:before {
	width: 100%;
	bottom: 0%;
	left: 1%;
}

.mark-height-10:before {
	height: 10% !important;
}

.mark-height-20:before {
	height: 20% !important;
}

.mark-height-30:before {
	height: 30% !important;
}

.mark-height-40:before {
	height: 40% !important;
}

.mark-height-50:before {
	height: 50% !important;
}

.mark-height-60:before {
	height: 60% !important;
}

.mark-height-70:before {
	height: 70% !important;
}

.mark-height-80:before {
	height: 80% !important;
}

.mark-height-90:before {
	height: 90% !important;
}

.mark-height-100:before {
	height: 100% !important;
}

.mark.mark-color-primary:before {
	background: var(--primary);
}

.mark.mark-color-secondary:before {
	background: var(--secondary);
}

.mark.mark-color-tertiary:before {
	background: var(--tertiary);
}

.mark.mark-color-quaternary:before {
	background: var(--quaternary);
}

.mark.mark-color-dark:before {
	background: var(--dark);
}

.mark.mark-color-light:before {
	background: var(--light);
}

.mark.mark-gradient.mark-gradient-primary:before {
	background: var(--primary);
	background: linear-gradient(90deg, var(--primary) 0%, rgba(255, 255, 255, 0) 100%);
}

.mark.mark-gradient.mark-gradient-secondary:before {
	background: var(--secondary);
	background: linear-gradient(90deg, var(--secondary) 0%, rgba(255, 255, 255, 0) 100%);
}

.mark.mark-gradient.mark-gradient-tertiary:before {
	background: var(--tertiary);
	background: linear-gradient(90deg, var(--tertiary) 0%, rgba(255, 255, 255, 0) 100%);
}

.mark.mark-gradient.mark-gradient-quaternary:before {
	background: var(--quaternary);
	background: linear-gradient(90deg, var(--quaternary) 0%, rgba(255, 255, 255, 0) 100%);
}

.mark.mark-gradient.mark-gradient-dark:before {
	background: var(--dark);
	background: linear-gradient(90deg, var(--dark) 0%, rgba(255, 255, 255, 0) 100%);
}

.mark.mark-gradient.mark-gradient-light:before {
	background: var(--light);
	background: linear-gradient(90deg, var(--light) 0%, rgba(255, 255, 255, 0) 100%);
}

.mark.appear-animation:before {
	transition: width 1s cubic-bezier(0.55, 0, 0.1, 1);
	width: 0;
}

.mark.appear-animation.appear-animation-visible:before {
	transition: width 1s cubic-bezier(0.55, 0, 0.1, 1);
	width: 104%;
}

.mark.appear-animation.mark-pos-2.appear-animation-visible:before {
	width: 100%;
}

/* Text Stroke */
.text-stroke {
	-webkit-text-stroke-width: 1px;
	-webkit-text-stroke-color: var(--dark);
	-webkit-text-fill-color: transparent;
	stroke-width: 1px;
}

.text-stroke-width-2 {
	-webkit-text-stroke-width: 2px;
	stroke-width: 2px;
}

.text-stroke-width-3 {
	-webkit-text-stroke-width: 3px;
	stroke-width: 3px;
}

.text-stroke-color-primary {
	-webkit-text-stroke-color: var(--primary);
}

.text-stroke-color-secondary {
	-webkit-text-stroke-color: var(--secondary);
}

.text-stroke-color-tertiary {
	-webkit-text-stroke-color: var(--tertiary);
}

.text-stroke-color-quaternary {
	-webkit-text-stroke-color: var(--quaternary);
}

.text-stroke-color-dark {
	-webkit-text-stroke-color: var(--dark);
}

.text-stroke-color-light {
	-webkit-text-stroke-color: var(--light);
}

.text-stroke-gradient {
	-webkit-text-stroke-color: #00000000;
	stroke: #00000000;
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--primary) 0%, var(--dark) 80%);
	-webkit-background-clip: text;
	background-clip: text;
}

.text-stroke-gradient-primary-dark {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--primary) 0%, var(--dark) 80%);
}

.text-stroke-gradient-primary-light {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--primary) 0%, var(--light) 80%);
}

.text-stroke-gradient-secondary-dark {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--secondary) 0%, var(--dark) 80%);
}

.text-stroke-gradient-secondary-light {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--secondary) 0%, var(--light) 80%);
}

.text-stroke-gradient-tertiary-dark {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--tertiary) 0%, var(--dark) 80%);
}

.text-stroke-gradient-tertiary-light {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--tertiary) 0%, var(--light) 80%);
}

.text-stroke-gradient-quaternary-dark {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--quaternary) 0%, var(--dark) 80%);
}

.text-stroke-gradient-quaternary-light {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--quaternary) 0%, var(--light) 80%);
}

.text-stroke-gradient-dark-dark {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--dark) 0%, var(--dark) 80%);
}

.text-stroke-gradient-dark-light {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--dark) 0%, var(--light) 80%);
}

.text-stroke-gradient-light-dark {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--light) 0%, var(--dark) 80%);
}

.text-stroke-gradient-light-light {
	background-color: transparent;
	background-image: linear-gradient(180deg, var(--light) 0%, var(--light) 80%);
}

/* Video */
section.video {
	background-position: 50% 50%;
	background-repeat: no-repeat;
	padding: 50px 0;
	position: relative;
	overflow: hidden;
	width: 100%;
}

section.video .container {
	position: relative;
	z-index: 3;
}

html.boxed section.video > div:first-child {
	z-index: 0 !important;
}

html.boxed section.video > div:first-child video {
	z-index: 0 !important;
}

/* Video - Skin */
section.video blockquote i.fa-quote-left {
	color: var(--primary);
}

.word-rotator i, .word-rotator em, .word-rotator b {
	font-style: normal;
	font-weight: inherit;
}

.word-rotator-words {
	display: inline-flex !important;
	position: relative;
	text-align: left;
}

.word-rotator-words b {
	display: inline-block;
	position: absolute;
	white-space: nowrap;
	left: 0;
	top: 0;
}

.word-rotator-words b.is-visible {
	position: relative;
}

.word-rotator-words[class*="bg-"] {
	background: var(--grey-500);
}

.word-rotator-words[class*="bg-"] b {
	color: var(--light);
	padding: 1.6px 8px;
	padding: 0.1rem 0.5rem;
}

/* rotate-1 */
.word-rotator.rotate-1 .word-rotator-words {
	perspective: 300px;
}

.word-rotator.rotate-1 b {
	opacity: 0;
	transform-origin: 50% 100%;
	transform: rotateX(180deg);
}

.word-rotator.rotate-1 b.is-visible {
	opacity: 1;
	transform: rotateX(0deg);
	animation: word-rotator-rotate-1-in 1.2s;
}

.word-rotator.rotate-1 b.is-hidden {
	transform: rotateX(180deg);
	animation: word-rotator-rotate-1-out 1.2s;
}

@keyframes word-rotator-rotate-1-in {
	0% {
		transform: rotateX(180deg);
		opacity: 0;
	}

	35% {
		transform: rotateX(120deg);
		opacity: 0;
	}

	65% {
		opacity: 0;
	}

	100% {
		transform: rotateX(360deg);
		opacity: 1;
	}
}

@keyframes word-rotator-rotate-1-out {
	0% {
		transform: rotateX(0deg);
		opacity: 1;
	}

	35% {
		transform: rotateX(-40deg);
		opacity: 1;
	}

	65% {
		opacity: 0;
	}

	100% {
		transform: rotateX(180deg);
		opacity: 0;
	}
}

/* type */
.word-rotator.type .word-rotator-words {
	vertical-align: top;
	overflow: hidden;
	position: relative;
	padding-right: 3px;
}

.word-rotator.type .word-rotator-words::after {
	content: ' ';
	position: absolute;
	right: 0;
	top: 5%;
	height: 90%;
	width: 2px;
	display: block;
	background-color: transparent;
}

.word-rotator.type .word-rotator-words.waiting::after {
	animation: 1s word-rotator-pulse step-end infinite;
}

.word-rotator.type .word-rotator-words.selected {
	background-color: var(--dark) !important;
}

.word-rotator.type .word-rotator-words.selected::after {
	visibility: hidden;
}

.word-rotator.type .word-rotator-words.selected b {
	color: var(--light) !important;
}

.word-rotator.type b {
	visibility: hidden;
}

.word-rotator.type b.is-visible {
	visibility: visible;
}

.word-rotator.type i {
	position: absolute;
	visibility: hidden;
}

.word-rotator.type i.in {
	position: relative;
	visibility: visible;
}

.word-rotator.type.type-clean-light .word-rotator-words:after {
	width: 8px;
}

.word-rotator.type.type-clean-light .word-rotator-words.waiting::after {
	animation: 500ms word-rotator-pulse-light step-end infinite;
}

.word-rotator.type.type-clean-light .word-rotator-words.selected {
	background-color: transparent !important;
}

@keyframes word-rotator-pulse {
	from, to {
		background-color: transparent;
	}

	50% {
		background-color: black;
	}
}

@keyframes word-rotator-pulse-light {
	from, to {
		background-color: transparent;
	}

	50% {
		background-color: white;
	}
}

/* rotate-2 */
.word-rotator.rotate-2 .word-rotator-words {
	perspective: 300px;
}

.word-rotator.rotate-2 i, .word-rotator.rotate-2 em {
	display: inline-block;
	backface-visibility: hidden;
}

.word-rotator.rotate-2 b {
	opacity: 0;
}

.word-rotator.rotate-2 i {
	transform-style: preserve-3d;
	transform: translateZ(-20px) rotateX(90deg);
	opacity: 0;
}

.is-visible .word-rotator.rotate-2 i {
	opacity: 1;
}

.word-rotator.rotate-2 i.in {
	animation: word-rotator-rotate-2-in .4s forwards;
}

.word-rotator.rotate-2 i.out {
	animation: word-rotator-rotate-2-out .4s forwards;
}

.word-rotator.rotate-2 em {
	transform: translateZ(20px);
}

.no-csstransitions .word-rotator.rotate-2 i {
	transform: rotateX(0deg);
	opacity: 0;
}

.no-csstransitions .word-rotator.rotate-2 i em {
	transform: scale(1);
}

.no-csstransitions .word-rotator.rotate-2 .is-visible i {
	opacity: 1;
}

@keyframes word-rotator-rotate-2-in {
	0% {
		opacity: 0;
		transform: translateZ(-20px) rotateX(90deg);
	}

	60% {
		opacity: 1;
		transform: translateZ(-20px) rotateX(-10deg);
	}

	100% {
		opacity: 1;
		transform: translateZ(-20px) rotateX(0deg);
	}
}

@keyframes word-rotator-rotate-2-out {
	0% {
		opacity: 1;
		transform: translateZ(-20px) rotateX(0);
	}

	60% {
		opacity: 0;
		transform: translateZ(-20px) rotateX(-100deg);
	}

	100% {
		opacity: 0;
		transform: translateZ(-20px) rotateX(-90deg);
	}
}

/* loading-bar */
.word-rotator.loading-bar span {
	display: inline-block;
	padding: .2em 0;
}

.word-rotator.loading-bar .word-rotator-words {
	overflow: hidden;
	vertical-align: top;
}

.word-rotator.loading-bar .word-rotator-words::after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	height: 3px;
	width: 0;
	background: var(--dark);
	z-index: 2;
	transition: width .3s -0.1s;
}

.word-rotator.loading-bar .word-rotator-words.is-loading::after {
	width: 100%;
	transition: width 3s;
}

.word-rotator.loading-bar .word-rotator-words.bg-light::after {
	background-color: var(--dark) !important;
}

.word-rotator.loading-bar .word-rotator-words[class*="bg-"]:not(.bg-light)::after {
	background-color: var(--light) !important;
}

.word-rotator.loading-bar b {
	top: .2em;
	opacity: 0;
	transition: opacity .3s;
}

.word-rotator.loading-bar b.is-visible {
	opacity: 1;
	top: 0;
}

/* slide */
.word-rotator.slide span {
	display: inline-block;
	padding: .2em 0;
}

.word-rotator.slide .word-rotator-words {
	overflow: hidden;
	vertical-align: top;
}

.word-rotator.slide b {
	opacity: 0;
	top: .2em;
}

.word-rotator.slide b.is-visible {
	top: 0;
	opacity: 1;
	animation: slide-in .6s;
}

.word-rotator.slide b.is-hidden {
	animation: slide-out .6s;
}

@keyframes slide-in {
	0% {
		opacity: 0;
		transform: translateY(-100%);
	}

	60% {
		opacity: 1;
		transform: translateY(20%);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slide-out {
	0% {
		opacity: 1;
		transform: translateY(0);
	}

	60% {
		opacity: 0;
		transform: translateY(120%);
	}

	100% {
		opacity: 0;
		transform: translateY(100%);
	}
}

/* clip */
.word-rotator.clip span {
	display: inline-block;
	padding: .2em 0;
}

.word-rotator.clip .word-rotator-words {
	overflow: hidden;
	vertical-align: top;
}

.word-rotator.clip .word-rotator-words::after {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 2px;
	height: 100%;
	background-color: var(--dark);
}

.word-rotator.clip b {
	opacity: 0;
}

.word-rotator.clip b.is-visible {
	opacity: 1;
}

/* zoom */
.word-rotator.zoom .word-rotator-words {
	perspective: 300px;
}

.word-rotator.zoom b {
	opacity: 0;
}

.word-rotator.zoom b.is-visible {
	opacity: 1;
	animation: zoom-in .8s;
}

.word-rotator.zoom b.is-hidden {
	animation: zoom-out .8s;
}

@keyframes zoom-in {
	0% {
		opacity: 0;
		transform: translateZ(100px);
	}

	100% {
		opacity: 1;
		transform: translateZ(0);
	}
}

@keyframes zoom-out {
	0% {
		opacity: 1;
		transform: translateZ(0);
	}

	100% {
		opacity: 0;
		transform: translateZ(-100px);
	}
}

.word-rotator.rotate-3 .word-rotator-words {
	perspective: 300px;
}

.word-rotator.rotate-3 b {
	opacity: 0;
}

.word-rotator.rotate-3 i {
	display: inline-block;
	transform: rotateY(180deg);
	backface-visibility: hidden;
}

.is-visible .word-rotator.rotate-3 i {
	transform: rotateY(0deg);
}

.word-rotator.rotate-3 i.in {
	animation: word-rotator-rotate-3-in .6s forwards;
}

.word-rotator.rotate-3 i.out {
	animation: word-rotator-rotate-3-out .6s forwards;
}

.no-csstransitions .word-rotator.rotate-3 i {
	transform: rotateY(0deg);
	opacity: 0;
}

.no-csstransitions .word-rotator.rotate-3 .is-visible i {
	opacity: 1;
}

@keyframes word-rotator-rotate-3-in {
	0% {
		transform: rotateY(180deg);
	}

	100% {
		transform: rotateY(0deg);
	}
}

@keyframes word-rotator-rotate-3-out {
	0% {
		transform: rotateY(0);
	}

	100% {
		transform: rotateY(-180deg);
	}
}

/* scale */
.word-rotator.scale b {
	opacity: 0;
}

.word-rotator.scale i {
	display: inline-block;
	opacity: 0;
	transform: scale(0);
}

.is-visible .word-rotator.scale i {
	opacity: 1;
}

.word-rotator.scale i.in {
	animation: scale-up .6s forwards;
}

.word-rotator.scale i.out {
	animation: scale-down .6s forwards;
}

.no-csstransitions .word-rotator.scale i {
	transform: scale(1);
	opacity: 0;
}

.no-csstransitions .word-rotator.scale .is-visible i {
	opacity: 1;
}

@keyframes scale-up {
	0% {
		transform: scale(0);
		opacity: 0;
	}

	60% {
		transform: scale(1.2);
		opacity: 1;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

@keyframes scale-down {
	0% {
		transform: scale(1);
		opacity: 1;
	}

	60% {
		transform: scale(0);
		opacity: 0;
	}
}

/* push */
.word-rotator.push b {
	opacity: 0;
}

.word-rotator.push b.is-visible {
	opacity: 1;
	animation: push-in .6s;
}

.word-rotator.push b.is-hidden {
	animation: push-out .6s;
}

@keyframes push-in {
	0% {
		opacity: 0;
		transform: translateX(-100%);
	}

	60% {
		opacity: 1;
		transform: translateX(10%);
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes push-out {
	0% {
		opacity: 1;
		transform: translateX(0);
	}

	60% {
		opacity: 0;
		transform: translateX(110%);
	}

	100% {
		opacity: 0;
		transform: translateX(100%);
	}
}

/* Word Rotator - Skin */
.word-rotator.loading-bar-primary .word-rotator-words::after {
	background-color: var(--primary);
}

.word-rotator.loading-bar-secondary .word-rotator-words::after {
	background-color: var(--secondary);
}

.word-rotator.loading-bar-tertiary .word-rotator-words::after {
	background-color: var(--tertiary);
}

.word-rotator.loading-bar-quaternary .word-rotator-words::after {
	background-color: var(--quaternary);
}

.word-rotator.loading-bar-dark .word-rotator-words::after {
	background-color: var(--dark);
}

.word-rotator.loading-bar-light .word-rotator-words::after {
	background-color: var(--light);
}

/* Custom Map */
section.section-custom-map {
	background: transparent url(../img/map.png) center 0 no-repeat;
	padding: 129px 0 0;
	margin: 20px 0 0;
	border: 0;
}

section.section-custom-map section.section {
	border-top: 0;
	background: rgba(var(--grey-100), 0.5);
}

/* Home Intro */
.home-intro {
	background-color: var(--dark);
	margin-bottom: 60px;
	overflow: hidden;
	padding: 38.4px 0;
	padding: 2.4rem 0;
	position: relative;
	text-align: left;
	letter-spacing: -0.5px;
}

.home-intro p {
	color: var(--light);
	display: inline-block;
	font-size: 1.4em;
	font-weight: 600;
	margin-bottom: 0;
}

.home-intro p span:not(.highlighted-word) {
	color: var(--light);
	display: block;
	font-size: 0.8em;
	font-weight: 300;
	padding-top: 5px;
	opacity: 0.7;
}

.home-intro p .highlighted-word {
	font-size: 1.6em;
}

.home-intro .get-started {
	position: relative;
}

.home-intro .get-started .btn {
	position: relative;
	z-index: 1;
}

.home-intro .get-started .btn i {
	font-size: 1.1em;
	font-weight: normal;
	margin-left: 5px;
}

.home-intro .get-started a {
	color: var(--light);
}

.home-intro .get-started a:not(.btn) {
	color: var(--light);
	font-weight: 600;
}

.home-intro .learn-more {
	color: var(--light);
	margin-left: 15px;
	font-weight: 300;
}

.home-intro.light {
	background-color: #EDEDED;
}

.home-intro.light.secondary {
	background-color: #E0E1E4;
}

.home-intro.light p,
.home-intro.light .learn-more,
.home-intro.light p span:not(.highlighted-word),
.home-intro.light .get-started a:not(.btn) {
	color: var(--dark--200);
}

/* Home Intro Compact */
.home-intro-compact {
	border-radius: 0 0 0.3rem 0.3rem;
	margin-top: -10px;
	margin: -10px auto 60px;
	max-width: 1116px;
}

.home-intro-compact p {
	padding-left: 20px;
}

.home-intro-compact .get-started {
	padding-right: 20px;
}

@media (max-width: 991px) {
		/* Home Intro */
	.home-intro p {
		margin-bottom: 2rem;
	}

	.home-intro-compact .get-started {
		padding-left: 20px;
	}
}

@media (max-width: 767px) {
		/* Home Intro */
	.home-intro-compact {
		margin-left: 0;
		margin-right: 0;
		padding-left: 0;
		padding-right: 0;
	}
}

/* Home Concept SVG Animations */
@keyframes animatedLineBg {
	from {
		stroke-dasharray: 1000;
	}

	to {
		stroke-dasharray: 1360;
	}
}

@keyframes animatedLineSmallCircle {
	from {
		stroke-dasharray: 1000;
	}

	to {
		stroke-dasharray: 1120;
	}
}

@keyframes animatedLineBigCircle {
	from {
		stroke-dasharray: 1000;
	}

	to {
		stroke-dasharray: 1266;
	}
}

@keyframes animatedInitialPin {
	0% {
		transform: translate(2px, 69px) scale(0.23);
	}

	33% {
		transform: translate(2px, 79px) scale(0.23);
	}

	66% {
		transform: translate(2px, 74px) scale(0.23);
	}

	100% {
		transform: translate(2px, 79px) scale(0.23);
	}
}

@keyframes animatedFinalPin {
	0% {
		transform: translate(325px, -19px) scale(0.23) rotate(36deg);
	}

	33% {
		transform: translate(325px, -13px) scale(0.23) rotate(36deg);
	}

	66% {
		transform: translate(325px, -16px) scale(0.23) rotate(36deg);
	}

	100% {
		transform: translate(325px, -13px) scale(0.23) rotate(36deg);
	}
}

/* Firefox Final Pin Animation */
@keyframes animatedFinalPinFirefox {
	0% {
		transform: translate(193px, -16px) scale(0.23) rotate(36deg);
	}

	33% {
		transform: translate(193px, -10px) scale(0.23) rotate(36deg);
	}

	66% {
		transform: translate(193px, -13px) scale(0.23) rotate(36deg);
	}

	100% {
		transform: translate(193px, -10px) scale(0.23) rotate(36deg);
	}
}

/* Home Concept */
.home-concept {
	background: transparent url(../img/home/<USER>
	width: 100%;
	overflow: hidden;
}

.home-concept strong {
	display: block;
	font-family: "Shadows Into Light", cursive;
	font-size: 1.8em;
	font-weight: normal !important;
	position: relative;
	margin-top: 30px;
}

.home-concept .row {
	position: relative;
}

.home-concept .process-image {
	background: transparent url(../img/home/<USER>
	width: 160px;
	margin: 120px auto 0 auto;
	padding-bottom: 50px;
	position: relative;
	z-index: 1;
}

.home-concept .process-image img {
	border-radius: 150px;
	margin: 7px 8px;
	width: auto;
	height: auto;
	max-width: 145px;
	max-height: 145px;
}

@media (min-width: 992px) {
	.home-concept .process-image.process-image-on-middle {
		margin-top: 95px;
	}
}

.home-concept .our-work {
	margin-top: 52px;
	font-size: 2.2em;
}

.home-concept .project-image {
	background: transparent url(../img/home/<USER>
	width: 350px;
	margin: 20px 0 0 -30px;
	padding-bottom: 45px;
	position: relative;
	z-index: 1;
}

.home-concept .sun {
	background: transparent url(../img/home/<USER>
	width: 60px;
	height: 56px;
	display: block;
	position: absolute;
	left: 0;
	top: 65px;
}

.home-concept .cloud {
	background: transparent url(../img/home/<USER>
	width: 116px;
	height: 56px;
	display: block;
	position: absolute;
	left: 53%;
	top: 35px;
}

@media (max-width: 991px) {
	.home-concept {
		background-image: none;
	}

	.home-concept .project-image {
		margin: 20px auto 0 auto;
	}

	.home-concept .process-image {
		margin-top: 0;
		padding-bottom: 25px;
	}
}

@media (max-width: 479px) {
	.home-concept .project-image {
		background-position: 0 0;
		margin-left: auto;
		max-width: 160px;
	}

	.home-concept .fc-slideshow {
		max-width: 145px;
		height: 145px;
	}

	.home-concept .fc-slideshow img {
		max-height: 145px;
		max-width: 145px;
	}
}

/* Home - Skin */
.slider .tp-bannertimer {
	background-color: var(--primary);
}

.home-concept strong {
	color: var(--primary);
}

/* Home Intro - Skin */
.home-intro-primary {
	background: var(--primary);
}

.home-intro-secondary {
	background: var(--secondary);
}

.home-intro-tertiary {
	background: var(--tertiary);
}

.home-intro-quaternary {
	background: var(--quaternary);
}

.home-intro-dark {
	background: var(--dark);
}

.home-intro-light {
	background: var(--light);
}

/* Portfolio */
.portfolio-title {
	margin-bottom: -10px;
}

.portfolio-title a {
	display: inline-block;
}

.portfolio-title [class*="fa-"], .portfolio-title .icons {
	font-size: 1.7em;
}

.portfolio-title .portfolio-nav {
	margin-top: 11px;
}

.portfolio-title .portfolio-nav-all {
	margin-top: 9px;
}

.portfolio-title .portfolio-nav {
	text-align: right;
}

.portfolio-title .portfolio-nav a {
	width: 40%;
}

.portfolio-title .portfolio-nav-all .fa,
.portfolio-title .portfolio-nav-all .icons {
	font-size: 2em;
}

.portfolio-title .fa-angle-right {
	text-align: right;
}

.portfolio-title .portfolio-nav-prev {
	text-align: left;
}

/* Portfolio Info */
.portfolio-info {
	margin: 2px 0 10px;
}

.portfolio-info ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.portfolio-info ul li {
	display: inline-block;
	border-right: 1px solid #E6E6E6;
	color: #B1B1B1;
	padding-right: 8px;
	margin-right: 8px;
	font-size: 0.9em;
}

.portfolio-info ul li:last-child {
	border-right: 0;
	padding-right: 0;
	margin-right: 0;
}

.portfolio-info ul li a {
	text-decoration: none;
	color: #B1B1B1;
}

.portfolio-info ul li a:hover .fa-heart {
	color: #E53F51;
}

.portfolio-info ul li [class*="fa-"], .portfolio-info ul li .icons {
	margin-right: 4px;
}

/* Portfolio List */
.portfolio-list {
	list-style: none;
	padding: 0;
	margin-top: 0;
	margin-bottom: 0;
}

.portfolio-list:not(.row) {
	margin: 0;
}

.portfolio-list .portfolio-item {
	position: relative;
	margin-bottom: 35px;
}

.portfolio-list .portfolio-item.portfolio-item-updated:after, .portfolio-list .portfolio-item.portfolio-item-new:after {
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 10px;
	background: var(--grey-500);
	content: "NEW";
	display: block;
	padding: 0px 8px;
	font-size: 9px;
	font-weight: 600;
	text-transform: uppercase;
	color: var(--light);
	border-radius: 2px;
	line-height: 22px;
}

.portfolio-list .portfolio-item.portfolio-item-updated:after {
	content: "UPDATED";
	background: #36C57A;
}

/* Portfolio Details */
.portfolio-details {
	list-style: none;
	padding: 0;
	margin: 45px 0 0 5px;
	font-size: 0.9em;
}

.portfolio-details > li {
	padding: 0;
	margin: 0 0 20px;
}

.portfolio-details p {
	padding: 0;
	margin: 0;
}

.portfolio-details blockquote {
	padding: 0 0 0 10px;
	margin: 15px 0 0;
	color: #999;
}

.portfolio-details .list-icons {
	margin-left: 0;
}

.portfolio-details .list-icons > li {
	padding-left: 18px;
	margin-right: 10px;
}

.portfolio-details .list-icons > li [class*="fa-"], .portfolio-details .list-icons > li .icons {
	top: 7px;
}

/* Portfolio Modal */
.portfolio-modal .mfp-arrow,
.portfolio-ajax-modal .mfp-arrow {
	display: none;
}

.portfolio-load-more-loader {
	display: none;
	position: relative;
	height: 49px;
}

.btn-portfolio-infinite-scroll {
	opacity: 0;
}

/* Portfolio Navigation */
.portfolio-next:hover [class*="fa-"], .portfolio-next:hover .icons,
.portfolio-prev:hover [class*="fa-"],
.portfolio-prev:hover .icons {
	animation: navItemArrow 600ms ease;
	animation-iteration-count: infinite;
}

@media (max-width: 991px) {
	.portfolio-nav, .portfolio-nav-all {
		display: none;
	}
}

/* Portfolio - Skin */
.portfolio-list .portfolio-item.portfolio-item-new:after {
	background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 80%);
}

/* Team List */
ul.team-list {
	list-style: none;
	margin: 0;
	padding: 0;
}

ul.team-list .team-item {
	margin-bottom: 35px;
}

ul.team-list .team-item .thumb-info-social-icons {
	margin: 0 10px;
}

@media (max-width: 991px) {
	ul.team-list .team-item {
		margin: 0 auto 35px;
		max-width: 270px;
	}
}

/* Search Results */
.search-results {
	min-height: 300px;
}

/* Page 404 */
.http-error {
	padding: 50px 0;
}

.http-error h2 {
	font-size: 140px;
	font-weight: 700;
	line-height: 140px;
}

.http-error h4 {
	color: var(--default);
}

.http-error p {
	font-size: 1.4em;
	line-height: 36px;
}

@media (max-width: 575px) {
	.http-error {
		padding: 0;
	}

	.http-error h2 {
		font-size: 100px;
		letter-spacing: 0;
		line-height: 100px;
	}
}
