::-moz-selection {
	color: #FFF;
	background: #2E3191;
}

::selection {
	color: #FFF;
	background: #2E3191;
}

:root {
	--primary: #2E3191;
	--secondary: #45BEE4;
	--tertiary: #EAEFF3;
	--quaternary: #080808;
}

a {
	color: #2E3191;
}

a:hover {
	color: #e35938;
}

a:focus {
	color: #e35938;
}

a:active {
	color: #cc3e1d;
}

html .text-color-primary,
html .text-primary {
	color: #2E3191 !important;
}

html .text-color-hover-primary:hover,
html .text-hover-primary:hover {
	color: #2E3191 !important;
}

html .text-color-secondary,
html .text-secondary {
	color: #45BEE4 !important;
}

html .text-color-hover-secondary:hover,
html .text-hover-secondary:hover {
	color: #45BEE4 !important;
}

html .text-color-tertiary,
html .text-tertiary {
	color: #EAEFF3 !important;
}

html .text-color-hover-tertiary:hover,
html .text-hover-tertiary:hover {
	color: #EAEFF3 !important;
}

html .text-color-quaternary,
html .text-quaternary {
	color: #080808 !important;
}

html .text-color-hover-quaternary:hover,
html .text-hover-quaternary:hover {
	color: #080808 !important;
}

html .text-color-dark,
html .text-dark {
	color: #212529 !important;
}

html .text-color-hover-dark:hover,
html .text-hover-dark:hover {
	color: #212529 !important;
}

html .text-color-light,
html .text-light {
	color: #FFF !important;
}

html .text-color-hover-light:hover,
html .text-hover-light:hover {
	color: #FFF !important;
}

html .svg-fill-color-primary {
	fill: #2E3191 !important;
}

html .svg-fill-color-primary svg path,
html .svg-fill-color-primary svg rect,
html .svg-fill-color-primary svg line,
html .svg-fill-color-primary svg polyline,
html .svg-fill-color-primary svg polygon {
	fill: #2E3191 !important;
}

html .svg-fill-color-hover-primary:hover {
	fill: #2E3191 !important;
}

html .svg-fill-color-hover-primary svg:hover path,
html .svg-fill-color-hover-primary svg:hover rect,
html .svg-fill-color-hover-primary svg:hover line,
html .svg-fill-color-hover-primary svg:hover polyline,
html .svg-fill-color-hover-primary svg:hover polygon {
	fill: #2E3191 !important;
}

html .svg-stroke-color-primary {
	stroke: #2E3191 !important;
}

html .svg-stroke-color-primary svg path,
html .svg-stroke-color-primary svg rect,
html .svg-stroke-color-primary svg line,
html .svg-stroke-color-primary svg polyline,
html .svg-stroke-color-primary svg polygon {
	stroke: #2E3191 !important;
}

html .svg-stroke-color-hover-primary:hover {
	stroke: #2E3191 !important;
}

html .svg-stroke-color-hover-primary svg:hover path,
html .svg-stroke-color-hover-primary svg:hover rect,
html .svg-stroke-color-hover-primary svg:hover line,
html .svg-stroke-color-hover-primary svg:hover polyline,
html .svg-stroke-color-hover-primary svg:hover polygon {
	stroke: #2E3191 !important;
}

html .svg-fill-color-secondary {
	fill: #45BEE4 !important;
}

html .svg-fill-color-secondary svg path,
html .svg-fill-color-secondary svg rect,
html .svg-fill-color-secondary svg line,
html .svg-fill-color-secondary svg polyline,
html .svg-fill-color-secondary svg polygon {
	fill: #45BEE4 !important;
}

html .svg-fill-color-hover-secondary:hover {
	fill: #45BEE4 !important;
}

html .svg-fill-color-hover-secondary svg:hover path,
html .svg-fill-color-hover-secondary svg:hover rect,
html .svg-fill-color-hover-secondary svg:hover line,
html .svg-fill-color-hover-secondary svg:hover polyline,
html .svg-fill-color-hover-secondary svg:hover polygon {
	fill: #45BEE4 !important;
}

html .svg-stroke-color-secondary {
	stroke: #45BEE4 !important;
}

html .svg-stroke-color-secondary svg path,
html .svg-stroke-color-secondary svg rect,
html .svg-stroke-color-secondary svg line,
html .svg-stroke-color-secondary svg polyline,
html .svg-stroke-color-secondary svg polygon {
	stroke: #45BEE4 !important;
}

html .svg-stroke-color-hover-secondary:hover {
	stroke: #45BEE4 !important;
}

html .svg-stroke-color-hover-secondary svg:hover path,
html .svg-stroke-color-hover-secondary svg:hover rect,
html .svg-stroke-color-hover-secondary svg:hover line,
html .svg-stroke-color-hover-secondary svg:hover polyline,
html .svg-stroke-color-hover-secondary svg:hover polygon {
	stroke: #45BEE4 !important;
}

html .svg-fill-color-tertiary {
	fill: #EAEFF3 !important;
}

html .svg-fill-color-tertiary svg path,
html .svg-fill-color-tertiary svg rect,
html .svg-fill-color-tertiary svg line,
html .svg-fill-color-tertiary svg polyline,
html .svg-fill-color-tertiary svg polygon {
	fill: #EAEFF3 !important;
}

html .svg-fill-color-hover-tertiary:hover {
	fill: #EAEFF3 !important;
}

html .svg-fill-color-hover-tertiary svg:hover path,
html .svg-fill-color-hover-tertiary svg:hover rect,
html .svg-fill-color-hover-tertiary svg:hover line,
html .svg-fill-color-hover-tertiary svg:hover polyline,
html .svg-fill-color-hover-tertiary svg:hover polygon {
	fill: #EAEFF3 !important;
}

html .svg-stroke-color-tertiary {
	stroke: #EAEFF3 !important;
}

html .svg-stroke-color-tertiary svg path,
html .svg-stroke-color-tertiary svg rect,
html .svg-stroke-color-tertiary svg line,
html .svg-stroke-color-tertiary svg polyline,
html .svg-stroke-color-tertiary svg polygon {
	stroke: #EAEFF3 !important;
}

html .svg-stroke-color-hover-tertiary:hover {
	stroke: #EAEFF3 !important;
}

html .svg-stroke-color-hover-tertiary svg:hover path,
html .svg-stroke-color-hover-tertiary svg:hover rect,
html .svg-stroke-color-hover-tertiary svg:hover line,
html .svg-stroke-color-hover-tertiary svg:hover polyline,
html .svg-stroke-color-hover-tertiary svg:hover polygon {
	stroke: #EAEFF3 !important;
}

html .svg-fill-color-quaternary {
	fill: #080808 !important;
}

html .svg-fill-color-quaternary svg path,
html .svg-fill-color-quaternary svg rect,
html .svg-fill-color-quaternary svg line,
html .svg-fill-color-quaternary svg polyline,
html .svg-fill-color-quaternary svg polygon {
	fill: #080808 !important;
}

html .svg-fill-color-hover-quaternary:hover {
	fill: #080808 !important;
}

html .svg-fill-color-hover-quaternary svg:hover path,
html .svg-fill-color-hover-quaternary svg:hover rect,
html .svg-fill-color-hover-quaternary svg:hover line,
html .svg-fill-color-hover-quaternary svg:hover polyline,
html .svg-fill-color-hover-quaternary svg:hover polygon {
	fill: #080808 !important;
}

html .svg-stroke-color-quaternary {
	stroke: #080808 !important;
}

html .svg-stroke-color-quaternary svg path,
html .svg-stroke-color-quaternary svg rect,
html .svg-stroke-color-quaternary svg line,
html .svg-stroke-color-quaternary svg polyline,
html .svg-stroke-color-quaternary svg polygon {
	stroke: #080808 !important;
}

html .svg-stroke-color-hover-quaternary:hover {
	stroke: #080808 !important;
}

html .svg-stroke-color-hover-quaternary svg:hover path,
html .svg-stroke-color-hover-quaternary svg:hover rect,
html .svg-stroke-color-hover-quaternary svg:hover line,
html .svg-stroke-color-hover-quaternary svg:hover polyline,
html .svg-stroke-color-hover-quaternary svg:hover polygon {
	stroke: #080808 !important;
}

html .svg-fill-color-dark {
	fill: #212529 !important;
}

html .svg-fill-color-dark svg path,
html .svg-fill-color-dark svg rect,
html .svg-fill-color-dark svg line,
html .svg-fill-color-dark svg polyline,
html .svg-fill-color-dark svg polygon {
	fill: #212529 !important;
}

html .svg-fill-color-hover-dark:hover {
	fill: #212529 !important;
}

html .svg-fill-color-hover-dark svg:hover path,
html .svg-fill-color-hover-dark svg:hover rect,
html .svg-fill-color-hover-dark svg:hover line,
html .svg-fill-color-hover-dark svg:hover polyline,
html .svg-fill-color-hover-dark svg:hover polygon {
	fill: #212529 !important;
}

html .svg-stroke-color-dark {
	stroke: #212529 !important;
}

html .svg-stroke-color-dark svg path,
html .svg-stroke-color-dark svg rect,
html .svg-stroke-color-dark svg line,
html .svg-stroke-color-dark svg polyline,
html .svg-stroke-color-dark svg polygon {
	stroke: #212529 !important;
}

html .svg-stroke-color-hover-dark:hover {
	stroke: #212529 !important;
}

html .svg-stroke-color-hover-dark svg:hover path,
html .svg-stroke-color-hover-dark svg:hover rect,
html .svg-stroke-color-hover-dark svg:hover line,
html .svg-stroke-color-hover-dark svg:hover polyline,
html .svg-stroke-color-hover-dark svg:hover polygon {
	stroke: #212529 !important;
}

html .svg-fill-color-light {
	fill: #FFF !important;
}

html .svg-fill-color-light svg path,
html .svg-fill-color-light svg rect,
html .svg-fill-color-light svg line,
html .svg-fill-color-light svg polyline,
html .svg-fill-color-light svg polygon {
	fill: #FFF !important;
}

html .svg-fill-color-hover-light:hover {
	fill: #FFF !important;
}

html .svg-fill-color-hover-light svg:hover path,
html .svg-fill-color-hover-light svg:hover rect,
html .svg-fill-color-hover-light svg:hover line,
html .svg-fill-color-hover-light svg:hover polyline,
html .svg-fill-color-hover-light svg:hover polygon {
	fill: #FFF !important;
}

html .svg-stroke-color-light {
	stroke: #FFF !important;
}

html .svg-stroke-color-light svg path,
html .svg-stroke-color-light svg rect,
html .svg-stroke-color-light svg line,
html .svg-stroke-color-light svg polyline,
html .svg-stroke-color-light svg polygon {
	stroke: #FFF !important;
}

html .svg-stroke-color-hover-light:hover {
	stroke: #FFF !important;
}

html .svg-stroke-color-hover-light svg:hover path,
html .svg-stroke-color-hover-light svg:hover rect,
html .svg-stroke-color-hover-light svg:hover line,
html .svg-stroke-color-hover-light svg:hover polyline,
html .svg-stroke-color-hover-light svg:hover polygon {
	stroke: #FFF !important;
}

.svg-animation-effect-1-hover:hover svg path,
.svg-animation-effect-1-hover:hover svg polygon,
.svg-animation-effect-1-hover:hover svg polyline,
.svg-animation-effect-1-hover:hover svg rect {
	stroke: #2E3191;
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg path,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg polygon,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg polyline,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-primary:hover svg rect {
	stroke: #2E3191;
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg path,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg polygon,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg polyline,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-secondary:hover svg rect {
	stroke: #45BEE4;
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg path,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg polygon,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg polyline,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-tertiary:hover svg rect {
	stroke: #EAEFF3;
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg path,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg polygon,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg polyline,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-quaternary:hover svg rect {
	stroke: #080808;
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg path,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg polygon,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg polyline,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-dark:hover svg rect {
	stroke: #212529;
}

.svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg path,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg polygon,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg polyline,
.svg-animation-effect-1-hover.svg-animation-effect-1-hover-light:hover svg rect {
	stroke: #FFF;
}

.gradient-text-color {
	color: #2E3191;
	background: linear-gradient(to bottom right, #2E3191, #45BEE4);
	background-image: linear-gradient(to right, #2E3191, #45BEE4);
}

html .bg-color-primary,
html .bg-primary {
	background-color: #2E3191 !important;
}

html .bg-color-hover-primary:hover,
html .bg-hover-primary:hover {
	background-color: #2E3191 !important;
}

html .bg-color-after-primary:after {
	background-color: #2E3191 !important;
}

html .bg-color-hover-after-primary:after:hover {
	background-color: #2E3191 !important;
}

html .bg-color-before-primary:before {
	background-color: #2E3191 !important;
}

html .bg-color-hover-before-primary:before:hover {
	background-color: #2E3191 !important;
}

html .bg-color-secondary,
html .bg-secondary {
	background-color: #45BEE4 !important;
}

html .bg-color-hover-secondary:hover,
html .bg-hover-secondary:hover {
	background-color: #45BEE4 !important;
}

html .bg-color-after-secondary:after {
	background-color: #45BEE4 !important;
}

html .bg-color-hover-after-secondary:after:hover {
	background-color: #45BEE4 !important;
}

html .bg-color-before-secondary:before {
	background-color: #45BEE4 !important;
}

html .bg-color-hover-before-secondary:before:hover {
	background-color: #45BEE4 !important;
}

html .bg-color-tertiary,
html .bg-tertiary {
	background-color: #EAEFF3 !important;
}

html .bg-color-hover-tertiary:hover,
html .bg-hover-tertiary:hover {
	background-color: #EAEFF3 !important;
}

html .bg-color-after-tertiary:after {
	background-color: #EAEFF3 !important;
}

html .bg-color-hover-after-tertiary:after:hover {
	background-color: #EAEFF3 !important;
}

html .bg-color-before-tertiary:before {
	background-color: #EAEFF3 !important;
}

html .bg-color-hover-before-tertiary:before:hover {
	background-color: #EAEFF3 !important;
}

html .bg-color-quaternary,
html .bg-quaternary {
	background-color: #080808 !important;
}

html .bg-color-hover-quaternary:hover,
html .bg-hover-quaternary:hover {
	background-color: #080808 !important;
}

html .bg-color-after-quaternary:after {
	background-color: #080808 !important;
}

html .bg-color-hover-after-quaternary:after:hover {
	background-color: #080808 !important;
}

html .bg-color-before-quaternary:before {
	background-color: #080808 !important;
}

html .bg-color-hover-before-quaternary:before:hover {
	background-color: #080808 !important;
}

html .bg-color-dark,
html .bg-dark {
	background-color: #212529 !important;
}

html .bg-color-hover-dark:hover,
html .bg-hover-dark:hover {
	background-color: #212529 !important;
}

html .bg-color-after-dark:after {
	background-color: #212529 !important;
}

html .bg-color-hover-after-dark:after:hover {
	background-color: #212529 !important;
}

html .bg-color-before-dark:before {
	background-color: #212529 !important;
}

html .bg-color-hover-before-dark:before:hover {
	background-color: #212529 !important;
}

html .bg-color-light,
html .bg-light {
	background-color: #FFF !important;
}

html .bg-color-hover-light:hover,
html .bg-hover-light:hover {
	background-color: #FFF !important;
}

html .bg-color-after-light:after {
	background-color: #FFF !important;
}

html .bg-color-hover-after-light:after:hover {
	background-color: #FFF !important;
}

html .bg-color-before-light:before {
	background-color: #FFF !important;
}

html .bg-color-hover-before-light:before:hover {
	background-color: #FFF !important;
}

.bg-gradient {
	background-color: #2E3191 !important;
	background-image: linear-gradient(to right, #2E3191 0%, #45BEE4 100%) !important;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2E3191', endColorstr='#45BEE4', GradientType=1);
}

.bg-gradient-to-top {
	background-color: #2E3191 !important;
	background-image: linear-gradient(to top, #2E3191 0%, #45BEE4 100%) !important;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2E3191', endColorstr='#45BEE4', GradientType=1);
}

.bg-gradient-to-bottom {
	background-color: #2E3191 !important;
	background-image: linear-gradient(to bottom, #2E3191 0%, #45BEE4 100%) !important;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2E3191', endColorstr='#45BEE4', GradientType=1);
}
/* Color Transition */
@-webkit-keyframes colorTransition {
	0% {
		background-color: #2E3191;
	}

	33% {
		background-color: #45BEE4;
	}

	66% {
		background-color: #EAEFF3;
	}

	100% {
		background-color: #080808;
	}
}

@keyframes colorTransition {
	0% {
		background-color: #2E3191;
	}

	33% {
		background-color: #45BEE4;
	}

	66% {
		background-color: #EAEFF3;
	}

	100% {
		background-color: #080808;
	}
}

html .border-color-primary {
	border-color: #2E3191 !important;
}

html .border-color-hover-primary:hover {
	border-color: #2E3191 !important;
}

html .border-color-secondary {
	border-color: #45BEE4 !important;
}

html .border-color-hover-secondary:hover {
	border-color: #45BEE4 !important;
}

html .border-color-tertiary {
	border-color: #EAEFF3 !important;
}

html .border-color-hover-tertiary:hover {
	border-color: #EAEFF3 !important;
}

html .border-color-quaternary {
	border-color: #080808 !important;
}

html .border-color-hover-quaternary:hover {
	border-color: #080808 !important;
}

html .border-color-dark {
	border-color: #212529 !important;
}

html .border-color-hover-dark:hover {
	border-color: #212529 !important;
}

html .border-color-light {
	border-color: #FFF !important;
}

html .border-color-hover-light:hover {
	border-color: #FFF !important;
}

.alternative-font {
	color: #2E3191;
}

html .box-shadow-1-primary:before {
	box-shadow: 0 30px 90px #2E3191 !important;
}

html .box-shadow-1-secondary:before {
	box-shadow: 0 30px 90px #45BEE4 !important;
}

html .box-shadow-1-tertiary:before {
	box-shadow: 0 30px 90px #EAEFF3 !important;
}

html .box-shadow-1-quaternary:before {
	box-shadow: 0 30px 90px #080808 !important;
}

html .box-shadow-1-dark:before {
	box-shadow: 0 30px 90px #212529 !important;
}

html .box-shadow-1-light:before {
	box-shadow: 0 30px 90px #FFF !important;
}

html .blockquote-primary {
	border-color: #2E3191 !important;
}

html .blockquote-secondary {
	border-color: #45BEE4 !important;
}

html .blockquote-tertiary {
	border-color: #EAEFF3 !important;
}

html .blockquote-quaternary {
	border-color: #080808 !important;
}

html .blockquote-dark {
	border-color: #212529 !important;
}

html .blockquote-light {
	border-color: #FFF !important;
}

p.drop-caps:first-letter {
	color: #2E3191;
}

p.drop-caps.drop-caps-style-2:first-letter {
	background-color: #2E3191;
}

html .nav-color-primary nav > ul > li > a {
	color: #2E3191 !important;
}

html .nav-color-primary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: #2E3191 !important;
}

html .nav-color-primary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: #2E3191 !important;
}

html .nav-color-secondary nav > ul > li > a {
	color: #45BEE4 !important;
}

html .nav-color-secondary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: #45BEE4 !important;
}

html .nav-color-secondary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: #45BEE4 !important;
}

html .nav-color-tertiary nav > ul > li > a {
	color: #EAEFF3 !important;
}

html .nav-color-tertiary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: #EAEFF3 !important;
}

html .nav-color-tertiary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: #EAEFF3 !important;
}

html .nav-color-quaternary nav > ul > li > a {
	color: #080808 !important;
}

html .nav-color-quaternary:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: #080808 !important;
}

html .nav-color-quaternary:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: #080808 !important;
}

html .nav-color-dark nav > ul > li > a {
	color: #212529 !important;
}

html .nav-color-dark:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: #212529 !important;
}

html .nav-color-dark:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: #212529 !important;
}

html .nav-color-light nav > ul > li > a {
	color: #FFF !important;
}

html .nav-color-light:not(.header-nav-main-dropdown-arrow) nav > ul > li > a:before {
	background-color: #FFF !important;
}

html .nav-color-light:not(.header-nav-main-dropdown-arrow) nav > ul > li ul {
	border-top-color: #FFF !important;
}

.nav-pills > li.active > a,
.nav-pills .nav-link.active {
	background-color: #2E3191;
}

.nav-pills > li.active > a:hover,
.nav-pills .nav-link.active:hover,
.nav-pills > li.active > a:focus,
.nav-pills .nav-link.active:focus {
	background-color: #2E3191;
}

.nav-active-style-1 > li > a:hover,
.nav-active-style-1 > li > a:focus,
.nav-active-style-1 > li > a.active {
	border-bottom-color: #2E3191;
}

html .nav-pills-primary a {
	color: #2E3191;
}

html .nav-pills-primary a:hover {
	color: #e35938;
}

html .nav-pills-primary a:focus {
	color: #e35938;
}

html .nav-pills-primary a:active {
	color: #cc3e1d;
}

html .nav-pills-primary .nav-link.active,
html .nav-pills-primary > li.active > a {
	background-color: #2E3191;
}

html .nav-pills-primary .nav-link.active:hover,
html .nav-pills-primary > li.active > a:hover,
html .nav-pills-primary .nav-link.active:focus,
html .nav-pills-primary > li.active > a:focus {
	background-color: #2E3191;
}

html .nav-pills-secondary a {
	color: #45BEE4;
}

html .nav-pills-secondary a:hover {
	color: #f0b43e;
}

html .nav-pills-secondary a:focus {
	color: #f0b43e;
}

html .nav-pills-secondary a:active {
	color: #e8a112;
}

html .nav-pills-secondary .nav-link.active,
html .nav-pills-secondary > li.active > a {
	background-color: #45BEE4;
}

html .nav-pills-secondary .nav-link.active:hover,
html .nav-pills-secondary > li.active > a:hover,
html .nav-pills-secondary .nav-link.active:focus,
html .nav-pills-secondary > li.active > a:focus {
	background-color: #45BEE4;
}

html .nav-pills-tertiary a {
	color: #EAEFF3;
}

html .nav-pills-tertiary a:hover {
	color: #fafbfc;
}

html .nav-pills-tertiary a:focus {
	color: #fafbfc;
}

html .nav-pills-tertiary a:active {
	color: #dae3ea;
}

html .nav-pills-tertiary .nav-link.active,
html .nav-pills-tertiary > li.active > a {
	background-color: #EAEFF3;
}

html .nav-pills-tertiary .nav-link.active:hover,
html .nav-pills-tertiary > li.active > a:hover,
html .nav-pills-tertiary .nav-link.active:focus,
html .nav-pills-tertiary > li.active > a:focus {
	background-color: #EAEFF3;
}

html .nav-pills-quaternary a {
	color: #080808;
}

html .nav-pills-quaternary a:hover {
	color: #151515;
}

html .nav-pills-quaternary a:focus {
	color: #151515;
}

html .nav-pills-quaternary a:active {
	color: #000000;
}

html .nav-pills-quaternary .nav-link.active,
html .nav-pills-quaternary > li.active > a {
	background-color: #080808;
}

html .nav-pills-quaternary .nav-link.active:hover,
html .nav-pills-quaternary > li.active > a:hover,
html .nav-pills-quaternary .nav-link.active:focus,
html .nav-pills-quaternary > li.active > a:focus {
	background-color: #080808;
}

html .nav-pills-dark a {
	color: #212529;
}

html .nav-pills-dark a:hover {
	color: #2c3237;
}

html .nav-pills-dark a:focus {
	color: #2c3237;
}

html .nav-pills-dark a:active {
	color: #16181b;
}

html .nav-pills-dark .nav-link.active,
html .nav-pills-dark > li.active > a {
	background-color: #212529;
}

html .nav-pills-dark .nav-link.active:hover,
html .nav-pills-dark > li.active > a:hover,
html .nav-pills-dark .nav-link.active:focus,
html .nav-pills-dark > li.active > a:focus {
	background-color: #212529;
}

html .nav-pills-light a {
	color: #FFF;
}

html .nav-pills-light a:hover {
	color: #ffffff;
}

html .nav-pills-light a:focus {
	color: #ffffff;
}

html .nav-pills-light a:active {
	color: #f2f2f2;
}

html .nav-pills-light .nav-link.active,
html .nav-pills-light > li.active > a {
	background-color: #FFF;
}

html .nav-pills-light .nav-link.active:hover,
html .nav-pills-light > li.active > a:hover,
html .nav-pills-light .nav-link.active:focus,
html .nav-pills-light > li.active > a:focus {
	background-color: #FFF;
}

.nav-link {
	color: #2E3191;
}

.nav-link:hover {
	color: #e35938;
}

.nav-link:focus {
	color: #e35938;
}

.nav-link:active {
	color: #cc3e1d;
}

.section-scroll-dots-navigation-colored > ul > li.active > a:before {
	background: #2E3191;
}

.sort-source-wrapper .nav > li.active > a {
	color: #2E3191;
}

.sort-source-wrapper .nav > li.active > a:hover,
.sort-source-wrapper .nav > li.active > a:focus {
	color: #2E3191;
}

.sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: #2E3191;
}

.sort-source.sort-source-style-3 > li.active > a {
	border-bottom-color: #2E3191 !important;
	color: #2E3191 !important;
}

html .badge-primary {
	background-color: #2E3191;
}

html .badge-secondary {
	background-color: #45BEE4;
}

html .badge-tertiary {
	background-color: #EAEFF3;
}

html .badge-quaternary {
	background-color: #080808;
}

html .badge-dark {
	background-color: #212529;
}

html .badge-light {
	background-color: #FFF;
}

html .overlay-color-primary:not(.no-skin):before {
	background-color: #2E3191 !important;
}

html .overlay-color-secondary:not(.no-skin):before {
	background-color: #45BEE4 !important;
}

html .overlay-color-tertiary:not(.no-skin):before {
	background-color: #EAEFF3 !important;
}

html .overlay-color-quaternary:not(.no-skin):before {
	background-color: #080808 !important;
}

html .overlay-color-dark:not(.no-skin):before {
	background-color: #212529 !important;
}

html .overlay-color-light:not(.no-skin):before {
	background-color: #FFF !important;
}

.overlay-gradient:before {
	background-color: #EAEFF3 !important;
	background-image: linear-gradient(to right, #EAEFF3 0%, #080808 100%) !important;
}

.btn-link {
	color: #2E3191;
}

.btn-link:hover {
	color: #e35938;
}

.btn-link:active {
	color: #cc3e1d;
}

html .btn-primary {
	background-color: #2E3191;
	border-color: #2E3191 #2E3191 #A7A9AC;
	color: #FFF;
	--color: #2E3191;
	--hover: #3f9bd0;
	--disabled: #2E3191;
	--active: #606060;
}

html .btn-primary:hover,
html .btn-primary.hover {
	border-color: #231F20 #231F20 #2E3191;
	color: #FFF;
}

html .btn-primary:hover:not(.bg-transparent),
html .btn-primary.hover:not(.bg-transparent) {
	background-color: #3f9bd0;
}

html .btn-primary:focus,
html .btn-primary.focus {
	border-color: #A7A9AC #A7A9AC #892913;
	color: #FFF;
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

html .btn-primary:focus:not(.bg-transparent),
html .btn-primary.focus:not(.bg-transparent) {
	background-color: #606060;
}

html .btn-primary.disabled,
html .btn-primary:disabled {
	border-color: #2E3191 #2E3191 #A7A9AC;
}

html .btn-primary.disabled:not(.bg-transparent),
html .btn-primary:disabled:not(.bg-transparent) {
	background-color: #2E3191;
}

html .btn-primary:active,
html .btn-primary.active {
	border-color: #A7A9AC #A7A9AC #892913 !important;
}

html .btn-primary:active:not(.bg-transparent),
html .btn-primary.active:not(.bg-transparent) {
	background-color: #606060 !important;
	background-image: none !important;
}

html .btn-primary-scale-2 {
	background-color: #A7A9AC;
	border-color: #A7A9AC #A7A9AC #892913;
	color: #FFF;
}

html .btn-primary-scale-2:hover,
html .btn-primary-scale-2.hover {
	border-color: #2E3191 #2E3191 #A7A9AC;
	color: #FFF;
}

html .btn-primary-scale-2:hover:not(.bg-transparent),
html .btn-primary-scale-2.hover:not(.bg-transparent) {
	background-color: #d7411e;
}

html .btn-primary-scale-2:focus,
html .btn-primary-scale-2.focus {
	border-color: #892913 #892913 #5c1c0d;
	color: #FFF;
	box-shadow: 0 0 0 3px rgba(182, 55, 25, 0.5);
}

html .btn-primary-scale-2:focus:not(.bg-transparent),
html .btn-primary-scale-2.focus:not(.bg-transparent) {
	background-color: #942d15;
}

html .btn-primary-scale-2.disabled,
html .btn-primary-scale-2:disabled {
	border-color: #A7A9AC #A7A9AC #892913;
}

html .btn-primary-scale-2.disabled:not(.bg-transparent),
html .btn-primary-scale-2:disabled:not(.bg-transparent) {
	background-color: #A7A9AC;
}

html .btn-primary-scale-2:active,
html .btn-primary-scale-2.active {
	border-color: #892913 #892913 #5c1c0d !important;
}

html .btn-primary-scale-2:active:not(.bg-transparent),
html .btn-primary-scale-2.active:not(.bg-transparent) {
	background-color: #942d15 !important;
	background-image: none !important;
}

html .show > .btn-primary.dropdown-toggle,
html .show > .btn-primary-scale-2.dropdown-toggle {
	background-color: #606060 !important;
	background-image: none !important;
	border-color: #A7A9AC #A7A9AC #892913 !important;
}

html .btn-check:active + .btn-primary,
html .btn-check:checked + .btn-primary,
html .btn-check:focus + .btn-primary {
	background-color: #cc3e1d;
	border-color: #2E3191 #2E3191 #cc3e1d;
	color: #FFF;
}

html .btn-secondary {
	background-color: #45BEE4;
	border-color: #45BEE4 #45BEE4 #d19010;
	color: #777;
	--color: #45BEE4;
	--hover: #f1b949;
	--disabled: #45BEE4;
	--active: #dc9811;
}

html .btn-secondary:hover,
html .btn-secondary.hover {
	border-color: #f2bd55 #f2bd55 #45BEE4;
	color: #777;
}

html .btn-secondary:hover:not(.bg-transparent),
html .btn-secondary.hover:not(.bg-transparent) {
	background-color: #f1b949;
}

html .btn-secondary:focus,
html .btn-secondary.focus {
	border-color: #d19010 #d19010 #a1700d;
	color: #777;
	box-shadow: 0 0 0 3px rgba(238, 171, 38, 0.5);
}

html .btn-secondary:focus:not(.bg-transparent),
html .btn-secondary.focus:not(.bg-transparent) {
	background-color: #dc9811;
}

html .btn-secondary.disabled,
html .btn-secondary:disabled {
	border-color: #45BEE4 #45BEE4 #d19010;
}

html .btn-secondary.disabled:not(.bg-transparent),
html .btn-secondary:disabled:not(.bg-transparent) {
	background-color: #45BEE4;
}

html .btn-secondary:active,
html .btn-secondary.active {
	border-color: #d19010 #d19010 #a1700d !important;
}

html .btn-secondary:active:not(.bg-transparent),
html .btn-secondary.active:not(.bg-transparent) {
	background-color: #dc9811 !important;
	background-image: none !important;
}

html .btn-secondary-scale-2 {
	background-color: #d19010;
	border-color: #d19010 #d19010 #a1700d;
	color: #777;
}

html .btn-secondary-scale-2:hover,
html .btn-secondary-scale-2.hover {
	border-color: #45BEE4 #45BEE4 #d19010;
	color: #777;
}

html .btn-secondary-scale-2:hover:not(.bg-transparent),
html .btn-secondary-scale-2.hover:not(.bg-transparent) {
	background-color: #eda61a;
}

html .btn-secondary-scale-2:focus,
html .btn-secondary-scale-2.focus {
	border-color: #a1700d #a1700d #724f09;
	color: #777;
	box-shadow: 0 0 0 3px rgba(209, 144, 16, 0.5);
}

html .btn-secondary-scale-2:focus:not(.bg-transparent),
html .btn-secondary-scale-2.focus:not(.bg-transparent) {
	background-color: #ad780e;
}

html .btn-secondary-scale-2.disabled,
html .btn-secondary-scale-2:disabled {
	border-color: #d19010 #d19010 #a1700d;
}

html .btn-secondary-scale-2.disabled:not(.bg-transparent),
html .btn-secondary-scale-2:disabled:not(.bg-transparent) {
	background-color: #d19010;
}

html .btn-secondary-scale-2:active,
html .btn-secondary-scale-2.active {
	border-color: #a1700d #a1700d #724f09 !important;
}

html .btn-secondary-scale-2:active:not(.bg-transparent),
html .btn-secondary-scale-2.active:not(.bg-transparent) {
	background-color: #ad780e !important;
	background-image: none !important;
}

html .show > .btn-secondary.dropdown-toggle,
html .show > .btn-secondary-scale-2.dropdown-toggle {
	background-color: #dc9811 !important;
	background-image: none !important;
	border-color: #d19010 #d19010 #a1700d !important;
}

html .btn-check:active + .btn-secondary,
html .btn-check:checked + .btn-secondary,
html .btn-check:focus + .btn-secondary {
	background-color: #e8a112;
	border-color: #45BEE4 #45BEE4 #e8a112;
	color: #777;
}

html .btn-tertiary {
	background-color: #EAEFF3;
	border-color: #EAEFF3 #EAEFF3 #cad6e0;
	color: #777;
	--color: #EAEFF3;
	--hover: #ffffff;
	--disabled: #EAEFF3;
	--active: #d2dce5;
}

html .btn-tertiary:hover,
html .btn-tertiary.hover {
	border-color: #ffffff #ffffff #EAEFF3;
	color: #777;
}

html .btn-tertiary:hover:not(.bg-transparent),
html .btn-tertiary.hover:not(.bg-transparent) {
	background-color: #ffffff;
}

html .btn-tertiary:focus,
html .btn-tertiary.focus {
	border-color: #cad6e0 #cad6e0 #a9bece;
	color: #777;
	box-shadow: 0 0 0 3px rgba(234, 239, 243, 0.5);
}

html .btn-tertiary:focus:not(.bg-transparent),
html .btn-tertiary.focus:not(.bg-transparent) {
	background-color: #d2dce5;
}

html .btn-tertiary.disabled,
html .btn-tertiary:disabled {
	border-color: #EAEFF3 #EAEFF3 #cad6e0;
}

html .btn-tertiary.disabled:not(.bg-transparent),
html .btn-tertiary:disabled:not(.bg-transparent) {
	background-color: #EAEFF3;
}

html .btn-tertiary:active,
html .btn-tertiary.active {
	border-color: #cad6e0 #cad6e0 #a9bece !important;
}

html .btn-tertiary:active:not(.bg-transparent),
html .btn-tertiary.active:not(.bg-transparent) {
	background-color: #d2dce5 !important;
	background-image: none !important;
}

html .btn-tertiary-scale-2 {
	background-color: #cad6e0;
	border-color: #cad6e0 #cad6e0 #a9bece;
	color: #777;
}

html .btn-tertiary-scale-2:hover,
html .btn-tertiary-scale-2.hover {
	border-color: #eaeff3 #eaeff3 #cad6e0;
	color: #777;
}

html .btn-tertiary-scale-2:hover:not(.bg-transparent),
html .btn-tertiary-scale-2.hover:not(.bg-transparent) {
	background-color: #e2e9ee;
}

html .btn-tertiary-scale-2:focus,
html .btn-tertiary-scale-2.focus {
	border-color: #a9bece #a9bece #89a5bb;
	color: #777;
	box-shadow: 0 0 0 3px rgba(202, 214, 224, 0.5);
}

html .btn-tertiary-scale-2:focus:not(.bg-transparent),
html .btn-tertiary-scale-2.focus:not(.bg-transparent) {
	background-color: #b1c4d3;
}

html .btn-tertiary-scale-2.disabled,
html .btn-tertiary-scale-2:disabled {
	border-color: #cad6e0 #cad6e0 #a9bece;
}

html .btn-tertiary-scale-2.disabled:not(.bg-transparent),
html .btn-tertiary-scale-2:disabled:not(.bg-transparent) {
	background-color: #cad6e0;
}

html .btn-tertiary-scale-2:active,
html .btn-tertiary-scale-2.active {
	border-color: #a9bece #a9bece #89a5bb !important;
}

html .btn-tertiary-scale-2:active:not(.bg-transparent),
html .btn-tertiary-scale-2.active:not(.bg-transparent) {
	background-color: #b1c4d3 !important;
	background-image: none !important;
}

html .show > .btn-tertiary.dropdown-toggle,
html .show > .btn-tertiary-scale-2.dropdown-toggle {
	background-color: #d2dce5 !important;
	background-image: none !important;
	border-color: #cad6e0 #cad6e0 #a9bece !important;
}

html .btn-check:active + .btn-tertiary,
html .btn-check:checked + .btn-tertiary,
html .btn-check:focus + .btn-tertiary {
	background-color: #dae3ea;
	border-color: #EAEFF3 #EAEFF3 #dae3ea;
	color: #777;
}

html .btn-quaternary {
	background-color: #080808;
	border-color: #080808 #080808 #000000;
	color: #FFF;
	--color: #080808;
	--hover: #1b1b1b;
	--disabled: #080808;
	--active: #000000;
}

html .btn-quaternary:hover,
html .btn-quaternary.hover {
	border-color: #222222 #222222 #080808;
	color: #FFF;
}

html .btn-quaternary:hover:not(.bg-transparent),
html .btn-quaternary.hover:not(.bg-transparent) {
	background-color: #1b1b1b;
}

html .btn-quaternary:focus,
html .btn-quaternary.focus {
	border-color: #000000 #000000 #000000;
	color: #FFF;
	box-shadow: 0 0 0 3px rgba(8, 8, 8, 0.5);
}

html .btn-quaternary:focus:not(.bg-transparent),
html .btn-quaternary.focus:not(.bg-transparent) {
	background-color: #000000;
}

html .btn-quaternary.disabled,
html .btn-quaternary:disabled {
	border-color: #080808 #080808 #000000;
}

html .btn-quaternary.disabled:not(.bg-transparent),
html .btn-quaternary:disabled:not(.bg-transparent) {
	background-color: #080808;
}

html .btn-quaternary:active,
html .btn-quaternary.active {
	border-color: #000000 #000000 #000000 !important;
}

html .btn-quaternary:active:not(.bg-transparent),
html .btn-quaternary.active:not(.bg-transparent) {
	background-color: #000000 !important;
	background-image: none !important;
}

html .btn-quaternary-scale-2 {
	background-color: #000000;
	border-color: #000000 #000000 #000000;
	color: #FFF;
}

html .btn-quaternary-scale-2:hover,
html .btn-quaternary-scale-2.hover {
	border-color: #1a1a1a #1a1a1a #000000;
	color: #FFF;
}

html .btn-quaternary-scale-2:hover:not(.bg-transparent),
html .btn-quaternary-scale-2.hover:not(.bg-transparent) {
	background-color: #131313;
}

html .btn-quaternary-scale-2:focus,
html .btn-quaternary-scale-2.focus {
	border-color: #000000 #000000 #000000;
	color: #FFF;
	box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.5);
}

html .btn-quaternary-scale-2:focus:not(.bg-transparent),
html .btn-quaternary-scale-2.focus:not(.bg-transparent) {
	background-color: #000000;
}

html .btn-quaternary-scale-2.disabled,
html .btn-quaternary-scale-2:disabled {
	border-color: #000000 #000000 #000000;
}

html .btn-quaternary-scale-2.disabled:not(.bg-transparent),
html .btn-quaternary-scale-2:disabled:not(.bg-transparent) {
	background-color: #000000;
}

html .btn-quaternary-scale-2:active,
html .btn-quaternary-scale-2.active {
	border-color: #000000 #000000 #000000 !important;
}

html .btn-quaternary-scale-2:active:not(.bg-transparent),
html .btn-quaternary-scale-2.active:not(.bg-transparent) {
	background-color: #000000 !important;
	background-image: none !important;
}

html .show > .btn-quaternary.dropdown-toggle,
html .show > .btn-quaternary-scale-2.dropdown-toggle {
	background-color: #000000 !important;
	background-image: none !important;
	border-color: #000000 #000000 #000000 !important;
}

html .btn-check:active + .btn-quaternary,
html .btn-check:checked + .btn-quaternary,
html .btn-check:focus + .btn-quaternary {
	background-color: #000000;
	border-color: #080808 #080808 #000000;
	color: #FFF;
}

html .btn-dark {
	background-color: #212529;
	border-color: #212529 #212529 #0a0c0d;
	color: #FFF;
	--color: #212529;
	--hover: #32383e;
	--disabled: #212529;
	--active: #101214;
}

html .btn-dark:hover,
html .btn-dark.hover {
	border-color: #383f45 #383f45 #212529;
	color: #FFF;
}

html .btn-dark:hover:not(.bg-transparent),
html .btn-dark.hover:not(.bg-transparent) {
	background-color: #32383e;
}

html .btn-dark:focus,
html .btn-dark.focus {
	border-color: #0a0c0d #0a0c0d #000000;
	color: #FFF;
	box-shadow: 0 0 0 3px rgba(33, 37, 41, 0.5);
}

html .btn-dark:focus:not(.bg-transparent),
html .btn-dark.focus:not(.bg-transparent) {
	background-color: #101214;
}

html .btn-dark.disabled,
html .btn-dark:disabled {
	border-color: #212529 #212529 #0a0c0d;
}

html .btn-dark.disabled:not(.bg-transparent),
html .btn-dark:disabled:not(.bg-transparent) {
	background-color: #212529;
}

html .btn-dark:active,
html .btn-dark.active {
	border-color: #0a0c0d #0a0c0d #000000 !important;
}

html .btn-dark:active:not(.bg-transparent),
html .btn-dark.active:not(.bg-transparent) {
	background-color: #101214 !important;
	background-image: none !important;
}

html .btn-dark-scale-2 {
	background-color: #0a0c0d;
	border-color: #0a0c0d #0a0c0d #000000;
	color: #FFF;
}

html .btn-dark-scale-2:hover,
html .btn-dark-scale-2.hover {
	border-color: #212529 #212529 #0a0c0d;
	color: #FFF;
}

html .btn-dark-scale-2:hover:not(.bg-transparent),
html .btn-dark-scale-2.hover:not(.bg-transparent) {
	background-color: #1b1f22;
}

html .btn-dark-scale-2:focus,
html .btn-dark-scale-2.focus {
	border-color: #000000 #000000 #000000;
	color: #FFF;
	box-shadow: 0 0 0 3px rgba(10, 11, 13, 0.5);
}

html .btn-dark-scale-2:focus:not(.bg-transparent),
html .btn-dark-scale-2.focus:not(.bg-transparent) {
	background-color: #000000;
}

html .btn-dark-scale-2.disabled,
html .btn-dark-scale-2:disabled {
	border-color: #0a0c0d #0a0c0d #000000;
}

html .btn-dark-scale-2.disabled:not(.bg-transparent),
html .btn-dark-scale-2:disabled:not(.bg-transparent) {
	background-color: #0a0c0d;
}

html .btn-dark-scale-2:active,
html .btn-dark-scale-2.active {
	border-color: #000000 #000000 #000000 !important;
}

html .btn-dark-scale-2:active:not(.bg-transparent),
html .btn-dark-scale-2.active:not(.bg-transparent) {
	background-color: #000000 !important;
	background-image: none !important;
}

html .show > .btn-dark.dropdown-toggle,
html .show > .btn-dark-scale-2.dropdown-toggle {
	background-color: #101214 !important;
	background-image: none !important;
	border-color: #0a0c0d #0a0c0d #000000 !important;
}

html .btn-check:active + .btn-dark,
html .btn-check:checked + .btn-dark,
html .btn-check:focus + .btn-dark {
	background-color: #16181b;
	border-color: #212529 #212529 #16181b;
	color: #FFF;
}

html .btn-light {
	background-color: #FFF;
	border-color: #FFF #FFF #e6e6e6;
	color: #777;
	--color: #FFF;
	--hover: #ffffff;
	--disabled: #FFF;
	--active: #ececec;
}

html .btn-light:hover,
html .btn-light.hover {
	border-color: #ffffff #ffffff #FFF;
	color: #777;
}

html .btn-light:hover:not(.bg-transparent),
html .btn-light.hover:not(.bg-transparent) {
	background-color: #ffffff;
}

html .btn-light:focus,
html .btn-light.focus {
	border-color: #e6e6e6 #e6e6e6 #cccccc;
	color: #777;
	box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
}

html .btn-light:focus:not(.bg-transparent),
html .btn-light.focus:not(.bg-transparent) {
	background-color: #ececec;
}

html .btn-light.disabled,
html .btn-light:disabled {
	border-color: #FFF #FFF #e6e6e6;
}

html .btn-light.disabled:not(.bg-transparent),
html .btn-light:disabled:not(.bg-transparent) {
	background-color: #FFF;
}

html .btn-light:active,
html .btn-light.active {
	border-color: #e6e6e6 #e6e6e6 #cccccc !important;
}

html .btn-light:active:not(.bg-transparent),
html .btn-light.active:not(.bg-transparent) {
	background-color: #ececec !important;
	background-image: none !important;
}

html .btn-light-scale-2 {
	background-color: #e6e6e6;
	border-color: #e6e6e6 #e6e6e6 #cccccc;
	color: #777;
}

html .btn-light-scale-2:hover,
html .btn-light-scale-2.hover {
	border-color: #ffffff #ffffff #e6e6e6;
	color: #777;
}

html .btn-light-scale-2:hover:not(.bg-transparent),
html .btn-light-scale-2.hover:not(.bg-transparent) {
	background-color: #f9f9f9;
}

html .btn-light-scale-2:focus,
html .btn-light-scale-2.focus {
	border-color: #cccccc #cccccc #b3b3b3;
	color: #777;
	box-shadow: 0 0 0 3px rgba(230, 230, 230, 0.5);
}

html .btn-light-scale-2:focus:not(.bg-transparent),
html .btn-light-scale-2.focus:not(.bg-transparent) {
	background-color: #d2d2d2;
}

html .btn-light-scale-2.disabled,
html .btn-light-scale-2:disabled {
	border-color: #e6e6e6 #e6e6e6 #cccccc;
}

html .btn-light-scale-2.disabled:not(.bg-transparent),
html .btn-light-scale-2:disabled:not(.bg-transparent) {
	background-color: #e6e6e6;
}

html .btn-light-scale-2:active,
html .btn-light-scale-2.active {
	border-color: #cccccc #cccccc #b3b3b3 !important;
}

html .btn-light-scale-2:active:not(.bg-transparent),
html .btn-light-scale-2.active:not(.bg-transparent) {
	background-color: #d2d2d2 !important;
	background-image: none !important;
}

html .show > .btn-light.dropdown-toggle,
html .show > .btn-light-scale-2.dropdown-toggle {
	background-color: #ececec !important;
	background-image: none !important;
	border-color: #e6e6e6 #e6e6e6 #cccccc !important;
}

html .btn-check:active + .btn-light,
html .btn-check:checked + .btn-light,
html .btn-check:focus + .btn-light {
	background-color: #f2f2f2;
	border-color: #FFF #FFF #f2f2f2;
	color: #777;
}

html .btn-outline.btn-primary {
	color: #2E3191;
	background-color: transparent;
	background-image: none;
	border-color: #2E3191;
}

html .btn-outline.btn-primary:hover,
html .btn-outline.btn-primary.hover {
	color: #FFF;
	background-color: #2E3191;
	border-color: #2E3191;
}

html .btn-outline.btn-primary:focus,
html .btn-outline.btn-primary.focus {
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

html .btn-outline.btn-primary.disabled,
html .btn-outline.btn-primary:disabled {
	color: #2E3191;
	background-color: transparent;
}

html .btn-outline.btn-primary:active,
html .btn-outline.btn-primary.active {
	color: #FFF !important;
	background-color: #2E3191 !important;
	border-color: #2E3191 !important;
}

html .show > .btn-outline.btn-primary.dropdown-toggle {
	color: #FFF !important;
	background-color: #2E3191 !important;
	border-color: #2E3191 !important;
}

html .btn-outline.btn-secondary {
	color: #45BEE4;
	background-color: transparent;
	background-image: none;
	border-color: #45BEE4;
}

html .btn-outline.btn-secondary:hover,
html .btn-outline.btn-secondary.hover {
	color: #777;
	background-color: #45BEE4;
	border-color: #45BEE4;
}

html .btn-outline.btn-secondary:focus,
html .btn-outline.btn-secondary.focus {
	box-shadow: 0 0 0 3px rgba(238, 171, 38, 0.5);
}

html .btn-outline.btn-secondary.disabled,
html .btn-outline.btn-secondary:disabled {
	color: #45BEE4;
	background-color: transparent;
}

html .btn-outline.btn-secondary:active,
html .btn-outline.btn-secondary.active {
	color: #777 !important;
	background-color: #45BEE4 !important;
	border-color: #45BEE4 !important;
}

html .show > .btn-outline.btn-secondary.dropdown-toggle {
	color: #777 !important;
	background-color: #45BEE4 !important;
	border-color: #45BEE4 !important;
}

html .btn-outline.btn-tertiary {
	color: #EAEFF3;
	background-color: transparent;
	background-image: none;
	border-color: #EAEFF3;
}

html .btn-outline.btn-tertiary:hover,
html .btn-outline.btn-tertiary.hover {
	color: #777;
	background-color: #EAEFF3;
	border-color: #EAEFF3;
}

html .btn-outline.btn-tertiary:focus,
html .btn-outline.btn-tertiary.focus {
	box-shadow: 0 0 0 3px rgba(234, 239, 243, 0.5);
}

html .btn-outline.btn-tertiary.disabled,
html .btn-outline.btn-tertiary:disabled {
	color: #EAEFF3;
	background-color: transparent;
}

html .btn-outline.btn-tertiary:active,
html .btn-outline.btn-tertiary.active {
	color: #777 !important;
	background-color: #EAEFF3 !important;
	border-color: #EAEFF3 !important;
}

html .show > .btn-outline.btn-tertiary.dropdown-toggle {
	color: #777 !important;
	background-color: #EAEFF3 !important;
	border-color: #EAEFF3 !important;
}

html .btn-outline.btn-quaternary {
	color: #080808;
	background-color: transparent;
	background-image: none;
	border-color: #080808;
}

html .btn-outline.btn-quaternary:hover,
html .btn-outline.btn-quaternary.hover {
	color: #FFF;
	background-color: #080808;
	border-color: #080808;
}

html .btn-outline.btn-quaternary:focus,
html .btn-outline.btn-quaternary.focus {
	box-shadow: 0 0 0 3px rgba(8, 8, 8, 0.5);
}

html .btn-outline.btn-quaternary.disabled,
html .btn-outline.btn-quaternary:disabled {
	color: #080808;
	background-color: transparent;
}

html .btn-outline.btn-quaternary:active,
html .btn-outline.btn-quaternary.active {
	color: #FFF !important;
	background-color: #080808 !important;
	border-color: #080808 !important;
}

html .show > .btn-outline.btn-quaternary.dropdown-toggle {
	color: #FFF !important;
	background-color: #080808 !important;
	border-color: #080808 !important;
}

html .btn-outline.btn-dark {
	color: #212529;
	background-color: transparent;
	background-image: none;
	border-color: #212529;
}

html .btn-outline.btn-dark:hover,
html .btn-outline.btn-dark.hover {
	color: #FFF;
	background-color: #212529;
	border-color: #212529;
}

html .btn-outline.btn-dark:focus,
html .btn-outline.btn-dark.focus {
	box-shadow: 0 0 0 3px rgba(33, 37, 41, 0.5);
}

html .btn-outline.btn-dark.disabled,
html .btn-outline.btn-dark:disabled {
	color: #212529;
	background-color: transparent;
}

html .btn-outline.btn-dark:active,
html .btn-outline.btn-dark.active {
	color: #FFF !important;
	background-color: #212529 !important;
	border-color: #212529 !important;
}

html .show > .btn-outline.btn-dark.dropdown-toggle {
	color: #FFF !important;
	background-color: #212529 !important;
	border-color: #212529 !important;
}

html .btn-outline.btn-light {
	color: #FFF;
	background-color: transparent;
	background-image: none;
	border-color: #FFF;
}

html .btn-outline.btn-light:hover,
html .btn-outline.btn-light.hover {
	color: #777;
	background-color: #FFF;
	border-color: #FFF;
}

html .btn-outline.btn-light:focus,
html .btn-outline.btn-light.focus {
	box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
}

html .btn-outline.btn-light.disabled,
html .btn-outline.btn-light:disabled {
	color: #FFF;
	background-color: transparent;
}

html .btn-outline.btn-light:active,
html .btn-outline.btn-light.active {
	color: #777 !important;
	background-color: #FFF !important;
	border-color: #FFF !important;
}

html .show > .btn-outline.btn-light.dropdown-toggle {
	color: #777 !important;
	background-color: #FFF !important;
	border-color: #FFF !important;
}

html .btn-with-arrow.btn-primary {
	background-color: transparent;
	border-color: transparent;
	color: #2E3191;
}

html .btn-with-arrow.btn-primary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: #2E3191 !important;
}

html .btn-with-arrow.btn-primary span {
	background-color: #2E3191;
	box-shadow: 2px 3px 18px -3px #2E3191;
}

html .btn-with-arrow.btn-primary.btn-outline {
	border-color: #2E3191 !important;
}

html .btn-with-arrow.btn-primary.btn-outline:hover span {
	background-color: #e35938 !important;
	box-shadow: none;
}

html .btn-with-arrow-solid.btn-primary span {
	background-color: #cc3e1d;
}

html .btn-with-arrow.btn-secondary {
	background-color: transparent;
	border-color: transparent;
	color: #45BEE4;
}

html .btn-with-arrow.btn-secondary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: #45BEE4 !important;
}

html .btn-with-arrow.btn-secondary span {
	background-color: #45BEE4;
	box-shadow: 2px 3px 18px -3px #45BEE4;
}

html .btn-with-arrow.btn-secondary.btn-outline {
	border-color: #45BEE4 !important;
}

html .btn-with-arrow.btn-secondary.btn-outline:hover span {
	background-color: #f0b43e !important;
	box-shadow: none;
}

html .btn-with-arrow-solid.btn-secondary span {
	background-color: #e8a112;
}

html .btn-with-arrow.btn-tertiary {
	background-color: transparent;
	border-color: transparent;
	color: #EAEFF3;
}

html .btn-with-arrow.btn-tertiary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: #EAEFF3 !important;
}

html .btn-with-arrow.btn-tertiary span {
	background-color: #EAEFF3;
	box-shadow: 2px 3px 18px -3px #EAEFF3;
}

html .btn-with-arrow.btn-tertiary.btn-outline {
	border-color: #EAEFF3 !important;
}

html .btn-with-arrow.btn-tertiary.btn-outline:hover span {
	background-color: #fafbfc !important;
	box-shadow: none;
}

html .btn-with-arrow-solid.btn-tertiary span {
	background-color: #dae3ea;
}

html .btn-with-arrow.btn-quaternary {
	background-color: transparent;
	border-color: transparent;
	color: #080808;
}

html .btn-with-arrow.btn-quaternary:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: #080808 !important;
}

html .btn-with-arrow.btn-quaternary span {
	background-color: #080808;
	box-shadow: 2px 3px 18px -3px #080808;
}

html .btn-with-arrow.btn-quaternary.btn-outline {
	border-color: #080808 !important;
}

html .btn-with-arrow.btn-quaternary.btn-outline:hover span {
	background-color: #151515 !important;
	box-shadow: none;
}

html .btn-with-arrow-solid.btn-quaternary span {
	background-color: #000000;
}

html .btn-with-arrow.btn-dark {
	background-color: transparent;
	border-color: transparent;
	color: #212529;
}

html .btn-with-arrow.btn-dark:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: #212529 !important;
}

html .btn-with-arrow.btn-dark span {
	background-color: #212529;
	box-shadow: 2px 3px 18px -3px #212529;
}

html .btn-with-arrow.btn-dark.btn-outline {
	border-color: #212529 !important;
}

html .btn-with-arrow.btn-dark.btn-outline:hover span {
	background-color: #2c3237 !important;
	box-shadow: none;
}

html .btn-with-arrow-solid.btn-dark span {
	background-color: #16181b;
}

html .btn-with-arrow.btn-light {
	background-color: transparent;
	border-color: transparent;
	color: #FFF;
}

html .btn-with-arrow.btn-light:active {
	background-color: transparent !important;
	border-color: transparent !important;
	color: #FFF !important;
}

html .btn-with-arrow.btn-light span {
	background-color: #FFF;
	box-shadow: 2px 3px 18px -3px #FFF;
}

html .btn-with-arrow.btn-light.btn-outline {
	border-color: #FFF !important;
}

html .btn-with-arrow.btn-light.btn-outline:hover span {
	background-color: #ffffff !important;
	box-shadow: none;
}

html .btn-with-arrow-solid.btn-light span {
	background-color: #f2f2f2;
}

.btn-gradient:not(.btn-outline) {
	background: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%) !important;
	color: #FFF;
}

.btn-gradient:not(.btn-outline):hover,
.btn-gradient:not(.btn-outline).hover {
	background: linear-gradient(135deg, #3f9bd0 0%, #f1b949 80%) !important;
	color: #FFF;
}

.btn-gradient:not(.btn-outline):focus,
.btn-gradient:not(.btn-outline).focus {
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

.btn-gradient:not(.btn-outline).disabled,
.btn-gradient:not(.btn-outline):disabled {
	background: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%) !important;
}

.btn-gradient:not(.btn-outline):active,
.btn-gradient:not(.btn-outline).active {
	background: linear-gradient(135deg, #606060 0%, #dc9811 80%) !important;
	color: #FFF !important;
}

.btn-gradient.btn-outline {
	-o-border-image: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%);
	border-image: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%);
	color: #2E3191;
}

.btn-gradient.btn-outline:hover,
.btn-gradient.btn-outline.hover {
	-o-border-image: linear-gradient(135deg, #3f9bd0 0%, #f1b949 80%);
	border-image: linear-gradient(135deg, #3f9bd0 0%, #f1b949 80%);
	color: #3f9bd0;
}

.btn-gradient.btn-outline:focus,
.btn-gradient.btn-outline.focus {
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

.btn-gradient.btn-outline.disabled,
.btn-gradient.btn-outline:disabled {
	-o-border-image: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%);
	border-image: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%);
}

.btn-gradient.btn-outline:active,
.btn-gradient.btn-outline.active {
	-o-border-image: linear-gradient(135deg, #606060 0%, #dc9811 80%);
	border-image: linear-gradient(135deg, #606060 0%, #dc9811 80%);
	color: #606060;
}

.btn-gradient-primary:not(.btn-outline) {
	background: linear-gradient(135deg, #e35534 0%, #d9421e 80%) !important;
	color: #FFF;
}

.btn-gradient-primary:not(.btn-outline):hover,
.btn-gradient-primary:not(.btn-outline).hover {
	background: linear-gradient(135deg, #e2512f 0%, #3f9bd0 80%) !important;
	color: #FFF;
}

.btn-gradient-primary:not(.btn-outline):focus,
.btn-gradient-primary:not(.btn-outline).focus {
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

.btn-gradient-primary:not(.btn-outline).disabled,
.btn-gradient-primary:not(.btn-outline):disabled {
	background: linear-gradient(135deg, #e35534 80%, #d9421e 0%) !important;
}

.btn-gradient-primary:not(.btn-outline):active,
.btn-gradient-primary:not(.btn-outline).active {
	background: linear-gradient(135deg, #e2512f 0%, #3f9bd0 80%) !important;
	color: #FFF !important;
}

.btn-gradient-primary.btn-outline {
	-o-border-image: linear-gradient(135deg, #2E3191 0%, #e56446 80%);
	border-image: linear-gradient(135deg, #2E3191 0%, #e56446 80%);
	color: #2E3191;
}

.btn-gradient-primary.btn-outline:hover,
.btn-gradient-primary.btn-outline.hover {
	-o-border-image: linear-gradient(135deg, #3f9bd0 0%, #3f9bd0 80%);
	border-image: linear-gradient(135deg, #3f9bd0 0%, #3f9bd0 80%);
	color: #3f9bd0;
}

.btn-gradient-primary.btn-outline:focus,
.btn-gradient-primary.btn-outline.focus {
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

.btn-gradient-primary.btn-outline.disabled,
.btn-gradient-primary.btn-outline:disabled {
	-o-border-image: linear-gradient(135deg, #2E3191 0%, #e56446 80%);
	border-image: linear-gradient(135deg, #2E3191 0%, #e56446 80%);
}

.btn-gradient-primary.btn-outline:active,
.btn-gradient-primary.btn-outline.active {
	-o-border-image: linear-gradient(135deg, #3f9bd0 0%, #3f9bd0 80%);
	border-image: linear-gradient(135deg, #3f9bd0 0%, #3f9bd0 80%);
	color: #3f9bd0;
}

.pagination > a,
.pagination > a:hover,
.pagination > a:focus,
.pagination > li > a,
.pagination > li > span,
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
	color: #2E3191;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus,
.pagination .page-item.active .page-link {
	background-color: #2E3191 !important;
	border-color: #2E3191;
}

body.dark .pagination > li > a,
body.dark .pagination > li > span,
body.dark .pagination > li > a:hover,
body.dark .pagination > li > span:hover,
body.dark .pagination > li > a:focus,
body.dark .pagination > li > span:focus {
	color: #2E3191;
}

body.dark .pagination > .active > a,
body.dark .pagination > .active > span,
body.dark .pagination > .active > a:hover,
body.dark .pagination > .active > span:hover,
body.dark .pagination > .active > a:focus,
body.dark .pagination > .active > span:focus {
	background-color: #2E3191;
	border-color: #2E3191;
}

.pagination > .active > a,
body.dark .pagination > .active > a {
	background-color: #2E3191;
	border-color: #2E3191 #2E3191 #A7A9AC;
	color: #fff;
}

.pagination > .active > a:hover,
body.dark .pagination > .active > a:hover,
.pagination > .active > a.hover,
body.dark .pagination > .active > a.hover {
	border-color: #231F20 #231F20 #2E3191;
	color: #fff;
}

.pagination > .active > a:hover:not(.bg-transparent),
body.dark .pagination > .active > a:hover:not(.bg-transparent),
.pagination > .active > a.hover:not(.bg-transparent),
body.dark .pagination > .active > a.hover:not(.bg-transparent) {
	background-color: #3f9bd0;
}

.pagination > .active > a:focus,
body.dark .pagination > .active > a:focus,
.pagination > .active > a.focus,
body.dark .pagination > .active > a.focus {
	border-color: #A7A9AC #A7A9AC #892913;
	color: #fff;
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

.pagination > .active > a:focus:not(.bg-transparent),
body.dark .pagination > .active > a:focus:not(.bg-transparent),
.pagination > .active > a.focus:not(.bg-transparent),
body.dark .pagination > .active > a.focus:not(.bg-transparent) {
	background-color: #606060;
}

.pagination > .active > a.disabled,
body.dark .pagination > .active > a.disabled,
.pagination > .active > a:disabled,
body.dark .pagination > .active > a:disabled {
	border-color: #2E3191 #2E3191 #A7A9AC;
}

.pagination > .active > a.disabled:not(.bg-transparent),
body.dark .pagination > .active > a.disabled:not(.bg-transparent),
.pagination > .active > a:disabled:not(.bg-transparent),
body.dark .pagination > .active > a:disabled:not(.bg-transparent) {
	background-color: #2E3191;
}

.pagination > .active > a:active,
body.dark .pagination > .active > a:active,
.pagination > .active > a.active,
body.dark .pagination > .active > a.active {
	border-color: #A7A9AC #A7A9AC #892913 !important;
}

.pagination > .active > a:active:not(.bg-transparent),
body.dark .pagination > .active > a:active:not(.bg-transparent),
.pagination > .active > a.active:not(.bg-transparent),
body.dark .pagination > .active > a.active:not(.bg-transparent) {
	background-color: #606060 !important;
	background-image: none !important;
}

.custom-control-input:checked ~ .custom-control-label::before,
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
	background-color: #2E3191;
}

.custom-checkbox-switch:checked {
	background-color: #2E3191;
	border-color: #2E3191;
}

html .alert-primary {
	background-color: #2E3191;
	border-color: #d5411e;
	color: #FFF;
}

html .alert-primary .alert-link {
	color: #FFF;
}

html .alert-secondary {
	background-color: #45BEE4;
	border-color: #eda618;
	color: #777;
}

html .alert-secondary .alert-link {
	color: #777;
}

html .alert-tertiary {
	background-color: #EAEFF3;
	border-color: #e0e8ed;
	color: #777;
}

html .alert-tertiary .alert-link {
	color: #777;
}

html .alert-quaternary {
	background-color: #080808;
	border-color: #000000;
	color: #FFF;
}

html .alert-quaternary .alert-link {
	color: #FFF;
}

html .alert-dark {
	background-color: #212529;
	border-color: #1a1d21;
	color: #FFF;
}

html .alert-dark .alert-link {
	color: #FFF;
}

html .alert-light {
	background-color: #FFF;
	border-color: #f7f7f7;
	color: #777;
}

html .alert-light .alert-link {
	color: #777;
}

html .progress-bar-primary {
	background-color: #2E3191;
}

html .progress-bar-secondary {
	background-color: #45BEE4;
}

html .progress-bar-tertiary {
	background-color: #EAEFF3;
}

html .progress-bar-quaternary {
	background-color: #080808;
}

html .progress-bar-dark {
	background-color: #212529;
}

html .progress-bar-light {
	background-color: #FFF;
}

html .word-rotator.loading-bar-primary .word-rotator-words::after {
	background-color: #2E3191;
}

html .word-rotator.loading-bar-secondary .word-rotator-words::after {
	background-color: #45BEE4;
}

html .word-rotator.loading-bar-tertiary .word-rotator-words::after {
	background-color: #EAEFF3;
}

html .word-rotator.loading-bar-quaternary .word-rotator-words::after {
	background-color: #080808;
}

html .word-rotator.loading-bar-dark .word-rotator-words::after {
	background-color: #212529;
}

html .word-rotator.loading-bar-light .word-rotator-words::after {
	background-color: #FFF;
}
/* Mark */
html .mark.mark-gradient.mark-gradient-primary:before {
	background: #2E3191;
	background: linear-gradient(90deg, #2E3191 0%, rgba(224, 70, 34, 0) 100%);
}

html .mark.mark-gradient.mark-gradient-secondary:before {
	background: #45BEE4;
	background: linear-gradient(90deg, #45BEE4 0%, rgba(238, 171, 38, 0) 100%);
}

html .mark.mark-gradient.mark-gradient-tertiary:before {
	background: #eaeff3;
	background: linear-gradient(90deg, #eaeff3 0%, rgba(234, 239, 243, 0) 100%);
}

html .mark.mark-gradient.mark-gradient-quaternary:before {
	background: #080808;
	background: linear-gradient(90deg, #080808 0%, rgba(8, 8, 8, 0) 100%);
}

html .mark.mark-gradient.mark-gradient-dark:before {
	background: #212529;
	background: linear-gradient(90deg, #212529 0%, rgba(33, 37, 41, 0) 100%);
}

html .mark.mark-gradient.mark-gradient-light:before {
	background: #ffffff;
	background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.process .process-step-circle {
	border-color: #2E3191;
}

.process .process-step:hover .process-step-circle {
	background-color: #2E3191;
}

.process .process-step:hover .process-step-circle .process-step-circle-content {
	color: #FFF !important;
}

.process.process-shapes .process-step-circle .process-step-circle-content,
.process.process-shapes .process-step-circle:before,
.process.process-shapes .process-step-circle:after {
	background-color: #2E3191;
}

.process-connecting-line .connecting-line {
	background: #2E3191;
}

html .rating-primary .filled-stars {
	color: #2E3191;
}

html .rating-secondary .filled-stars {
	color: #45BEE4;
}

html .rating-tertiary .filled-stars {
	color: #EAEFF3;
}

html .rating-quaternary .filled-stars {
	color: #080808;
}

html .rating-dark .filled-stars {
	color: #212529;
}

html .rating-light .filled-stars {
	color: #FFF;
}

html section.section-primary {
	background-color: #2E3191 !important;
	border-color: #cc3e1d !important;
}

html section.section-primary h1,
html section.section-primary h2,
html section.section-primary h3,
html section.section-primary h4,
html section.section-primary h5,
html section.section-primary h6 {
	color: #FFF;
}

html section.section-primary p {
	color: #e6e6e6;
}

html section.section-primary-scale-2 {
	background-color: #A7A9AC !important;
	border-color: #9f3016 !important;
}

html section.section-primary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: #A7A9AC;
}

html section.section-secondary {
	background-color: #45BEE4 !important;
	border-color: #e8a112 !important;
}

html section.section-secondary h1,
html section.section-secondary h2,
html section.section-secondary h3,
html section.section-secondary h4,
html section.section-secondary h5,
html section.section-secondary h6 {
	color: #777;
}

html section.section-secondary p {
	color: #5e5e5e;
}

html section.section-secondary-scale-2 {
	background-color: #d19010 !important;
	border-color: #b9800e !important;
}

html section.section-secondary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: #d19010;
}

html section.section-tertiary {
	background-color: #EAEFF3 !important;
	border-color: #dae3ea !important;
}

html section.section-tertiary h1,
html section.section-tertiary h2,
html section.section-tertiary h3,
html section.section-tertiary h4,
html section.section-tertiary h5,
html section.section-tertiary h6 {
	color: #777;
}

html section.section-tertiary p {
	color: #5e5e5e;
}

html section.section-tertiary-scale-2 {
	background-color: #cad6e0 !important;
	border-color: #b9cad7 !important;
}

html section.section-tertiary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: #cad6e0;
}

html section.section-quaternary {
	background-color: #080808 !important;
	border-color: #000000 !important;
}

html section.section-quaternary h1,
html section.section-quaternary h2,
html section.section-quaternary h3,
html section.section-quaternary h4,
html section.section-quaternary h5,
html section.section-quaternary h6 {
	color: #FFF;
}

html section.section-quaternary p {
	color: #e6e6e6;
}

html section.section-quaternary-scale-2 {
	background-color: #000000 !important;
	border-color: #000000 !important;
}

html section.section-quaternary-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: #000000;
}

html section.section-dark {
	background-color: #212529 !important;
	border-color: #16181b !important;
}

html section.section-dark h1,
html section.section-dark h2,
html section.section-dark h3,
html section.section-dark h4,
html section.section-dark h5,
html section.section-dark h6 {
	color: #FFF;
}

html section.section-dark p {
	color: #e6e6e6;
}

html section.section-dark-scale-2 {
	background-color: #0a0c0d !important;
	border-color: #000000 !important;
}

html section.section-dark-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: #0a0c0d;
}

html section.section-light {
	background-color: #FFF !important;
	border-color: #f2f2f2 !important;
}

html section.section-light h1,
html section.section-light h2,
html section.section-light h3,
html section.section-light h4,
html section.section-light h5,
html section.section-light h6 {
	color: #777;
}

html section.section-light p {
	color: #5e5e5e;
}

html section.section-light-scale-2 {
	background-color: #e6e6e6 !important;
	border-color: #d9d9d9 !important;
}

html section.section-light-scale-2 .sort-source.sort-source-style-2 > li.active > a:after {
	border-top-color: #e6e6e6;
}

section.section.section-background-half-primary-half-secondary {
	background: linear-gradient(to right, #2E3191 50%, #45BEE4 50%);
}

section.section.section-overlay-opacity-gradient:before {
	background: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%);
}

html .bg-color-light-scale-1 {
	background-color: #f7f7f7 !important;
}

html .section.bg-color-light-scale-1,
html .bg-color-light-scale-border-top-1 {
	border-top-color: #efefef !important;
}

html .bg-color-light-scale-2 {
	background-color: #e8e8e8 !important;
}

html .section.bg-color-light-scale-2,
html .bg-color-light-scale-border-top-2 {
	border-top-color: #e0e0e0 !important;
}

html .bg-color-light-scale-3 {
	background-color: #e0e0e0 !important;
}

html .section.bg-color-light-scale-3,
html .bg-color-light-scale-border-top-3 {
	border-top-color: #d8d8d8 !important;
}

html .bg-color-light-scale-4 {
	background-color: #d8d8d8 !important;
}

html .section.bg-color-light-scale-4,
html .bg-color-light-scale-border-top-4 {
	border-top-color: #d1d1d1 !important;
}

html .bg-color-light-scale-5 {
	background-color: #d1d1d1 !important;
}

html .section.bg-color-light-scale-5,
html .bg-color-light-scale-border-top-5 {
	border-top-color: #c9c9c9 !important;
}

html .bg-color-light-scale-6 {
	background-color: #c9c9c9 !important;
}

html .section.bg-color-light-scale-6,
html .bg-color-light-scale-border-top-6 {
	border-top-color: #c1c1c1 !important;
}

html .bg-color-light-scale-7 {
	background-color: #c1c1c1 !important;
}

html .section.bg-color-light-scale-7,
html .bg-color-light-scale-border-top-7 {
	border-top-color: #bababa !important;
}

html .bg-color-light-scale-8 {
	background-color: #bababa !important;
}

html .section.bg-color-light-scale-8,
html .bg-color-light-scale-border-top-8 {
	border-top-color: #b2b2b2 !important;
}

html .bg-color-light-scale-9 {
	background-color: #b2b2b2 !important;
}

html .section.bg-color-light-scale-9,
html .bg-color-light-scale-border-top-9 {
	border-top-color: #ababab !important;
}

html .bg-color-dark-scale-1 {
	background-color: #212529 !important;
}

html .section.bg-color-dark-scale-1,
html .bg-color-dark-scale-border-top-1 {
	border-top-color: #1a1d21 !important;
}

html .bg-color-dark-scale-2 {
	background-color: #1c2023 !important;
}

html .section.bg-color-dark-scale-2,
html .bg-color-dark-scale-border-top-2 {
	border-top-color: #16181b !important;
}

html .bg-color-dark-scale-3 {
	background-color: #181b1e !important;
}

html .section.bg-color-dark-scale-3,
html .bg-color-dark-scale-border-top-3 {
	border-top-color: #111315 !important;
}

html .bg-color-dark-scale-4 {
	background-color: #131618 !important;
}

html .section.bg-color-dark-scale-4,
html .bg-color-dark-scale-border-top-4 {
	border-top-color: #0d0e10 !important;
}

html .bg-color-dark-scale-5 {
	background-color: #0f1112 !important;
}

html .section.bg-color-dark-scale-5,
html .bg-color-dark-scale-border-top-5 {
	border-top-color: #08090a !important;
}

html .bg-color-dark-scale-6 {
	background-color: #0a0c0d !important;
}

html .section.bg-color-dark-scale-6,
html .bg-color-dark-scale-border-top-6 {
	border-top-color: #030404 !important;
}

html .bg-color-dark-scale-7 {
	background-color: #060607 !important;
}

html .section.bg-color-dark-scale-7,
html .bg-color-dark-scale-border-top-7 {
	border-top-color: #000000 !important;
}

html .bg-color-dark-scale-8 {
	background-color: #010101 !important;
}

html .section.bg-color-dark-scale-8,
html .bg-color-dark-scale-border-top-8 {
	border-top-color: #000000 !important;
}

html .bg-color-dark-scale-9 {
	background-color: #000000 !important;
}

html .section.bg-color-dark-scale-9,
html .bg-color-dark-scale-border-top-9 {
	border-top-color: #000000 !important;
}

section.page-header .page-header-title-border {
	background-color: #2E3191 !important;
}

section.page-header.custom-product {
	background-color: #cc3e1d;
	border-top-color: #d9421e;
}

html .page-header-color.page-header-primary {
	background-color: #2E3191;
	border-bottom-color: #2E3191;
	color: #FFF;
}

html .page-header-color.page-header-primary h1 {
	color: #FFF;
}

html .page-header-color.page-header-primary h1 span {
	color: #FFF;
}

html .page-header-color.page-header-primary a {
	color: #FFF;
}

html .page-header-color.page-header-primary .breadcrumb > .active {
	color: #FFF;
}

html .page-header-color.page-header-secondary {
	background-color: #45BEE4;
	border-bottom-color: #45BEE4;
	color: #777;
}

html .page-header-color.page-header-secondary h1 {
	color: #777;
}

html .page-header-color.page-header-secondary h1 span {
	color: #777;
}

html .page-header-color.page-header-secondary a {
	color: #777;
}

html .page-header-color.page-header-secondary .breadcrumb > .active {
	color: #777;
}

html .page-header-color.page-header-tertiary {
	background-color: #EAEFF3;
	border-bottom-color: #EAEFF3;
	color: #777;
}

html .page-header-color.page-header-tertiary h1 {
	color: #777;
}

html .page-header-color.page-header-tertiary h1 span {
	color: #777;
}

html .page-header-color.page-header-tertiary a {
	color: #777;
}

html .page-header-color.page-header-tertiary .breadcrumb > .active {
	color: #777;
}

html .page-header-color.page-header-quaternary {
	background-color: #080808;
	border-bottom-color: #080808;
	color: #FFF;
}

html .page-header-color.page-header-quaternary h1 {
	color: #FFF;
}

html .page-header-color.page-header-quaternary h1 span {
	color: #FFF;
}

html .page-header-color.page-header-quaternary a {
	color: #FFF;
}

html .page-header-color.page-header-quaternary .breadcrumb > .active {
	color: #FFF;
}

html .page-header-color.page-header-dark {
	background-color: #212529;
	border-bottom-color: #212529;
	color: #FFF;
}

html .page-header-color.page-header-dark h1 {
	color: #FFF;
}

html .page-header-color.page-header-dark h1 span {
	color: #FFF;
}

html .page-header-color.page-header-dark a {
	color: #FFF;
}

html .page-header-color.page-header-dark .breadcrumb > .active {
	color: #FFF;
}

html .page-header-color.page-header-light {
	background-color: #FFF;
	border-bottom-color: #FFF;
	color: #777;
}

html .page-header-color.page-header-light h1 {
	color: #777;
}

html .page-header-color.page-header-light h1 span {
	color: #777;
}

html .page-header-color.page-header-light a {
	color: #777;
}

html .page-header-color.page-header-light .breadcrumb > .active {
	color: #777;
}

html .toggle-primary .toggle label,
html .toggle-primary .toggle .toggle-title {
	color: #2E3191;
	border-left-color: #2E3191;
	border-right-color: #2E3191;
}

html .toggle-primary .toggle.active > label,
html .toggle-primary .toggle.active > .toggle-title {
	background-color: #2E3191;
	border-color: #2E3191;
	color: #FFF;
}

html .toggle-primary.toggle-simple .toggle > label:after,
html .toggle-primary.toggle-simple .toggle > .toggle-title:after {
	background-color: #2E3191;
}

html .toggle-primary.toggle-minimal .toggle.active > label,
html .toggle-primary.toggle-minimal .toggle.active > .toggle-title {
	color: #2E3191;
}

html .toggle-secondary .toggle label,
html .toggle-secondary .toggle .toggle-title {
	color: #45BEE4;
	border-left-color: #45BEE4;
	border-right-color: #45BEE4;
}

html .toggle-secondary .toggle.active > label,
html .toggle-secondary .toggle.active > .toggle-title {
	background-color: #45BEE4;
	border-color: #45BEE4;
	color: #777;
}

html .toggle-secondary.toggle-simple .toggle > label:after,
html .toggle-secondary.toggle-simple .toggle > .toggle-title:after {
	background-color: #45BEE4;
}

html .toggle-secondary.toggle-minimal .toggle.active > label,
html .toggle-secondary.toggle-minimal .toggle.active > .toggle-title {
	color: #45BEE4;
}

html .toggle-tertiary .toggle label,
html .toggle-tertiary .toggle .toggle-title {
	color: #EAEFF3;
	border-left-color: #EAEFF3;
	border-right-color: #EAEFF3;
}

html .toggle-tertiary .toggle.active > label,
html .toggle-tertiary .toggle.active > .toggle-title {
	background-color: #EAEFF3;
	border-color: #EAEFF3;
	color: #777;
}

html .toggle-tertiary.toggle-simple .toggle > label:after,
html .toggle-tertiary.toggle-simple .toggle > .toggle-title:after {
	background-color: #EAEFF3;
}

html .toggle-tertiary.toggle-minimal .toggle.active > label,
html .toggle-tertiary.toggle-minimal .toggle.active > .toggle-title {
	color: #EAEFF3;
}

html .toggle-quaternary .toggle label,
html .toggle-quaternary .toggle .toggle-title {
	color: #080808;
	border-left-color: #080808;
	border-right-color: #080808;
}

html .toggle-quaternary .toggle.active > label,
html .toggle-quaternary .toggle.active > .toggle-title {
	background-color: #080808;
	border-color: #080808;
	color: #FFF;
}

html .toggle-quaternary.toggle-simple .toggle > label:after,
html .toggle-quaternary.toggle-simple .toggle > .toggle-title:after {
	background-color: #080808;
}

html .toggle-quaternary.toggle-minimal .toggle.active > label,
html .toggle-quaternary.toggle-minimal .toggle.active > .toggle-title {
	color: #080808;
}

html .toggle-dark .toggle label,
html .toggle-dark .toggle .toggle-title {
	color: #212529;
	border-left-color: #212529;
	border-right-color: #212529;
}

html .toggle-dark .toggle.active > label,
html .toggle-dark .toggle.active > .toggle-title {
	background-color: #212529;
	border-color: #212529;
	color: #FFF;
}

html .toggle-dark.toggle-simple .toggle > label:after,
html .toggle-dark.toggle-simple .toggle > .toggle-title:after {
	background-color: #212529;
}

html .toggle-dark.toggle-minimal .toggle.active > label,
html .toggle-dark.toggle-minimal .toggle.active > .toggle-title {
	color: #212529;
}

html .toggle-light .toggle label,
html .toggle-light .toggle .toggle-title {
	color: #FFF;
	border-left-color: #FFF;
	border-right-color: #FFF;
}

html .toggle-light .toggle.active > label,
html .toggle-light .toggle.active > .toggle-title {
	background-color: #FFF;
	border-color: #FFF;
	color: #777;
}

html .toggle-light.toggle-simple .toggle > label:after,
html .toggle-light.toggle-simple .toggle > .toggle-title:after {
	background-color: #FFF;
}

html .toggle-light.toggle-minimal .toggle.active > label,
html .toggle-light.toggle-minimal .toggle.active > .toggle-title {
	color: #FFF;
}

.thumb-info .thumb-info-type,
.thumb-info .thumb-info-action-icon,
.thumb-info-social-icons a,
.thumbnail .zoom,
.img-thumbnail .zoom,
.thumb-info-ribbon {
	background-color: #2E3191;
}

html .thumb-info .thumb-info-action-icon-primary {
	background-color: #2E3191;
}

html .thumb-info .thumb-info-action-icon-primary i {
	color: #FFF;
}

html .thumb-info .thumb-info-action-icon-secondary {
	background-color: #45BEE4;
}

html .thumb-info .thumb-info-action-icon-secondary i {
	color: #777;
}

html .thumb-info .thumb-info-action-icon-tertiary {
	background-color: #EAEFF3;
}

html .thumb-info .thumb-info-action-icon-tertiary i {
	color: #777;
}

html .thumb-info .thumb-info-action-icon-quaternary {
	background-color: #080808;
}

html .thumb-info .thumb-info-action-icon-quaternary i {
	color: #FFF;
}

html .thumb-info .thumb-info-action-icon-dark {
	background-color: #212529;
}

html .thumb-info .thumb-info-action-icon-dark i {
	color: #FFF;
}

html .thumb-info .thumb-info-action-icon-light {
	background-color: #FFF;
}

html .thumb-info .thumb-info-action-icon-light i {
	color: #777;
}

.thumb-info-ribbon:before {
	border-right-color: #9f3016;
	border-left-color: #9f3016;
}

.thumb-info.thumb-info-block-primary .thumb-info-wrapper:before {
	background: rgba(224, 70, 34, 0.9);
}

.thumb-info-floating-caption-title .thumb-info-floating-caption-type {
	background-color: #2E3191;
}

.inverted {
	background-color: #2E3191;
}

html .inverted-primary {
	background-color: #2E3191;
}

html .inverted-secondary {
	background-color: #45BEE4;
}

html .inverted-tertiary {
	background-color: #EAEFF3;
}

html .inverted-quaternary {
	background-color: #080808;
}

html .inverted-dark {
	background-color: #212529;
}

html .inverted-light {
	background-color: #FFF;
}

.owl-carousel .owl-dots .owl-dot.active span,
.owl-carousel .owl-dots .owl-dot:hover span {
	background-color: #c73c1c;
}

.owl-carousel.show-nav-title .owl-nav button[class*="owl-"],
.owl-carousel.show-nav-title .owl-nav button[class*="owl-"]:hover,
.owl-carousel.show-nav-title .owl-nav button[class*="owl-"].hover {
	color: #2E3191;
}

.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"] {
	background-color: #2E3191;
	border-color: #2E3191 #2E3191 #A7A9AC;
	color: #FFF;
}

.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"]:hover,
.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"].hover {
	background-color: #3f9bd0;
	border-color: #231F20 #231F20 #2E3191;
}

.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"]:active,
.owl-carousel:not(.nav-arrows-1):not(.show-nav-title) .owl-nav button[class*="owl-"].active {
	background-color: #606060;
	background-image: none;
	border-color: #A7A9AC #A7A9AC #892913;
}

.owl-carousel.nav-with-transparency:not(.nav-style-1):not(.show-nav-title):not(.nav-arrows-1) .owl-nav button[class*="owl-"] {
	background-color: rgba(224, 70, 34, 0.4);
}

.owl-carousel.nav-style-1 .owl-nav .owl-next,
.owl-carousel.nav-style-1 .owl-nav .owl-prev {
	color: #2E3191 !important;
}

.owl-carousel.nav-style-2 .owl-nav .owl-next:before,
.owl-carousel.nav-style-2 .owl-nav .owl-prev:before,
.owl-carousel.nav-style-2 .owl-nav .owl-next:after,
.owl-carousel.nav-style-2 .owl-nav .owl-prev:after {
	border-color: #2E3191;
}

.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-prev svg polygon,
.owl-carousel.nav-svg-arrows-1 .owl-nav .owl-next svg polygon {
	fill: #2E3191;
	stroke: #2E3191;
}

.owl-carousel.nav-arrows-1 .owl-nav .owl-prev,
.owl-carousel.nav-arrows-1 .owl-nav .owl-next {
	color: #2E3191;
}

.owl-carousel.carousel-center-active-item-2 .owl-item.active > div {
	background: #2E3191;
}

.owl-carousel.carousel-bottom-inside-shadow .owl-stage-outer:after {
	background-image: linear-gradient(360deg, #2E3191 0%, transparent 100%);
}

html body .tabs .nav-tabs .nav-link,
html.dark body .tabs .nav-tabs .nav-link,
html body .tabs .nav-tabs .nav-link:hover,
html.dark body .tabs .nav-tabs .nav-link:hover,
html body .tabs .nav-tabs .nav-link.active,
html.dark body .tabs .nav-tabs .nav-link.active {
	color: #2E3191;
}

html body .tabs .nav-tabs .nav-link:hover,
html.dark body .tabs .nav-tabs .nav-link:hover,
html body .tabs .nav-tabs .nav-link:focus,
html.dark body .tabs .nav-tabs .nav-link:focus,
html body .tabs .nav-tabs .nav-link.active,
html.dark body .tabs .nav-tabs .nav-link.active,
html body .tabs .nav-tabs.nav-justified .nav-link.active,
html.dark body .tabs .nav-tabs.nav-justified .nav-link.active,
html body .tabs .nav-tabs.nav-justified .nav-link:hover,
html.dark body .tabs .nav-tabs.nav-justified .nav-link:hover,
html body .tabs .nav-tabs.nav-justified .nav-link:focus,
html.dark body .tabs .nav-tabs.nav-justified .nav-link:focus {
	border-top-color: #2E3191;
}

html body .tabs.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs.tabs-bottom .nav-tabs .nav-link.active,
html.dark body .tabs.tabs-bottom .nav-tabs .nav-link.active,
html body .tabs.tabs-bottom .nav-tabs .nav-link.active:hover,
html.dark body .tabs.tabs-bottom .nav-tabs .nav-link.active:hover,
html body .tabs.tabs-bottom .nav-tabs .nav-link.active:focus,
html.dark body .tabs.tabs-bottom .nav-tabs .nav-link.active:focus {
	border-bottom-color: #2E3191;
}

html body .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link:hover,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link:hover,
html body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active,
html body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:hover,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:hover,
html body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:focus,
html.dark body .tabs.tabs-vertical.tabs-left .nav-tabs .nav-link.active:focus {
	border-left-color: #2E3191;
}

html body .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link:hover,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link:hover,
html body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active,
html body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:hover,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:hover,
html body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:focus,
html.dark body .tabs.tabs-vertical.tabs-right .nav-tabs .nav-link.active:focus {
	border-right-color: #2E3191;
}

html body .tabs.tabs-simple .nav-tabs > li .nav-link.active,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link.active,
html body .tabs.tabs-simple .nav-tabs > li .nav-link.active:focus,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link.active:focus,
html body .tabs.tabs-simple .nav-tabs > li .nav-link:hover,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link:hover,
html body .tabs.tabs-simple .nav-tabs > li .nav-link.active:hover,
html.dark body .tabs.tabs-simple .nav-tabs > li .nav-link.active:hover {
	border-top-color: #2E3191;
	color: #2E3191;
}

html body .tabs-primary .nav-tabs li .nav-link,
html.dark body .tabs-primary .nav-tabs li .nav-link,
html body .tabs-primary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link,
html body .tabs-primary .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs li .nav-link:hover,
html body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover {
	color: #2E3191;
}

html body .tabs-primary .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs li .nav-link:hover,
html body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: #2E3191;
}

html body .tabs-primary .nav-tabs li .nav-link.active,
html.dark body .tabs-primary .nav-tabs li .nav-link.active,
html body .tabs-primary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-primary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-primary .nav-tabs li .nav-link.active:hover,
html body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-primary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-primary .nav-tabs li .nav-link.active:focus,
html body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-primary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: #2E3191;
	color: #2E3191;
}

html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active,
html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: #2E3191;
}

html body .tabs-primary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link:hover,
html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active,
html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:hover,
html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-simple .nav-tabs li .nav-link.active:focus,
html body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: #2E3191 !important;
	border-bottom-color: #2E3191;
}

html body .tabs-primary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link:hover,
html body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active,
html body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:hover,
html body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: #2E3191;
}

html body .tabs-primary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link:hover,
html body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active,
html body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:hover,
html body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-primary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: #2E3191;
}

html body .tabs-secondary .nav-tabs li .nav-link,
html.dark body .tabs-secondary .nav-tabs li .nav-link,
html body .tabs-secondary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link,
html body .tabs-secondary .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs li .nav-link:hover,
html body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover {
	color: #45BEE4;
}

html body .tabs-secondary .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs li .nav-link:hover,
html body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: #45BEE4;
}

html body .tabs-secondary .nav-tabs li .nav-link.active,
html.dark body .tabs-secondary .nav-tabs li .nav-link.active,
html body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-secondary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-secondary .nav-tabs li .nav-link.active:hover,
html body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-secondary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-secondary .nav-tabs li .nav-link.active:focus,
html body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-secondary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: #45BEE4;
	color: #45BEE4;
}

html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active,
html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: #45BEE4;
}

html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link:hover,
html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active,
html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:hover,
html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-simple .nav-tabs li .nav-link.active:focus,
html body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: #45BEE4 !important;
	border-bottom-color: #45BEE4;
}

html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link:hover,
html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active,
html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:hover,
html body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: #45BEE4;
}

html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link:hover,
html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active,
html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:hover,
html body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-secondary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: #45BEE4;
}

html body .tabs-tertiary .nav-tabs li .nav-link,
html.dark body .tabs-tertiary .nav-tabs li .nav-link,
html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link,
html body .tabs-tertiary .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs li .nav-link:hover,
html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover {
	color: #EAEFF3;
}

html body .tabs-tertiary .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs li .nav-link:hover,
html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: #EAEFF3;
}

html body .tabs-tertiary .nav-tabs li .nav-link.active,
html.dark body .tabs-tertiary .nav-tabs li .nav-link.active,
html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-tertiary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-tertiary .nav-tabs li .nav-link.active:hover,
html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-tertiary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-tertiary .nav-tabs li .nav-link.active:focus,
html body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-tertiary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: #EAEFF3;
	color: #EAEFF3;
}

html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active,
html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: #EAEFF3;
}

html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link:hover,
html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active,
html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:hover,
html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs li .nav-link.active:focus,
html body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: #EAEFF3 !important;
	border-bottom-color: #EAEFF3;
}

html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link:hover,
html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active,
html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:hover,
html body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: #EAEFF3;
}

html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link:hover,
html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active,
html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:hover,
html body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-tertiary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: #EAEFF3;
}

html body .tabs-quaternary .nav-tabs li .nav-link,
html.dark body .tabs-quaternary .nav-tabs li .nav-link,
html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link,
html body .tabs-quaternary .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs li .nav-link:hover,
html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover {
	color: #080808;
}

html body .tabs-quaternary .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs li .nav-link:hover,
html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: #080808;
}

html body .tabs-quaternary .nav-tabs li .nav-link.active,
html.dark body .tabs-quaternary .nav-tabs li .nav-link.active,
html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-quaternary .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-quaternary .nav-tabs li .nav-link.active:hover,
html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-quaternary .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-quaternary .nav-tabs li .nav-link.active:focus,
html body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-quaternary .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: #080808;
	color: #080808;
}

html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active,
html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:hover,
html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs li .nav-link.active:focus,
html body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: #080808;
}

html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link:hover,
html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active,
html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:hover,
html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs li .nav-link.active:focus,
html body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: #080808 !important;
	border-bottom-color: #080808;
}

html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link:hover,
html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active,
html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:hover,
html body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: #080808;
}

html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link:hover,
html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active,
html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:hover,
html body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-quaternary.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: #080808;
}

html body .tabs-dark .nav-tabs li .nav-link,
html.dark body .tabs-dark .nav-tabs li .nav-link,
html body .tabs-dark .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link,
html body .tabs-dark .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs li .nav-link:hover,
html body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover {
	color: #212529;
}

html body .tabs-dark .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs li .nav-link:hover,
html body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: #212529;
}

html body .tabs-dark .nav-tabs li .nav-link.active,
html.dark body .tabs-dark .nav-tabs li .nav-link.active,
html body .tabs-dark .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-dark .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-dark .nav-tabs li .nav-link.active:hover,
html body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-dark .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-dark .nav-tabs li .nav-link.active:focus,
html body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-dark .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: #212529;
	color: #212529;
}

html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active,
html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:hover,
html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-bottom .nav-tabs li .nav-link.active:focus,
html body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: #212529;
}

html body .tabs-dark.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link:hover,
html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active,
html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:hover,
html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-simple .nav-tabs li .nav-link.active:focus,
html body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: #212529 !important;
	border-bottom-color: #212529;
}

html body .tabs-dark.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link:hover,
html body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active,
html body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:hover,
html body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: #212529;
}

html body .tabs-dark.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link:hover,
html body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active,
html body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:hover,
html body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-dark.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: #212529;
}

html body .tabs-light .nav-tabs li .nav-link,
html.dark body .tabs-light .nav-tabs li .nav-link,
html body .tabs-light .nav-tabs.nav-justified li .nav-link,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link,
html body .tabs-light .nav-tabs li .nav-link:hover,
html.dark body .tabs-light .nav-tabs li .nav-link:hover,
html body .tabs-light .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link:hover {
	color: #FFF;
}

html body .tabs-light .nav-tabs li .nav-link:hover,
html.dark body .tabs-light .nav-tabs li .nav-link:hover,
html body .tabs-light .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link:hover {
	border-top-color: #FFF;
}

html body .tabs-light .nav-tabs li .nav-link.active,
html.dark body .tabs-light .nav-tabs li .nav-link.active,
html body .tabs-light .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-light .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-light .nav-tabs li .nav-link.active:hover,
html body .tabs-light .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-light .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-light .nav-tabs li .nav-link.active:focus,
html body .tabs-light .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-light .nav-tabs.nav-justified li .nav-link.active:focus {
	border-top-color: #FFF;
	color: #FFF;
}

html body .tabs-light.tabs-bottom .nav-tabs li .nav-link:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link:hover,
html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active,
html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:hover,
html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-light.tabs-bottom .nav-tabs li .nav-link.active:focus,
html body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-light.tabs-bottom .nav-tabs.nav-justified li .nav-link.active:focus {
	border-bottom-color: #FFF;
}

html body .tabs-light.tabs-simple .nav-tabs li .nav-link:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link:hover,
html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link:hover,
html body .tabs-light.tabs-simple .nav-tabs li .nav-link.active,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link.active,
html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active,
html body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:hover,
html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:hover,
html body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:focus,
html.dark body .tabs-light.tabs-simple .nav-tabs li .nav-link.active:focus,
html body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus,
html.dark body .tabs-light.tabs-simple .nav-tabs.nav-justified li .nav-link.active:focus {
	color: #FFF !important;
	border-bottom-color: #FFF;
}

html body .tabs-light.tabs-vertical.tabs-left li .nav-link:hover,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link:hover,
html body .tabs-light.tabs-vertical.tabs-left li .nav-link.active,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link.active,
html body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:hover,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:hover,
html body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:focus,
html.dark body .tabs-light.tabs-vertical.tabs-left li .nav-link.active:focus {
	border-left-color: #FFF;
}

html body .tabs-light.tabs-vertical.tabs-right li .nav-link:hover,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link:hover,
html body .tabs-light.tabs-vertical.tabs-right li .nav-link.active,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link.active,
html body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:hover,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:hover,
html body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:focus,
html.dark body .tabs-light.tabs-vertical.tabs-right li .nav-link.active:focus {
	border-right-color: #FFF;
}

html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link:hover html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link.active,
html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link.active:hover,
html[dir="rtl"] .tabs.tabs-vertical.tabs-left .nav-tabs li .nav-link.active:focus {
	border-right-color: #2E3191;
	border-left-color: transparent;
}

html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link:hover html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link.active,
html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link.active:hover,
html[dir="rtl"] .tabs.tabs-vertical.tabs-right .nav-tabs li .nav-link.active:focus {
	border-right-color: transparent;
	border-left-color: #2E3191;
}

.list.list-icons li > [class*="fa-"]:first-child,
.list.list-icons li a:first-child > [class*="fa-"]:first-child,
.list.list-icons li > .icons:first-child,
.list.list-icons li a:first-child > .icons:first-child {
	color: #2E3191;
	border-color: #2E3191;
}

.list.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
.list.list-icons.list-icons-style-3 li > .icons:first-child,
.list.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: #2E3191;
}

.list.list-ordened li:before {
	color: #2E3191;
	border-color: #2E3191;
}

.list.list-ordened.list-ordened-style-3 li:before {
	background-color: #2E3191;
}

html .list-primary.list-icons li > [class*="fa-"]:first-child,
html .list-primary.list-icons li a:first-child > [class*="fa-"]:first-child,
html .list-primary.list-icons li > .icons:first-child,
html .list-primary.list-icons li a:first-child > .icons:first-child {
	color: #2E3191;
	border-color: #2E3191;
}

html .list-primary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
html .list-primary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
html .list-primary.list-icons.list-icons-style-3 li > .icons:first-child,
html .list-primary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: #2E3191;
	color: #FFF;
}

html .list-primary.list-ordened li:before {
	color: #2E3191;
}

html .list-primary.list-ordened.list-ordened-style-3 li:before {
	background-color: #2E3191;
	color: #FFF;
}

html .list-secondary.list-icons li > [class*="fa-"]:first-child,
html .list-secondary.list-icons li a:first-child > [class*="fa-"]:first-child,
html .list-secondary.list-icons li > .icons:first-child,
html .list-secondary.list-icons li a:first-child > .icons:first-child {
	color: #45BEE4;
	border-color: #45BEE4;
}

html .list-secondary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
html .list-secondary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
html .list-secondary.list-icons.list-icons-style-3 li > .icons:first-child,
html .list-secondary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: #45BEE4;
	color: #777;
}

html .list-secondary.list-ordened li:before {
	color: #45BEE4;
}

html .list-secondary.list-ordened.list-ordened-style-3 li:before {
	background-color: #45BEE4;
	color: #777;
}

html .list-tertiary.list-icons li > [class*="fa-"]:first-child,
html .list-tertiary.list-icons li a:first-child > [class*="fa-"]:first-child,
html .list-tertiary.list-icons li > .icons:first-child,
html .list-tertiary.list-icons li a:first-child > .icons:first-child {
	color: #EAEFF3;
	border-color: #EAEFF3;
}

html .list-tertiary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
html .list-tertiary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
html .list-tertiary.list-icons.list-icons-style-3 li > .icons:first-child,
html .list-tertiary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: #EAEFF3;
	color: #777;
}

html .list-tertiary.list-ordened li:before {
	color: #EAEFF3;
}

html .list-tertiary.list-ordened.list-ordened-style-3 li:before {
	background-color: #EAEFF3;
	color: #777;
}

html .list-quaternary.list-icons li > [class*="fa-"]:first-child,
html .list-quaternary.list-icons li a:first-child > [class*="fa-"]:first-child,
html .list-quaternary.list-icons li > .icons:first-child,
html .list-quaternary.list-icons li a:first-child > .icons:first-child {
	color: #080808;
	border-color: #080808;
}

html .list-quaternary.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
html .list-quaternary.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
html .list-quaternary.list-icons.list-icons-style-3 li > .icons:first-child,
html .list-quaternary.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: #080808;
	color: #FFF;
}

html .list-quaternary.list-ordened li:before {
	color: #080808;
}

html .list-quaternary.list-ordened.list-ordened-style-3 li:before {
	background-color: #080808;
	color: #FFF;
}

html .list-dark.list-icons li > [class*="fa-"]:first-child,
html .list-dark.list-icons li a:first-child > [class*="fa-"]:first-child,
html .list-dark.list-icons li > .icons:first-child,
html .list-dark.list-icons li a:first-child > .icons:first-child {
	color: #212529;
	border-color: #212529;
}

html .list-dark.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
html .list-dark.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
html .list-dark.list-icons.list-icons-style-3 li > .icons:first-child,
html .list-dark.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: #212529;
	color: #FFF;
}

html .list-dark.list-ordened li:before {
	color: #212529;
}

html .list-dark.list-ordened.list-ordened-style-3 li:before {
	background-color: #212529;
	color: #FFF;
}

html .list-light.list-icons li > [class*="fa-"]:first-child,
html .list-light.list-icons li a:first-child > [class*="fa-"]:first-child,
html .list-light.list-icons li > .icons:first-child,
html .list-light.list-icons li a:first-child > .icons:first-child {
	color: #FFF;
	border-color: #FFF;
}

html .list-light.list-icons.list-icons-style-3 li > [class*="fa-"]:first-child,
html .list-light.list-icons.list-icons-style-3 li a:first-child > [class*="fa-"]:first-child,
html .list-light.list-icons.list-icons-style-3 li > .icons:first-child,
html .list-light.list-icons.list-icons-style-3 li a:first-child > .icons:first-child {
	background-color: #FFF;
	color: #777;
}

html .list-light.list-ordened li:before {
	color: #FFF;
}

html .list-light.list-ordened.list-ordened-style-3 li:before {
	background-color: #FFF;
	color: #777;
}

html .highlight-primary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, #2E3191 0);
}

html .highlight-primary.highlight-bg-opacity {
    background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, rgb(34 52 224 / 20%) 0);
}

html .highlight-primary.highlight-change-text-color {
	color: #FFF;
}

html .highlight-secondary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, #45BEE4 0);
}

html .highlight-secondary.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, rgba(238, 171, 38, 0.2) 0);
}

html .highlight-secondary.highlight-change-text-color {
	color: #777;
}

html .highlight-tertiary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, #EAEFF3 0);
}

html .highlight-tertiary.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, rgba(234, 239, 243, 0.2) 0);
}

html .highlight-tertiary.highlight-change-text-color {
	color: #777;
}

html .highlight-quaternary {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, #080808 0);
}

html .highlight-quaternary.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, rgba(8, 8, 8, 0.2) 0);
}

html .highlight-quaternary.highlight-change-text-color {
	color: #FFF;
}

html .highlight-dark {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, #212529 0);
}

html .highlight-dark.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, rgba(33, 37, 41, 0.2) 0);
}

html .highlight-dark.highlight-change-text-color {
	color: #FFF;
}

html .highlight-light {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, #FFF 0);
}

html .highlight-light.highlight-bg-opacity {
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.2) 0);
}

html .highlight-light.highlight-change-text-color {
	color: #777;
}

.parallax blockquote i.fa-quote-left {
	color: #2E3191;
}

section.video blockquote i.fa-quote-left {
	color: #2E3191;
}

.accordion .card-header a {
	color: #2E3191;
}

html .accordion.accordion-primary .card-header {
	background-color: #2E3191 !important;
}

html .accordion.accordion-primary .card-header a {
	color: #FFF;
}

html .accordion-modern-status-primary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: #2E3191 !important;
	color: #FFF !important;
}

html .accordion-modern-status-primary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: #2E3191 !important;
}

html .accordion.accordion-secondary .card-header {
	background-color: #45BEE4 !important;
}

html .accordion.accordion-secondary .card-header a {
	color: #777;
}

html .accordion-modern-status-secondary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: #45BEE4 !important;
	color: #777 !important;
}

html .accordion-modern-status-secondary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: #45BEE4 !important;
}

html .accordion.accordion-tertiary .card-header {
	background-color: #EAEFF3 !important;
}

html .accordion.accordion-tertiary .card-header a {
	color: #777;
}

html .accordion-modern-status-tertiary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: #EAEFF3 !important;
	color: #777 !important;
}

html .accordion-modern-status-tertiary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: #EAEFF3 !important;
}

html .accordion.accordion-quaternary .card-header {
	background-color: #080808 !important;
}

html .accordion.accordion-quaternary .card-header a {
	color: #FFF;
}

html .accordion-modern-status-quaternary.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: #080808 !important;
	color: #FFF !important;
}

html .accordion-modern-status-quaternary.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: #080808 !important;
}

html .accordion.accordion-dark .card-header {
	background-color: #212529 !important;
}

html .accordion.accordion-dark .card-header a {
	color: #FFF;
}

html .accordion-modern-status-dark.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: #212529 !important;
	color: #FFF !important;
}

html .accordion-modern-status-dark.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: #212529 !important;
}

html .accordion.accordion-light .card-header {
	background-color: #FFF !important;
}

html .accordion.accordion-light .card-header a {
	color: #777;
}

html .accordion-modern-status-light.accordion-modern-status > .card .card-header a:not(.collapsed) {
	background-color: #FFF !important;
	color: #777 !important;
}

html .accordion-modern-status-light.accordion-modern-status > .card:hover .card-header a.collapsed {
	color: #FFF !important;
}

html .divider.divider-primary [class*="fa-"],
html .divider.divider-primary .icons {
	color: #2E3191;
}

html .divider.divider-primary.divider-small hr {
	background: #2E3191;
}

html .divider.divider-secondary [class*="fa-"],
html .divider.divider-secondary .icons {
	color: #45BEE4;
}

html .divider.divider-secondary.divider-small hr {
	background: #45BEE4;
}

html .divider.divider-tertiary [class*="fa-"],
html .divider.divider-tertiary .icons {
	color: #EAEFF3;
}

html .divider.divider-tertiary.divider-small hr {
	background: #EAEFF3;
}

html .divider.divider-quaternary [class*="fa-"],
html .divider.divider-quaternary .icons {
	color: #080808;
}

html .divider.divider-quaternary.divider-small hr {
	background: #080808;
}

html .divider.divider-dark [class*="fa-"],
html .divider.divider-dark .icons {
	color: #212529;
}

html .divider.divider-dark.divider-small hr {
	background: #212529;
}

html .divider.divider-light [class*="fa-"],
html .divider.divider-light .icons {
	color: #FFF;
}

html .divider.divider-light.divider-small hr {
	background: #FFF;
}

html .divider.divider-style-2.divider-primary [class*="fa-"],
html .divider.divider-style-2.divider-primary .icons {
	background: #2E3191;
	color: #FFF;
}

html .divider.divider-style-2.divider-secondary [class*="fa-"],
html .divider.divider-style-2.divider-secondary .icons {
	background: #45BEE4;
	color: #777;
}

html .divider.divider-style-2.divider-tertiary [class*="fa-"],
html .divider.divider-style-2.divider-tertiary .icons {
	background: #EAEFF3;
	color: #777;
}

html .divider.divider-style-2.divider-quaternary [class*="fa-"],
html .divider.divider-style-2.divider-quaternary .icons {
	background: #080808;
	color: #FFF;
}

html .divider.divider-style-2.divider-dark [class*="fa-"],
html .divider.divider-style-2.divider-dark .icons {
	background: #212529;
	color: #FFF;
}

html .divider.divider-style-2.divider-light [class*="fa-"],
html .divider.divider-style-2.divider-light .icons {
	background: #FFF;
	color: #777;
}

html .divider.divider-style-3.divider-primary [class*="fa-"],
html .divider.divider-style-3.divider-primary .icons {
	border-color: #2E3191;
}

html .divider.divider-style-3.divider-secondary [class*="fa-"],
html .divider.divider-style-3.divider-secondary .icons {
	border-color: #45BEE4;
}

html .divider.divider-style-3.divider-tertiary [class*="fa-"],
html .divider.divider-style-3.divider-tertiary .icons {
	border-color: #EAEFF3;
}

html .divider.divider-style-3.divider-quaternary [class*="fa-"],
html .divider.divider-style-3.divider-quaternary .icons {
	border-color: #080808;
}

html .divider.divider-style-3.divider-dark [class*="fa-"],
html .divider.divider-style-3.divider-dark .icons {
	border-color: #212529;
}

html .divider.divider-style-3.divider-light [class*="fa-"],
html .divider.divider-style-3.divider-light .icons {
	border-color: #FFF;
}

#header .tip {
	background: #2E3191;
	color: #FFF;
}

#header .tip:before {
	border-right-color: #2E3191;
	border-left-color: transparent;
}

html #header .tip-primary {
	background: #2E3191 !important;
	color: #FFF !important;
}

html #header .tip-primary:before {
	border-right-color: #2E3191 !important;
}

html #header .tip-secondary {
	background: #45BEE4 !important;
	color: #777 !important;
}

html #header .tip-secondary:before {
	border-right-color: #45BEE4 !important;
}

html #header .tip-tertiary {
	background: #EAEFF3 !important;
	color: #777 !important;
}

html #header .tip-tertiary:before {
	border-right-color: #EAEFF3 !important;
}

html #header .tip-quaternary {
	background: #080808 !important;
	color: #FFF !important;
}

html #header .tip-quaternary:before {
	border-right-color: #080808 !important;
}

html #header .tip-dark {
	background: #212529 !important;
	color: #FFF !important;
}

html #header .tip-dark:before {
	border-right-color: #212529 !important;
}

html #header .tip-light {
	background: #FFF !important;
	color: #777 !important;
}

html #header .tip-light:before {
	border-right-color: #FFF !important;
}

html[dir="rtl"] #header .tip:before {
	border-left-color: #2E3191;
}

html[dir="rtl"] #header .tip.skin:before {
	border-left-color: #2E3191;
}

html[dir="rtl"] #header .tip-primary:before {
	border-left-color: #2E3191 !important;
	border-right-color: transparent !important;
}

html[dir="rtl"] #header .tip-secondary:before {
	border-left-color: #45BEE4 !important;
	border-right-color: transparent !important;
}

html[dir="rtl"] #header .tip-tertiary:before {
	border-left-color: #EAEFF3 !important;
	border-right-color: transparent !important;
}

html[dir="rtl"] #header .tip-quaternary:before {
	border-left-color: #080808 !important;
	border-right-color: transparent !important;
}

html[dir="rtl"] #header .tip-dark:before {
	border-left-color: #212529 !important;
	border-right-color: transparent !important;
}

html[dir="rtl"] #header .tip-light:before {
	border-left-color: #FFF !important;
	border-right-color: transparent !important;
}

#header .header-btn-collapse-nav {
	background: #2E3191;
}

@media (min-width: 992px) {
	#header .header-nav-main nav > ul > li > a {
		color: #2E3191;
	}

	#header .header-nav-main nav > ul > li.open > a,
	#header .header-nav-main nav > ul > li:hover > a {
		background: #2E3191;
	}

	#header .header-nav-main nav > ul > li.dropdown .dropdown-menu {
		border-top-color: #2E3191;
	}

	#header .header-nav-main nav > ul > li.dropdown.open > a:before,
	#header .header-nav-main nav > ul > li.dropdown:hover > a:before {
		border-bottom-color: #2E3191;
	}

	#header .header-nav-main nav > ul > li.dropdown-mega-signin .dropdown-menu {
		border-top-color: #2E3191;
	}

	#header .header-nav-main nav > ul > li.dropdown-mega-signin .dropdown-menu li a {
		color: #2E3191;
	}

	#header .header-nav-main nav > ul:not(:hover) > li > a.active {
		background-color: #2E3191;
		color: #FFF;
	}

	#header .header-nav.header-nav-stripe nav > ul > li:hover > a,
	#header .header-nav.header-nav-stripe nav > ul > li.open > a {
		background-color: #2E3191;
	}

	#header .header-nav.header-nav-stripe nav > ul:not(:hover) > li > a.active {
		background-color: #2E3191;
	}

	#header .header-nav.header-nav-stripe nav > ul:hover > li > a.active {
		color: #2E3191;
	}

	#header .header-nav.header-nav-links nav > ul li:hover > a {
		color: #2E3191;
	}

	#header .header-nav.header-nav-links nav > ul:not(:hover) > li > a.active {
		color: #2E3191;
	}

	#header .header-nav.header-nav-links.header-nav-links-vertical-slide nav ul li.dropdown .dropdown-menu::-webkit-scrollbar-thumb {
		background: #2E3191 !important;
	}

	#header .header-nav.header-nav-line nav > ul li:hover > a {
		color: #2E3191;
	}

	#header .header-nav.header-nav-line nav > ul li:hover > a:before {
		background: #2E3191;
	}

	#header .header-nav.header-nav-line nav > ul:not(:hover) li > a.active {
		color: #2E3191;
	}

	#header .header-nav.header-nav-line nav > ul:not(:hover) li > a.active:before {
		background: #2E3191;
	}

	#header .header-nav-main.header-nav-main-slide nav > ul > li.open > span > a,
	#header .header-nav-main.header-nav-main-slide nav > ul > li:hover > span > a {
		color: #2E3191;
	}

	#header .header-nav-main.header-nav-main-slide nav > ul:not(:hover) > li > span > a.active {
		color: #2E3191;
	}

	html.side-header #header.side-header .header-nav-main nav > ul li.dropdown.open > .dropdown-menu,
	html.side-header #header.side-header .header-nav-main nav > ul li.dropdown:hover > .dropdown-menu {
		border-left-color: #2E3191;
	}

	html.side-header-right #header.side-header .header-nav-main nav > ul li.dropdown.open > .dropdown-menu,
	html.side-header-right #header.side-header .header-nav-main nav > ul li.dropdown:hover > .dropdown-menu {
		border-right-color: #2E3191;
	}
}

@media (min-width: 992px) {
	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary .dropdown-menu {
		background-color: #2E3191;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary .dropdown-menu li a {
		color: #FFF;
		border-bottom-color: #d5411e;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary .dropdown-menu li:hover > a {
		background: #e2512f;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary.open > a:before,
	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary:hover > a:before {
		border-bottom-color: #2E3191;
		background: #2E3191;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-mega.dropdown-primary .dropdown-mega-sub-title {
		color: #FFF;
		opacity: 0.7;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary:hover > a {
		background: #2E3191;
		color: #FFF;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary .dropdown-menu li a {
		color: #FFF;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-primary > a.active {
		background: #2E3191;
		color: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary a:focus,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary a:hover {
		color: #2E3191;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary a:focus:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary a:hover:before {
		background-color: #2E3191;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary:hover > a {
		color: #2E3191;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary.open > a:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary:hover > a:before {
		background: #2E3191;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-primary > a.active {
		color: #2E3191;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-primary > a.active:before {
		background-color: #2E3191;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary a {
		color: #2E3191;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary a:focus,
	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary a:hover {
		color: #2E3191;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary:hover > a {
		color: #2E3191;
	}

	html #header .header-nav-links .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-primary > a.active {
		color: #2E3191;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary .dropdown-menu {
		background-color: #45BEE4;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary .dropdown-menu li a {
		color: #777;
		border-bottom-color: #eda618;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary .dropdown-menu li:hover > a {
		background: #efb034;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary.open > a:before,
	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary:hover > a:before {
		border-bottom-color: #45BEE4;
		background: #45BEE4;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-mega.dropdown-secondary .dropdown-mega-sub-title {
		color: #777;
		opacity: 0.7;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary:hover > a {
		background: #45BEE4;
		color: #777;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary .dropdown-menu li a {
		color: #777;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-secondary > a.active {
		background: #45BEE4;
		color: #777;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary a:focus,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary a:hover {
		color: #45BEE4;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary a:focus:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary a:hover:before {
		background-color: #45BEE4;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary:hover > a {
		color: #45BEE4;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary.open > a:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary:hover > a:before {
		background: #45BEE4;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-secondary > a.active {
		color: #45BEE4;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-secondary > a.active:before {
		background-color: #45BEE4;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary a {
		color: #45BEE4;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary a:focus,
	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary a:hover {
		color: #45BEE4;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary:hover > a {
		color: #45BEE4;
	}

	html #header .header-nav-links .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-secondary > a.active {
		color: #45BEE4;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary .dropdown-menu {
		background-color: #EAEFF3;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary .dropdown-menu li a {
		color: #777;
		border-bottom-color: #e0e8ed;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary .dropdown-menu li:hover > a {
		background: #f4f6f9;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary.open > a:before,
	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary:hover > a:before {
		border-bottom-color: #EAEFF3;
		background: #EAEFF3;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-mega.dropdown-tertiary .dropdown-mega-sub-title {
		color: #777;
		opacity: 0.7;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary:hover > a {
		background: #EAEFF3;
		color: #777;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary .dropdown-menu li a {
		color: #777;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-tertiary > a.active {
		background: #EAEFF3;
		color: #777;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary a:focus,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary a:hover {
		color: #EAEFF3;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary a:focus:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary a:hover:before {
		background-color: #EAEFF3;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary:hover > a {
		color: #EAEFF3;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary.open > a:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary:hover > a:before {
		background: #EAEFF3;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-tertiary > a.active {
		color: #EAEFF3;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-tertiary > a.active:before {
		background-color: #EAEFF3;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary a {
		color: #EAEFF3;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary a:focus,
	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary a:hover {
		color: #EAEFF3;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary:hover > a {
		color: #EAEFF3;
	}

	html #header .header-nav-links .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-tertiary > a.active {
		color: #EAEFF3;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary .dropdown-menu {
		background-color: #080808;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary .dropdown-menu li a {
		color: #FFF;
		border-bottom-color: #000000;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary .dropdown-menu li:hover > a {
		background: #101010;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary.open > a:before,
	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary:hover > a:before {
		border-bottom-color: #080808;
		background: #080808;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-mega.dropdown-quaternary .dropdown-mega-sub-title {
		color: #FFF;
		opacity: 0.7;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary:hover > a {
		background: #080808;
		color: #FFF;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary .dropdown-menu li a {
		color: #FFF;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-quaternary > a.active {
		background: #080808;
		color: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary a:focus,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary a:hover {
		color: #080808;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary a:focus:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary a:hover:before {
		background-color: #080808;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary:hover > a {
		color: #080808;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary.open > a:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary:hover > a:before {
		background: #080808;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-quaternary > a.active {
		color: #080808;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-quaternary > a.active:before {
		background-color: #080808;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary a {
		color: #080808;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary a:focus,
	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary a:hover {
		color: #080808;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary:hover > a {
		color: #080808;
	}

	html #header .header-nav-links .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-quaternary > a.active {
		color: #080808;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark .dropdown-menu {
		background-color: #212529;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark .dropdown-menu li a {
		color: #FFF;
		border-bottom-color: #1a1d21;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark .dropdown-menu li:hover > a {
		background: #282d31;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark.open > a:before,
	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark:hover > a:before {
		border-bottom-color: #212529;
		background: #212529;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-mega.dropdown-dark .dropdown-mega-sub-title {
		color: #FFF;
		opacity: 0.7;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark:hover > a {
		background: #212529;
		color: #FFF;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark .dropdown-menu li a {
		color: #FFF;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-dark > a.active {
		background: #212529;
		color: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark a:focus,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark a:hover {
		color: #212529;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark a:focus:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark a:hover:before {
		background-color: #212529;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark:hover > a {
		color: #212529;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark.open > a:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark:hover > a:before {
		background: #212529;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-dark > a.active {
		color: #212529;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-dark > a.active:before {
		background-color: #212529;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark a {
		color: #212529;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark a:focus,
	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark a:hover {
		color: #212529;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-dark:hover > a {
		color: #212529;
	}

	html #header .header-nav-links .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-dark > a.active {
		color: #212529;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light .dropdown-menu {
		background-color: #FFF;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light .dropdown-menu li a {
		color: #777;
		border-bottom-color: #f7f7f7;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light .dropdown-menu li:hover > a {
		background: #ffffff;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light.open > a:before,
	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light:hover > a:before {
		border-bottom-color: #FFF;
		background: #FFF;
	}

	html #header .header-nav .header-nav-main nav > ul > li.dropdown-full-color.dropdown-mega.dropdown-light .dropdown-mega-sub-title {
		color: #777;
		opacity: 0.7;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light:hover > a {
		background: #FFF;
		color: #777;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light .dropdown-menu li a {
		color: #777;
	}

	html #header .header-nav:not(.header-nav-line):not(.header-nav-links) .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-light > a.active {
		background: #FFF;
		color: #777;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light a:focus,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light a:hover {
		color: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light a:focus:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light a:hover:before {
		background-color: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light:hover > a {
		color: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light.open > a:before,
	html #header .header-nav-line .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light:hover > a:before {
		background: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-light > a.active {
		color: #FFF;
	}

	html #header .header-nav-line .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-light > a.active:before {
		background-color: #FFF;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light a {
		color: #FFF;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light a:focus,
	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light a:hover {
		color: #FFF;
	}

	html #header .header-nav-links .header-nav-main nav > ul > li.dropdown-full-color.dropdown-light:hover > a {
		color: #FFF;
	}

	html #header .header-nav-links .header-nav-main nav > ul:not(:hover) > li.dropdown-full-color.dropdown-light > a.active {
		color: #FFF;
	}
}

@media (min-width: 992px) {
	html #header .header-nav-force-light-text-active-skin-color .header-nav-main nav > ul > li:hover > a,
	html #header .header-nav-force-light-text-active-skin-color .header-nav-main nav > ul > li .wrapper-items-cloned:hover > a {
		color: #2E3191 !important;
	}

	html #header .header-nav-force-light-text-active-skin-color .header-nav-main nav > ul:not(:hover) > li > a.active,
	html #header .header-nav-force-light-text-active-skin-color .header-nav-main nav > ul:not(:hover) > li .wrapper-items-cloned > a.active {
		color: #2E3191 !important;
	}
}

@media (max-width: 991px) {
	#header .header-nav-main:not(.header-nav-main-mobile-dark) nav > ul > li > a {
		color: #2E3191;
	}

	#header .header-nav-main:not(.header-nav-main-mobile-dark) nav > ul > li > a:active {
		color: #2E3191;
	}

	#header .header-nav-main:not(.header-nav-main-mobile-dark) nav > ul > li > a.active {
		background: #2E3191;
	}

	#header .header-nav-main:not(.header-nav-main-mobile-dark) nav > ul > li > a.active:focus,
	#header .header-nav-main:not(.header-nav-main-mobile-dark) nav > ul > li > a.active:hover {
		background: #2E3191;
	}

	#header .header-nav-main.header-nav-main-mobile-dark nav > ul > li > a.active {
		background: #2E3191;
	}

	#header .header-nav-main.header-nav-main-mobile-dark nav > ul > li > a.active:focus,
	#header .header-nav-main.header-nav-main-mobile-dark nav > ul > li > a.active:hover {
		background: #2E3191;
	}

	html.side-header-hamburguer-sidebar #header.side-header .header-nav.header-nav-links .header-nav-main.header-nav-main-mobile-dark nav > ul > li > a.active {
		color: #2E3191 !important;
	}

	html.side-header-hamburguer-sidebar #header.side-header .header-nav.header-nav-links nav::-webkit-scrollbar-thumb {
		background-color: #2E3191;
	}

	html.side-header-hamburguer-sidebar #header.side-header .header-nav.header-nav-links nav > ul > li > a.active {
		color: #2E3191 !important;
	}

	html.side-header-hamburguer-sidebar #header.side-header .header-nav.header-nav-links nav > ul > li:hover > a {
		color: #2E3191 !important;
	}
}

@media (max-width: 991px) {
	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary > a {
		color: #2E3191;
	}

	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-primary > a.active {
		background: #2E3191 !important;
		color: #FFF !important;
	}

	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary > a {
		color: #45BEE4;
	}

	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-secondary > a.active {
		background: #45BEE4 !important;
		color: #777 !important;
	}

	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary > a {
		color: #EAEFF3;
	}

	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-tertiary > a.active {
		background: #EAEFF3 !important;
		color: #777 !important;
	}

	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary > a {
		color: #080808;
	}

	html #header .header-nav-main nav > ul > li.dropdown-full-color.dropdown-quaternary > a.active {
		background: #080808 !important;
		color: #FFF !important;
	}
}

html #header .header-top.header-top-primary {
	background: #2E3191;
	border-top-color: #cc3e1d;
}

html #header .header-top.header-top-primary .header-nav-top ul > li.open > a {
	background: #e35938;
}

html #header .header-top.header-top-primary .header-nav-top ul > li > a:hover,
html #header .header-top.header-top-primary .header-nav-top ul > li.open > a:hover,
html #header .header-top.header-top-primary .header-nav-top ul > li > a:focus,
html #header .header-top.header-top-primary .header-nav-top ul > li.open > a:focus {
	background: #e35938;
}

html #header .header-top.header-top-primary .dropdown-menu {
	background: #e35938;
	border-color: #e35938;
}

html #header .header-top.header-top-primary .dropdown-menu a:hover,
html #header .header-top.header-top-primary .dropdown-menu a:focus {
	background: #231F20 !important;
}

html #header .header-top.header-top-secondary {
	background: #45BEE4;
	border-top-color: #e8a112;
}

html #header .header-top.header-top-secondary .header-nav-top ul > li.open > a {
	background: #f0b43e;
}

html #header .header-top.header-top-secondary .header-nav-top ul > li > a:hover,
html #header .header-top.header-top-secondary .header-nav-top ul > li.open > a:hover,
html #header .header-top.header-top-secondary .header-nav-top ul > li > a:focus,
html #header .header-top.header-top-secondary .header-nav-top ul > li.open > a:focus {
	background: #f0b43e;
}

html #header .header-top.header-top-secondary .dropdown-menu {
	background: #f0b43e;
	border-color: #f0b43e;
}

html #header .header-top.header-top-secondary .dropdown-menu a:hover,
html #header .header-top.header-top-secondary .dropdown-menu a:focus {
	background: #f2bd55 !important;
}

html #header .header-top.header-top-tertiary {
	background: #EAEFF3;
	border-top-color: #dae3ea;
}

html #header .header-top.header-top-tertiary .header-nav-top ul > li.open > a {
	background: #fafbfc;
}

html #header .header-top.header-top-tertiary .header-nav-top ul > li > a:hover,
html #header .header-top.header-top-tertiary .header-nav-top ul > li.open > a:hover,
html #header .header-top.header-top-tertiary .header-nav-top ul > li > a:focus,
html #header .header-top.header-top-tertiary .header-nav-top ul > li.open > a:focus {
	background: #fafbfc;
}

html #header .header-top.header-top-tertiary .dropdown-menu {
	background: #fafbfc;
	border-color: #fafbfc;
}

html #header .header-top.header-top-tertiary .dropdown-menu a:hover,
html #header .header-top.header-top-tertiary .dropdown-menu a:focus {
	background: #ffffff !important;
}

html #header .header-top.header-top-quaternary {
	background: #080808;
	border-top-color: #000000;
}

html #header .header-top.header-top-quaternary .header-nav-top ul > li.open > a {
	background: #151515;
}

html #header .header-top.header-top-quaternary .header-nav-top ul > li > a:hover,
html #header .header-top.header-top-quaternary .header-nav-top ul > li.open > a:hover,
html #header .header-top.header-top-quaternary .header-nav-top ul > li > a:focus,
html #header .header-top.header-top-quaternary .header-nav-top ul > li.open > a:focus {
	background: #151515;
}

html #header .header-top.header-top-quaternary .dropdown-menu {
	background: #151515;
	border-color: #151515;
}

html #header .header-top.header-top-quaternary .dropdown-menu a:hover,
html #header .header-top.header-top-quaternary .dropdown-menu a:focus {
	background: #222222 !important;
}

html #header .header-top.header-top-dark {
	background: #212529;
	border-top-color: #16181b;
}

html #header .header-top.header-top-dark .header-nav-top ul > li.open > a {
	background: #2c3237;
}

html #header .header-top.header-top-dark .header-nav-top ul > li > a:hover,
html #header .header-top.header-top-dark .header-nav-top ul > li.open > a:hover,
html #header .header-top.header-top-dark .header-nav-top ul > li > a:focus,
html #header .header-top.header-top-dark .header-nav-top ul > li.open > a:focus {
	background: #2c3237;
}

html #header .header-top.header-top-dark .dropdown-menu {
	background: #2c3237;
	border-color: #2c3237;
}

html #header .header-top.header-top-dark .dropdown-menu a:hover,
html #header .header-top.header-top-dark .dropdown-menu a:focus {
	background: #383f45 !important;
}

html #header .header-top.header-top-light {
	background: #FFF;
	border-top-color: #f2f2f2;
}

html #header .header-top.header-top-light .header-nav-top ul > li.open > a {
	background: #ffffff;
}

html #header .header-top.header-top-light .header-nav-top ul > li > a:hover,
html #header .header-top.header-top-light .header-nav-top ul > li.open > a:hover,
html #header .header-top.header-top-light .header-nav-top ul > li > a:focus,
html #header .header-top.header-top-light .header-nav-top ul > li.open > a:focus {
	background: #ffffff;
}

html #header .header-top.header-top-light .dropdown-menu {
	background: #ffffff;
	border-color: #ffffff;
}

html #header .header-top.header-top-light .dropdown-menu a:hover,
html #header .header-top.header-top-light .dropdown-menu a:focus {
	background: #ffffff !important;
}

@media (min-width: 992px) {
	html #header .header-nav-bar-primary {
		background: #2E3191 !important;
		color: #FFF !important;
	}

	html #header .header-nav-bar-secondary {
		background: #45BEE4 !important;
		color: #777 !important;
	}

	html #header .header-nav-bar-tertiary {
		background: #EAEFF3 !important;
		color: #777 !important;
	}

	html #header .header-nav-bar-quaternary {
		background: #080808 !important;
		color: #FFF !important;
	}

	html #header .header-nav-bar-dark {
		background: #212529 !important;
		color: #FFF !important;
	}

	html #header .header-nav-bar-light {
		background: #FFF !important;
		color: #777 !important;
	}
}

@media (min-width: 992px) {
	#header .header-nav-main.header-nav-main-light nav > ul > li.open > a,
	#header .header-nav-main.header-nav-main-light nav > ul > li:hover > a {
		color: #2E3191;
	}

	#header .header-nav-main.header-nav-main-light nav > ul > li > a.active {
		color: #2E3191;
	}
}

#header .header-nav-features .header-nav-features-search-reveal-big-search .form-control {
	border-bottom-color: #2E3191;
}

.testimonial blockquote {
	background: #e35938;
}

.testimonial .testimonial-arrow-down {
	border-top-color: #e35938;
}

html .testimonial-primary blockquote {
	background: #e35938;
}

html .testimonial-primary .testimonial-arrow-down {
	border-top-color: #e35938;
}

html .testimonial-primary.testimonial-style-7 {
	background: #e35938;
}

html .testimonial-quotes-primary blockquote:before,
html .testimonial-quotes-primary blockquote:after {
	color: #2E3191 !important;
}

html .testimonial-secondary blockquote {
	background: #f0b43e;
}

html .testimonial-secondary .testimonial-arrow-down {
	border-top-color: #f0b43e;
}

html .testimonial-secondary.testimonial-style-7 {
	background: #f0b43e;
}

html .testimonial-quotes-secondary blockquote:before,
html .testimonial-quotes-secondary blockquote:after {
	color: #2E3191 !important;
}

html .testimonial-tertiary blockquote {
	background: #fafbfc;
}

html .testimonial-tertiary .testimonial-arrow-down {
	border-top-color: #fafbfc;
}

html .testimonial-tertiary.testimonial-style-7 {
	background: #fafbfc;
}

html .testimonial-quotes-tertiary blockquote:before,
html .testimonial-quotes-tertiary blockquote:after {
	color: #2E3191 !important;
}

html .testimonial-quaternary blockquote {
	background: #151515;
}

html .testimonial-quaternary .testimonial-arrow-down {
	border-top-color: #151515;
}

html .testimonial-quaternary.testimonial-style-7 {
	background: #151515;
}

html .testimonial-quotes-quaternary blockquote:before,
html .testimonial-quotes-quaternary blockquote:after {
	color: #2E3191 !important;
}

html .testimonial-dark blockquote {
	background: #2c3237;
}

html .testimonial-dark .testimonial-arrow-down {
	border-top-color: #2c3237;
}

html .testimonial-dark.testimonial-style-7 {
	background: #2c3237;
}

html .testimonial-quotes-dark blockquote:before,
html .testimonial-quotes-dark blockquote:after {
	color: #2E3191 !important;
}

html .testimonial-light blockquote {
	background: #ffffff;
}

html .testimonial-light .testimonial-arrow-down {
	border-top-color: #ffffff;
}

html .testimonial-light.testimonial-style-7 {
	background: #ffffff;
}

html .testimonial-quotes-light blockquote:before,
html .testimonial-quotes-light blockquote:after {
	color: #2E3191 !important;
}

.circular-bar.only-icon [class*="fa-"],
.circular-bar.only-icon .icons {
	color: #2E3191;
}

.form-control.bg-primary,
.form-control.bg-color-primary {
	color: #FFF;
	border-color: #2E3191;
}

.form-control.bg-primary::-webkit-input-placeholder,
.form-control.bg-color-primary::-webkit-input-placeholder {
	color: #e6e6e6;
}

.form-control.bg-primary::-moz-placeholder,
.form-control.bg-color-primary::-moz-placeholder {
	color: #e6e6e6;
}

.form-control.bg-primary:-ms-input-placeholder,
.form-control.bg-color-primary:-ms-input-placeholder {
	color: #e6e6e6;
}

.form-control.bg-primary:-moz-placeholder,
.form-control.bg-color-primary:-moz-placeholder {
	color: #e6e6e6;
}

.form-control .testimonial-primary blockquote {
	background: #e35938;
}

.form-control .testimonial-primary .testimonial-arrow-down {
	border-top-color: #e35938;
}

.form-control .testimonial-primary.testimonial-style-7 {
	background: #e35938;
}

.form-control .testimonial-quotes-primary blockquote:before,
.form-control .testimonial-quotes-primary blockquote:after {
	color: #2E3191 !important;
}

.form-control.bg-secondary,
.form-control.bg-color-secondary {
	color: #777;
	border-color: #45BEE4;
}

.form-control.bg-secondary::-webkit-input-placeholder,
.form-control.bg-color-secondary::-webkit-input-placeholder {
	color: #5e5e5e;
}

.form-control.bg-secondary::-moz-placeholder,
.form-control.bg-color-secondary::-moz-placeholder {
	color: #5e5e5e;
}

.form-control.bg-secondary:-ms-input-placeholder,
.form-control.bg-color-secondary:-ms-input-placeholder {
	color: #5e5e5e;
}

.form-control.bg-secondary:-moz-placeholder,
.form-control.bg-color-secondary:-moz-placeholder {
	color: #5e5e5e;
}

.form-control .testimonial-secondary blockquote {
	background: #f0b43e;
}

.form-control .testimonial-secondary .testimonial-arrow-down {
	border-top-color: #f0b43e;
}

.form-control .testimonial-secondary.testimonial-style-7 {
	background: #f0b43e;
}

.form-control .testimonial-quotes-secondary blockquote:before,
.form-control .testimonial-quotes-secondary blockquote:after {
	color: #2E3191 !important;
}

.form-control.bg-tertiary,
.form-control.bg-color-tertiary {
	color: #777;
	border-color: #EAEFF3;
}

.form-control.bg-tertiary::-webkit-input-placeholder,
.form-control.bg-color-tertiary::-webkit-input-placeholder {
	color: #5e5e5e;
}

.form-control.bg-tertiary::-moz-placeholder,
.form-control.bg-color-tertiary::-moz-placeholder {
	color: #5e5e5e;
}

.form-control.bg-tertiary:-ms-input-placeholder,
.form-control.bg-color-tertiary:-ms-input-placeholder {
	color: #5e5e5e;
}

.form-control.bg-tertiary:-moz-placeholder,
.form-control.bg-color-tertiary:-moz-placeholder {
	color: #5e5e5e;
}

.form-control .testimonial-tertiary blockquote {
	background: #fafbfc;
}

.form-control .testimonial-tertiary .testimonial-arrow-down {
	border-top-color: #fafbfc;
}

.form-control .testimonial-tertiary.testimonial-style-7 {
	background: #fafbfc;
}

.form-control .testimonial-quotes-tertiary blockquote:before,
.form-control .testimonial-quotes-tertiary blockquote:after {
	color: #2E3191 !important;
}

.form-control.bg-quaternary,
.form-control.bg-color-quaternary {
	color: #FFF;
	border-color: #080808;
}

.form-control.bg-quaternary::-webkit-input-placeholder,
.form-control.bg-color-quaternary::-webkit-input-placeholder {
	color: #e6e6e6;
}

.form-control.bg-quaternary::-moz-placeholder,
.form-control.bg-color-quaternary::-moz-placeholder {
	color: #e6e6e6;
}

.form-control.bg-quaternary:-ms-input-placeholder,
.form-control.bg-color-quaternary:-ms-input-placeholder {
	color: #e6e6e6;
}

.form-control.bg-quaternary:-moz-placeholder,
.form-control.bg-color-quaternary:-moz-placeholder {
	color: #e6e6e6;
}

.form-control .testimonial-quaternary blockquote {
	background: #151515;
}

.form-control .testimonial-quaternary .testimonial-arrow-down {
	border-top-color: #151515;
}

.form-control .testimonial-quaternary.testimonial-style-7 {
	background: #151515;
}

.form-control .testimonial-quotes-quaternary blockquote:before,
.form-control .testimonial-quotes-quaternary blockquote:after {
	color: #2E3191 !important;
}

.form-control.bg-dark,
.form-control.bg-color-dark {
	color: #FFF;
	border-color: #212529;
}

.form-control.bg-dark::-webkit-input-placeholder,
.form-control.bg-color-dark::-webkit-input-placeholder {
	color: #e6e6e6;
}

.form-control.bg-dark::-moz-placeholder,
.form-control.bg-color-dark::-moz-placeholder {
	color: #e6e6e6;
}

.form-control.bg-dark:-ms-input-placeholder,
.form-control.bg-color-dark:-ms-input-placeholder {
	color: #e6e6e6;
}

.form-control.bg-dark:-moz-placeholder,
.form-control.bg-color-dark:-moz-placeholder {
	color: #e6e6e6;
}

.form-control .testimonial-dark blockquote {
	background: #2c3237;
}

.form-control .testimonial-dark .testimonial-arrow-down {
	border-top-color: #2c3237;
}

.form-control .testimonial-dark.testimonial-style-7 {
	background: #2c3237;
}

.form-control .testimonial-quotes-dark blockquote:before,
.form-control .testimonial-quotes-dark blockquote:after {
	color: #2E3191 !important;
}

.form-control.bg-light,
.form-control.bg-color-light {
	color: #777;
	border-color: #FFF;
}

.form-control.bg-light::-webkit-input-placeholder,
.form-control.bg-color-light::-webkit-input-placeholder {
	color: #5e5e5e;
}

.form-control.bg-light::-moz-placeholder,
.form-control.bg-color-light::-moz-placeholder {
	color: #5e5e5e;
}

.form-control.bg-light:-ms-input-placeholder,
.form-control.bg-color-light:-ms-input-placeholder {
	color: #5e5e5e;
}

.form-control.bg-light:-moz-placeholder,
.form-control.bg-color-light:-moz-placeholder {
	color: #5e5e5e;
}

.form-control .testimonial-light blockquote {
	background: #ffffff;
}

.form-control .testimonial-light .testimonial-arrow-down {
	border-top-color: #ffffff;
}

.form-control .testimonial-light.testimonial-style-7 {
	background: #ffffff;
}

.form-control .testimonial-quotes-light blockquote:before,
.form-control .testimonial-quotes-light blockquote:after {
	color: #2E3191 !important;
}

.form-range::-webkit-slider-thumb {
	background-color: #2E3191;
}

.form-range::-webkit-slider-thumb:active {
	background-color: rgba(224, 70, 34, 0.25);
}

.form-range:focus::-webkit-slider-thumb {
	box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(224, 70, 34, 0.25);
}

html .counters .counter-primary [class*="fa-"],
html .counters .counter-primary .icons,
html .counters .counter-primary strong {
	color: #2E3191;
}

html .counters .counter-secondary [class*="fa-"],
html .counters .counter-secondary .icons,
html .counters .counter-secondary strong {
	color: #45BEE4;
}

html .counters .counter-tertiary [class*="fa-"],
html .counters .counter-tertiary .icons,
html .counters .counter-tertiary strong {
	color: #EAEFF3;
}

html .counters .counter-quaternary [class*="fa-"],
html .counters .counter-quaternary .icons,
html .counters .counter-quaternary strong {
	color: #080808;
}

html .counters .counter-dark [class*="fa-"],
html .counters .counter-dark .icons,
html .counters .counter-dark strong {
	color: #212529;
}

html .counters .counter-light [class*="fa-"],
html .counters .counter-light .icons,
html .counters .counter-light strong {
	color: #FFF;
}

html .countdown.countdown-primary > span > span {
	color: #2E3191;
}

html .countdown.countdown-secondary > span > span {
	color: #45BEE4;
}

html .countdown.countdown-tertiary > span > span {
	color: #EAEFF3;
}

html .countdown.countdown-quaternary > span > span {
	color: #080808;
}

html .countdown.countdown-dark > span > span {
	color: #212529;
}

html .countdown.countdown-light > span > span {
	color: #FFF;
}

html .image-hotspot-primary strong {
	color: #FFF !important;
}

html .image-hotspot-primary .circle {
	background: #2E3191 !important;
}

html .image-hotspot-primary .ring {
	border-color: rgba(224, 70, 34, 0.9);
}

html .image-hotspot-secondary strong {
	color: #777 !important;
}

html .image-hotspot-secondary .circle {
	background: #45BEE4 !important;
}

html .image-hotspot-secondary .ring {
	border-color: rgba(238, 171, 38, 0.9);
}

html .image-hotspot-tertiary strong {
	color: #777 !important;
}

html .image-hotspot-tertiary .circle {
	background: #EAEFF3 !important;
}

html .image-hotspot-tertiary .ring {
	border-color: rgba(234, 239, 243, 0.9);
}

html .image-hotspot-quaternary strong {
	color: #FFF !important;
}

html .image-hotspot-quaternary .circle {
	background: #080808 !important;
}

html .image-hotspot-quaternary .ring {
	border-color: rgba(8, 8, 8, 0.9);
}

html .image-hotspot-dark strong {
	color: #FFF !important;
}

html .image-hotspot-dark .circle {
	background: #212529 !important;
}

html .image-hotspot-dark .ring {
	border-color: rgba(33, 37, 41, 0.9);
}

html .image-hotspot-light strong {
	color: #777 !important;
}

html .image-hotspot-light .circle {
	background: #FFF !important;
}

html .image-hotspot-light .ring {
	border-color: rgba(255, 255, 255, 0.9);
}

.icon-featured {
	background-color: #2E3191;
}

html .featured-box-primary .icon-featured {
	background-color: #2E3191;
}

html .featured-box-primary h4 {
	color: #2E3191;
}

html .featured-box-primary .box-content {
	border-top-color: #2E3191;
}

html .featured-box-primary .box-content-border-bottom {
	border-bottom-color: #2E3191;
}

html .featured-box-secondary .icon-featured {
	background-color: #45BEE4;
}

html .featured-box-secondary h4 {
	color: #45BEE4;
}

html .featured-box-secondary .box-content {
	border-top-color: #45BEE4;
}

html .featured-box-secondary .box-content-border-bottom {
	border-bottom-color: #45BEE4;
}

html .featured-box-tertiary .icon-featured {
	background-color: #EAEFF3;
}

html .featured-box-tertiary h4 {
	color: #EAEFF3;
}

html .featured-box-tertiary .box-content {
	border-top-color: #EAEFF3;
}

html .featured-box-tertiary .box-content-border-bottom {
	border-bottom-color: #EAEFF3;
}

html .featured-box-quaternary .icon-featured {
	background-color: #080808;
}

html .featured-box-quaternary h4 {
	color: #080808;
}

html .featured-box-quaternary .box-content {
	border-top-color: #080808;
}

html .featured-box-quaternary .box-content-border-bottom {
	border-bottom-color: #080808;
}

html .featured-box-dark .icon-featured {
	background-color: #212529;
}

html .featured-box-dark h4 {
	color: #212529;
}

html .featured-box-dark .box-content {
	border-top-color: #212529;
}

html .featured-box-dark .box-content-border-bottom {
	border-bottom-color: #212529;
}

html .featured-box-light .icon-featured {
	background-color: #FFF;
}

html .featured-box-light h4 {
	color: #FFF;
}

html .featured-box-light .box-content {
	border-top-color: #FFF;
}

html .featured-box-light .box-content-border-bottom {
	border-bottom-color: #FFF;
}

html .featured-boxes-style-3 .featured-box.featured-box-primary .icon-featured {
	border-color: #2E3191;
	color: #2E3191;
}

html .featured-boxes-style-3 .featured-box.featured-box-secondary .icon-featured {
	border-color: #45BEE4;
	color: #45BEE4;
}

html .featured-boxes-style-3 .featured-box.featured-box-tertiary .icon-featured {
	border-color: #EAEFF3;
	color: #EAEFF3;
}

html .featured-boxes-style-3 .featured-box.featured-box-quaternary .icon-featured {
	border-color: #080808;
	color: #080808;
}

html .featured-boxes-style-3 .featured-box.featured-box-dark .icon-featured {
	border-color: #212529;
	color: #212529;
}

html .featured-boxes-style-3 .featured-box.featured-box-light .icon-featured {
	border-color: #FFF;
	color: #FFF;
}

html .featured-boxes-style-4 .featured-box.featured-box-primary .icon-featured {
	border-color: #2E3191;
	color: #2E3191;
}

html .featured-boxes-style-4 .featured-box.featured-box-secondary .icon-featured {
	border-color: #45BEE4;
	color: #45BEE4;
}

html .featured-boxes-style-4 .featured-box.featured-box-tertiary .icon-featured {
	border-color: #EAEFF3;
	color: #EAEFF3;
}

html .featured-boxes-style-4 .featured-box.featured-box-quaternary .icon-featured {
	border-color: #080808;
	color: #080808;
}

html .featured-boxes-style-4 .featured-box.featured-box-dark .icon-featured {
	border-color: #212529;
	color: #212529;
}

html .featured-boxes-style-4 .featured-box.featured-box-light .icon-featured {
	border-color: #FFF;
	color: #FFF;
}

html .featured-boxes-style-5 .featured-box.featured-box-primary .icon-featured {
	color: #2E3191;
}

html .featured-boxes-style-5 .featured-box.featured-box-secondary .icon-featured {
	color: #45BEE4;
}

html .featured-boxes-style-5 .featured-box.featured-box-tertiary .icon-featured {
	color: #EAEFF3;
}

html .featured-boxes-style-5 .featured-box.featured-box-quaternary .icon-featured {
	color: #080808;
}

html .featured-boxes-style-5 .featured-box.featured-box-dark .icon-featured {
	color: #212529;
}

html .featured-boxes-style-5 .featured-box.featured-box-light .icon-featured {
	color: #FFF;
}

html .featured-boxes-style-6 .featured-box.featured-box-primary .icon-featured {
	color: #2E3191;
}

html .featured-boxes-style-6 .featured-box.featured-box-secondary .icon-featured {
	color: #45BEE4;
}

html .featured-boxes-style-6 .featured-box.featured-box-tertiary .icon-featured {
	color: #EAEFF3;
}

html .featured-boxes-style-6 .featured-box.featured-box-quaternary .icon-featured {
	color: #080808;
}

html .featured-boxes-style-6 .featured-box.featured-box-dark .icon-featured {
	color: #212529;
}

html .featured-boxes-style-6 .featured-box.featured-box-light .icon-featured {
	color: #FFF;
}

html .featured-boxes-style-8 .featured-box.featured-box-primary .icon-featured {
	color: #2E3191;
}

html .featured-boxes-style-8 .featured-box.featured-box-secondary .icon-featured {
	color: #45BEE4;
}

html .featured-boxes-style-8 .featured-box.featured-box-tertiary .icon-featured {
	color: #EAEFF3;
}

html .featured-boxes-style-8 .featured-box.featured-box-quaternary .icon-featured {
	color: #080808;
}

html .featured-boxes-style-8 .featured-box.featured-box-dark .icon-featured {
	color: #212529;
}

html .featured-boxes-style-8 .featured-box.featured-box-light .icon-featured {
	color: #FFF;
}

.featured-boxes-modern-style-primary .featured-box {
	background: #2E3191;
	background: linear-gradient(135deg, #2E3191 0%, #080808 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2E3191', endColorstr='#080808', GradientType=1);
}

.featured-boxes-modern-style-secondary .featured-box {
	background: #45BEE4;
	background: linear-gradient(135deg, #45BEE4 0%, #080808 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#45BEE4', endColorstr='#080808', GradientType=1);
}

.featured-boxes-modern-style-tertiary .featured-box {
	background: #EAEFF3;
	background: linear-gradient(135deg, #EAEFF3 0%, #080808 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#EAEFF3', endColorstr='#080808', GradientType=1);
}

html .featured-box-effect-2.featured-box-primary .icon-featured:after {
	box-shadow: 0 0 0 3px #2E3191;
}

html .featured-box-effect-2.featured-box-secondary .icon-featured:after {
	box-shadow: 0 0 0 3px #45BEE4;
}

html .featured-box-effect-2.featured-box-tertiary .icon-featured:after {
	box-shadow: 0 0 0 3px #EAEFF3;
}

html .featured-box-effect-2.featured-box-quaternary .icon-featured:after {
	box-shadow: 0 0 0 3px #080808;
}

html .featured-box-effect-2.featured-box-dark .icon-featured:after {
	box-shadow: 0 0 0 3px #212529;
}

html .featured-box-effect-2.featured-box-light .icon-featured:after {
	box-shadow: 0 0 0 3px #FFF;
}

html .featured-box-effect-3.featured-box-primary .icon-featured:after {
	box-shadow: 0 0 0 10px #2E3191;
}

html .featured-box-effect-3.featured-box-primary:hover .icon-featured {
	background: #2E3191 !important;
}

html .featured-box-effect-3.featured-box-secondary .icon-featured:after {
	box-shadow: 0 0 0 10px #45BEE4;
}

html .featured-box-effect-3.featured-box-secondary:hover .icon-featured {
	background: #45BEE4 !important;
}

html .featured-box-effect-3.featured-box-tertiary .icon-featured:after {
	box-shadow: 0 0 0 10px #EAEFF3;
}

html .featured-box-effect-3.featured-box-tertiary:hover .icon-featured {
	background: #EAEFF3 !important;
}

html .featured-box-effect-3.featured-box-quaternary .icon-featured:after {
	box-shadow: 0 0 0 10px #080808;
}

html .featured-box-effect-3.featured-box-quaternary:hover .icon-featured {
	background: #080808 !important;
}

html .featured-box-effect-3.featured-box-dark .icon-featured:after {
	box-shadow: 0 0 0 10px #212529;
}

html .featured-box-effect-3.featured-box-dark:hover .icon-featured {
	background: #212529 !important;
}

html .featured-box-effect-3.featured-box-light .icon-featured:after {
	box-shadow: 0 0 0 10px #FFF;
}

html .featured-box-effect-3.featured-box-light:hover .icon-featured {
	background: #FFF !important;
}

.feature-box .feature-box-icon {
	background-color: #2E3191;
}

html .feature-box-primary .feature-box-icon {
	background-color: #2E3191;
}

html .feature-box-secondary .feature-box-icon {
	background-color: #45BEE4;
}

html .feature-box-tertiary .feature-box-icon {
	background-color: #EAEFF3;
}

html .feature-box-quaternary .feature-box-icon {
	background-color: #080808;
}

html .feature-box-dark .feature-box-icon {
	background-color: #212529;
}

html .feature-box-light .feature-box-icon {
	background-color: #FFF;
}

.feature-box.feature-box-style-2 .feature-box-icon [class*="fa-"],
.feature-box.feature-box-style-2 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-primary.feature-box-style-2 .feature-box-icon [class*="fa-"],
html .feature-box-primary.feature-box-style-2 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-secondary.feature-box-style-2 .feature-box-icon [class*="fa-"],
html .feature-box-secondary.feature-box-style-2 .feature-box-icon .icons {
	color: #45BEE4;
}

html .feature-box-tertiary.feature-box-style-2 .feature-box-icon [class*="fa-"],
html .feature-box-tertiary.feature-box-style-2 .feature-box-icon .icons {
	color: #EAEFF3;
}

html .feature-box-quaternary.feature-box-style-2 .feature-box-icon [class*="fa-"],
html .feature-box-quaternary.feature-box-style-2 .feature-box-icon .icons {
	color: #080808;
}

html .feature-box-dark.feature-box-style-2 .feature-box-icon [class*="fa-"],
html .feature-box-dark.feature-box-style-2 .feature-box-icon .icons {
	color: #212529;
}

html .feature-box-light.feature-box-style-2 .feature-box-icon [class*="fa-"],
html .feature-box-light.feature-box-style-2 .feature-box-icon .icons {
	color: #FFF;
}

.feature-box.feature-box-style-3 .feature-box-icon {
	border-color: #2E3191;
}

.feature-box.feature-box-style-3 .feature-box-icon [class*="fa-"],
.feature-box.feature-box-style-3 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-primary.feature-box-style-3 .feature-box-icon {
	border-color: #2E3191;
}

html .feature-box-primary.feature-box-style-3 .feature-box-icon [class*="fa-"],
html .feature-box-primary.feature-box-style-3 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-secondary.feature-box-style-3 .feature-box-icon {
	border-color: #45BEE4;
}

html .feature-box-secondary.feature-box-style-3 .feature-box-icon [class*="fa-"],
html .feature-box-secondary.feature-box-style-3 .feature-box-icon .icons {
	color: #45BEE4;
}

html .feature-box-tertiary.feature-box-style-3 .feature-box-icon {
	border-color: #EAEFF3;
}

html .feature-box-tertiary.feature-box-style-3 .feature-box-icon [class*="fa-"],
html .feature-box-tertiary.feature-box-style-3 .feature-box-icon .icons {
	color: #EAEFF3;
}

html .feature-box-quaternary.feature-box-style-3 .feature-box-icon {
	border-color: #080808;
}

html .feature-box-quaternary.feature-box-style-3 .feature-box-icon [class*="fa-"],
html .feature-box-quaternary.feature-box-style-3 .feature-box-icon .icons {
	color: #080808;
}

html .feature-box-dark.feature-box-style-3 .feature-box-icon {
	border-color: #212529;
}

html .feature-box-dark.feature-box-style-3 .feature-box-icon [class*="fa-"],
html .feature-box-dark.feature-box-style-3 .feature-box-icon .icons {
	color: #212529;
}

html .feature-box-light.feature-box-style-3 .feature-box-icon {
	border-color: #FFF;
}

html .feature-box-light.feature-box-style-3 .feature-box-icon [class*="fa-"],
html .feature-box-light.feature-box-style-3 .feature-box-icon .icons {
	color: #FFF;
}

.feature-box.feature-box-style-4 .feature-box-icon [class*="fa-"],
.feature-box.feature-box-style-4 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-primary.feature-box-style-4 .feature-box-icon [class*="fa-"],
html .feature-box-primary.feature-box-style-4 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-secondary.feature-box-style-4 .feature-box-icon [class*="fa-"],
html .feature-box-secondary.feature-box-style-4 .feature-box-icon .icons {
	color: #45BEE4;
}

html .feature-box-tertiary.feature-box-style-4 .feature-box-icon [class*="fa-"],
html .feature-box-tertiary.feature-box-style-4 .feature-box-icon .icons {
	color: #EAEFF3;
}

html .feature-box-quaternary.feature-box-style-4 .feature-box-icon [class*="fa-"],
html .feature-box-quaternary.feature-box-style-4 .feature-box-icon .icons {
	color: #080808;
}

html .feature-box-dark.feature-box-style-4 .feature-box-icon [class*="fa-"],
html .feature-box-dark.feature-box-style-4 .feature-box-icon .icons {
	color: #212529;
}

html .feature-box-light.feature-box-style-4 .feature-box-icon [class*="fa-"],
html .feature-box-light.feature-box-style-4 .feature-box-icon .icons {
	color: #FFF;
}

.feature-box.feature-box-style-5 .feature-box-icon [class*="fa-"],
.feature-box.feature-box-style-5 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-primary.feature-box-style-5 .feature-box-icon [class*="fa-"],
html .feature-box-primary.feature-box-style-5 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-secondary.feature-box-style-5 .feature-box-icon [class*="fa-"],
html .feature-box-secondary.feature-box-style-5 .feature-box-icon .icons {
	color: #45BEE4;
}

html .feature-box-tertiary.feature-box-style-5 .feature-box-icon [class*="fa-"],
html .feature-box-tertiary.feature-box-style-5 .feature-box-icon .icons {
	color: #EAEFF3;
}

html .feature-box-quaternary.feature-box-style-5 .feature-box-icon [class*="fa-"],
html .feature-box-quaternary.feature-box-style-5 .feature-box-icon .icons {
	color: #080808;
}

html .feature-box-dark.feature-box-style-5 .feature-box-icon [class*="fa-"],
html .feature-box-dark.feature-box-style-5 .feature-box-icon .icons {
	color: #212529;
}

html .feature-box-light.feature-box-style-5 .feature-box-icon [class*="fa-"],
html .feature-box-light.feature-box-style-5 .feature-box-icon .icons {
	color: #FFF;
}

html .feature-box-primary.feature-box-style-6 .feature-box-icon [class*="fa-"],
html .feature-box-primary.feature-box-style-6 .feature-box-icon .icons {
	color: #2E3191;
}

html .feature-box-secondary.feature-box-style-6 .feature-box-icon [class*="fa-"],
html .feature-box-secondary.feature-box-style-6 .feature-box-icon .icons {
	color: #45BEE4;
}

html .feature-box-tertiary.feature-box-style-6 .feature-box-icon [class*="fa-"],
html .feature-box-tertiary.feature-box-style-6 .feature-box-icon .icons {
	color: #EAEFF3;
}

html .feature-box-quaternary.feature-box-style-6 .feature-box-icon [class*="fa-"],
html .feature-box-quaternary.feature-box-style-6 .feature-box-icon .icons {
	color: #080808;
}

html .feature-box-dark.feature-box-style-6 .feature-box-icon [class*="fa-"],
html .feature-box-dark.feature-box-style-6 .feature-box-icon .icons {
	color: #212529;
}

html .feature-box-light.feature-box-style-6 .feature-box-icon [class*="fa-"],
html .feature-box-light.feature-box-style-6 .feature-box-icon .icons {
	color: #FFF;
}

html .featured-boxes-full-scale > .featured-box-full-primary:nth-child(1) {
	background-color: #A7A9AC;
}

html .featured-boxes-full-scale > .featured-box-full-primary:nth-child(2) {
	background-color: #cc3e1d;
}

html .featured-boxes-full-scale > .featured-box-full-primary:nth-child(3) {
	background-color: #2E3191;
}

html .featured-boxes-full-scale > .featured-box-full-primary:nth-child(4) {
	background-color: #e35938;
}

html .featured-boxes-full-scale > .featured-box-full-primary:nth-child(5) {
	background-color: #231F20;
}

html .featured-boxes-full-scale > .featured-box-full-primary:nth-child(6) {
	background-color: #e97e65;
}

html .featured-boxes-full .featured-box-full-primary {
	background-color: #2E3191;
	color: #FFF;
}

html .featured-boxes-full .featured-box-full-secondary {
	background-color: #45BEE4;
	color: #777;
}

html .featured-boxes-full .featured-box-full-tertiary {
	background-color: #EAEFF3;
	color: #777;
}

html .featured-boxes-full .featured-box-full-quaternary {
	background-color: #080808;
	color: #FFF;
}

html .featured-boxes-full .featured-box-full-dark {
	background-color: #212529;
	color: #FFF;
}

html .featured-boxes-full .featured-box-full-light {
	background-color: #FFF;
	color: #777;
}

.pricing-table .most-popular {
	border-color: #2E3191;
}

.pricing-table .most-popular h3 {
	background-color: #2E3191;
}

.pricing-table.princig-table-flat .plan h3 {
	background-color: #2E3191;
}

.pricing-table.princig-table-flat .plan h3 span {
	background: #2E3191;
}

html .call-to-action.featured.featured-primary {
	border-top-color: #2E3191;
}

html .call-to-action.call-to-action-primary {
	background: #2E3191;
}

html .call-to-action.with-full-borders-primary {
	border-color: #2E3191;
}

html .call-to-action.call-to-action-primary {
	background: #2E3191;
}

html .call-to-action.call-to-action-primary h3,
html .call-to-action.call-to-action-primary p {
	color: #FFF;
}

html .call-to-action.featured.featured-secondary {
	border-top-color: #45BEE4;
}

html .call-to-action.call-to-action-secondary {
	background: #45BEE4;
}

html .call-to-action.with-full-borders-secondary {
	border-color: #45BEE4;
}

html .call-to-action.call-to-action-secondary {
	background: #45BEE4;
}

html .call-to-action.call-to-action-secondary h3,
html .call-to-action.call-to-action-secondary p {
	color: #777;
}

html .call-to-action.featured.featured-tertiary {
	border-top-color: #EAEFF3;
}

html .call-to-action.call-to-action-tertiary {
	background: #EAEFF3;
}

html .call-to-action.with-full-borders-tertiary {
	border-color: #EAEFF3;
}

html .call-to-action.call-to-action-tertiary {
	background: #EAEFF3;
}

html .call-to-action.call-to-action-tertiary h3,
html .call-to-action.call-to-action-tertiary p {
	color: #777;
}

html .call-to-action.featured.featured-quaternary {
	border-top-color: #080808;
}

html .call-to-action.call-to-action-quaternary {
	background: #080808;
}

html .call-to-action.with-full-borders-quaternary {
	border-color: #080808;
}

html .call-to-action.call-to-action-quaternary {
	background: #080808;
}

html .call-to-action.call-to-action-quaternary h3,
html .call-to-action.call-to-action-quaternary p {
	color: #FFF;
}

html .call-to-action.featured.featured-dark {
	border-top-color: #212529;
}

html .call-to-action.call-to-action-dark {
	background: #212529;
}

html .call-to-action.with-full-borders-dark {
	border-color: #212529;
}

html .call-to-action.call-to-action-dark {
	background: #212529;
}

html .call-to-action.call-to-action-dark h3,
html .call-to-action.call-to-action-dark p {
	color: #FFF;
}

html .call-to-action.featured.featured-light {
	border-top-color: #FFF;
}

html .call-to-action.call-to-action-light {
	background: #FFF;
}

html .call-to-action.with-full-borders-light {
	border-color: #FFF;
}

html .call-to-action.call-to-action-light {
	background: #FFF;
}

html .call-to-action.call-to-action-light h3,
html .call-to-action.call-to-action-light p {
	color: #777;
}

section.timeline .timeline-box.left:before,
section.timeline .timeline-box.right:before {
	background: #2E3191;
	box-shadow: 0 0 0 3px #FFF, 0 0 0 6px #2E3191;
}

ul.history li .featured-box .box-content {
	border-top-color: #2E3191;
}

.portfolio-list .portfolio-item.portfolio-item-new:after {
	background: linear-gradient(135deg, #2E3191 0%, #45BEE4 80%);
}

.recent-posts .date .month,
article.post .post-date .month {
	background-color: #2E3191;
}

.recent-posts .date .day,
article.post .post-date .day {
	color: #2E3191;
}

.slider .tp-bannertimer {
	background-color: #2E3191;
}

.home-concept strong {
	color: #2E3191;
}

html .home-intro-primary {
	background: #2E3191;
}

html .home-intro-secondary {
	background: #45BEE4;
}

html .home-intro-tertiary {
	background: #EAEFF3;
}

html .home-intro-quaternary {
	background: #080808;
}

html .home-intro-dark {
	background: #212529;
}

html .home-intro-light {
	background: #FFF;
}

html .slide-overlay-primary .slotholder:after {
	background-color: #2E3191 !important;
}

html .slider-container .tp-caption-overlay-primary {
	background: #2E3191;
}

html .slider-container .tp-caption-overlay-opacity-primary {
	background: rgba(224, 70, 34, 0.4);
}

html .tp-bullets.bullets-color-primary .tp-bullet:hover,
html .tp-bullets.bullets-color-primary .tp-bullet.selected {
	background: #2E3191;
}

html .slide-overlay-secondary .slotholder:after {
	background-color: #45BEE4 !important;
}

html .slider-container .tp-caption-overlay-secondary {
	background: #45BEE4;
}

html .slider-container .tp-caption-overlay-opacity-secondary {
	background: rgba(238, 171, 38, 0.4);
}

html .tp-bullets.bullets-color-secondary .tp-bullet:hover,
html .tp-bullets.bullets-color-secondary .tp-bullet.selected {
	background: #45BEE4;
}

html .slide-overlay-tertiary .slotholder:after {
	background-color: #EAEFF3 !important;
}

html .slider-container .tp-caption-overlay-tertiary {
	background: #EAEFF3;
}

html .slider-container .tp-caption-overlay-opacity-tertiary {
	background: rgba(234, 239, 243, 0.4);
}

html .tp-bullets.bullets-color-tertiary .tp-bullet:hover,
html .tp-bullets.bullets-color-tertiary .tp-bullet.selected {
	background: #EAEFF3;
}

html .slide-overlay-quaternary .slotholder:after {
	background-color: #080808 !important;
}

html .slider-container .tp-caption-overlay-quaternary {
	background: #080808;
}

html .slider-container .tp-caption-overlay-opacity-quaternary {
	background: rgba(8, 8, 8, 0.4);
}

html .tp-bullets.bullets-color-quaternary .tp-bullet:hover,
html .tp-bullets.bullets-color-quaternary .tp-bullet.selected {
	background: #080808;
}

html .slide-overlay-dark .slotholder:after {
	background-color: #212529 !important;
}

html .slider-container .tp-caption-overlay-dark {
	background: #212529;
}

html .slider-container .tp-caption-overlay-opacity-dark {
	background: rgba(33, 37, 41, 0.4);
}

html .tp-bullets.bullets-color-dark .tp-bullet:hover,
html .tp-bullets.bullets-color-dark .tp-bullet.selected {
	background: #212529;
}

html .slide-overlay-light .slotholder:after {
	background-color: #FFF !important;
}

html .slider-container .tp-caption-overlay-light {
	background: #FFF;
}

html .slider-container .tp-caption-overlay-opacity-light {
	background: rgba(255, 255, 255, 0.4);
}

html .tp-bullets.bullets-color-light .tp-bullet:hover,
html .tp-bullets.bullets-color-light .tp-bullet.selected {
	background: #FFF;
}

html .slide-overlay.slide-overlay-gradient .slotholder:after {
	background-color: #EAEFF3 !important;
	background-image: linear-gradient(to right, #EAEFF3 0%, #080808 100%) !important;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#EAEFF3', endColorstr='#080808', GradientType=1);
}

.cd-product-viewer-handle .handle {
	background: #2E3191;
}

.tparrows.tparrows-carousel.tp-leftarrow,
.tparrows.tparrows-carousel.tp-rightarrow {
	background-color: #2E3191;
	border-color: #2E3191 #2E3191 #A7A9AC;
	color: #fff;
}

.tparrows.tparrows-carousel.tp-leftarrow:hover,
.tparrows.tparrows-carousel.tp-rightarrow:hover,
.tparrows.tparrows-carousel.tp-leftarrow.hover,
.tparrows.tparrows-carousel.tp-rightarrow.hover {
	border-color: #231F20 #231F20 #2E3191;
	color: #fff;
}

.tparrows.tparrows-carousel.tp-leftarrow:hover:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow:hover:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-leftarrow.hover:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow.hover:not(.bg-transparent) {
	background-color: #3f9bd0;
}

.tparrows.tparrows-carousel.tp-leftarrow:focus,
.tparrows.tparrows-carousel.tp-rightarrow:focus,
.tparrows.tparrows-carousel.tp-leftarrow.focus,
.tparrows.tparrows-carousel.tp-rightarrow.focus {
	border-color: #A7A9AC #A7A9AC #892913;
	color: #fff;
	box-shadow: 0 0 0 3px rgba(224, 70, 34, 0.5);
}

.tparrows.tparrows-carousel.tp-leftarrow:focus:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow:focus:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-leftarrow.focus:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow.focus:not(.bg-transparent) {
	background-color: #606060;
}

.tparrows.tparrows-carousel.tp-leftarrow.disabled,
.tparrows.tparrows-carousel.tp-rightarrow.disabled,
.tparrows.tparrows-carousel.tp-leftarrow:disabled,
.tparrows.tparrows-carousel.tp-rightarrow:disabled {
	border-color: #2E3191 #2E3191 #A7A9AC;
}

.tparrows.tparrows-carousel.tp-leftarrow.disabled:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow.disabled:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-leftarrow:disabled:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow:disabled:not(.bg-transparent) {
	background-color: #2E3191;
}

.tparrows.tparrows-carousel.tp-leftarrow:active,
.tparrows.tparrows-carousel.tp-rightarrow:active,
.tparrows.tparrows-carousel.tp-leftarrow.active,
.tparrows.tparrows-carousel.tp-rightarrow.active {
	border-color: #A7A9AC #A7A9AC #892913 !important;
}

.tparrows.tparrows-carousel.tp-leftarrow:active:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow:active:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-leftarrow.active:not(.bg-transparent),
.tparrows.tparrows-carousel.tp-rightarrow.active:not(.bg-transparent) {
	background-color: #606060 !important;
	background-image: none !important;
}

.tparrows.arrows-primary:before {
	color: #2E3191;
}

.shop .products .product .onsale {
	background-color: #2E3191;
	border-bottom-color: #be3a1b;
}

.shop .cart-totals tr.total .amount {
	color: #2E3191;
}

.shop .products .product .quick-view {
	background: rgba(224, 70, 34, 0.7);
}

.shop .products .product:hover .quick-view:hover {
	background: #2E3191;
}

.product .product-thumb-info .addtocart-btn-wrapper .addtocart-btn:hover {
	color: #FFF;
	background: #2E3191;
}

.product .product-thumb-info .countdown-offer-wrapper {
	background: #2E3191;
}

.dropdown-menu.dropdown-menu-arrow-centered a:focus,
.dropdown-menu.dropdown-menu-arrow-centered a:active {
	background-color: #2E3191;
}

#footer .footer-ribbon {
	background: #2E3191;
}

#footer .footer-ribbon:before {
	border-right-color: #9f3016;
	border-left-color: #9f3016;
}

#footer .footer-nav.footer-nav-bottom-line nav > ul:not(:hover) > li > a.active {
	color: #2E3191 !important;
}

#footer .footer-nav.footer-nav-bottom-line nav > ul:not(:hover) > li > a.active:before {
	background: #2E3191 !important;
}

#footer .footer-nav.footer-nav-bottom-line nav > ul:hover > li:hover > a {
	color: #2E3191 !important;
}

#footer .footer-nav.footer-nav-bottom-line nav > ul:hover > li:hover > a:before {
	background: #2E3191 !important;
}

.scrollable.colored-slider .scrollable-slider {
	background: #2E3191;
}

.sample-icon-list .sample-icon a:hover {
	background-color: #2E3191;
}

.cursor-inner {
	background-color: #2E3191;
}

.cursor-inner.cursor-inner-plus:before {
	color: #2E3191;
}

.cursor-outer {
	border-color: #2E3191;
}

.cssload-thecube .cssload-cube:before {
	background-color: #2E3191;
}

.cssload-cube-progress {
	border-color: #2E3191;
}

.cssload-cube-progress-inner {
	background-color: #2E3191;
}

.cssload-float-rings-inner.cssload-one {
	border-bottom-color: #2E3191;
}

.cssload-float-rings-inner.cssload-two {
	border-right-color: #2E3191;
}

.cssload-float-rings-inner.cssload-three {
	border-top-color: #2E3191;
}

.cssload-float-bars-loading,
.cssload-float-bars-loading:after,
.cssload-float-bars-loading:before {
	background: #2E3191;
}

.cssload-speeding-wheel {
	border-top-color: #2E3191;
	border-bottom-color: #2E3191;
}

.cssload-zenith {
	box-shadow: 3px 3px 1px #2E3191;
}

.cssload-spinning-square-loading {
	background: #2E3191;
}

.cssload-spinning-square-loading:before {
	background: #45BEE4;
}

.cssload-spinning-square-loading:after {
	background: #FFF;
}

.cssload-pulse-loader {
	background: #2E3191;
}

.map-rounded-pin {
	background: #2E3191;
}

html.boxed .body {
	border-top-color: #2E3191;
}

.rounded,
.img-thumbnail,
.img-thumbnail img,
code,
pre,
.form-control,
.form-control-sm,
.form-control-sm .form-control,
.form-control-lg,
.form-control-lg .form-control,
.form-control-sm,
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn,
.form-control-lg,
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn,
.invalid-tooltip,
.btn:not(.btn-rounded):not(.btn-circle),
.btn-group-4 > .btn:not(.btn-rounded),
.btn-sm,
.btn-group-2 > .btn:not(.btn-rounded),
.btn-xs,
.btn-group-1 > .btn:not(.btn-rounded),
.btn-lg,
.btn-group-lg > .btn:not(.btn-rounded),
.btn-sm,
.btn-group-sm > .btn:not(.btn-rounded),
.dropdown-menu,
.input-group-4 > .form-control,
.input-group-4 > .input-group-addon,
.input-group-4 > .input-group-btn > .btn,
.input-group-2 > .form-control,
.input-group-2 > .input-group-addon,
.input-group-2 > .input-group-btn > .btn,
.input-group-addon,
.input-group-addon.form-control-sm,
.input-group-addon.form-control-lg,
.nav-tabs > li > a,
.nav-tabs.nav-justified > li > a,
.nav-pills > li > a,
.nav-tabs-justified > li > a,
.navbar,
.navbar-toggle,
.navbar-toggle .icon-bar,
.breadcrumb,
.pagination,
.pager li > a,
.pager li > span,
.label,
.badge,
.container .jumbotron,
.container-fluid .jumbotron,
.thumbnail,
.alert,
.progress,
.card,
.accordion .card,
.well,
.well-lg,
.well-sm,
.modal-content,
.tooltip-inner,
.popover,
.popover-title,
.carousel-indicators li,
.pagination > li:first-child > a,
.pagination > li:first-child > span,
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span,
.pagination > li:last-child > a,
.pagination > li:last-child > span,
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span,
#header .header-search .form-control,
#header .header-nav-main nav > ul > li > a,
#header .header-nav-main nav > ul > li.dropdown .dropdown-menu,
#header .header-nav-main nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu > .dropdown-menu,
#header .header-nav-main nav > ul > li.dropdown-mega > .dropdown-menu,
#header .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav > li > a,
#header.header-transparent .header-nav-main,
#header.header-semi-transparent .header-nav-main,
#header.header-semi-transparent-light .header-nav-main,
#header .header-nav-main nav > ul > li.dropdown-mega.dropdown-mega-signin .dropdown-menu,
#header .header-nav-main nav > ul > li.dropdown.open > a,
#header .header-nav-main nav > ul > li.dropdown:hover > a,
#header .header-nav-top .dropdown-menu,
.slider-container .tparrows,
.home-intro-compact,
html.dark #header,
html.boxed .body,
blockquote.with-borders,
p.drop-caps.drop-caps-style-2:first-child:first-letter,
.btn-3d,
select,
.captcha-refresh,
.featured-icon,
.accordion .card-header,
.tabs,
.nav-tabs li a,
.tab-content,
.tabs.tabs-bottom .tab-content,
.tabs.tabs-bottom .nav-tabs li a,
.tabs-left .tab-content,
.tabs-left .nav-tabs > li:first-child a,
.tabs-left .nav-tabs > li:last-child a,
.tabs-right .tab-content,
.tabs-right .nav-tabs > li:first-child a,
.tabs-right .nav-tabs > li:last-child a,
.nav-tabs.nav-justified li:first-child a,
.nav-tabs.nav-justified li:first-child a:hover,
.nav-tabs.nav-justified li:last-child a,
.nav-pills .nav-link,
.nav-tabs.nav-justified li:last-child a:hover,
.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:first-child a,
.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:last-child a,
.tabs-navigation,
.tabs-navigation .nav-tabs > li:first-child a,
.tabs-navigation .nav-tabs > li:last-child a,
.nav-pills .nav-link,
.navbar-toggler,
.card,
.card-header:first-child,
.card-footer:last-child,
.card-img,
.badge,
.badge-pill,
.jumbotron,
.alert,
.progress,
.modal-content,
.tooltip-inner,
.popover,
.rounded,
.toggle > label,
.toggle.toggle-simple .toggle > label,
.toggle.toggle-simple .toggle > label:after,
.owl-carousel .owl-nav button[class*="owl-"],
.owl-carousel.rounded-nav .owl-nav button[class*="owl-"],
.owl-carousel.rounded-nav .owl-nav button[class*="owl-"]:hover,
.owl-carousel.full-width .owl-nav button.owl-prev,
.owl-carousel.big-nav .owl-nav button.owl-prev,
.owl-carousel.full-width .owl-nav button.owl-next,
.owl-carousel.big-nav .owl-nav button.owl-next,
.embed-responsive.embed-responsive-borders,
.pricing-table h3,
.pricing-table .plan,
.featured-box,
.featured-box .box-content,
.thumb-info,
.thumb-info .thumb-info-wrapper,
.thumb-info .thumb-info-wrapper:after,
.thumb-info .thumb-info-type,
.thumb-info img,
section.timeline .timeline-date,
section.timeline .timeline-box,
.testimonial blockquote,
.testimonial.testimonial-style-2 blockquote,
.testimonial.testimonial-style-3 blockquote,
.testimonial.testimonial-style-4,
.testimonial.testimonial-style-4 blockquote,
.testimonial.testimonial-style-5 blockquote,
.testimonial.testimonial-style-6 blockquote,
.testimonial.testimonial-style-7 blockquote,
.container .call-to-action,
.progress,
.progress.progress-border-radius,
.progress.progress-border-radius .progress-bar,
.progress-bar,
.progress-bar-tooltip,
.scrollable .scrollable-slider,
.counters.with-borders .counter,
html .scroll-to-top,
.google-map.small,
.google-map-borders,
article.post .post-date .month,
article.post .post-date .day,
article .post-video,
article .post-audio,
ul.comments .comment-block,
.recent-posts .date .month,
#header nav.mega-menu .mega-menu-shop .dropdown-menu,
.shop ul.products .product .add-to-cart-product,
.product-info,
.product-info img,
.product-info + .product-info,
.img-thumbnail.img-thumbnail-hover-icon:before,
.product-thumb-info,
.accordion.accordion-modern-2 > .card .card-header a {
	border-radius: 0 !important;
}

@media (min-width: 992px) {
	#header nav.mega-menu ul.nav-main li.mega-menu-item ul.sub-menu a,
	#header nav.mega-menu ul.nav-main li.mega-menu-fullwidth ul.dropdown-menu,
	#header.flat-menu nav.mega-menu ul.nav-main li.mega-menu-fullwidth ul.dropdown-menu,
	#header.flat-menu nav ul.nav-main li a,
	html.boxed #header.flat-menu .header-top {
		border-radius: 0;
	}
}

@media (min-width: 768px) {
	.nav-tabs.nav-justified > li > a,
	.nav-tabs-justified > li > a,
	.navbar {
		border-radius: 0;
	}
}

@media (max-width: 575px) {
	.tabs .nav.nav-tabs.nav-justified li:first-child a,
	.tabs .nav.nav-tabs.nav-justified li:first-child a:hover,
	.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:last-child a,
	.tabs.tabs-bottom .nav.nav-tabs.nav-justified li:last-child a:hover {
		border-radius: 0;
	}
}
/*
* Header
*/
html #header .header-logo.custom-header-logo:before {
	background-color: #2E3191;
}
/*
* Sticky Sidebar
*/
#sidebar a.active {
	color: #2E3191 !important;
}
/*
* Thumb Gallery Wrapper
*/
.thumb-gallery-wrapper .thumb-gallery-thumbs .owl-item:hover,
.thumb-gallery-wrapper .thumb-gallery-thumbs .owl-item.selected {
	border-color: #2E3191 !important;
}
/*
* Accordion
*/
.custom-accordion-style-1 > .card .card-header a:not(.collapsed) {
	background-color: #2E3191;
	color: #FFF !important;
}

.custom-accordion-style-1 > .card:hover .card-header a.collapsed {
	color: #2E3191 !important;
}
/*
* Process
*/
.custom-process-style-1.process.process-vertical .process-step-circle .process-step-circle-content {
	color: #2E3191;
}

.custom-process-style-1.process.process-vertical .process-step:hover .process-step-circle {
	border-color: #2E3191;
}

.custom-process-style-1.process.process-vertical .process-step:hover .process-step-circle .process-step-circle-content {
	color: #FFF;
}
