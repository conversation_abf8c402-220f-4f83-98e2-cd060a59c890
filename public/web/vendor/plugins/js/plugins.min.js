((t,e)=>{"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(t.document)return e(t);throw new Error("jQuery requires a window with a document")}:e(t)})("undefined"!=typeof window?window:this,function(x,z){function y(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item}function R(t){return null!=t&&t===t.window}var e=[],N=Object.getPrototypeOf,a=e.slice,q=e.flat?function(t){return e.flat.call(t)}:function(t){return e.concat.apply([],t)},H=e.push,_=e.indexOf,F={},W=F.toString,$=F.hasOwnProperty,B=$.toString,V=B.call(Object),m={},C=x.document,U={type:!0,src:!0,nonce:!0,noModule:!0};function Q(t,e,i){var n,o,r=(i=i||C).createElement("script");if(r.text=t,e)for(n in U)(o=e[n]||e.getAttribute&&e.getAttribute(n))&&r.setAttribute(n,o);i.head.appendChild(r).parentNode.removeChild(r)}function Y(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?F[W.call(t)]||"object":typeof t}var t="3.7.1",X=/HTML$/i,T=function(t,e){return new T.fn.init(t,e)};function G(t){var e=!!t&&"length"in t&&t.length,i=Y(t);return!y(t)&&!R(t)&&("array"===i||0===e||"number"==typeof e&&0<e&&e-1 in t)}function b(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}T.fn=T.prototype={jquery:t,constructor:T,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){t=T.merge(this.constructor(),t);return t.prevObject=this,t},each:function(t){return T.each(this,t)},map:function(i){return this.pushStack(T.map(this,function(t,e){return i.call(t,e,t)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(T.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(T.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,t=+t+(t<0?e:0);return this.pushStack(0<=t&&t<e?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:H,sort:e.sort,splice:e.splice},T.extend=T.fn.extend=function(){var t,e,i,n,o,r=arguments[0]||{},s=1,a=arguments.length,l=!1;for("boolean"==typeof r&&(l=r,r=arguments[s]||{},s++),"object"==typeof r||y(r)||(r={}),s===a&&(r=this,s--);s<a;s++)if(null!=(t=arguments[s]))for(e in t)i=t[e],"__proto__"!==e&&r!==i&&(l&&i&&(T.isPlainObject(i)||(n=Array.isArray(i)))?(o=r[e],o=n&&!Array.isArray(o)?[]:n||T.isPlainObject(o)?o:{},n=!1,r[e]=T.extend(l,o,i)):void 0!==i&&(r[e]=i));return r},T.extend({expando:"jQuery"+(t+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){return!(!t||"[object Object]"!==W.call(t)||(t=N(t))&&("function"!=typeof(t=$.call(t,"constructor")&&t.constructor)||B.call(t)!==V))},isEmptyObject:function(t){for(var e in t)return!1;return!0},globalEval:function(t,e,i){Q(t,{nonce:e&&e.nonce},i)},each:function(t,e){var i,n=0;if(G(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},text:function(t){var e,i="",n=0,o=t.nodeType;if(!o)for(;e=t[n++];)i+=T.text(e);return 1===o||11===o?t.textContent:9===o?t.documentElement.textContent:3===o||4===o?t.nodeValue:i},makeArray:function(t,e){e=e||[];return null!=t&&(G(Object(t))?T.merge(e,"string"==typeof t?[t]:t):H.call(e,t)),e},inArray:function(t,e,i){return null==e?-1:_.call(e,t,i)},isXMLDoc:function(t){var e=t&&t.namespaceURI,t=t&&(t.ownerDocument||t).documentElement;return!X.test(e||t&&t.nodeName||"HTML")},merge:function(t,e){for(var i=+e.length,n=0,o=t.length;n<i;n++)t[o++]=e[n];return t.length=o,t},grep:function(t,e,i){for(var n=[],o=0,r=t.length,s=!i;o<r;o++)!e(t[o],o)!=s&&n.push(t[o]);return n},map:function(t,e,i){var n,o,r=0,s=[];if(G(t))for(n=t.length;r<n;r++)null!=(o=e(t[r],r,i))&&s.push(o);else for(r in t)null!=(o=e(t[r],r,i))&&s.push(o);return q(s)},guid:1,support:m}),"function"==typeof Symbol&&(T.fn[Symbol.iterator]=e[Symbol.iterator]),T.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){F["[object "+e+"]"]=e.toLowerCase()});var Z=e.pop,K=e.sort,J=e.splice,i="[\\x20\\t\\r\\n\\f]",tt=new RegExp("^"+i+"+|((?:^|[^\\\\])(?:\\\\.)*)"+i+"+$","g"),et=(T.contains=function(t,e){e=e&&e.parentNode;return t===e||!(!e||1!==e.nodeType||!(t.contains?t.contains(e):t.compareDocumentPosition&&16&t.compareDocumentPosition(e)))},/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g);function it(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}T.escapeSelector=function(t){return(t+"").replace(et,it)};var nt,w,ot,rt,st,E,n,S,d,at,o=C,lt=H,k=lt,I=T.expando,A=0,ct=0,ut=At(),ht=At(),dt=At(),pt=At(),ft=function(t,e){return t===e&&(st=!0),0},mt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",t="(?:\\\\[\\da-fA-F]{1,6}"+i+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",r="\\["+i+"*("+t+")(?:"+i+"*([*^$|!~]?=)"+i+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+t+"))|)"+i+"*\\]",s=":("+t+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+r+")*)|.*)\\)|)",gt=new RegExp(i+"+","g"),vt=new RegExp("^"+i+"*,"+i+"*"),yt=new RegExp("^"+i+"*([>+~]|"+i+")"+i+"*"),bt=new RegExp(i+"|>"),_t=new RegExp(s),wt=new RegExp("^"+t+"$"),xt={ID:new RegExp("^#("+t+")"),CLASS:new RegExp("^\\.("+t+")"),TAG:new RegExp("^("+t+"|[*])"),ATTR:new RegExp("^"+r),PSEUDO:new RegExp("^"+s),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+i+"*(even|odd|(([+-]|)(\\d*)n|)"+i+"*(?:([+-]|)"+i+"*(\\d+)|))"+i+"*\\)|)","i"),bool:new RegExp("^(?:"+mt+")$","i"),needsContext:new RegExp("^"+i+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+i+"*((?:-\\d)?\\d*)"+i+"*\\)|)(?=[^-]|$)","i")},Ct=/^(?:input|select|textarea|button)$/i,Tt=/^h\d$/i,Et=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,St=/[+~]/,h=new RegExp("\\\\[\\da-fA-F]{1,6}"+i+"?|\\\\([^\\r\\n\\f])","g"),p=function(t,e){t="0x"+t.slice(1)-65536;return e||(t<0?String.fromCharCode(65536+t):String.fromCharCode(t>>10|55296,1023&t|56320))},kt=function(){jt()},It=Nt(function(t){return!0===t.disabled&&b(t,"fieldset")},{dir:"parentNode",next:"legend"});try{k.apply(e=a.call(o.childNodes),o.childNodes),e[o.childNodes.length].nodeType}catch(nt){k={apply:function(t,e){lt.apply(t,a.call(e))},call:function(t){lt.apply(t,a.call(arguments,1))}}}function O(t,e,i,n){var o,r,s,a,l,c,u=e&&e.ownerDocument,h=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==h&&9!==h&&11!==h)return i;if(!n&&(jt(e),e=e||E,S)){if(11!==h&&(a=Et.exec(t)))if(o=a[1]){if(9===h){if(!(c=e.getElementById(o)))return i;if(c.id===o)return k.call(i,c),i}else if(u&&(c=u.getElementById(o))&&O.contains(e,c)&&c.id===o)return k.call(i,c),i}else{if(a[2])return k.apply(i,e.getElementsByTagName(t)),i;if((o=a[3])&&e.getElementsByClassName)return k.apply(i,e.getElementsByClassName(o)),i}if(!(pt[t+" "]||d&&d.test(t))){if(c=t,u=e,1===h&&(bt.test(t)||yt.test(t))){for((u=St.test(t)&&Pt(e.parentNode)||e)==e&&m.scope||((s=e.getAttribute("id"))?s=T.escapeSelector(s):e.setAttribute("id",s=I)),r=(l=zt(t)).length;r--;)l[r]=(s?"#"+s:":scope")+" "+Rt(l[r]);c=l.join(",")}try{return k.apply(i,u.querySelectorAll(c)),i}catch(e){pt(t,!0)}finally{s===I&&e.removeAttribute("id")}}}return $t(t.replace(tt,"$1"),e,i,n)}function At(){var n=[];return function t(e,i){return n.push(e+" ")>w.cacheLength&&delete t[n.shift()],t[e+" "]=i}}function l(t){return t[I]=!0,t}function Ot(t){var e=E.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e)}}function Lt(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&It(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function Dt(s){return l(function(r){return r=+r,l(function(t,e){for(var i,n=s([],t.length,r),o=n.length;o--;)t[i=n[o]]&&(t[i]=!(e[i]=t[i]))})})}function Pt(t){return t&&void 0!==t.getElementsByTagName&&t}function jt(t){var t=t?t.ownerDocument||t:o;return t!=E&&9===t.nodeType&&t.documentElement&&(n=(E=t).documentElement,S=!T.isXMLDoc(E),at=n.matches||n.webkitMatchesSelector||n.msMatchesSelector,n.msMatchesSelector&&o!=E&&(t=E.defaultView)&&t.top!==t&&t.addEventListener("unload",kt),m.getById=Ot(function(t){return n.appendChild(t).id=T.expando,!E.getElementsByName||!E.getElementsByName(T.expando).length}),m.disconnectedMatch=Ot(function(t){return at.call(t,"*")}),m.scope=Ot(function(){return E.querySelectorAll(":scope")}),m.cssHas=Ot(function(){try{return E.querySelector(":has(*,:jqfake)"),0}catch(t){return 1}}),m.getById?(w.filter.ID=function(t){var e=t.replace(h,p);return function(t){return t.getAttribute("id")===e}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&S)return(e=e.getElementById(t))?[e]:[]}):(w.filter.ID=function(t){var e=t.replace(h,p);return function(t){t=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return t&&t.value===e}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&S){var i,n,o,r=e.getElementById(t);if(r){if((i=r.getAttributeNode("id"))&&i.value===t)return[r];for(o=e.getElementsByName(t),n=0;r=o[n++];)if((i=r.getAttributeNode("id"))&&i.value===t)return[r]}return[]}}),w.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},w.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&S)return e.getElementsByClassName(t)},d=[],Ot(function(t){var e;n.appendChild(t).innerHTML="<a id='"+I+"' href='' disabled='disabled'></a><select id='"+I+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||d.push("\\["+i+"*(?:value|"+mt+")"),t.querySelectorAll("[id~="+I+"-]").length||d.push("~="),t.querySelectorAll("a#"+I+"+*").length||d.push(".#.+[+~]"),t.querySelectorAll(":checked").length||d.push(":checked"),(e=E.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),n.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(e=E.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||d.push("\\["+i+"*name"+i+"*="+i+"*(?:''|\"\")")}),m.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),ft=function(t,e){var i;return t===e?(st=!0,0):!t.compareDocumentPosition-!e.compareDocumentPosition||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!m.sortDetached&&e.compareDocumentPosition(t)===i?t===E||t.ownerDocument==o&&O.contains(o,t)?-1:e===E||e.ownerDocument==o&&O.contains(o,e)?1:rt?_.call(rt,t)-_.call(rt,e):0:4&i?-1:1)}),E}for(nt in O.matches=function(t,e){return O(t,null,null,e)},O.matchesSelector=function(t,e){if(jt(t),S&&!pt[e+" "]&&(!d||!d.test(e)))try{var i=at.call(t,e);if(i||m.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){pt(e,!0)}return 0<O(e,E,null,[t]).length},O.contains=function(t,e){return(t.ownerDocument||t)!=E&&jt(t),T.contains(t,e)},O.attr=function(t,e){(t.ownerDocument||t)!=E&&jt(t);var i=w.attrHandle[e.toLowerCase()],i=i&&$.call(w.attrHandle,e.toLowerCase())?i(t,e,!S):void 0;return void 0!==i?i:t.getAttribute(e)},O.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},T.uniqueSort=function(t){var e,i=[],n=0,o=0;if(st=!m.sortStable,rt=!m.sortStable&&a.call(t,0),K.call(t,ft),st){for(;e=t[o++];)e===t[o]&&(n=i.push(o));for(;n--;)J.call(t,i[n],1)}return rt=null,t},T.fn.uniqueSort=function(){return this.pushStack(T.uniqueSort(a.apply(this)))},(w=T.expr={cacheLength:50,createPseudo:l,match:xt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(h,p),t[3]=(t[3]||t[4]||t[5]||"").replace(h,p),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||O.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&O.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return xt.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&_t.test(i)&&(e=(e=zt(i,!0))&&i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(h,p).toLowerCase();return"*"===t?function(){return!0}:function(t){return b(t,e)}},CLASS:function(t){var e=ut[t+" "];return e||(e=new RegExp("(^|"+i+")"+t+"("+i+"|$)"))&&ut(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(e,i,n){return function(t){t=O.attr(t,e);return null==t?"!="===i:!i||(t+="","="===i?t===n:"!="===i?t!==n:"^="===i?n&&0===t.indexOf(n):"*="===i?n&&-1<t.indexOf(n):"$="===i?n&&t.slice(-n.length)===n:"~="===i?-1<(" "+t.replace(gt," ")+" ").indexOf(n):"|="===i&&(t===n||t.slice(0,n.length+1)===n+"-"))}},CHILD:function(p,t,e,f,m){var g="nth"!==p.slice(0,3),v="last"!==p.slice(-4),y="of-type"===t;return 1===f&&0===m?function(t){return!!t.parentNode}:function(t,e,i){var n,o,r,s,a,l=g!=v?"nextSibling":"previousSibling",c=t.parentNode,u=y&&t.nodeName.toLowerCase(),h=!i&&!y,d=!1;if(c){if(g){for(;l;){for(r=t;r=r[l];)if(y?b(r,u):1===r.nodeType)return!1;a=l="only"===p&&!a&&"nextSibling"}return!0}if(a=[v?c.firstChild:c.lastChild],v&&h){for(d=(s=(n=(o=c[I]||(c[I]={}))[p]||[])[0]===A&&n[1])&&n[2],r=s&&c.childNodes[s];r=++s&&r&&r[l]||(d=s=0,a.pop());)if(1===r.nodeType&&++d&&r===t){o[p]=[A,s,d];break}}else if(!1===(d=h?s=(n=(o=t[I]||(t[I]={}))[p]||[])[0]===A&&n[1]:d))for(;(r=++s&&r&&r[l]||(d=s=0,a.pop()))&&((y?!b(r,u):1!==r.nodeType)||!++d||(h&&((o=r[I]||(r[I]={}))[p]=[A,d]),r!==t)););return(d-=m)===f||d%f==0&&0<=d/f}}},PSEUDO:function(t,r){var e,s=w.pseudos[t]||w.setFilters[t.toLowerCase()]||O.error("unsupported pseudo: "+t);return s[I]?s(r):1<s.length?(e=[t,t,"",r],w.setFilters.hasOwnProperty(t.toLowerCase())?l(function(t,e){for(var i,n=s(t,r),o=n.length;o--;)t[i=_.call(t,n[o])]=!(e[i]=n[o])}):function(t){return s(t,0,e)}):s}},pseudos:{not:l(function(t){var n=[],o=[],a=Wt(t.replace(tt,"$1"));return a[I]?l(function(t,e,i,n){for(var o,r=a(t,null,n,[]),s=t.length;s--;)(o=r[s])&&(t[s]=!(e[s]=o))}):function(t,e,i){return n[0]=t,a(n,null,i,o),n[0]=null,!o.pop()}}),has:l(function(e){return function(t){return 0<O(e,t).length}}),contains:l(function(e){return e=e.replace(h,p),function(t){return-1<(t.textContent||T.text(t)).indexOf(e)}}),lang:l(function(i){return wt.test(i||"")||O.error("unsupported lang: "+i),i=i.replace(h,p).toLowerCase(),function(t){var e;do{if(e=S?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(e=e.toLowerCase())===i||0===e.indexOf(i+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var e=x.location&&x.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===n},focus:function(t){return t===(()=>{try{return E.activeElement}catch(t){}})()&&E.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:Lt(!1),disabled:Lt(!0),checked:function(t){return b(t,"input")&&!!t.checked||b(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!w.pseudos.empty(t)},header:function(t){return Tt.test(t.nodeName)},input:function(t){return Ct.test(t.nodeName)},button:function(t){return b(t,"input")&&"button"===t.type||b(t,"button")},text:function(t){return b(t,"input")&&"text"===t.type&&(null==(t=t.getAttribute("type"))||"text"===t.toLowerCase())},first:Dt(function(){return[0]}),last:Dt(function(t,e){return[e-1]}),eq:Dt(function(t,e,i){return[i<0?i+e:i]}),even:Dt(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:Dt(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:Dt(function(t,e,i){for(var n=i<0?i+e:e<i?e:i;0<=--n;)t.push(n);return t}),gt:Dt(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[nt]=(e=>function(t){return b(t,"input")&&t.type===e})(nt);for(nt in{submit:!0,reset:!0})w.pseudos[nt]=(e=>function(t){return(b(t,"input")||b(t,"button"))&&t.type===e})(nt);function Mt(){}function zt(t,e){var i,n,o,r,s,a,l,c=ht[t+" "];if(c)return e?0:c.slice(0);for(s=t,a=[],l=w.preFilter;s;){for(r in i&&!(n=vt.exec(s))||(n&&(s=s.slice(n[0].length)||s),a.push(o=[])),i=!1,(n=yt.exec(s))&&(i=n.shift(),o.push({value:i,type:n[0].replace(tt," ")}),s=s.slice(i.length)),w.filter)!(n=xt[r].exec(s))||l[r]&&!(n=l[r](n))||(i=n.shift(),o.push({value:i,type:r,matches:n}),s=s.slice(i.length));if(!i)break}return e?s.length:s?O.error(t):ht(t,a).slice(0)}function Rt(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function Nt(s,t,e){var a=t.dir,l=t.next,c=l||a,u=e&&"parentNode"===c,h=ct++;return t.first?function(t,e,i){for(;t=t[a];)if(1===t.nodeType||u)return s(t,e,i);return!1}:function(t,e,i){var n,o,r=[A,h];if(i){for(;t=t[a];)if((1===t.nodeType||u)&&s(t,e,i))return!0}else for(;t=t[a];)if(1===t.nodeType||u)if(o=t[I]||(t[I]={}),l&&b(t,l))t=t[a]||t;else{if((n=o[c])&&n[0]===A&&n[1]===h)return r[2]=n[2];if((o[c]=r)[2]=s(t,e,i))return!0}return!1}}function qt(o){return 1<o.length?function(t,e,i){for(var n=o.length;n--;)if(!o[n](t,e,i))return!1;return!0}:o[0]}function Ht(t,e,i,n,o){for(var r,s=[],a=0,l=t.length,c=null!=e;a<l;a++)!(r=t[a])||i&&!i(r,n,o)||(s.push(r),c&&e.push(a));return s}function Ft(p,f,m,g,v,t){return g&&!g[I]&&(g=Ft(g)),v&&!v[I]&&(v=Ft(v,t)),l(function(t,e,i,n){var o,r,s,a,l=[],c=[],u=e.length,h=t||((t,e,i)=>{for(var n=0,o=e.length;n<o;n++)O(t,e[n],i);return i})(f||"*",i.nodeType?[i]:i,[]),d=!p||!t&&f?h:Ht(h,l,p,i,n);if(m?m(d,a=v||(t?p:u||g)?[]:e,i,n):a=d,g)for(o=Ht(a,c),g(o,[],i,n),r=o.length;r--;)(s=o[r])&&(a[c[r]]=!(d[c[r]]=s));if(t){if(v||p){if(v){for(o=[],r=a.length;r--;)(s=a[r])&&o.push(d[r]=s);v(null,a=[],o,n)}for(r=a.length;r--;)(s=a[r])&&-1<(o=v?_.call(t,s):l[r])&&(t[o]=!(e[o]=s))}}else a=Ht(a===e?a.splice(u,a.length):a),v?v(null,e,a,n):k.apply(e,a)})}function Wt(t,e){var i,g,v,y,b,n,o=[],r=[],s=dt[t+" "];if(!s){for(i=(e=e||zt(t)).length;i--;)((s=function t(e){for(var n,i,o,r=e.length,s=w.relative[e[0].type],a=s||w.relative[" "],l=s?1:0,c=Nt(function(t){return t===n},a,!0),u=Nt(function(t){return-1<_.call(n,t)},a,!0),h=[function(t,e,i){return t=!s&&(i||e!=ot)||((n=e).nodeType?c:u)(t,e,i),n=null,t}];l<r;l++)if(i=w.relative[e[l].type])h=[Nt(qt(h),i)];else{if((i=w.filter[e[l].type].apply(null,e[l].matches))[I]){for(o=++l;o<r&&!w.relative[e[o].type];o++);return Ft(1<l&&qt(h),1<l&&Rt(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(tt,"$1"),i,l<o&&t(e.slice(l,o)),o<r&&t(e=e.slice(o)),o<r&&Rt(e))}h.push(i)}return qt(h)}(e[i]))[I]?o:r).push(s);(s=dt(t,(y=0<(v=o).length,b=0<(g=r).length,n=function(t,e,i,n,o){var r,s,a,l=0,c="0",u=t&&[],h=[],d=ot,p=t||b&&w.find.TAG("*",o),f=A+=null==d?1:Math.random()||.1,m=p.length;for(o&&(ot=e==E||e||o);c!==m&&null!=(r=p[c]);c++){if(b&&r){for(s=0,e||r.ownerDocument==E||(jt(r),i=!S);a=g[s++];)if(a(r,e||E,i)){k.call(n,r);break}o&&(A=f)}y&&((r=!a&&r)&&l--,t)&&u.push(r)}if(l+=c,y&&c!==l){for(s=0;a=v[s++];)a(u,h,e,i);if(t){if(0<l)for(;c--;)u[c]||h[c]||(h[c]=Z.call(n));h=Ht(h)}k.apply(n,h),o&&!t&&0<h.length&&1<l+v.length&&T.uniqueSort(n)}return o&&(A=f,ot=d),u},y?l(n):n))).selector=t}return s}function $t(t,e,i,n){var o,r,s,a,l,c="function"==typeof t&&t,u=!n&&zt(t=c.selector||t);if(i=i||[],1===u.length){if(2<(r=u[0]=u[0].slice(0)).length&&"ID"===(s=r[0]).type&&9===e.nodeType&&S&&w.relative[r[1].type]){if(!(e=(w.find.ID(s.matches[0].replace(h,p),e)||[])[0]))return i;c&&(e=e.parentNode),t=t.slice(r.shift().value.length)}for(o=xt.needsContext.test(t)?0:r.length;o--&&(s=r[o],!w.relative[a=s.type]);)if((l=w.find[a])&&(n=l(s.matches[0].replace(h,p),St.test(r[0].type)&&Pt(e.parentNode)||e))){if(r.splice(o,1),t=n.length&&Rt(r))break;return k.apply(i,n),i}}return(c||Wt(t,u))(n,e,!S,i,!e||St.test(t)&&Pt(e.parentNode)||e),i}Mt.prototype=w.filters=w.pseudos,w.setFilters=new Mt,m.sortStable=I.split("").sort(ft).join("")===I,jt(),m.sortDetached=Ot(function(t){return 1&t.compareDocumentPosition(E.createElement("fieldset"))}),T.find=O,T.expr[":"]=T.expr.pseudos,T.unique=T.uniqueSort,O.compile=Wt,O.select=$t,O.setDocument=jt,O.tokenize=zt,O.escape=T.escapeSelector,O.getText=T.text,O.isXML=T.isXMLDoc,O.selectors=T.expr,O.support=T.support,O.uniqueSort=T.uniqueSort;function Bt(t,e,i){for(var n=[],o=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&T(t).is(i))break;n.push(t)}return n}function Vt(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i}var Ut=T.expr.match.needsContext,Qt=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Yt(t,i,n){return y(i)?T.grep(t,function(t,e){return!!i.call(t,e,t)!==n}):i.nodeType?T.grep(t,function(t){return t===i!==n}):"string"!=typeof i?T.grep(t,function(t){return-1<_.call(i,t)!==n}):T.filter(i,t,n)}T.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?T.find.matchesSelector(n,t)?[n]:[]:T.find.matches(t,T.grep(e,function(t){return 1===t.nodeType}))},T.fn.extend({find:function(t){var e,i,n=this.length,o=this;if("string"!=typeof t)return this.pushStack(T(t).filter(function(){for(e=0;e<n;e++)if(T.contains(o[e],this))return!0}));for(i=this.pushStack([]),e=0;e<n;e++)T.find(t,o[e],i);return 1<n?T.uniqueSort(i):i},filter:function(t){return this.pushStack(Yt(this,t||[],!1))},not:function(t){return this.pushStack(Yt(this,t||[],!0))},is:function(t){return!!Yt(this,"string"==typeof t&&Ut.test(t)?T(t):t||[],!1).length}});var Xt,Gt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Zt=((T.fn.init=function(t,e,i){if(t){if(i=i||Xt,"string"!=typeof t)return t.nodeType?(this[0]=t,this.length=1,this):y(t)?void 0!==i.ready?i.ready(t):t(T):T.makeArray(t,this);if(!(n="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:Gt.exec(t))||!n[1]&&e)return(!e||e.jquery?e||i:this.constructor(e)).find(t);if(n[1]){if(e=e instanceof T?e[0]:e,T.merge(this,T.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:C,!0)),Qt.test(n[1])&&T.isPlainObject(e))for(var n in e)y(this[n])?this[n](e[n]):this.attr(n,e[n])}else(i=C.getElementById(n[2]))&&(this[0]=i,this.length=1)}return this}).prototype=T.fn,Xt=T(C),/^(?:parents|prev(?:Until|All))/),Kt={children:!0,contents:!0,next:!0,prev:!0};function Jt(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}T.fn.extend({has:function(t){var e=T(t,this),i=e.length;return this.filter(function(){for(var t=0;t<i;t++)if(T.contains(this,e[t]))return!0})},closest:function(t,e){var i,n=0,o=this.length,r=[],s="string"!=typeof t&&T(t);if(!Ut.test(t))for(;n<o;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(s?-1<s.index(i):1===i.nodeType&&T.find.matchesSelector(i,t))){r.push(i);break}return this.pushStack(1<r.length?T.uniqueSort(r):r)},index:function(t){return t?"string"==typeof t?_.call(T(t),this[0]):_.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(T.uniqueSort(T.merge(this.get(),T(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),T.each({parent:function(t){t=t.parentNode;return t&&11!==t.nodeType?t:null},parents:function(t){return Bt(t,"parentNode")},parentsUntil:function(t,e,i){return Bt(t,"parentNode",i)},next:function(t){return Jt(t,"nextSibling")},prev:function(t){return Jt(t,"previousSibling")},nextAll:function(t){return Bt(t,"nextSibling")},prevAll:function(t){return Bt(t,"previousSibling")},nextUntil:function(t,e,i){return Bt(t,"nextSibling",i)},prevUntil:function(t,e,i){return Bt(t,"previousSibling",i)},siblings:function(t){return Vt((t.parentNode||{}).firstChild,t)},children:function(t){return Vt(t.firstChild)},contents:function(t){return null!=t.contentDocument&&N(t.contentDocument)?t.contentDocument:(b(t,"template")&&(t=t.content||t),T.merge([],t.childNodes))}},function(n,o){T.fn[n]=function(t,e){var i=T.map(this,o,t);return(e="Until"!==n.slice(-5)?t:e)&&"string"==typeof e&&(i=T.filter(e,i)),1<this.length&&(Kt[n]||T.uniqueSort(i),Zt.test(n))&&i.reverse(),this.pushStack(i)}});var L=/[^\x20\t\r\n\f]+/g;function te(t){return t}function ee(t){throw t}function ie(t,e,i,n){var o;try{t&&y(o=t.promise)?o.call(t).done(e).fail(i):t&&y(o=t.then)?o.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}T.Callbacks=function(n){var t,i;n="string"==typeof n?(t=n,i={},T.each(t.match(L)||[],function(t,e){i[e]=!0}),i):T.extend({},n);function o(){for(a=a||n.once,s=r=!0;c.length;u=-1)for(e=c.shift();++u<l.length;)!1===l[u].apply(e[0],e[1])&&n.stopOnFalse&&(u=l.length,e=!1);n.memory||(e=!1),r=!1,a&&(l=e?[]:"")}var r,e,s,a,l=[],c=[],u=-1,h={add:function(){return l&&(e&&!r&&(u=l.length-1,c.push(e)),function i(t){T.each(t,function(t,e){y(e)?n.unique&&h.has(e)||l.push(e):e&&e.length&&"string"!==Y(e)&&i(e)})}(arguments),e)&&!r&&o(),this},remove:function(){return T.each(arguments,function(t,e){for(var i;-1<(i=T.inArray(e,l,i));)l.splice(i,1),i<=u&&u--}),this},has:function(t){return t?-1<T.inArray(t,l):0<l.length},empty:function(){return l=l&&[],this},disable:function(){return a=c=[],l=e="",this},disabled:function(){return!l},lock:function(){return a=c=[],e||r||(l=e=""),this},locked:function(){return!!a},fireWith:function(t,e){return a||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),r)||o(),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!s}};return h},T.extend({Deferred:function(t){var r=[["notify","progress",T.Callbacks("memory"),T.Callbacks("memory"),2],["resolve","done",T.Callbacks("once memory"),T.Callbacks("once memory"),0,"resolved"],["reject","fail",T.Callbacks("once memory"),T.Callbacks("once memory"),1,"rejected"]],o="pending",s={state:function(){return o},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return s.then(null,t)},pipe:function(){var o=arguments;return T.Deferred(function(n){T.each(r,function(t,e){var i=y(o[e[4]])&&o[e[4]];a[e[1]](function(){var t=i&&i.apply(this,arguments);t&&y(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[e[0]+"With"](this,i?[t]:arguments)})}),o=null}).promise()},then:function(e,i,n){var l=0;function c(o,r,s,a){return function(){function t(){var t,e;if(!(o<l)){if((t=s.apply(i,n))===r.promise())throw new TypeError("Thenable self-resolution");e=t&&("object"==typeof t||"function"==typeof t)&&t.then,y(e)?a?e.call(t,c(l,r,te,a),c(l,r,ee,a)):(l++,e.call(t,c(l,r,te,a),c(l,r,ee,a),c(l,r,te,r.notifyWith))):(s!==te&&(i=void 0,n=[t]),(a||r.resolveWith)(i,n))}}var i=this,n=arguments,e=a?t:function(){try{t()}catch(t){T.Deferred.exceptionHook&&T.Deferred.exceptionHook(t,e.error),l<=o+1&&(s!==ee&&(i=void 0,n=[t]),r.rejectWith(i,n))}};o?e():(T.Deferred.getErrorHook?e.error=T.Deferred.getErrorHook():T.Deferred.getStackHook&&(e.error=T.Deferred.getStackHook()),x.setTimeout(e))}}return T.Deferred(function(t){r[0][3].add(c(0,t,y(n)?n:te,t.notifyWith)),r[1][3].add(c(0,t,y(e)?e:te)),r[2][3].add(c(0,t,y(i)?i:ee))}).promise()},promise:function(t){return null!=t?T.extend(t,s):s}},a={};return T.each(r,function(t,e){var i=e[2],n=e[5];s[e[1]]=i.add,n&&i.add(function(){o=n},r[3-t][2].disable,r[3-t][3].disable,r[0][2].lock,r[0][3].lock),i.add(e[3].fire),a[e[0]]=function(){return a[e[0]+"With"](this===a?void 0:this,arguments),this},a[e[0]+"With"]=i.fireWith}),s.promise(a),t&&t.call(a,a),a},when:function(t){function e(e){return function(t){o[e]=this,r[e]=1<arguments.length?a.call(arguments):t,--i||s.resolveWith(o,r)}}var i=arguments.length,n=i,o=Array(n),r=a.call(arguments),s=T.Deferred();if(i<=1&&(ie(t,s.done(e(n)).resolve,s.reject,!i),"pending"===s.state()||y(r[n]&&r[n].then)))return s.then();for(;n--;)ie(r[n],e(n),s.reject);return s.promise()}});var ne=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,oe=(T.Deferred.exceptionHook=function(t,e){x.console&&x.console.warn&&t&&ne.test(t.name)&&x.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},T.readyException=function(t){x.setTimeout(function(){throw t})},T.Deferred());function re(){C.removeEventListener("DOMContentLoaded",re),x.removeEventListener("load",re),T.ready()}T.fn.ready=function(t){return oe.then(t).catch(function(t){T.readyException(t)}),this},T.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--T.readyWait:T.isReady)||(T.isReady=!0)!==t&&0<--T.readyWait||oe.resolveWith(C,[T])}}),T.ready.then=oe.then,"complete"===C.readyState||"loading"!==C.readyState&&!C.documentElement.doScroll?x.setTimeout(T.ready):(C.addEventListener("DOMContentLoaded",re),x.addEventListener("load",re));function u(t,e,i,n,o,r,s){var a=0,l=t.length,c=null==i;if("object"===Y(i))for(a in o=!0,i)u(t,e,a,i[a],!0,r,s);else if(void 0!==n&&(o=!0,y(n)||(s=!0),e=c?s?(e.call(t,n),null):(c=e,function(t,e,i){return c.call(T(t),i)}):e))for(;a<l;a++)e(t[a],i,s?n:n.call(t[a],a,e(t[a],i)));return o?t:c?e.call(t):l?e(t[0],i):r}var se=/^-ms-/,ae=/-([a-z])/g;function le(t,e){return e.toUpperCase()}function D(t){return t.replace(se,"ms-").replace(ae,le)}function ce(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function ue(){this.expando=T.expando+ue.uid++}ue.uid=1,ue.prototype={cache:function(t){var e=t[this.expando];return e||(e={},ce(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,o=this.cache(t);if("string"==typeof e)o[D(e)]=i;else for(n in e)o[D(n)]=e[n];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][D(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(D):(e=D(e))in n?[e]:e.match(L)||[]).length;for(;i--;)delete n[e[i]]}void 0!==e&&!T.isEmptyObject(n)||(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){t=t[this.expando];return void 0!==t&&!T.isEmptyObject(t)}};var v=new ue,c=new ue,he=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,de=/[A-Z]/g;function pe(t,e,i){var n,o;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(de,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i="true"===(o=i)||"false"!==o&&("null"===o?null:o===+o+""?+o:he.test(o)?JSON.parse(o):o)}catch(t){}c.set(t,e,i)}else i=void 0;return i}T.extend({hasData:function(t){return c.hasData(t)||v.hasData(t)},data:function(t,e,i){return c.access(t,e,i)},removeData:function(t,e){c.remove(t,e)},_data:function(t,e,i){return v.access(t,e,i)},_removeData:function(t,e){v.remove(t,e)}}),T.fn.extend({data:function(i,t){var e,n,o,r=this[0],s=r&&r.attributes;if(void 0!==i)return"object"==typeof i?this.each(function(){c.set(this,i)}):u(this,function(t){var e;if(r&&void 0===t)return void 0!==(e=c.get(r,i))||void 0!==(e=pe(r,i))?e:void 0;this.each(function(){c.set(this,i,t)})},null,t,1<arguments.length,null,!0);if(this.length&&(o=c.get(r),1===r.nodeType)&&!v.get(r,"hasDataAttrs")){for(e=s.length;e--;)s[e]&&0===(n=s[e].name).indexOf("data-")&&(n=D(n.slice(5)),pe(r,n,o[n]));v.set(r,"hasDataAttrs",!0)}return o},removeData:function(t){return this.each(function(){c.remove(this,t)})}}),T.extend({queue:function(t,e,i){var n;if(t)return n=v.get(t,e=(e||"fx")+"queue"),i&&(!n||Array.isArray(i)?n=v.access(t,e,T.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=T.queue(t,e),n=i.length,o=i.shift(),r=T._queueHooks(t,e);"inprogress"===o&&(o=i.shift(),n--),o&&("fx"===e&&i.unshift("inprogress"),delete r.stop,o.call(t,function(){T.dequeue(t,e)},r)),!n&&r&&r.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return v.get(t,i)||v.access(t,i,{empty:T.Callbacks("once memory").add(function(){v.remove(t,[e+"queue",i])})})}}),T.fn.extend({queue:function(e,i){var t=2;return"string"!=typeof e&&(i=e,e="fx",t--),arguments.length<t?T.queue(this[0],e):void 0===i?this:this.each(function(){var t=T.queue(this,e,i);T._queueHooks(this,e),"fx"===e&&"inprogress"!==t[0]&&T.dequeue(this,e)})},dequeue:function(t){return this.each(function(){T.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){function i(){--o||r.resolveWith(s,[s])}var n,o=1,r=T.Deferred(),s=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(n=v.get(s[a],t+"queueHooks"))&&n.empty&&(o++,n.empty.add(i));return i(),r.promise(e)}});function fe(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&ye(t)&&"none"===T.css(t,"display")}var t=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,me=new RegExp("^(?:([+-])=|)("+t+")([a-z%]*)$","i"),ge=["Top","Right","Bottom","Left"],ve=C.documentElement,ye=function(t){return T.contains(t.ownerDocument,t)},be={composed:!0};ve.getRootNode&&(ye=function(t){return T.contains(t.ownerDocument,t)||t.getRootNode(be)===t.ownerDocument});function _e(t,e,i,n){var o,r,s=20,a=n?function(){return n.cur()}:function(){return T.css(t,e,"")},l=a(),c=i&&i[3]||(T.cssNumber[e]?"":"px"),u=t.nodeType&&(T.cssNumber[e]||"px"!==c&&+l)&&me.exec(T.css(t,e));if(u&&u[3]!==c){for(c=c||u[3],u=+(l/=2)||1;s--;)T.style(t,e,u+c),(1-r)*(1-(r=a()/l||.5))<=0&&(s=0),u/=r;T.style(t,e,(u*=2)+c),i=i||[]}return i&&(u=+u||+l||0,o=i[1]?u+(i[1]+1)*i[2]:+i[2],n)&&(n.unit=c,n.start=u,n.end=o),o}var we={};function xe(t,e){for(var i,n,o,r,s,a,l=[],c=0,u=t.length;c<u;c++)(n=t[c]).style&&(i=n.style.display,e?("none"===i&&(l[c]=v.get(n,"display")||null,l[c]||(n.style.display="")),""===n.style.display&&fe(n)&&(l[c]=(a=r=o=void 0,r=n.ownerDocument,(a=we[s=n.nodeName])||(o=r.body.appendChild(r.createElement(s)),a=T.css(o,"display"),o.parentNode.removeChild(o),we[s]=a="none"===a?"block":a)))):"none"!==i&&(l[c]="none",v.set(n,"display",i)));for(c=0;c<u;c++)null!=l[c]&&(t[c].style.display=l[c]);return t}T.fn.extend({show:function(){return xe(this,!0)},hide:function(){return xe(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){fe(this)?T(this).show():T(this).hide()})}});var Ce=/^(?:checkbox|radio)$/i,Te=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ee=/^$|^module$|\/(?:java|ecma)script/i,r=C.createDocumentFragment().appendChild(C.createElement("div")),f=((s=C.createElement("input")).setAttribute("type","radio"),s.setAttribute("checked","checked"),s.setAttribute("name","t"),r.appendChild(s),m.checkClone=r.cloneNode(!0).cloneNode(!0).lastChild.checked,r.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!r.cloneNode(!0).lastChild.defaultValue,r.innerHTML="<option></option>",m.option=!!r.lastChild,{thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function g(t,e){var i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[];return void 0===e||e&&b(t,e)?T.merge([t],i):i}function Se(t,e){for(var i=0,n=t.length;i<n;i++)v.set(t[i],"globalEval",!e||v.get(e[i],"globalEval"))}f.tbody=f.tfoot=f.colgroup=f.caption=f.thead,f.th=f.td,m.option||(f.optgroup=f.option=[1,"<select multiple='multiple'>","</select>"]);var ke=/<|&#?\w+;/;function Ie(t,e,i,n,o){for(var r,s,a,l,c,u=e.createDocumentFragment(),h=[],d=0,p=t.length;d<p;d++)if((r=t[d])||0===r)if("object"===Y(r))T.merge(h,r.nodeType?[r]:r);else if(ke.test(r)){for(s=s||u.appendChild(e.createElement("div")),a=(Te.exec(r)||["",""])[1].toLowerCase(),a=f[a]||f._default,s.innerHTML=a[1]+T.htmlPrefilter(r)+a[2],c=a[0];c--;)s=s.lastChild;T.merge(h,s.childNodes),(s=u.firstChild).textContent=""}else h.push(e.createTextNode(r));for(u.textContent="",d=0;r=h[d++];)if(n&&-1<T.inArray(r,n))o&&o.push(r);else if(l=ye(r),s=g(u.appendChild(r),"script"),l&&Se(s),i)for(c=0;r=s[c++];)Ee.test(r.type||"")&&i.push(r);return u}var Ae=/^([^.]*)(?:\.(.+)|)/;function Oe(){return!0}function Le(){return!1}function De(t,e,i,n,o,r){var s,a;if("object"==typeof e){for(a in"string"!=typeof i&&(n=n||i,i=void 0),e)De(t,a,i,n,e[a],r);return t}if(null==n&&null==o?(o=i,n=i=void 0):null==o&&("string"==typeof i?(o=n,n=void 0):(o=n,n=i,i=void 0)),!1===o)o=Le;else if(!o)return t;return 1===r&&(s=o,(o=function(t){return T().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=T.guid++)),t.each(function(){T.event.add(this,e,o,n,i)})}function Pe(t,n,e){e?(v.set(t,n,!1),T.event.add(t,n,{namespace:!1,handler:function(t){var e,i=v.get(this,n);if(1&t.isTrigger&&this[n]){if(i)(T.event.special[n]||{}).delegateType&&t.stopPropagation();else if(i=a.call(arguments),v.set(this,n,i),this[n](),e=v.get(this,n),v.set(this,n,!1),i!==e)return t.stopImmediatePropagation(),t.preventDefault(),e}else i&&(v.set(this,n,T.event.trigger(i[0],i.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=Oe)}})):void 0===v.get(t,n)&&T.event.add(t,n,Oe)}T.event={global:{},add:function(e,t,i,n,o){var r,s,a,l,c,u,h,d,p,f=v.get(e);if(ce(e))for(i.handler&&(i=(r=i).handler,o=r.selector),o&&T.find.matchesSelector(ve,o),i.guid||(i.guid=T.guid++),a=(a=f.events)||(f.events=Object.create(null)),s=(s=f.handle)||(f.handle=function(t){return void 0!==T&&T.event.triggered!==t.type?T.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match(L)||[""]).length;l--;)h=p=(d=Ae.exec(t[l])||[])[1],d=(d[2]||"").split(".").sort(),h&&(c=T.event.special[h]||{},h=(o?c.delegateType:c.bindType)||h,c=T.event.special[h]||{},p=T.extend({type:h,origType:p,data:n,handler:i,guid:i.guid,selector:o,needsContext:o&&T.expr.match.needsContext.test(o),namespace:d.join(".")},r),(u=a[h])||((u=a[h]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(e,n,d,s))||e.addEventListener&&e.addEventListener(h,s),c.add&&(c.add.call(e,p),p.handler.guid||(p.handler.guid=i.guid)),o?u.splice(u.delegateCount++,0,p):u.push(p),T.event.global[h]=!0)},remove:function(t,e,i,n,o){var r,s,a,l,c,u,h,d,p,f,m,g=v.hasData(t)&&v.get(t);if(g&&(l=g.events)){for(c=(e=(e||"").match(L)||[""]).length;c--;)if(p=m=(a=Ae.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),p){for(h=T.event.special[p]||{},d=l[p=(n?h.delegateType:h.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=r=d.length;r--;)u=d[r],!o&&m!==u.origType||i&&i.guid!==u.guid||a&&!a.test(u.namespace)||n&&n!==u.selector&&("**"!==n||!u.selector)||(d.splice(r,1),u.selector&&d.delegateCount--,h.remove&&h.remove.call(t,u));s&&!d.length&&(h.teardown&&!1!==h.teardown.call(t,f,g.handle)||T.removeEvent(t,p,g.handle),delete l[p])}else for(p in l)T.event.remove(t,p+e[c],i,n,!0);T.isEmptyObject(l)&&v.remove(t,"handle events")}},dispatch:function(t){var e,i,n,o,r,s=new Array(arguments.length),a=T.event.fix(t),t=(v.get(this,"events")||Object.create(null))[a.type]||[],l=T.event.special[a.type]||{};for(s[0]=a,e=1;e<arguments.length;e++)s[e]=arguments[e];if(a.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,a)){for(r=T.event.handlers.call(this,a,t),e=0;(n=r[e++])&&!a.isPropagationStopped();)for(a.currentTarget=n.elem,i=0;(o=n.handlers[i++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!1!==o.namespace&&!a.rnamespace.test(o.namespace)||(a.handleObj=o,a.data=o.data,void 0!==(o=((T.event.special[o.origType]||{}).handle||o.handler).apply(n.elem,s))&&!1===(a.result=o)&&(a.preventDefault(),a.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,a),a.result}},handlers:function(t,e){var i,n,o,r,s,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(r=[],s={},i=0;i<l;i++)void 0===s[o=(n=e[i]).selector+" "]&&(s[o]=n.needsContext?-1<T(o,this).index(c):T.find(o,this,null,[c]).length),s[o]&&r.push(n);r.length&&a.push({elem:c,handlers:r})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(e,t){Object.defineProperty(T.Event.prototype,e,{enumerable:!0,configurable:!0,get:y(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(t){return t[T.expando]?t:new T.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){t=this||t;return Ce.test(t.type)&&t.click&&b(t,"input")&&Pe(t,"click",!0),!1},trigger:function(t){t=this||t;return Ce.test(t.type)&&t.click&&b(t,"input")&&Pe(t,"click"),!0},_default:function(t){t=t.target;return Ce.test(t.type)&&t.click&&b(t,"input")&&v.get(t,"click")||b(t,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},T.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},T.Event=function(t,e){if(!(this instanceof T.Event))return new T.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Oe:Le,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&T.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[T.expando]=!0},T.Event.prototype={constructor:T.Event,isDefaultPrevented:Le,isPropagationStopped:Le,isImmediatePropagationStopped:Le,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Oe,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Oe,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Oe,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},T.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},T.event.addProp),T.each({focus:"focusin",blur:"focusout"},function(n,o){function r(t){var e,i;C.documentMode?(e=v.get(this,"handle"),(i=T.event.fix(t)).type="focusin"===t.type?"focus":"blur",i.isSimulated=!0,e(t),i.target===i.currentTarget&&e(i)):T.event.simulate(o,t.target,T.event.fix(t))}T.event.special[n]={setup:function(){var t;if(Pe(this,n,!0),!C.documentMode)return!1;(t=v.get(this,o))||this.addEventListener(o,r),v.set(this,o,(t||0)+1)},trigger:function(){return Pe(this,n),!0},teardown:function(){var t;if(!C.documentMode)return!1;(t=v.get(this,o)-1)?v.set(this,o,t):(this.removeEventListener(o,r),v.remove(this,o))},_default:function(t){return v.get(t.target,n)},delegateType:o},T.event.special[o]={setup:function(){var t=this.ownerDocument||this.document||this,e=C.documentMode?this:t,i=v.get(e,o);i||(C.documentMode?this.addEventListener(o,r):t.addEventListener(n,r,!0)),v.set(e,o,(i||0)+1)},teardown:function(){var t=this.ownerDocument||this.document||this,e=C.documentMode?this:t,i=v.get(e,o)-1;i?v.set(e,o,i):(C.documentMode?this.removeEventListener(o,r):t.removeEventListener(n,r,!0),v.remove(e,o))}}}),T.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,o){T.event.special[t]={delegateType:o,bindType:o,handle:function(t){var e,i=t.relatedTarget,n=t.handleObj;return i&&(i===this||T.contains(this,i))||(t.type=n.origType,e=n.handler.apply(this,arguments),t.type=o),e}}}),T.fn.extend({on:function(t,e,i,n){return De(this,t,e,i,n)},one:function(t,e,i,n){return De(this,t,e,i,n,1)},off:function(t,e,i){var n,o;if(t&&t.preventDefault&&t.handleObj)n=t.handleObj,T(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler);else{if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=Le),this.each(function(){T.event.remove(this,t,i,e)});for(o in t)this.off(o,e,t[o])}return this}});var je=/<script|<style|<link/i,Me=/checked\s*(?:[^=]|=\s*.checked.)/i,ze=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Re(t,e){return b(t,"table")&&b(11!==e.nodeType?e:e.firstChild,"tr")&&T(t).children("tbody")[0]||t}function Ne(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function qe(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function He(t,e){var i,n,o,r;if(1===e.nodeType){if(v.hasData(t)&&(r=v.get(t).events))for(o in v.remove(e,"handle events"),r)for(i=0,n=r[o].length;i<n;i++)T.event.add(e,o,r[o][i]);c.hasData(t)&&(t=c.access(t),t=T.extend({},t),c.set(e,t))}}function Fe(i,n,o,r){n=q(n);var t,e,s,a,l,c,u=0,h=i.length,d=h-1,p=n[0],f=y(p);if(f||1<h&&"string"==typeof p&&!m.checkClone&&Me.test(p))return i.each(function(t){var e=i.eq(t);f&&(n[0]=p.call(this,t,e.html())),Fe(e,n,o,r)});if(h&&(e=(t=Ie(n,i[0].ownerDocument,!1,i,r)).firstChild,1===t.childNodes.length&&(t=e),e||r)){for(a=(s=T.map(g(t,"script"),Ne)).length;u<h;u++)l=t,u!==d&&(l=T.clone(l,!0,!0),a)&&T.merge(s,g(l,"script")),o.call(i[u],l,u);if(a)for(c=s[s.length-1].ownerDocument,T.map(s,qe),u=0;u<a;u++)l=s[u],Ee.test(l.type||"")&&!v.access(l,"globalEval")&&T.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?T._evalUrl&&!l.noModule&&T._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):Q(l.textContent.replace(ze,""),l,c))}return i}function We(t,e,i){for(var n,o=e?T.filter(e,t):t,r=0;null!=(n=o[r]);r++)i||1!==n.nodeType||T.cleanData(g(n)),n.parentNode&&(i&&ye(n)&&Se(g(n,"script")),n.parentNode.removeChild(n));return t}T.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,o,r,s,a,l,c,u=t.cloneNode(!0),h=ye(t);if(!(m.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||T.isXMLDoc(t)))for(s=g(u),n=0,o=(r=g(t)).length;n<o;n++)a=r[n],"input"===(c=(l=s[n]).nodeName.toLowerCase())&&Ce.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(e)if(i)for(r=r||g(t),s=s||g(u),n=0,o=r.length;n<o;n++)He(r[n],s[n]);else He(t,u);return 0<(s=g(u,"script")).length&&Se(s,!h&&g(t,"script")),u},cleanData:function(t){for(var e,i,n,o=T.event.special,r=0;void 0!==(i=t[r]);r++)if(ce(i)){if(e=i[v.expando]){if(e.events)for(n in e.events)o[n]?T.event.remove(i,n):T.removeEvent(i,n,e.handle);i[v.expando]=void 0}i[c.expando]&&(i[c.expando]=void 0)}}}),T.fn.extend({detach:function(t){return We(this,t,!0)},remove:function(t){return We(this,t)},text:function(t){return u(this,function(t){return void 0===t?T.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Fe(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Re(this,t).appendChild(t)})},prepend:function(){return Fe(this,arguments,function(t){var e;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(e=Re(this,t)).insertBefore(t,e.firstChild)})},before:function(){return Fe(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Fe(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(T.cleanData(g(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return T.clone(this,t,e)})},html:function(t){return u(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!je.test(t)&&!f[(Te.exec(t)||["",""])[1].toLowerCase()]){t=T.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(T.cleanData(g(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var i=[];return Fe(this,arguments,function(t){var e=this.parentNode;T.inArray(this,i)<0&&(T.cleanData(g(this)),e)&&e.replaceChild(t,this)},i)}}),T.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,s){T.fn[t]=function(t){for(var e,i=[],n=T(t),o=n.length-1,r=0;r<=o;r++)e=r===o?this:this.clone(!0),T(n[r])[s](e),H.apply(i,e.get());return this.pushStack(i)}});function $e(t){var e=t.ownerDocument.defaultView;return(e=e&&e.opener?e:x).getComputedStyle(t)}function Be(t,e,i){var n,o={};for(n in e)o[n]=t.style[n],t.style[n]=e[n];for(n in i=i.call(t),e)t.style[n]=o[n];return i}var Ve,Ue,Qe,Ye,Xe,Ge,Ze,P,Ke=new RegExp("^("+t+")(?!px)[a-z%]+$","i"),Je=/^--/,ti=new RegExp(ge.join("|"),"i");function ei(t,e,i){var n,o=Je.test(e),r=t.style;return(i=i||$e(t))&&(n=i.getPropertyValue(e)||i[e],""!==(n=o?n&&(n.replace(tt,"$1")||void 0):n)||ye(t)||(n=T.style(t,e)),!m.pixelBoxStyles())&&Ke.test(n)&&ti.test(e)&&(o=r.width,t=r.minWidth,e=r.maxWidth,r.minWidth=r.maxWidth=r.width=n,n=i.width,r.width=o,r.minWidth=t,r.maxWidth=e),void 0!==n?n+"":n}function ii(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}function ni(){var t;P&&(Ze.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",P.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ve.appendChild(Ze).appendChild(P),t=x.getComputedStyle(P),Ve="1%"!==t.top,Ge=12===oi(t.marginLeft),P.style.right="60%",Ye=36===oi(t.right),Ue=36===oi(t.width),P.style.position="absolute",Qe=12===oi(P.offsetWidth/3),ve.removeChild(Ze),P=null)}function oi(t){return Math.round(parseFloat(t))}Ze=C.createElement("div"),(P=C.createElement("div")).style&&(P.style.backgroundClip="content-box",P.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===P.style.backgroundClip,T.extend(m,{boxSizingReliable:function(){return ni(),Ue},pixelBoxStyles:function(){return ni(),Ye},pixelPosition:function(){return ni(),Ve},reliableMarginLeft:function(){return ni(),Ge},scrollboxSize:function(){return ni(),Qe},reliableTrDimensions:function(){var t,e,i;return null==Xe&&(t=C.createElement("table"),e=C.createElement("tr"),i=C.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="box-sizing:content-box;border:1px solid",e.style.height="1px",i.style.height="9px",i.style.display="block",ve.appendChild(t).appendChild(e).appendChild(i),i=x.getComputedStyle(e),Xe=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===e.offsetHeight,ve.removeChild(t)),Xe}}));var ri=["Webkit","Moz","ms"],si=C.createElement("div").style,ai={};function li(t){return T.cssProps[t]||ai[t]||(t in si?t:ai[t]=(t=>{for(var e=t[0].toUpperCase()+t.slice(1),i=ri.length;i--;)if((t=ri[i]+e)in si)return t})(t)||t)}var ci=/^(none|table(?!-c[ea]).+)/,ui={position:"absolute",visibility:"hidden",display:"block"},hi={letterSpacing:"0",fontWeight:"400"};function di(t,e,i){var n=me.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function pi(t,e,i,n,o,r){var s="width"===e?1:0,a=0,l=0,c=0;if(i===(n?"border":"content"))return 0;for(;s<4;s+=2)"margin"===i&&(c+=T.css(t,i+ge[s],!0,o)),n?("content"===i&&(l-=T.css(t,"padding"+ge[s],!0,o)),"margin"!==i&&(l-=T.css(t,"border"+ge[s]+"Width",!0,o))):(l+=T.css(t,"padding"+ge[s],!0,o),"padding"!==i?l+=T.css(t,"border"+ge[s]+"Width",!0,o):a+=T.css(t,"border"+ge[s]+"Width",!0,o));return!n&&0<=r&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-r-l-a-.5))||0),l+c}function fi(t,e,i){var n=$e(t),o=(!m.boxSizingReliable()||i)&&"border-box"===T.css(t,"boxSizing",!1,n),r=o,s=ei(t,e,n),a="offset"+e[0].toUpperCase()+e.slice(1);if(Ke.test(s)){if(!i)return s;s="auto"}return(!m.boxSizingReliable()&&o||!m.reliableTrDimensions()&&b(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===T.css(t,"display",!1,n))&&t.getClientRects().length&&(o="border-box"===T.css(t,"boxSizing",!1,n),r=a in t)&&(s=t[a]),(s=parseFloat(s)||0)+pi(t,e,i||(o?"border":"content"),r,n,s)+"px"}function j(t,e,i,n,o){return new j.prototype.init(t,e,i,n,o)}T.extend({cssHooks:{opacity:{get:function(t,e){if(e)return""===(e=ei(t,"opacity"))?"1":e}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,r,s,a=D(e),l=Je.test(e),c=t.style;if(l||(e=li(a)),s=T.cssHooks[e]||T.cssHooks[a],void 0===i)return s&&"get"in s&&void 0!==(o=s.get(t,!1,n))?o:c[e];"string"==(r=typeof i)&&(o=me.exec(i))&&o[1]&&(i=_e(t,e,o),r="number"),null!=i&&i==i&&("number"!==r||l||(i+=o&&o[3]||(T.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==i||0!==e.indexOf("background")||(c[e]="inherit"),s&&"set"in s&&void 0===(i=s.set(t,i,n))||(l?c.setProperty(e,i):c[e]=i))}},css:function(t,e,i,n){var o,r=D(e);return Je.test(e)||(e=li(r)),"normal"===(o=void 0===(o=(r=T.cssHooks[e]||T.cssHooks[r])&&"get"in r?r.get(t,!0,i):o)?ei(t,e,n):o)&&e in hi&&(o=hi[e]),(""===i||i)&&(r=parseFloat(o),!0===i||isFinite(r))?r||0:o}}),T.each(["height","width"],function(t,s){T.cssHooks[s]={get:function(t,e,i){if(e)return!ci.test(T.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?fi(t,s,i):Be(t,ui,function(){return fi(t,s,i)})},set:function(t,e,i){var n=$e(t),o=!m.scrollboxSize()&&"absolute"===n.position,r=(o||i)&&"border-box"===T.css(t,"boxSizing",!1,n),i=i?pi(t,s,i,r,n):0;return r&&o&&(i-=Math.ceil(t["offset"+s[0].toUpperCase()+s.slice(1)]-parseFloat(n[s])-pi(t,s,"border",!1,n)-.5)),i&&(r=me.exec(e))&&"px"!==(r[3]||"px")&&(t.style[s]=e,e=T.css(t,s)),di(0,e,i)}}}),T.cssHooks.marginLeft=ii(m.reliableMarginLeft,function(t,e){if(e)return(parseFloat(ei(t,"marginLeft"))||t.getBoundingClientRect().left-Be(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),T.each({margin:"",padding:"",border:"Width"},function(o,r){T.cssHooks[o+r]={expand:function(t){for(var e=0,i={},n="string"==typeof t?t.split(" "):[t];e<4;e++)i[o+ge[e]+r]=n[e]||n[e-2]||n[0];return i}},"margin"!==o&&(T.cssHooks[o+r].set=di)}),T.fn.extend({css:function(t,e){return u(this,function(t,e,i){var n,o,r={},s=0;if(Array.isArray(e)){for(n=$e(t),o=e.length;s<o;s++)r[e[s]]=T.css(t,e[s],!1,n);return r}return void 0!==i?T.style(t,e,i):T.css(t,e)},t,e,1<arguments.length)}}),((T.Tween=j).prototype={constructor:j,init:function(t,e,i,n,o,r){this.elem=t,this.prop=i,this.easing=o||T.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=r||(T.cssNumber[i]?"":"px")},cur:function(){var t=j.propHooks[this.prop];return(t&&t.get?t:j.propHooks._default).get(this)},run:function(t){var e,i=j.propHooks[this.prop];return this.options.duration?this.pos=e=T.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(i&&i.set?i:j.propHooks._default).set(this),this}}).init.prototype=j.prototype,(j.propHooks={_default:{get:function(t){return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(t=T.css(t.elem,t.prop,""))&&"auto"!==t?t:0},set:function(t){T.fx.step[t.prop]?T.fx.step[t.prop](t):1!==t.elem.nodeType||!T.cssHooks[t.prop]&&null==t.elem.style[li(t.prop)]?t.elem[t.prop]=t.now:T.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=j.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},T.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},T.fx=j.prototype.init,T.fx.step={};var mi,gi,vi=/^(?:toggle|show|hide)$/,yi=/queueHooks$/;function bi(){gi&&(!1===C.hidden&&x.requestAnimationFrame?x.requestAnimationFrame(bi):x.setTimeout(bi,T.fx.interval),T.fx.tick())}function _i(){return x.setTimeout(function(){mi=void 0}),mi=Date.now()}function wi(t,e){var i,n=0,o={height:t};for(e=e?1:0;n<4;n+=2-e)o["margin"+(i=ge[n])]=o["padding"+i]=t;return e&&(o.opacity=o.width=t),o}function xi(t,e,i){for(var n,o=(M.tweeners[e]||[]).concat(M.tweeners["*"]),r=0,s=o.length;r<s;r++)if(n=o[r].call(i,e,t))return n}function M(o,t,e){var i,r,n,s,a,l,c,u=0,h=M.prefilters.length,d=T.Deferred().always(function(){delete p.elem}),p=function(){if(r)return!1;for(var t=mi||_i(),t=Math.max(0,f.startTime+f.duration-t),e=1-(t/f.duration||0),i=0,n=f.tweens.length;i<n;i++)f.tweens[i].run(e);return d.notifyWith(o,[f,e,t]),e<1&&n?t:(n||d.notifyWith(o,[f,1,0]),d.resolveWith(o,[f]),!1)},f=d.promise({elem:o,props:T.extend({},t),opts:T.extend(!0,{specialEasing:{},easing:T.easing._default},e),originalProperties:t,originalOptions:e,startTime:mi||_i(),duration:e.duration,tweens:[],createTween:function(t,e){e=T.Tween(o,f.opts,t,e,f.opts.specialEasing[t]||f.opts.easing);return f.tweens.push(e),e},stop:function(t){var e=0,i=t?f.tweens.length:0;if(!r){for(r=!0;e<i;e++)f.tweens[e].run(1);t?(d.notifyWith(o,[f,1,0]),d.resolveWith(o,[f,t])):d.rejectWith(o,[f,t])}return this}}),m=f.props,g=m,v=f.opts.specialEasing;for(n in g)if(a=v[s=D(n)],l=g[n],Array.isArray(l)&&(a=l[1],l=g[n]=l[0]),n!==s&&(g[s]=l,delete g[n]),(c=T.cssHooks[s])&&"expand"in c)for(n in l=c.expand(l),delete g[s],l)n in g||(g[n]=l[n],v[n]=a);else v[s]=a;for(;u<h;u++)if(i=M.prefilters[u].call(f,o,m,f.opts))return y(i.stop)&&(T._queueHooks(f.elem,f.opts.queue).stop=i.stop.bind(i)),i;return T.map(m,xi,f),y(f.opts.start)&&f.opts.start.call(o,f),f.progress(f.opts.progress).done(f.opts.done,f.opts.complete).fail(f.opts.fail).always(f.opts.always),T.fx.timer(T.extend(p,{elem:o,anim:f,queue:f.opts.queue})),f}T.Animation=T.extend(M,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return _e(i.elem,t,me.exec(e),i),i}]},tweener:function(t,e){for(var i,n=0,o=(t=y(t)?(e=t,["*"]):t.match(L)).length;n<o;n++)i=t[n],M.tweeners[i]=M.tweeners[i]||[],M.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,o,r,s,a,l,c,u="width"in e||"height"in e,h=this,d={},p=t.style,f=t.nodeType&&fe(t),m=v.get(t,"fxshow");for(n in i.queue||(null==(s=T._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,h.always(function(){h.always(function(){s.unqueued--,T.queue(t,"fx").length||s.empty.fire()})})),e)if(o=e[n],vi.test(o)){if(delete e[n],r=r||"toggle"===o,o===(f?"hide":"show")){if("show"!==o||!m||void 0===m[n])continue;f=!0}d[n]=m&&m[n]||T.style(t,n)}if((l=!T.isEmptyObject(e))||!T.isEmptyObject(d))for(n in u&&1===t.nodeType&&(i.overflow=[p.overflow,p.overflowX,p.overflowY],null==(c=m&&m.display)&&(c=v.get(t,"display")),"none"===(u=T.css(t,"display"))&&(c?u=c:(xe([t],!0),c=t.style.display||c,u=T.css(t,"display"),xe([t]))),"inline"===u||"inline-block"===u&&null!=c)&&"none"===T.css(t,"float")&&(l||(h.done(function(){p.display=c}),null==c&&(u=p.display,c="none"===u?"":u)),p.display="inline-block"),i.overflow&&(p.overflow="hidden",h.always(function(){p.overflow=i.overflow[0],p.overflowX=i.overflow[1],p.overflowY=i.overflow[2]})),l=!1,d)l||(m?"hidden"in m&&(f=m.hidden):m=v.access(t,"fxshow",{display:c}),r&&(m.hidden=!f),f&&xe([t],!0),h.done(function(){for(n in f||xe([t]),v.remove(t,"fxshow"),d)T.style(t,n,d[n])})),l=xi(f?m[n]:0,n,h),n in m||(m[n]=l.start,f&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?M.prefilters.unshift(t):M.prefilters.push(t)}}),T.speed=function(t,e,i){var n=t&&"object"==typeof t?T.extend({},t):{complete:i||!i&&e||y(t)&&t,duration:t,easing:i&&e||e&&!y(e)&&e};return T.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in T.fx.speeds?n.duration=T.fx.speeds[n.duration]:n.duration=T.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){y(n.old)&&n.old.call(this),n.queue&&T.dequeue(this,n.queue)},n},T.fn.extend({fadeTo:function(t,e,i,n){return this.filter(fe).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(e,t,i,n){function o(){var t=M(this,T.extend({},e),s);(r||v.get(this,"finish"))&&t.stop(!0)}var r=T.isEmptyObject(e),s=T.speed(t,i,n);return o.finish=o,r||!1===s.queue?this.each(o):this.queue(s.queue,o)},stop:function(o,t,r){function s(t){var e=t.stop;delete t.stop,e(r)}return"string"!=typeof o&&(r=t,t=o,o=void 0),t&&this.queue(o||"fx",[]),this.each(function(){var t=!0,e=null!=o&&o+"queueHooks",i=T.timers,n=v.get(this);if(e)n[e]&&n[e].stop&&s(n[e]);else for(e in n)n[e]&&n[e].stop&&yi.test(e)&&s(n[e]);for(e=i.length;e--;)i[e].elem!==this||null!=o&&i[e].queue!==o||(i[e].anim.stop(r),t=!1,i.splice(e,1));!t&&r||T.dequeue(this,o)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var t,e=v.get(this),i=e[s+"queue"],n=e[s+"queueHooks"],o=T.timers,r=i?i.length:0;for(e.finish=!0,T.queue(this,s,[]),n&&n.stop&&n.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===s&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<r;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete e.finish})}}),T.each(["toggle","show","hide"],function(t,n){var o=T.fn[n];T.fn[n]=function(t,e,i){return null==t||"boolean"==typeof t?o.apply(this,arguments):this.animate(wi(n,!0),t,e,i)}}),T.each({slideDown:wi("show"),slideUp:wi("hide"),slideToggle:wi("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,n){T.fn[t]=function(t,e,i){return this.animate(n,t,e,i)}}),T.timers=[],T.fx.tick=function(){var t,e=0,i=T.timers;for(mi=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||T.fx.stop(),mi=void 0},T.fx.timer=function(t){T.timers.push(t),T.fx.start()},T.fx.interval=13,T.fx.start=function(){gi||(gi=!0,bi())},T.fx.stop=function(){gi=null},T.fx.speeds={slow:600,fast:200,_default:400},T.fn.delay=function(n,t){return n=T.fx&&T.fx.speeds[n]||n,this.queue(t=t||"fx",function(t,e){var i=x.setTimeout(t,n);e.stop=function(){x.clearTimeout(i)}})},s=C.createElement("input"),r=C.createElement("select").appendChild(C.createElement("option")),s.type="checkbox",m.checkOn=""!==s.value,m.optSelected=r.selected,(s=C.createElement("input")).value="t",s.type="radio",m.radioValue="t"===s.value;var Ci,Ti=T.expr.attrHandle,Ei=(T.fn.extend({attr:function(t,e){return u(this,T.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){T.removeAttr(this,t)})}}),T.extend({attr:function(t,e,i){var n,o,r=t.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===t.getAttribute?T.prop(t,e,i):(1===r&&T.isXMLDoc(t)||(o=T.attrHooks[e.toLowerCase()]||(T.expr.match.bool.test(e)?Ci:void 0)),void 0!==i?null===i?void T.removeAttr(t,e):o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:(t.setAttribute(e,i+""),i):!(o&&"get"in o&&null!==(n=o.get(t,e)))&&null==(n=T.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){var i;if(!m.radioValue&&"radio"===e&&b(t,"input"))return i=t.value,t.setAttribute("type",e),i&&(t.value=i),e}}},removeAttr:function(t,e){var i,n=0,o=e&&e.match(L);if(o&&1===t.nodeType)for(;i=o[n++];)t.removeAttribute(i)}}),Ci={set:function(t,e,i){return!1===e?T.removeAttr(t,i):t.setAttribute(i,i),i}},T.each(T.expr.match.bool.source.match(/\w+/g),function(t,e){var s=Ti[e]||T.find.attr;Ti[e]=function(t,e,i){var n,o,r=e.toLowerCase();return i||(o=Ti[r],Ti[r]=n,n=null!=s(t,e,i)?r:null,Ti[r]=o),n}}),/^(?:input|select|textarea|button)$/i),Si=/^(?:a|area)$/i;function ki(t){return(t.match(L)||[]).join(" ")}function Ii(t){return t.getAttribute&&t.getAttribute("class")||""}function Ai(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(L)||[]}T.fn.extend({prop:function(t,e){return u(this,T.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[T.propFix[t]||t]})}}),T.extend({prop:function(t,e,i){var n,o,r=t.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&T.isXMLDoc(t)||(e=T.propFix[e]||e,o=T.propHooks[e]),void 0!==i?o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:t[e]=i:o&&"get"in o&&null!==(n=o.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=T.find.attr(t,"tabindex");return e?parseInt(e,10):Ei.test(t.nodeName)||Si.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(T.propHooks.selected={get:function(t){t=t.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(t){t=t.parentNode;t&&(t.selectedIndex,t.parentNode)&&t.parentNode.selectedIndex}}),T.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){T.propFix[this.toLowerCase()]=this}),T.fn.extend({addClass:function(e){var t,i,n,o,r,s;return y(e)?this.each(function(t){T(this).addClass(e.call(this,t,Ii(this)))}):(t=Ai(e)).length?this.each(function(){if(n=Ii(this),i=1===this.nodeType&&" "+ki(n)+" "){for(r=0;r<t.length;r++)o=t[r],i.indexOf(" "+o+" ")<0&&(i+=o+" ");s=ki(i),n!==s&&this.setAttribute("class",s)}}):this},removeClass:function(e){var t,i,n,o,r,s;return y(e)?this.each(function(t){T(this).removeClass(e.call(this,t,Ii(this)))}):arguments.length?(t=Ai(e)).length?this.each(function(){if(n=Ii(this),i=1===this.nodeType&&" "+ki(n)+" "){for(r=0;r<t.length;r++)for(o=t[r];-1<i.indexOf(" "+o+" ");)i=i.replace(" "+o+" "," ");s=ki(i),n!==s&&this.setAttribute("class",s)}}):this:this.attr("class","")},toggleClass:function(e,i){var t,n,o,r,s=typeof e,a="string"==s||Array.isArray(e);return y(e)?this.each(function(t){T(this).toggleClass(e.call(this,t,Ii(this),i),i)}):"boolean"==typeof i&&a?i?this.addClass(e):this.removeClass(e):(t=Ai(e),this.each(function(){if(a)for(r=T(this),o=0;o<t.length;o++)n=t[o],r.hasClass(n)?r.removeClass(n):r.addClass(n);else void 0!==e&&"boolean"!=s||((n=Ii(this))&&v.set(this,"__className__",n),this.setAttribute&&this.setAttribute("class",!n&&!1!==e&&v.get(this,"__className__")||""))}))},hasClass:function(t){for(var e,i=0,n=" "+t+" ";e=this[i++];)if(1===e.nodeType&&-1<(" "+ki(Ii(e))+" ").indexOf(n))return!0;return!1}});function Oi(t){t.stopPropagation()}var Li=/\r/g,Di=(T.fn.extend({val:function(e){var i,t,n,o=this[0];return arguments.length?(n=y(e),this.each(function(t){1===this.nodeType&&(null==(t=n?e.call(this,t,T(this).val()):e)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=T.map(t,function(t){return null==t?"":t+""})),(i=T.valHooks[this.type]||T.valHooks[this.nodeName.toLowerCase()])&&"set"in i&&void 0!==i.set(this,t,"value")||(this.value=t))})):o?(i=T.valHooks[o.type]||T.valHooks[o.nodeName.toLowerCase()])&&"get"in i&&void 0!==(t=i.get(o,"value"))?t:"string"==typeof(t=o.value)?t.replace(Li,""):null==t?"":t:void 0}}),T.extend({valHooks:{option:{get:function(t){var e=T.find.attr(t,"value");return null!=e?e:ki(T.text(t))}},select:{get:function(t){for(var e,i=t.options,n=t.selectedIndex,o="select-one"===t.type,r=o?null:[],s=o?n+1:i.length,a=n<0?s:o?n:0;a<s;a++)if(((e=i[a]).selected||a===n)&&!e.disabled&&(!e.parentNode.disabled||!b(e.parentNode,"optgroup"))){if(e=T(e).val(),o)return e;r.push(e)}return r},set:function(t,e){for(var i,n,o=t.options,r=T.makeArray(e),s=o.length;s--;)((n=o[s]).selected=-1<T.inArray(T.valHooks.option.get(n),r))&&(i=!0);return i||(t.selectedIndex=-1),r}}}}),T.each(["radio","checkbox"],function(){T.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<T.inArray(T(t).val(),e)}},m.checkOn||(T.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),x.location),Pi={guid:Date.now()},ji=/\?/,Mi=(T.parseXML=function(t){var e,i;if(!t||"string"!=typeof t)return null;try{e=(new x.DOMParser).parseFromString(t,"text/xml")}catch(t){}return i=e&&e.getElementsByTagName("parsererror")[0],e&&!i||T.error("Invalid XML: "+(i?T.map(i.childNodes,function(t){return t.textContent}).join("\n"):t)),e},/^(?:focusinfocus|focusoutblur)$/),zi=(T.extend(T.event,{trigger:function(t,e,i,n){var o,r,s,a,l,c,u,h=[i||C],d=$.call(t,"type")?t.type:t,p=$.call(t,"namespace")?t.namespace.split("."):[],f=u=r=i=i||C;if(3!==i.nodeType&&8!==i.nodeType&&!Mi.test(d+T.event.triggered)&&(-1<d.indexOf(".")&&(d=(p=d.split(".")).shift(),p.sort()),a=d.indexOf(":")<0&&"on"+d,(t=t[T.expando]?t:new T.Event(d,"object"==typeof t&&t)).isTrigger=n?2:3,t.namespace=p.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),e=null==e?[t]:T.makeArray(e,[t]),c=T.event.special[d]||{},n||!c.trigger||!1!==c.trigger.apply(i,e))){if(!n&&!c.noBubble&&!R(i)){for(s=c.delegateType||d,Mi.test(s+d)||(f=f.parentNode);f;f=f.parentNode)h.push(f),r=f;r===(i.ownerDocument||C)&&h.push(r.defaultView||r.parentWindow||x)}for(o=0;(f=h[o++])&&!t.isPropagationStopped();)u=f,t.type=1<o?s:c.bindType||d,(l=(v.get(f,"events")||Object.create(null))[t.type]&&v.get(f,"handle"))&&l.apply(f,e),(l=a&&f[a])&&l.apply&&ce(f)&&(t.result=l.apply(f,e),!1===t.result)&&t.preventDefault();return t.type=d,n||t.isDefaultPrevented()||c._default&&!1!==c._default.apply(h.pop(),e)||!ce(i)||a&&y(i[d])&&!R(i)&&((r=i[a])&&(i[a]=null),T.event.triggered=d,t.isPropagationStopped()&&u.addEventListener(d,Oi),i[d](),t.isPropagationStopped()&&u.removeEventListener(d,Oi),T.event.triggered=void 0,r)&&(i[a]=r),t.result}},simulate:function(t,e,i){i=T.extend(new T.Event,i,{type:t,isSimulated:!0});T.event.trigger(i,null,e)}}),T.fn.extend({trigger:function(t,e){return this.each(function(){T.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return T.event.trigger(t,e,i,!0)}}),/\[\]$/),Ri=/\r?\n/g,Ni=/^(?:submit|button|image|reset|file)$/i,qi=/^(?:input|select|textarea|keygen)/i;T.param=function(t,e){function i(t,e){e=y(e)?e():e,o[o.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==e?"":e)}var n,o=[];if(null==t)return"";if(Array.isArray(t)||t.jquery&&!T.isPlainObject(t))T.each(t,function(){i(this.name,this.value)});else for(n in t)!function i(n,t,o,r){if(Array.isArray(t))T.each(t,function(t,e){o||zi.test(n)?r(n,e):i(n+"["+("object"==typeof e&&null!=e?t:"")+"]",e,o,r)});else if(o||"object"!==Y(t))r(n,t);else for(var e in t)i(n+"["+e+"]",t[e],o,r)}(n,t[n],e,i);return o.join("&")},T.fn.extend({serialize:function(){return T.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=T.prop(this,"elements");return t?T.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!T(this).is(":disabled")&&qi.test(this.nodeName)&&!Ni.test(t)&&(this.checked||!Ce.test(t))}).map(function(t,e){var i=T(this).val();return null==i?null:Array.isArray(i)?T.map(i,function(t){return{name:e.name,value:t.replace(Ri,"\r\n")}}):{name:e.name,value:i.replace(Ri,"\r\n")}}).get()}});var Hi=/%20/g,Fi=/#.*$/,Wi=/([?&])_=[^&]*/,$i=/^(.*?):[ \t]*([^\r\n]*)$/gm,Bi=/^(?:GET|HEAD)$/,Vi=/^\/\//,Ui={},Qi={},Yi="*/".concat("*"),Xi=C.createElement("a");function Gi(r){return function(t,e){"string"!=typeof t&&(e=t,t="*");var i,n=0,o=t.toLowerCase().match(L)||[];if(y(e))for(;i=o[n++];)"+"===i[0]?(i=i.slice(1)||"*",(r[i]=r[i]||[]).unshift(e)):(r[i]=r[i]||[]).push(e)}}function Zi(e,n,o,r){var s={},a=e===Qi;function l(t){var i;return s[t]=!0,T.each(e[t]||[],function(t,e){e=e(n,o,r);return"string"!=typeof e||a||s[e]?a?!(i=e):void 0:(n.dataTypes.unshift(e),l(e),!1)}),i}return l(n.dataTypes[0])||!s["*"]&&l("*")}function Ki(t,e){var i,n,o=T.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((o[i]?t:n=n||{})[i]=e[i]);return n&&T.extend(!0,t,n),t}Xi.href=Di.href,T.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Di.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Di.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Yi,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":T.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Ki(Ki(t,T.ajaxSettings),e):Ki(T.ajaxSettings,t)},ajaxPrefilter:Gi(Ui),ajaxTransport:Gi(Qi),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0);var l,c,u,i,h,d,p,n,o,f=T.ajaxSetup({},e=e||{}),m=f.context||f,g=f.context&&(m.nodeType||m.jquery)?T(m):T.event,v=T.Deferred(),y=T.Callbacks("once memory"),b=f.statusCode||{},r={},s={},a="canceled",_={readyState:0,getResponseHeader:function(t){var e;if(d){if(!i)for(i={};e=$i.exec(u);)i[e[1].toLowerCase()+" "]=(i[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=i[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return d?u:null},setRequestHeader:function(t,e){return null==d&&(t=s[t.toLowerCase()]=s[t.toLowerCase()]||t,r[t]=e),this},overrideMimeType:function(t){return null==d&&(f.mimeType=t),this},statusCode:function(t){if(t)if(d)_.always(t[_.status]);else for(var e in t)b[e]=[b[e],t[e]];return this},abort:function(t){t=t||a;return l&&l.abort(t),w(0,t),this}};if(v.promise(_),f.url=((t||f.url||Di.href)+"").replace(Vi,Di.protocol+"//"),f.type=e.method||e.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(L)||[""],null==f.crossDomain){o=C.createElement("a");try{o.href=f.url,o.href=o.href,f.crossDomain=Xi.protocol+"//"+Xi.host!=o.protocol+"//"+o.host}catch(t){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=T.param(f.data,f.traditional)),Zi(Ui,f,e,_),!d){for(n in(p=T.event&&f.global)&&0==T.active++&&T.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Bi.test(f.type),c=f.url.replace(Fi,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(Hi,"+")):(o=f.url.slice(c.length),f.data&&(f.processData||"string"==typeof f.data)&&(c+=(ji.test(c)?"&":"?")+f.data,delete f.data),!1===f.cache&&(c=c.replace(Wi,"$1"),o=(ji.test(c)?"&":"?")+"_="+Pi.guid+++o),f.url=c+o),f.ifModified&&(T.lastModified[c]&&_.setRequestHeader("If-Modified-Since",T.lastModified[c]),T.etag[c])&&_.setRequestHeader("If-None-Match",T.etag[c]),(f.data&&f.hasContent&&!1!==f.contentType||e.contentType)&&_.setRequestHeader("Content-Type",f.contentType),_.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Yi+"; q=0.01":""):f.accepts["*"]),f.headers)_.setRequestHeader(n,f.headers[n]);if(f.beforeSend&&(!1===f.beforeSend.call(m,_,f)||d))return _.abort();if(a="abort",y.add(f.complete),_.done(f.success),_.fail(f.error),l=Zi(Qi,f,e,_)){if(_.readyState=1,p&&g.trigger("ajaxSend",[_,f]),d)return _;f.async&&0<f.timeout&&(h=x.setTimeout(function(){_.abort("timeout")},f.timeout));try{d=!1,l.send(r,w)}catch(t){if(d)throw t;w(-1,t)}}else w(-1,"No Transport")}return _;function w(t,e,i,n){var o,r,s,a=e;d||(d=!0,h&&x.clearTimeout(h),l=void 0,u=n||"",_.readyState=0<t?4:0,n=200<=t&&t<300||304===t,i&&(s=((t,e,i)=>{for(var n,o,r,s,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(o in a)if(a[o]&&a[o].test(n)){l.unshift(o);break}if(l[0]in i)r=l[0];else{for(o in i){if(!l[0]||t.converters[o+" "+l[0]]){r=o;break}s=s||o}r=r||s}if(r)return r!==l[0]&&l.unshift(r),i[r]})(f,_,i)),!n&&-1<T.inArray("script",f.dataTypes)&&T.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),s=((t,e,i,n)=>{var o,r,s,a,l,c={},u=t.dataTypes.slice();if(u[1])for(s in t.converters)c[s.toLowerCase()]=t.converters[s];for(r=u.shift();r;)if(t.responseFields[r]&&(i[t.responseFields[r]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=r,r=u.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(s=c[l+" "+r]||c["* "+r]))for(o in c)if((a=o.split(" "))[1]===r&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[o]:!0!==c[o]&&(r=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+l+" to "+r}}}return{state:"success",data:e}})(f,s,_,n),n?(f.ifModified&&((i=_.getResponseHeader("Last-Modified"))&&(T.lastModified[c]=i),i=_.getResponseHeader("etag"))&&(T.etag[c]=i),204===t||"HEAD"===f.type?a="nocontent":304===t?a="notmodified":(a=s.state,o=s.data,n=!(r=s.error))):(r=a,!t&&a||(a="error",t<0&&(t=0))),_.status=t,_.statusText=(e||a)+"",n?v.resolveWith(m,[o,a,_]):v.rejectWith(m,[_,a,r]),_.statusCode(b),b=void 0,p&&g.trigger(n?"ajaxSuccess":"ajaxError",[_,f,n?o:r]),y.fireWith(m,[_,a]),p&&(g.trigger("ajaxComplete",[_,f]),--T.active||T.event.trigger("ajaxStop")))}},getJSON:function(t,e,i){return T.get(t,e,i,"json")},getScript:function(t,e){return T.get(t,void 0,e,"script")}}),T.each(["get","post"],function(t,o){T[o]=function(t,e,i,n){return y(e)&&(n=n||i,i=e,e=void 0),T.ajax(T.extend({url:t,type:o,dataType:n,data:e,success:i},T.isPlainObject(t)&&t))}}),T.ajaxPrefilter(function(t){for(var e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),T._evalUrl=function(t,e,i){return T.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){T.globalEval(t,e,i)}})},T.fn.extend({wrapAll:function(t){return this[0]&&(y(t)&&(t=t.call(this[0])),t=T(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(i){return y(i)?this.each(function(t){T(this).wrapInner(i.call(this,t))}):this.each(function(){var t=T(this),e=t.contents();e.length?e.wrapAll(i):t.append(i)})},wrap:function(e){var i=y(e);return this.each(function(t){T(this).wrapAll(i?e.call(this,t):e)})},unwrap:function(t){return this.parent(t).not("body").each(function(){T(this).replaceWith(this.childNodes)}),this}}),T.expr.pseudos.hidden=function(t){return!T.expr.pseudos.visible(t)},T.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},T.ajaxSettings.xhr=function(){try{return new x.XMLHttpRequest}catch(t){}};var Ji={0:200,1223:204},tn=T.ajaxSettings.xhr();m.cors=!!tn&&"withCredentials"in tn,m.ajax=tn=!!tn,T.ajaxTransport(function(o){var r,s;if(m.cors||tn&&!o.crossDomain)return{send:function(t,e){var i,n=o.xhr();if(n.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(i in o.xhrFields)n[i]=o.xhrFields[i];for(i in o.mimeType&&n.overrideMimeType&&n.overrideMimeType(o.mimeType),o.crossDomain||t["X-Requested-With"]||(t["X-Requested-With"]="XMLHttpRequest"),t)n.setRequestHeader(i,t[i]);r=function(t){return function(){r&&(r=s=n.onload=n.onerror=n.onabort=n.ontimeout=n.onreadystatechange=null,"abort"===t?n.abort():"error"===t?"number"!=typeof n.status?e(0,"error"):e(n.status,n.statusText):e(Ji[n.status]||n.status,n.statusText,"text"!==(n.responseType||"text")||"string"!=typeof n.responseText?{binary:n.response}:{text:n.responseText},n.getAllResponseHeaders()))}},n.onload=r(),s=n.onerror=n.ontimeout=r("error"),void 0!==n.onabort?n.onabort=s:n.onreadystatechange=function(){4===n.readyState&&x.setTimeout(function(){r&&s()})},r=r("abort");try{n.send(o.hasContent&&o.data||null)}catch(t){if(r)throw t}},abort:function(){r&&r()}}}),T.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),T.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return T.globalEval(t),t}}}),T.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),T.ajaxTransport("script",function(i){var n,o;if(i.crossDomain||i.scriptAttrs)return{send:function(t,e){n=T("<script>").attr(i.scriptAttrs||{}).prop({charset:i.scriptCharset,src:i.url}).on("load error",o=function(t){n.remove(),o=null,t&&e("error"===t.type?404:200,t.type)}),C.head.appendChild(n[0])},abort:function(){o&&o()}}});var en=[],nn=/(=)\?(?=&|$)|\?\?/,on=(T.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=en.pop()||T.expando+"_"+Pi.guid++;return this[t]=!0,t}}),T.ajaxPrefilter("json jsonp",function(t,e,i){var n,o,r,s=!1!==t.jsonp&&(nn.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return n=t.jsonpCallback=y(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(nn,"$1"+n):!1!==t.jsonp&&(t.url+=(ji.test(t.url)?"&":"?")+t.jsonp+"="+n),t.converters["script json"]=function(){return r||T.error(n+" was not called"),r[0]},t.dataTypes[0]="json",o=x[n],x[n]=function(){r=arguments},i.always(function(){void 0===o?T(x).removeProp(n):x[n]=o,t[n]&&(t.jsonpCallback=e.jsonpCallback,en.push(n)),r&&y(o)&&o(r[0]),r=o=void 0}),"script"}),m.createHTMLDocument=((t=C.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===t.childNodes.length),T.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(m.createHTMLDocument?((n=(e=C.implementation.createHTMLDocument("")).createElement("base")).href=C.location.href,e.head.appendChild(n)):e=C),n=!i&&[],(i=Qt.exec(t))?[e.createElement(i[1])]:(i=Ie([t],e,n),n&&n.length&&T(n).remove(),T.merge([],i.childNodes)));var n},T.fn.load=function(t,e,i){var n,o,r,s=this,a=t.indexOf(" ");return-1<a&&(n=ki(t.slice(a)),t=t.slice(0,a)),y(e)?(i=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<s.length&&T.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done(function(t){r=arguments,s.html(n?T("<div>").append(T.parseHTML(t)).find(n):t)}).always(i&&function(t,e){s.each(function(){i.apply(this,r||[t.responseText,e,t])})}),this},T.expr.pseudos.animated=function(e){return T.grep(T.timers,function(t){return e===t.elem}).length},T.offset={setOffset:function(t,e,i){var n,o,r,s,a=T.css(t,"position"),l=T(t),c={};"static"===a&&(t.style.position="relative"),r=l.offset(),n=T.css(t,"top"),s=T.css(t,"left"),a=("absolute"===a||"fixed"===a)&&-1<(n+s).indexOf("auto")?(o=(a=l.position()).top,a.left):(o=parseFloat(n)||0,parseFloat(s)||0),null!=(e=y(e)?e.call(t,i,T.extend({},r)):e).top&&(c.top=e.top-r.top+o),null!=e.left&&(c.left=e.left-r.left+a),"using"in e?e.using.call(t,c):l.css(c)}},T.fn.extend({offset:function(e){var t,i;return arguments.length?void 0===e?this:this.each(function(t){T.offset.setOffset(this,e,t)}):(i=this[0])?i.getClientRects().length?(t=i.getBoundingClientRect(),i=i.ownerDocument.defaultView,{top:t.top+i.pageYOffset,left:t.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],o={top:0,left:0};if("fixed"===T.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===T.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((o=T(t).offset()).top+=T.css(t,"borderTopWidth",!0),o.left+=T.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-T.css(n,"marginTop",!0),left:e.left-o.left-T.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===T.css(t,"position");)t=t.offsetParent;return t||ve})}}),T.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,o){var r="pageYOffset"===o;T.fn[e]=function(t){return u(this,function(t,e,i){var n;if(R(t)?n=t:9===t.nodeType&&(n=t.defaultView),void 0===i)return n?n[o]:t[e];n?n.scrollTo(r?n.pageXOffset:i,r?i:n.pageYOffset):t[e]=i},e,t,arguments.length)}}),T.each(["top","left"],function(t,i){T.cssHooks[i]=ii(m.pixelPosition,function(t,e){if(e)return e=ei(t,i),Ke.test(e)?T(t).position()[i]+"px":e})}),T.each({Height:"height",Width:"width"},function(s,a){T.each({padding:"inner"+s,content:a,"":"outer"+s},function(n,r){T.fn[r]=function(t,e){var i=arguments.length&&(n||"boolean"!=typeof t),o=n||(!0===t||!0===e?"margin":"border");return u(this,function(t,e,i){var n;return R(t)?0===r.indexOf("outer")?t["inner"+s]:t.document.documentElement["client"+s]:9===t.nodeType?(n=t.documentElement,Math.max(t.body["scroll"+s],n["scroll"+s],t.body["offset"+s],n["offset"+s],n["client"+s])):void 0===i?T.css(t,e,o):T.style(t,e,i,o)},a,i?t:void 0,i)}})}),T.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){T.fn[e]=function(t){return this.on(e,t)}}),T.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),T.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,i){T.fn[i]=function(t,e){return 0<arguments.length?this.on(i,null,t,e):this.trigger(i)}}),/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g),rn=(T.proxy=function(t,e){var i,n;if("string"==typeof e&&(n=t[e],e=t,t=n),y(t))return i=a.call(arguments,2),(n=function(){return t.apply(e||this,i.concat(a.call(arguments)))}).guid=t.guid=t.guid||T.guid++,n},T.holdReady=function(t){t?T.readyWait++:T.ready(!0)},T.isArray=Array.isArray,T.parseJSON=JSON.parse,T.nodeName=b,T.isFunction=y,T.isWindow=R,T.camelCase=D,T.type=Y,T.now=Date.now,T.isNumeric=function(t){var e=T.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},T.trim=function(t){return null==t?"":(t+"").replace(on,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return T}),x.jQuery),sn=x.$;return T.noConflict=function(t){return x.$===T&&(x.$=sn),t&&x.jQuery===T&&(x.jQuery=rn),T},void 0===z&&(x.jQuery=x.$=T),T}),((t,e)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap=e()})(this,function(){let n=new Map,z={set(t,e,i){n.has(t)||n.set(t,new Map);t=n.get(t);t.has(e)||0===t.size?t.set(e,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(t.keys())[0]}.`)},get:(t,e)=>n.has(t)&&n.get(t).get(e)||null,remove(t,e){var i;n.has(t)&&((i=n.get(t)).delete(e),0===i.size)&&n.delete(t)}},R="transitionend",N=t=>t=t&&window.CSS&&window.CSS.escape?t.replace(/#([^\s"#']+)/g,(t,e)=>"#"+CSS.escape(e)):t,q=t=>{t.dispatchEvent(new Event(R))},s=t=>!(!t||"object"!=typeof t)&&void 0!==(t=void 0!==t.jquery?t[0]:t).nodeType,o=t=>s(t)?t.jquery?t[0]:t:"string"==typeof t&&0<t.length?document.querySelector(N(t)):null,r=e=>{if(!s(e)||0===e.getClientRects().length)return!1;let t="visible"===getComputedStyle(e).getPropertyValue("visibility"),i=e.closest("details:not([open])");if(i&&i!==e){let t=e.closest("summary");if(t&&t.parentNode!==i)return!1;if(null===t)return!1}return t},a=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),H=t=>{var e;return document.documentElement.attachShadow?"function"==typeof t.getRootNode?(e=t.getRootNode())instanceof ShadowRoot?e:null:t instanceof ShadowRoot?t:t.parentNode?H(t.parentNode):null:null},F=()=>{},W=t=>{t.offsetHeight},$=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,B=[],l=()=>"rtl"===document.documentElement.dir,t=n=>{var t=()=>{let i=$();if(i){let t=n.NAME,e=i.fn[t];i.fn[t]=n.jQueryInterface,i.fn[t].Constructor=n,i.fn[t].noConflict=()=>(i.fn[t]=e,n.jQueryInterface)}};"loading"===document.readyState?(B.length||document.addEventListener("DOMContentLoaded",()=>{for(var t of B)t()}),B.push(t)):t()},c=(t,e=[],i=t)=>"function"==typeof t?t(...e):i,V=(n,o,t=!0)=>{if(t){t=(()=>{if(!o)return 0;let{transitionDuration:t,transitionDelay:e}=window.getComputedStyle(o);var i=Number.parseFloat(t),n=Number.parseFloat(e);return i||n?(t=t.split(",")[0],e=e.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(e))):0})()+5;let e=!1,i=({target:t})=>{t===o&&(e=!0,o.removeEventListener(R,i),c(n))};o.addEventListener(R,i),setTimeout(()=>{e||q(o)},t)}else c(n)},U=(t,e,i,n)=>{var o=t.length;let r=t.indexOf(e);return-1===r?!i&&n?t[o-1]:t[0]:(r+=i?1:-1,n&&(r=(r+o)%o),t[Math.max(0,Math.min(r,o-1))])},Q=/[^.]*(?=\..*)\.|.*/,Y=/\..*/,X=/::\d+$/,G={},Z=1,K={mouseenter:"mouseover",mouseleave:"mouseout"},J=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function tt(t,e){return e&&e+"::"+Z++||t.uidEvent||Z++}function et(t){var e=tt(t);return t.uidEvent=e,G[e]=G[e]||{},G[e]}function it(t,e,i=null){return Object.values(t).find(t=>t.callable===e&&t.delegationSelector===i)}function nt(t,e,i){var n="string"==typeof e,e=!n&&e||i;let o=st(t);return[n,e,o=J.has(o)?o:t]}function ot(n,o,r,s,a){if("string"==typeof o&&n){let[t,e,i]=nt(o,r,s);if(o in K){let t=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};e=t(e)}var s=et(n),s=s[i]||(s[i]={}),l=it(s,e,t?r:null);if(l)return l.oneOff=l.oneOff&&a;var c,u,h,d,p,l=tt(e,o.replace(Q,"")),o=t?(h=n,d=r,p=e,function e(i){var n=h.querySelectorAll(d);for(let t=i.target;t&&t!==this;t=t.parentNode)for(var o of n)if(o===t)return at(i,{delegateTarget:t}),e.oneOff&&f.off(h,i.type,d,p),p.apply(t,[i])}):(c=n,u=e,function t(e){return at(e,{delegateTarget:c}),t.oneOff&&f.off(c,e.type,u),u.apply(c,[e])});o.delegationSelector=t?r:null,o.callable=e,o.oneOff=a,s[o.uidEvent=l]=o,n.addEventListener(i,o,t)}}function rt(t,e,i,n,o){n=it(e[i],n,o);n&&(t.removeEventListener(i,n,Boolean(o)),delete e[i][n.uidEvent])}function st(t){return t=t.replace(Y,""),K[t]||t}let f={on(t,e,i,n){ot(t,e,i,n,!1)},one(t,e,i,n){ot(t,e,i,n,!0)},off(a,l,c,u){if("string"==typeof l&&a){let[t,e,n]=nt(l,c,u),o=n!==l,r=et(a),s=r[n]||{},i=l.startsWith(".");if(void 0===e){if(i)for(let t of Object.keys(r)){h=void 0;d=void 0;p=void 0;f=void 0;v=void 0;m=void 0;g=void 0;var h=a;var d=r;var p=t;var f=l.slice(1);var m,g,v=d[p]||{};for([m,g]of Object.entries(v))m.includes(f)&&rt(h,d,p,g.callable,g.delegationSelector)}for(let[e,i]of Object.entries(s)){let t=e.replace(X,"");o&&!l.includes(t)||rt(a,r,n,i.callable,i.delegationSelector)}}else Object.keys(s).length&&rt(a,r,n,e,t?c:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;var n=$();let o=null,r=!0,s=!0,a=!1;e!==st(e)&&n&&(o=n.Event(e,i),n(t).trigger(o),r=!o.isPropagationStopped(),s=!o.isImmediatePropagationStopped(),a=o.isDefaultPrevented());n=at(new Event(e,{bubbles:r,cancelable:!0}),i);return a&&n.preventDefault(),s&&t.dispatchEvent(n),n.defaultPrevented&&o&&o.preventDefault(),n}};function at(i,n={}){for(let[t,e]of Object.entries(n))try{i[t]=e}catch(n){Object.defineProperty(i,t,{configurable:!0,get:()=>e})}return i}function lt(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function ct(t){return t.replace(/[A-Z]/g,t=>"-"+t.toLowerCase())}let u={setDataAttribute(t,e,i){t.setAttribute("data-bs-"+ct(e),i)},removeDataAttribute(t,e){t.removeAttribute("data-bs-"+ct(e))},getDataAttributes(e){if(!e)return{};var i,n={};for(i of Object.keys(e.dataset).filter(t=>t.startsWith("bs")&&!t.startsWith("bsConfig"))){let t=i.replace(/^bs/,"");n[t=t.charAt(0).toLowerCase()+t.slice(1,t.length)]=lt(e.dataset[i])}return n},getDataAttribute:(t,e)=>lt(t.getAttribute("data-bs-"+ct(e)))};class ut{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){var i=s(e)?u.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...s(e)?u.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(i,t=this.constructor.DefaultType){for(var[n,o]of Object.entries(t)){let t=i[n],e=s(t)?"element":null==(r=t)?""+r:Object.prototype.toString.call(r).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(o).test(e))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${e}" but expected type "${o}".`)}var r}}class e extends ut{constructor(t,e){super(),(t=o(t))&&(this._element=t,this._config=this._getConfig(e),z.set(this._element,this.constructor.DATA_KEY,this))}dispose(){z.remove(this._element,this.constructor.DATA_KEY),f.off(this._element,this.constructor.EVENT_KEY);for(var t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){V(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return z.get(o(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return"bs."+this.NAME}static get EVENT_KEY(){return"."+this.DATA_KEY}static eventName(t){return""+t+this.EVENT_KEY}}let ht=e=>{let i=e.getAttribute("data-bs-target");if(!i||"#"===i){let t=e.getAttribute("href");if(!t||!t.includes("#")&&!t.startsWith("."))return null;t.includes("#")&&!t.startsWith("#")&&(t="#"+t.split("#")[1]),i=t&&"#"!==t?t.trim():null}return i?i.split(",").map(t=>N(t)).join(","):null},h={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter(t=>t.matches(e)),parents(t,e){var i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){var e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(t=>t+':not([tabindex^="-"])').join(",");return this.find(e,t).filter(t=>!a(t)&&r(t))},getSelectorFromElement(t){t=ht(t);return t&&h.findOne(t)?t:null},getElementFromSelector(t){t=ht(t);return t?h.findOne(t):null},getMultipleElementsFromSelector(t){t=ht(t);return t?h.find(t):[]}},dt=(e,i="hide")=>{let t="click.dismiss"+e.EVENT_KEY,n=e.NAME;f.on(document,t,`[data-bs-dismiss="${n}"]`,function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),a(this)||(t=h.getElementFromSelector(this)||this.closest("."+n),e.getOrCreateInstance(t)[i]())})};class pt extends e{static get NAME(){return"alert"}close(){var t;f.trigger(this._element,"close.bs.alert").defaultPrevented||(this._element.classList.remove("show"),t=this._element.classList.contains("fade"),this._queueCallback(()=>this._destroyElement(),this._element,t))}_destroyElement(){this._element.remove(),f.trigger(this._element,"closed.bs.alert"),this.dispose()}static jQueryInterface(e){return this.each(function(){var t=pt.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}dt(pt,"close"),t(pt);let ft='[data-bs-toggle="button"]';class mt extends e{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){var t=mt.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}f.on(document,"click.bs.button.data-api",ft,t=>{t.preventDefault();t=t.target.closest(ft);mt.getOrCreateInstance(t).toggle()}),t(mt);let i=".bs.swipe",gt=(i,i,i,i,i,{endCallback:null,leftCallback:null,rightCallback:null}),vt={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class yt extends ut{constructor(t,e){super(),(this._element=t)&&yt.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return gt}static get DefaultType(){return vt}static get NAME(){return"swipe"}dispose(){f.off(this._element,i)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),c(this._config.endCallback)}_move(t){this._deltaX=t.touches&&1<t.touches.length?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){var t=Math.abs(this._deltaX);t<=40||(t=t/this._deltaX,this._deltaX=0,t&&c(0<t?this._config.rightCallback:this._config.leftCallback))}_initEvents(){this._supportPointerEvents?(f.on(this._element,"pointerdown.bs.swipe",t=>this._start(t)),f.on(this._element,"pointerup.bs.swipe",t=>this._end(t)),this._element.classList.add("pointer-event")):(f.on(this._element,"touchstart.bs.swipe",t=>this._start(t)),f.on(this._element,"touchmove.bs.swipe",t=>this._move(t)),f.on(this._element,"touchend.bs.swipe",t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||0<navigator.maxTouchPoints}}let bt=".bs.carousel",_t="next",d="prev",p="left",wt="right",xt="slid"+bt,Ct="carousel",Tt="active",Et=".active",St=".carousel-item",kt=(Et,St,{ArrowLeft:wt,ArrowRight:p}),It={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},At={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Ot extends e{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=h.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===Ct&&this.cycle()}static get Default(){return It}static get DefaultType(){return At}static get NAME(){return"carousel"}next(){this._slide(_t)}nextWhenVisible(){!document.hidden&&r(this._element)&&this.next()}prev(){this._slide(d)}pause(){this._isSliding&&q(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?f.one(this._element,xt,()=>this.cycle()):this.cycle())}to(t){var e,i=this._getItems();t>i.length-1||t<0||(this._isSliding?f.one(this._element,xt,()=>this.to(t)):(e=this._getItemIndex(this._getActive()))!==t&&(e=e<t?_t:d,this._slide(e,i[t])))}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&f.on(this._element,"keydown.bs.carousel",t=>this._keydown(t)),"hover"===this._config.pause&&(f.on(this._element,"mouseenter.bs.carousel",()=>this.pause()),f.on(this._element,"mouseleave.bs.carousel",()=>this._maybeEnableCycle())),this._config.touch&&yt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(let t of h.find(".carousel-item img",this._element))f.on(t,"dragstart.bs.carousel",t=>t.preventDefault());let t={leftCallback:()=>this._slide(this._directionToOrder(p)),rightCallback:()=>this._slide(this._directionToOrder(wt)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new yt(this._element,t)}_keydown(t){var e;/input|textarea/i.test(t.target.tagName)||(e=kt[t.key])&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){var e;this._indicatorsElement&&((e=h.findOne(Et,this._indicatorsElement)).classList.remove(Tt),e.removeAttribute("aria-current"),e=h.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement))&&(e.classList.add(Tt),e.setAttribute("aria-current","true"))}_updateInterval(){var t=this._activeElement||this._getActive();t&&(t=Number.parseInt(t.getAttribute("data-bs-interval"),10),this._config.interval=t||this._config.defaultInterval)}_slide(e,a=null){if(!this._isSliding){let o=this._getActive(),r=e===_t,s=a||U(this._getItems(),o,r,this._config.wrap);if(s!==o){let i=this._getItemIndex(s),n=t=>f.trigger(this._element,t,{relatedTarget:s,direction:this._orderToDirection(e),from:this._getItemIndex(o),to:i});if(!n("slide.bs.carousel").defaultPrevented&&o&&s){a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(i),this._activeElement=s;let t=r?"carousel-item-start":"carousel-item-end",e=r?"carousel-item-next":"carousel-item-prev";s.classList.add(e),W(s),o.classList.add(t),s.classList.add(t),this._queueCallback(()=>{s.classList.remove(t,e),s.classList.add(Tt),o.classList.remove(Tt,e,t),this._isSliding=!1,n(xt)},o,this._isAnimated()),a&&this.cycle()}}}}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return h.findOne(".active.carousel-item",this._element)}_getItems(){return h.find(St,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return l()?t===p?d:_t:t===p?_t:d}_orderToDirection(t){return l()?t===d?p:wt:t===d?wt:p}static jQueryInterface(e){return this.each(function(){var t=Ot.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)})}}f.on(document,"click.bs.carousel.data-api","[data-bs-slide], [data-bs-slide-to]",function(t){var e=h.getElementFromSelector(this);e&&e.classList.contains(Ct)&&(t.preventDefault(),t=Ot.getOrCreateInstance(e),(e=this.getAttribute("data-bs-slide-to"))?t.to(e):"next"===u.getDataAttribute(this,"slide")?t.next():t.prev(),t._maybeEnableCycle())}),f.on(window,"load.bs.carousel.data-api",()=>{var t;for(t of h.find('[data-bs-ride="carousel"]'))Ot.getOrCreateInstance(t)}),t(Ot);let Lt="show",m="collapse",Dt="collapsing",Pt=(m,m,'[data-bs-toggle="collapse"]'),jt={parent:null,toggle:!0},Mt={parent:"(null|element)",toggle:"boolean"};class zt extends e{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];let n=h.find(Pt);for(let i of n){let t=h.getSelectorFromElement(i),e=h.find(t).filter(t=>t===this._element);null!==t&&e.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return jt}static get DefaultType(){return Mt}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(!this._isTransitioning&&!this._isShown()){let e=[];if(!((e=this._config.parent?this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(t=>t!==this._element).map(t=>zt.getOrCreateInstance(t,{toggle:!1})):e).length&&e[0]._isTransitioning||f.trigger(this._element,"show.bs.collapse").defaultPrevented)){for(let t of e)t.hide();let t=this._getDimension();this._element.classList.remove(m),this._element.classList.add(Dt),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;var i="scroll"+(t[0].toUpperCase()+t.slice(1));this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Dt),this._element.classList.add(m,Lt),this._element.style[t]="",f.trigger(this._element,"shown.bs.collapse")},this._element,!0),this._element.style[t]=this._element[i]+"px"}}}hide(){if(!this._isTransitioning&&this._isShown()&&!f.trigger(this._element,"hide.bs.collapse").defaultPrevented){let t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",W(this._element),this._element.classList.add(Dt),this._element.classList.remove(m,Lt);for(let t of this._triggerArray){var e=h.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Dt),this._element.classList.add(m),f.trigger(this._element,"hidden.bs.collapse")},this._element,!0)}}_isShown(t=this._element){return t.classList.contains(Lt)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=o(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(this._config.parent){let t=this._getFirstLevelChildren(Pt);for(var e of t){let t=h.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}}_getFirstLevelChildren(t){let e=h.find(":scope .collapse .collapse",this._config.parent);return h.find(t,this._config.parent).filter(t=>!e.includes(t))}_addAriaAndCollapsedClass(t,e){if(t.length)for(var i of t)i.classList.toggle("collapsed",!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(e){let i={};return"string"==typeof e&&/show|hide/.test(e)&&(i.toggle=!1),this.each(function(){var t=zt.getOrCreateInstance(this,i);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}f.on(document,"click.bs.collapse.data-api",Pt,function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(let t of h.getMultipleElementsFromSelector(this))zt.getOrCreateInstance(t,{toggle:!1}).toggle()}),t(zt);var k="top",I="bottom",A="right",O="left",Rt="auto",L=[k,I,A,O],D="start",b="end",Nt="clippingParents",qt="viewport",Ht="popper",Ft="reference",Wt=L.reduce(function(t,e){return t.concat([e+"-"+D,e+"-"+b])},[]),$t=[].concat(L,[Rt]).reduce(function(t,e){return t.concat([e,e+"-"+D,e+"-"+b])},[]),Bt="beforeRead",Vt="afterRead",Ut="beforeMain",Qt="afterMain",Yt="beforeWrite",Xt="afterWrite",Gt=[Bt,"read",Vt,Ut,"main",Qt,Yt,"write",Xt];function g(t){return t?(t.nodeName||"").toLowerCase():null}function _(t){var e;return null==t?window:"[object Window]"!==t.toString()?(e=t.ownerDocument)&&e.defaultView||window:t}function v(t){return t instanceof _(t).Element||t instanceof Element}function y(t){return t instanceof _(t).HTMLElement||t instanceof HTMLElement}function Zt(t){return"undefined"!=typeof ShadowRoot&&(t instanceof _(t).ShadowRoot||t instanceof ShadowRoot)}var Kt={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var o=t.state;Object.keys(o.elements).forEach(function(t){var e=o.styles[t]||{},i=o.attributes[t]||{},n=o.elements[t];y(n)&&g(n)&&(Object.assign(n.style,e),Object.keys(i).forEach(function(t){var e=i[t];!1===e?n.removeAttribute(t):n.setAttribute(t,!0===e?"":e)}))})},effect:function(t){var n=t.state,o={popper:{position:n.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(n.elements.popper.style,o.popper),n.styles=o,n.elements.arrow&&Object.assign(n.elements.arrow.style,o.arrow),function(){Object.keys(n.elements).forEach(function(t){var e=n.elements[t],i=n.attributes[t]||{},t=Object.keys((n.styles.hasOwnProperty(t)?n.styles:o)[t]).reduce(function(t,e){return t[e]="",t},{});y(e)&&g(e)&&(Object.assign(e.style,t),Object.keys(i).forEach(function(t){e.removeAttribute(t)}))})}},requires:["computeStyles"]};function P(t){return t.split("-")[0]}var S=Math.max,Jt=Math.min,te=Math.round;function ee(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function ie(){return!/^((?!chrome|android).)*safari/i.test(ee())}function ne(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),o=1,r=1,e=(e&&y(t)&&(o=0<t.offsetWidth&&te(n.width)/t.offsetWidth||1,r=0<t.offsetHeight&&te(n.height)/t.offsetHeight||1),(v(t)?_(t):window).visualViewport),t=!ie()&&i,i=(n.left+(t&&e?e.offsetLeft:0))/o,t=(n.top+(t&&e?e.offsetTop:0))/r,e=n.width/o,o=n.height/r;return{width:e,height:o,top:t,right:i+e,bottom:t+o,left:i,x:i,y:t}}function oe(t){var e=ne(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function re(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&Zt(i)){var n=e;do{if(n&&t.isSameNode(n))return!0}while(n=n.parentNode||n.host)}return!1}function w(t){return _(t).getComputedStyle(t)}function x(t){return((v(t)?t.ownerDocument:t.document)||window.document).documentElement}function se(t){return"html"===g(t)?t:t.assignedSlot||t.parentNode||(Zt(t)?t.host:null)||x(t)}function ae(t){return y(t)&&"fixed"!==w(t).position?t.offsetParent:null}function le(t){for(var e,i=_(t),n=ae(t);n&&(e=n,0<=["table","td","th"].indexOf(g(e)))&&"static"===w(n).position;)n=ae(n);return(!n||"html"!==g(n)&&("body"!==g(n)||"static"!==w(n).position))&&(n||(t=>{var e=/firefox/i.test(ee());if(!/Trident/i.test(ee())||!y(t)||"fixed"!==w(t).position){var i=se(t);for(Zt(i)&&(i=i.host);y(i)&&["html","body"].indexOf(g(i))<0;){var n=w(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}}return null})(t))||i}function ce(t){return 0<=["top","bottom"].indexOf(t)?"x":"y"}function ue(t,e,i){return S(t,Jt(e,i))}function he(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function de(i,t){return t.reduce(function(t,e){return t[e]=i,t},{})}var pe={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i,n,o,r=t.state,s=t.name,t=t.options,a=r.elements.arrow,l=r.modifiersData.popperOffsets,c=P(r.placement),u=ce(c),c=0<=[O,A].indexOf(c)?"height":"width";a&&l&&(t=he("number"!=typeof(t="function"==typeof(t=t.padding)?t(Object.assign({},r.rects,{placement:r.placement})):t)?t:de(t,L)),e=oe(a),o="y"===u?k:O,n="y"===u?I:A,i=r.rects.reference[c]+r.rects.reference[u]-l[u]-r.rects.popper[c],l=l[u]-r.rects.reference[u],a=(a=le(a))?"y"===u?a.clientHeight||0:a.clientWidth||0:0,o=t[o],t=a-e[c]-t[n],o=ue(o,n=a/2-e[c]/2+(i/2-l/2),t),r.modifiersData[s]=((a={})[u]=o,a.centerOffset=o-n,a))},effect:function(t){var e=t.state,t=t.options.element,t=void 0===t?"[data-popper-arrow]":t;null!=t&&("string"!=typeof t||(t=e.elements.popper.querySelector(t)))&&re(e.elements.popper,t)&&(e.elements.arrow=t)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function fe(t){return t.split("-")[1]}var me={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ge(t){var e=t.popper,i=t.popperRect,n=t.placement,o=t.variation,r=t.offsets,s=t.position,a=t.gpuAcceleration,l=t.adaptive,c=t.roundOffsets,t=t.isFixed,u=r.x,u=void 0===u?0:u,h=r.y,h=void 0===h?0:h,d="function"==typeof c?c({x:u,y:h}):{x:u,y:h},u=d.x,h=d.y,d=r.hasOwnProperty("x"),r=r.hasOwnProperty("y"),p=O,f=k,m=window;l&&(v="clientHeight",g="clientWidth",(y=le(e))===_(e)&&"static"!==w(y=x(e)).position&&"absolute"===s&&(v="scrollHeight",g="scrollWidth"),n!==k&&(n!==O&&n!==A||o!==b)||(f=I,h=(h-((t&&y===m&&m.visualViewport?m.visualViewport.height:y[v])-i.height))*(a?1:-1)),n!==O&&(n!==k&&n!==I||o!==b)||(p=A,u=(u-((t&&y===m&&m.visualViewport?m.visualViewport.width:y[g])-i.width))*(a?1:-1)));var g,v=Object.assign({position:s},l&&me),y=!0===c?(n={x:u,y:h},o=_(e),t=n.y,o=o.devicePixelRatio||1,{x:te(n.x*o)/o||0,y:te(t*o)/o||0}):{x:u,y:h};return u=y.x,h=y.y,a?Object.assign({},v,((g={})[f]=r?"0":"",g[p]=d?"0":"",g.transform=(m.devicePixelRatio||1)<=1?"translate("+u+"px, "+h+"px)":"translate3d("+u+"px, "+h+"px, 0)",g)):Object.assign({},v,((i={})[f]=r?h+"px":"",i[p]=d?u+"px":"",i.transform="",i))}var ve={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,t=t.options,i=t.gpuAcceleration,i=void 0===i||i,n=t.adaptive,n=void 0===n||n,t=t.roundOffsets,t=void 0===t||t,i={placement:P(e.placement),variation:fe(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,ge(Object.assign({},i,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:n,roundOffsets:t})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,ge(Object.assign({},i,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:t})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},ye={passive:!0},be={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,t=t.options,n=t.scroll,o=void 0===n||n,n=t.resize,r=void 0===n||n,s=_(e.elements.popper),a=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&a.forEach(function(t){t.addEventListener("scroll",i.update,ye)}),r&&s.addEventListener("resize",i.update,ye),function(){o&&a.forEach(function(t){t.removeEventListener("scroll",i.update,ye)}),r&&s.removeEventListener("resize",i.update,ye)}},data:{}},_e={left:"right",right:"left",bottom:"top",top:"bottom"};function we(t){return t.replace(/left|right|bottom|top/g,function(t){return _e[t]})}var xe={start:"end",end:"start"};function Ce(t){return t.replace(/start|end/g,function(t){return xe[t]})}function Te(t){t=_(t);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Ee(t){return ne(x(t)).left+Te(t).scrollLeft}function Se(t){var t=w(t),e=t.overflow;return/auto|scroll|overlay|hidden/.test(e+t.overflowY+t.overflowX)}function ke(t,e){void 0===e&&(e=[]);var i=function t(e){return 0<=["html","body","#document"].indexOf(g(e))?e.ownerDocument.body:y(e)&&Se(e)?e:t(se(e))}(t),t=i===(null==(t=t.ownerDocument)?void 0:t.body),n=_(i),n=t?[n].concat(n.visualViewport||[],Se(i)?i:[]):i,i=e.concat(n);return t?i:i.concat(ke(se(n)))}function Ie(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function Ae(t,e,i){return e===qt?Ie((o=i,s=_(n=t),a=x(n),s=s.visualViewport,l=a.clientWidth,a=a.clientHeight,u=c=0,s&&(l=s.width,a=s.height,(r=ie())||!r&&"fixed"===o)&&(c=s.offsetLeft,u=s.offsetTop),{width:l,height:a,x:c+Ee(n),y:u})):v(e)?((o=ne(r=e,!1,"fixed"===i)).top=o.top+r.clientTop,o.left=o.left+r.clientLeft,o.bottom=o.top+r.clientHeight,o.right=o.left+r.clientWidth,o.width=r.clientWidth,o.height=r.clientHeight,o.x=o.left,o.y=o.top,o):Ie((s=x(t),l=x(s),a=Te(s),c=null==(c=s.ownerDocument)?void 0:c.body,n=S(l.scrollWidth,l.clientWidth,c?c.scrollWidth:0,c?c.clientWidth:0),u=S(l.scrollHeight,l.clientHeight,c?c.scrollHeight:0,c?c.clientHeight:0),s=-a.scrollLeft+Ee(s),a=-a.scrollTop,"rtl"===w(c||l).direction&&(s+=S(l.clientWidth,c?c.clientWidth:0)-n),{width:n,height:u,x:s,y:a}));var n,o,r,s,a,l,c,u}function Oe(t){var e,i=t.reference,n=t.element,t=t.placement,o=t?P(t):null,t=t?fe(t):null,r=i.x+i.width/2-n.width/2,s=i.y+i.height/2-n.height/2;switch(o){case k:e={x:r,y:i.y-n.height};break;case I:e={x:r,y:i.y+i.height};break;case A:e={x:i.x+i.width,y:s};break;case O:e={x:i.x-n.width,y:s};break;default:e={x:i.x,y:i.y}}var a=o?ce(o):null;if(null!=a){var l="y"===a?"height":"width";switch(t){case D:e[a]=e[a]-(i[l]/2-n[l]/2);break;case b:e[a]=e[a]+(i[l]/2-n[l]/2)}}return e}function Le(t,e){var i,n,o,r,s,a,e=e=void 0===e?{}:e,l=e.placement,l=void 0===l?t.placement:l,c=e.strategy,c=void 0===c?t.strategy:c,u=e.boundary,u=void 0===u?Nt:u,h=e.rootBoundary,h=void 0===h?qt:h,d=e.elementContext,d=void 0===d?Ht:d,p=e.altBoundary,p=void 0!==p&&p,e=e.padding,e=void 0===e?0:e,e=he("number"!=typeof e?e:de(e,L)),f=t.rects.popper,p=t.elements[p?d===Ht?Ft:Ht:d],c=(i=v(p)?p:p.contextElement||x(t.elements.popper),p=h,n=c,r="clippingParents"===(h=u)?(s=ke(se(r=i)),v(o=0<=["absolute","fixed"].indexOf(w(r).position)&&y(r)?le(r):r)?s.filter(function(t){return v(t)&&re(t,o)&&"body"!==g(t)}):[]):[].concat(h),s=[].concat(r,[p]),h=s[0],(p=s.reduce(function(t,e){e=Ae(i,e,n);return t.top=S(e.top,t.top),t.right=Jt(e.right,t.right),t.bottom=Jt(e.bottom,t.bottom),t.left=S(e.left,t.left),t},Ae(i,h,n))).width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p),u=ne(t.elements.reference),h=Oe({reference:u,element:f,strategy:"absolute",placement:l}),p=Ie(Object.assign({},f,h)),f=d===Ht?p:u,m={top:c.top-f.top+e.top,bottom:f.bottom-c.bottom+e.bottom,left:c.left-f.left+e.left,right:f.right-c.right+e.right},h=t.modifiersData.offset;return d===Ht&&h&&(a=h[l],Object.keys(m).forEach(function(t){var e=0<=[A,I].indexOf(t)?1:-1,i=0<=[k,I].indexOf(t)?"y":"x";m[t]+=a[i]*e})),m}var De={name:"flip",enabled:!0,phase:"main",fn:function(t){var h=t.state,e=t.options,t=t.name;if(!h.modifiersData[t]._skip){for(var i=e.mainAxis,n=void 0===i||i,i=e.altAxis,o=void 0===i||i,i=e.fallbackPlacements,d=e.padding,p=e.boundary,f=e.rootBoundary,r=e.altBoundary,s=e.flipVariations,m=void 0===s||s,g=e.allowedAutoPlacements,s=h.options.placement,e=P(s),i=i||(e!==s&&m?P(i=s)===Rt?[]:(e=we(i),[Ce(i),e,Ce(e)]):[we(s)]),a=[s].concat(i).reduce(function(t,e){return t.concat(P(e)===Rt?(i=h,n=(t=t=void 0===(t={placement:e,boundary:p,rootBoundary:f,padding:d,flipVariations:m,allowedAutoPlacements:g})?{}:t).placement,o=t.boundary,r=t.rootBoundary,s=t.padding,a=t.flipVariations,l=void 0===(t=t.allowedAutoPlacements)?$t:t,c=fe(n),t=c?a?Wt:Wt.filter(function(t){return fe(t)===c}):L,u=(n=0===(n=t.filter(function(t){return 0<=l.indexOf(t)})).length?t:n).reduce(function(t,e){return t[e]=Le(i,{placement:e,boundary:o,rootBoundary:r,padding:s})[P(e)],t},{}),Object.keys(u).sort(function(t,e){return u[t]-u[e]})):e);var i,n,o,r,s,a,l,c,u},[]),l=h.rects.reference,c=h.rects.popper,u=new Map,v=!0,y=a[0],b=0;b<a.length;b++){var _=a[b],w=P(_),x=fe(_)===D,C=0<=[k,I].indexOf(w),T=C?"width":"height",E=Le(h,{placement:_,boundary:p,rootBoundary:f,altBoundary:r,padding:d}),C=C?x?A:O:x?I:k,x=(l[T]>c[T]&&(C=we(C)),we(C)),T=[];if(n&&T.push(E[w]<=0),o&&T.push(E[C]<=0,E[x]<=0),T.every(function(t){return t})){y=_,v=!1;break}u.set(_,T)}if(v)for(var S=m?3:1;0<S&&"break"!==(e=>{var t=a.find(function(t){t=u.get(t);if(t)return t.slice(0,e).every(function(t){return t})});if(t)return y=t,"break"})(S);S--);h.placement!==y&&(h.modifiersData[t]._skip=!0,h.placement=y,h.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Pe(t,e,i){return{top:t.top-e.height-(i=void 0===i?{x:0,y:0}:i).y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function je(e){return[k,A,I,O].some(function(t){return 0<=e[t]})}var Me={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,t=t.name,i=e.rects.reference,n=e.rects.popper,o=e.modifiersData.preventOverflow,r=Le(e,{elementContext:"reference"}),s=Le(e,{altBoundary:!0}),r=Pe(r,i),i=Pe(s,n,o),s=je(r),n=je(i);e.modifiersData[t]={referenceClippingOffsets:r,popperEscapeOffsets:i,isReferenceHidden:s,hasPopperEscaped:n},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":s,"data-popper-escaped":n})}},ze={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var s=t.state,e=t.name,t=t.options.offset,a=void 0===t?[0,0]:t,t=$t.reduce(function(t,e){return t[e]=(e=e,i=s.rects,n=a,o=P(e),r=0<=[O,k].indexOf(o)?-1:1,i="function"==typeof n?n(Object.assign({},i,{placement:e})):n,e=i[0]||0,n=(i[1]||0)*r,0<=[O,A].indexOf(o)?{x:n,y:e}:{x:e,y:n}),t;var i,n,o,r},{}),i=t[s.placement],n=i.x,i=i.y;null!=s.modifiersData.popperOffsets&&(s.modifiersData.popperOffsets.x+=n,s.modifiersData.popperOffsets.y+=i),s.modifiersData[e]=t}},Re={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state;e.modifiersData[t.name]=Oe({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},Ne={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e,i,n,o,r,s,a,l,c,u=t.state,h=t.options,t=t.name,d=h.mainAxis,d=void 0===d||d,p=h.altAxis,p=void 0!==p&&p,f=h.tether,f=void 0===f||f,m=h.tetherOffset,m=void 0===m?0:m,h=Le(u,{boundary:h.boundary,rootBoundary:h.rootBoundary,padding:h.padding,altBoundary:h.altBoundary}),g=P(u.placement),v=fe(u.placement),y=!v,b=ce(g),_="x"===b?"y":"x",w=u.modifiersData.popperOffsets,x=u.rects.reference,C=u.rects.popper,m="function"==typeof m?m(Object.assign({},u.rects,{placement:u.placement})):m,m="number"==typeof m?{mainAxis:m,altAxis:m}:Object.assign({mainAxis:0,altAxis:0},m),T=u.modifiersData.offset?u.modifiersData.offset[u.placement]:null,E={x:0,y:0};w&&(d&&(d="y"===b?"height":"width",s=(a=w[b])+h[i="y"===b?k:O],l=a-h[c="y"===b?I:A],e=f?-C[d]/2:0,o=(v===D?x:C)[d],v=v===D?-C[d]:-x[d],r=u.elements.arrow,r=f&&r?oe(r):{width:0,height:0},i=(n=u.modifiersData["arrow#persistent"]?u.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0})[i],n=n[c],c=ue(0,x[d],r[d]),r=y?x[d]/2-e-c-i-m.mainAxis:o-c-i-m.mainAxis,o=y?-x[d]/2+e+c+n+m.mainAxis:v+c+n+m.mainAxis,y=(i=u.elements.arrow&&le(u.elements.arrow))?"y"===b?i.clientTop||0:i.clientLeft||0:0,v=a+o-(e=null!=(d=null==T?void 0:T[b])?d:0),c=ue(f?Jt(s,a+r-e-y):s,a,f?S(l,v):l),w[b]=c,E[b]=c-a),p&&(n="y"==_?"height":"width",o=(i=w[_])+h["x"===b?k:O],d=i-h["x"===b?I:A],r=-1!==[k,O].indexOf(g),y=null!=(e=null==T?void 0:T[_])?e:0,s=r?o:i-x[n]-C[n]-y+m.altAxis,v=r?i+x[n]+C[n]-y-m.altAxis:d,a=f&&r?(c=ue(s,i,l=v),l<c?l:c):ue(f?s:o,i,f?v:d),w[_]=a,E[_]=a-i),u.modifiersData[t]=E)},requiresIfExists:["offset"]};function qe(t){var i=new Map,n=new Set,o=[];return t.forEach(function(t){i.set(t.name,t)}),t.forEach(function(t){n.has(t.name)||function e(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){n.has(t)||(t=i.get(t))&&e(t)}),o.push(t)}(t)}),o}var He={placement:"bottom",modifiers:[],strategy:"absolute"};function Fe(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some(function(t){return!(t&&"function"==typeof t.getBoundingClientRect)})}function We(t){var t=t=void 0===t?{}:t,e=t.defaultModifiers,l=void 0===e?[]:e,e=t.defaultOptions,c=void 0===e?He:e;return function(n,o,e){void 0===e&&(e=c);var i,r,p={placement:"bottom",orderedModifiers:[],options:Object.assign({},He,c),modifiersData:{},elements:{reference:n,popper:o},attributes:{},styles:{}},s=[],f=!1,m={state:p,setOptions:function(t){t="function"==typeof t?t(p.options):t;a(),p.options=Object.assign({},c,p.options,t),p.scrollParents={reference:v(n)?ke(n):n.contextElement?ke(n.contextElement):[],popper:ke(o)};t=[].concat(l,p.options.modifiers),e=t.reduce(function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t},{}),i=qe(Object.keys(e).map(function(t){return e[t]}));var e,i,t=Gt.reduce(function(t,e){return t.concat(i.filter(function(t){return t.phase===e}))},[]);return p.orderedModifiers=t.filter(function(t){return t.enabled}),p.orderedModifiers.forEach(function(t){var e=t.name,i=t.options,t=t.effect;"function"==typeof t&&(t=t({state:p,name:e,instance:m,options:void 0===i?{}:i}),s.push(t||function(){}))}),m.update()},forceUpdate:function(){if(!f){var t=p.elements,e=t.reference,t=t.popper;if(Fe(e,t)){p.rects={reference:(e=e,s=le(t),void 0===(a="fixed"===p.options.strategy)&&(a=!1),l=y(s),c=y(s)&&(h=(c=s).getBoundingClientRect(),u=te(h.width)/c.offsetWidth||1,h=te(h.height)/c.offsetHeight||1,1!==u||1!==h),u=x(s),h=ne(e,c,a),e={scrollLeft:0,scrollTop:0},d={x:0,y:0},!l&&a||("body"===g(s)&&!Se(u)||(e=(l=s)!==_(l)&&y(l)?{scrollLeft:l.scrollLeft,scrollTop:l.scrollTop}:Te(l)),y(s)?((d=ne(s,!0)).x+=s.clientLeft,d.y+=s.clientTop):u&&(d.x=Ee(u))),{x:h.left+e.scrollLeft-d.x,y:h.top+e.scrollTop-d.y,width:h.width,height:h.height}),popper:oe(t)},p.reset=!1,p.placement=p.options.placement,p.orderedModifiers.forEach(function(t){return p.modifiersData[t.name]=Object.assign({},t.data)});for(var i,n,o,r=0;r<p.orderedModifiers.length;r++)!0!==p.reset?(n=(i=p.orderedModifiers[r]).fn,o=i.options,"function"==typeof n&&(p=n({state:p,options:void 0===o?{}:o,name:i.name,instance:m})||p)):(p.reset=!1,r=-1)}}var s,a,l,c,u,h,d},update:(i=function(){return new Promise(function(t){m.forceUpdate(),t(p)})},function(){return r=r||new Promise(function(t){Promise.resolve().then(function(){r=void 0,t(i())})})}),destroy:function(){a(),f=!0}};return Fe(n,o)&&m.setOptions(e).then(function(t){!f&&e.onFirstUpdate&&e.onFirstUpdate(t)}),m;function a(){s.forEach(function(t){return t()}),s=[]}}}var $e=We(),Be=We({defaultModifiers:[be,Re,ve,Kt]}),Ve=We({defaultModifiers:[be,Re,ve,Kt,ze,De,Ne,pe,Me]});let Ue=Object.freeze(Object.defineProperty({__proto__:null,afterMain:Qt,afterRead:Vt,afterWrite:Xt,applyStyles:Kt,arrow:pe,auto:Rt,basePlacements:L,beforeMain:Ut,beforeRead:Bt,beforeWrite:Yt,bottom:I,clippingParents:Nt,computeStyles:ve,createPopper:Ve,createPopperBase:$e,createPopperLite:Be,detectOverflow:Le,end:b,eventListeners:be,flip:De,hide:Me,left:O,main:"main",modifierPhases:Gt,offset:ze,placements:$t,popper:Ht,popperGenerator:We,popperOffsets:Re,preventOverflow:Ne,read:"read",reference:Ft,right:A,start:D,top:k,variationPlacements:Wt,viewport:qt,write:"write"},Symbol.toStringTag,{value:"Module"})),Qe="dropdown",Ye=".bs.dropdown",Xe=".data-api",Ge="ArrowDown",Ze="click"+Ye+Xe,Ke="keydown"+Ye+Xe,Je="show",C='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',ti=(C,".dropdown-menu"),ei=l()?"top-end":"top-start",ii=l()?"top-start":"top-end",ni=l()?"bottom-end":"bottom-start",oi=l()?"bottom-start":"bottom-end",ri=l()?"left-start":"right-start",si=l()?"right-start":"left-start",ai={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},li={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class T extends e{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=h.next(this._element,ti)[0]||h.prev(this._element,ti)[0]||h.findOne(ti,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return ai}static get DefaultType(){return li}static get NAME(){return Qe}toggle(){return this._isShown()?this.hide():this.show()}show(){if(!a(this._element)&&!this._isShown()){let t={relatedTarget:this._element};if(!f.trigger(this._element,"show.bs.dropdown",t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(let t of[].concat(...document.body.children))f.on(t,"mouseover",F);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Je),this._element.classList.add(Je),f.trigger(this._element,"shown.bs.dropdown",t)}}}hide(){var t;!a(this._element)&&this._isShown()&&(t={relatedTarget:this._element},this._completeHide(t))}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!f.trigger(this._element,"hide.bs.dropdown",t).defaultPrevented){if("ontouchstart"in document.documentElement)for(let t of[].concat(...document.body.children))f.off(t,"mouseover",F);this._popper&&this._popper.destroy(),this._menu.classList.remove(Je),this._element.classList.remove(Je),this._element.setAttribute("aria-expanded","false"),u.removeDataAttribute(this._menu,"popper"),f.trigger(this._element,"hidden.bs.dropdown",t)}}_getConfig(t){if("object"!=typeof(t=super._getConfig(t)).reference||s(t.reference)||"function"==typeof t.reference.getBoundingClientRect)return t;throw new TypeError(Qe.toUpperCase()+': Option "reference" provided type "object" without a required "getBoundingClientRect" method.')}_createPopper(){if(void 0===Ue)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;"parent"===this._config.reference?t=this._parent:s(this._config.reference)?t=o(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);var e=this._getPopperConfig();this._popper=Ve(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Je)}_getPlacement(){var t,e=this._parent;return e.classList.contains("dropend")?ri:e.classList.contains("dropstart")?si:e.classList.contains("dropup-center")?"top":e.classList.contains("dropdown-center")?"bottom":(t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim(),e.classList.contains("dropup")?t?ii:ei:t?oi:ni)}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){let e=this._config.offset;return"string"==typeof e?e.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){var t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return!this._inNavbar&&"static"!==this._config.display||(u.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...c(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){var i=h.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(t=>r(t));i.length&&U(i,e,t===Ge,!i.includes(e)).focus()}static jQueryInterface(e){return this.each(function(){var t=T.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(2!==e.button&&("keyup"!==e.type||"Tab"===e.key)){let t=h.find('[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled).show');for(var i of t){let t=T.getInstance(i);var n;t&&!1!==t._config.autoClose&&(n=(i=e.composedPath()).includes(t._menu),i.includes(t._element)||"inside"===t._config.autoClose&&!n||"outside"===t._config.autoClose&&n||t._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName))||(i={relatedTarget:t._element},"click"===e.type&&(i.clickEvent=e),t._completeHide(i)))}}}static dataApiKeydownHandler(t){var e=/input|textarea/i.test(t.target.tagName),i="Escape"===t.key,n=["ArrowUp",Ge].includes(t.key);!n&&!i||e&&!i||(t.preventDefault(),e=this.matches(C)?this:h.prev(this,C)[0]||h.next(this,C)[0]||h.findOne(C,t.delegateTarget.parentNode),i=T.getOrCreateInstance(e),n?(t.stopPropagation(),i.show(),i._selectMenuItem(t)):i._isShown()&&(t.stopPropagation(),i.hide(),e.focus()))}}f.on(document,Ke,C,T.dataApiKeydownHandler),f.on(document,Ke,ti,T.dataApiKeydownHandler),f.on(document,Ze,T.clearMenus),f.on(document,"keyup.bs.dropdown.data-api",T.clearMenus),f.on(document,Ze,C,function(t){t.preventDefault(),T.getOrCreateInstance(this).toggle()}),t(T);let ci="backdrop",ui="mousedown.bs."+ci,hi={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},di={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class pi extends ut{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return hi}static get DefaultType(){return di}static get NAME(){return ci}show(t){var e;this._config.isVisible?(this._append(),e=this._getElement(),this._config.isAnimated&&W(e),e.classList.add("show"),this._emulateAnimation(()=>{c(t)})):c(t)}hide(t){this._config.isVisible?(this._getElement().classList.remove("show"),this._emulateAnimation(()=>{this.dispose(),c(t)})):c(t)}dispose(){this._isAppended&&(f.off(this._element,ui),this._element.remove(),this._isAppended=!1)}_getElement(){var t;return this._element||((t=document.createElement("div")).className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t),this._element}_configAfterMerge(t){return t.rootElement=o(t.rootElement),t}_append(){var t;this._isAppended||(t=this._getElement(),this._config.rootElement.append(t),f.on(t,ui,()=>{c(this._config.clickCallback)}),this._isAppended=!0)}_emulateAnimation(t){V(t,this._getElement(),this._config.isAnimated)}}let fi=".bs.focustrap",mi=(fi,fi,"backward"),gi={autofocus:!0,trapElement:null},vi={autofocus:"boolean",trapElement:"element"};class yi extends ut{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return gi}static get DefaultType(){return vi}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),f.off(document,fi),f.on(document,"focusin.bs.focustrap",t=>this._handleFocusin(t)),f.on(document,"keydown.tab.bs.focustrap",t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,f.off(document,fi))}_handleFocusin(t){var e=this._config.trapElement;t.target===document||t.target===e||e.contains(t.target)||(0===(t=h.focusableChildren(e)).length?e:this._lastTabNavDirection===mi?t[t.length-1]:t[0]).focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?mi:"forward")}}let bi=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",_i=".sticky-top",wi="padding-right",xi="margin-right";class Ci{constructor(){this._element=document.body}getWidth(){var t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){let e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,wi,t=>t+e),this._setElementAttributes(bi,wi,t=>t+e),this._setElementAttributes(_i,xi,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,wi),this._resetElementAttributes(bi,wi),this._resetElementAttributes(_i,xi)}isOverflowing(){return 0<this.getWidth()}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,i,n){let o=this.getWidth();this._applyManipulationCallback(t,t=>{var e;t!==this._element&&window.innerWidth>t.clientWidth+o||(this._saveInitialAttribute(t,i),e=window.getComputedStyle(t).getPropertyValue(i),t.style.setProperty(i,n(Number.parseFloat(e))+"px"))})}_saveInitialAttribute(t,e){var i=t.style.getPropertyValue(e);i&&u.setDataAttribute(t,e,i)}_resetElementAttributes(t,i){this._applyManipulationCallback(t,t=>{var e=u.getDataAttribute(t,i);null!==e?(u.removeDataAttribute(t,i),t.style.setProperty(i,e)):t.style.removeProperty(i)})}_applyManipulationCallback(t,e){if(s(t))e(t);else for(var i of h.find(t,this._element))e(i)}}let E=".bs.modal",Ti=(E,E,"hidden"+E),Ei="show"+E,Si=(E,E,E,E,E,E,"modal-open"),ki="modal-static",Ii={backdrop:!0,focus:!0,keyboard:!0},Ai={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Oi extends e{constructor(t,e){super(t,e),this._dialog=h.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Ci,this._addEventListeners()}static get Default(){return Ii}static get DefaultType(){return Ai}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||f.trigger(this._element,Ei,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Si),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||f.trigger(this._element,"hide.bs.modal").defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove("show"),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){f.off(window,E),f.off(this._dialog,E),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new pi({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new yi({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;var e=h.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),W(this._element),this._element.classList.add("show"),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,f.trigger(this._element,"shown.bs.modal",{relatedTarget:t})},this._dialog,this._isAnimated())}_addEventListeners(){f.on(this._element,"keydown.dismiss.bs.modal",t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),f.on(window,"resize.bs.modal",()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),f.on(this._element,"mousedown.dismiss.bs.modal",e=>{f.one(this._element,"click.dismiss.bs.modal",t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Si),this._resetAdjustments(),this._scrollBar.reset(),f.trigger(this._element,Ti)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(!f.trigger(this._element,"hidePrevented.bs.modal").defaultPrevented){let t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(ki)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(ki),this._queueCallback(()=>{this._element.classList.remove(ki),this._queueCallback(()=>{this._element.style.overflowY=e},this._dialog)},this._dialog),this._element.focus())}}_adjustDialog(){let t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=0<e;if(i&&!t){let t=l()?"paddingLeft":"paddingRight";this._element.style[t]=e+"px"}if(!i&&t){let t=l()?"paddingRight":"paddingLeft";this._element.style[t]=e+"px"}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,i){return this.each(function(){var t=Oi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](i)}})}}f.on(document,"click.bs.modal.data-api",'[data-bs-toggle="modal"]',function(t){let e=h.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),f.one(e,Ei,t=>{t.defaultPrevented||f.one(e,Ti,()=>{r(this)&&this.focus()})});t=h.findOne(".modal.show");t&&Oi.getInstance(t).hide(),Oi.getOrCreateInstance(e).toggle(this)}),dt(Oi),t(Oi);let Li=".bs.offcanvas",Di="showing",Pi=".offcanvas.show",ji="hidePrevented"+Li,Mi="hidden"+Li,zi={backdrop:!0,keyboard:!0,scroll:!1},Ri={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class j extends e{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return zi}static get DefaultType(){return Ri}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||f.trigger(this._element,"show.bs.offcanvas",{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Ci).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Di),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add("show"),this._element.classList.remove(Di),f.trigger(this._element,"shown.bs.offcanvas",{relatedTarget:t})},this._element,!0))}hide(){!this._isShown||f.trigger(this._element,"hide.bs.offcanvas").defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add("hiding"),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove("show","hiding"),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new Ci).reset(),f.trigger(this._element,Mi)},this._element,!0))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){var t=Boolean(this._config.backdrop);return new pi({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():f.trigger(this._element,ji)}:null})}_initializeFocusTrap(){return new yi({trapElement:this._element})}_addEventListeners(){f.on(this._element,"keydown.dismiss.bs.offcanvas",t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():f.trigger(this._element,ji))})}static jQueryInterface(e){return this.each(function(){var t=j.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}f.on(document,"click.bs.offcanvas.data-api",'[data-bs-toggle="offcanvas"]',function(t){var e=h.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),a(this)||(f.one(e,Mi,()=>{r(this)&&this.focus()}),(t=h.findOne(Pi))&&t!==e&&j.getInstance(t).hide(),j.getOrCreateInstance(e).toggle(this))}),f.on(window,"load.bs.offcanvas.data-api",()=>{for(var t of h.find(Pi))j.getOrCreateInstance(t).show()}),f.on(window,"resize.bs.offcanvas",()=>{for(var t of h.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&j.getOrCreateInstance(t).hide()}),dt(j),t(j);let Ni={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},qi=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Hi=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Fi={allowList:Ni,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Wi={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},$i={entry:"(string|element|function|null)",selector:"(string|element)"};class Bi extends ut{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Fi}static get DefaultType(){return Wi}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return 0<this.getContent().length}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){var i=document.createElement("div");i.innerHTML=this._maybeSanitize(this._config.template);for(let[t,e]of Object.entries(this._config.content))this._setContent(i,e,t);let t=i.children[0],e=this._resolvePossibleFunction(this._config.extraClass);return e&&t.classList.add(...e.split(" ")),t}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(var[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},$i)}_setContent(t,e,i){i=h.findOne(i,t);i&&((e=this._resolvePossibleFunction(e))?s(e)?this._putElementInTemplate(o(e),i):this._config.html?i.innerHTML=this._maybeSanitize(e):i.textContent=e:i.remove())}_maybeSanitize(i){if(this._config.sanitize){var n=i,o=this._config.allowList,r=this._config.sanitizeFn;if(!n.length)return n;if(r&&"function"==typeof r)return r(n);let t=(new window.DOMParser).parseFromString(n,"text/html"),e=[].concat(...t.body.querySelectorAll("*"));for(let n of e){let t=n.nodeName.toLowerCase();if(Object.keys(o).includes(t)){let e=[].concat(...n.attributes),i=[].concat(o["*"]||[],o[t]||[]);for(let t of e)((t,e)=>{let i=t.nodeName.toLowerCase();return e.includes(i)?!qi.has(i)||Boolean(Hi.test(t.nodeValue)):e.filter(t=>t instanceof RegExp).some(t=>t.test(i))})(t,i)||n.removeAttribute(t.nodeName)}else n.remove()}return t.body.innerHTML}return i}_resolvePossibleFunction(t){return c(t,[this])}_putElementInTemplate(t,e){this._config.html?(e.innerHTML="",e.append(t)):e.textContent=t.textContent}}let Vi=new Set(["sanitize","allowList","sanitizeFn"]),Ui="fade",Qi="show",Yi="hide.bs.modal",Xi="hover",Gi="focus",Zi={AUTO:"auto",TOP:"top",RIGHT:l()?"left":"right",BOTTOM:"bottom",LEFT:l()?"right":"left"},Ki={allowList:Ni,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Ji={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class tn extends e{constructor(t,e){if(void 0===Ue)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Ki}static get DefaultType(){return Ji}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),f.off(this._element.closest(".modal"),Yi,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(this._isWithContent()&&this._isEnabled){let t=f.trigger(this._element,this.constructor.eventName("show")),e=(H(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(!t.defaultPrevented&&e){this._disposePopper();var i=this._getTipElement(),n=(this._element.setAttribute("aria-describedby",i.getAttribute("id")),this._config).container;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),f.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(Qi),"ontouchstart"in document.documentElement)for(let t of[].concat(...document.body.children))f.on(t,"mouseover",F);this._queueCallback(()=>{f.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}}}hide(){if(this._isShown()&&!f.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(Qi),"ontouchstart"in document.documentElement)for(var t of[].concat(...document.body.children))f.off(t,"mouseover",F);this._activeTrigger.click=!1,this._activeTrigger[Gi]=!1,this._activeTrigger[Xi]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),f.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){t=this._getTemplateFactory(t).toHtml();if(!t)return null;t.classList.remove(Ui,Qi),t.classList.add(`bs-${this.constructor.NAME}-auto`);var e=(t=>{for(;t+=Math.floor(1e6*Math.random()),document.getElementById(t););return t})(this.constructor.NAME).toString();return t.setAttribute("id",e),this._isAnimated()&&t.classList.add(Ui),t}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Bi({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Ui)}_isShown(){return this.tip&&this.tip.classList.contains(Qi)}_createPopper(t){var e=c(this._config.placement,[this,t,this._element]),e=Zi[e.toUpperCase()];return Ve(this._element,t,this._getPopperConfig(e))}_getOffset(){let e=this._config.offset;return"string"==typeof e?e.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(t){return c(t,[this._element])}_getPopperConfig(t){t={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...t,...c(this._config.popperConfig,[t])}}_setListeners(){let t=this._config.trigger.split(" ");for(var i of t)if("click"===i)f.on(this._element,this.constructor.eventName("click"),this._config.selector,t=>{this._initializeOnDelegatedTarget(t).toggle()});else if("manual"!==i){let t=i===Xi?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),e=i===Xi?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");f.on(this._element,t,this._config.selector,t=>{var e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?Gi:Xi]=!0,e._enter()}),f.on(this._element,e,this._config.selector,t=>{var e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?Gi:Xi]=e._element.contains(t.relatedTarget),e._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},f.on(this._element.closest(".modal"),Yi,this._hideModalHandler)}_fixTitle(){var t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){var e=u.getDataAttributes(this._element);for(let t of Object.keys(e))Vi.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:o(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){var t,e,i={};for([t,e]of Object.entries(this._config))this.constructor.Default[t]!==e&&(i[t]=e);return i.selector=!1,i.trigger="manual",i}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){var t=tn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}t(tn);let en={...tn.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},nn={...tn.DefaultType,content:"(null|string|element|function)"};class on extends tn{static get Default(){return en}static get DefaultType(){return nn}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){var t=on.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}t(on);let rn=".bs.scrollspy",sn="click"+rn,an="active",ln="[href]",cn={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},un={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class hn extends e{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return cn}static get DefaultType(){return un}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(var t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=o(t.target)||document.body,t.rootMargin=t.offset?t.offset+"px 0px -30%":t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map(t=>Number.parseFloat(t))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(f.off(this._config.target,sn),f.on(this._config.target,sn,ln,t=>{var e=this._observableSections.get(t.target.hash);e&&(t.preventDefault(),t=this._rootElement||window,e=e.offsetTop-this._element.offsetTop,t.scrollTo?t.scrollTo({top:e,behavior:"smooth"}):t.scrollTop=e)}))}_getNewObserver(){var t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(t=>this._observerCallback(t),t)}_observerCallback(t){let e=t=>this._targetLinks.get("#"+t.target.id),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,o=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(var r of t)if(r.isIntersecting){let t=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&t){if(i(r),!n)return}else o||t||i(r)}else this._activeTarget=null,this._clearActiveClass(e(r))}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;let t=h.find(ln,this._config.target);for(var e of t)if(e.hash&&!a(e)){let t=h.findOne(decodeURI(e.hash),this._element);r(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),(this._activeTarget=t).classList.add(an),this._activateParents(t),f.trigger(this._element,"activate.bs.scrollspy",{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))h.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(an);else for(var e of h.parents(t,".nav, .list-group"))for(let t of h.prev(e,".nav-link, .nav-item > .nav-link, .list-group-item"))t.classList.add(an)}_clearActiveClass(t){t.classList.remove(an);var e=h.find(ln+"."+an,t);for(let t of e)t.classList.remove(an)}static jQueryInterface(e){return this.each(function(){var t=hn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}})}}f.on(window,"load.bs.scrollspy.data-api",()=>{for(var t of h.find('[data-bs-spy="scroll"]'))hn.getOrCreateInstance(t)}),t(hn);let dn="ArrowRight",pn="ArrowDown",fn="Home",M="active",mn="show",gn=".dropdown-toggle",vn=`:not(${gn})`,yn='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',bn=`.nav-link${vn}, .list-group-item${vn}, [role="tab"]${vn}, `+yn;M,M,M;class _n extends e{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),f.on(this._element,"keydown.bs.tab",t=>this._keydown(t)))}static get NAME(){return"tab"}show(){var t,e,i=this._element;this._elemIsActive(i)||(e=(t=this._getActiveElem())?f.trigger(t,"hide.bs.tab",{relatedTarget:i}):null,f.trigger(i,"show.bs.tab",{relatedTarget:t}).defaultPrevented)||e&&e.defaultPrevented||(this._deactivate(t,i),this._activate(i,t))}_activate(t,e){t&&(t.classList.add(M),this._activate(h.getElementFromSelector(t)),this._queueCallback(()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),f.trigger(t,"shown.bs.tab",{relatedTarget:e})):t.classList.add(mn)},t,t.classList.contains("fade")))}_deactivate(t,e){t&&(t.classList.remove(M),t.blur(),this._deactivate(h.getElementFromSelector(t)),this._queueCallback(()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),f.trigger(t,"hidden.bs.tab",{relatedTarget:e})):t.classList.remove(mn)},t,t.classList.contains("fade")))}_keydown(e){if(["ArrowLeft",dn,"ArrowUp",pn,fn,"End"].includes(e.key)){e.stopPropagation(),e.preventDefault();var i,n=this._getChildren().filter(t=>!a(t));let t;(t=[fn,"End"].includes(e.key)?n[e.key===fn?0:n.length-1]:(i=[dn,pn].includes(e.key),U(n,e.target,i,!0)))&&(t.focus({preventScroll:!0}),_n.getOrCreateInstance(t).show())}}_getChildren(){return h.find(bn,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(let t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);var e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){var e=h.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id)&&this._setAttributeIfNotExists(e,"aria-labelledby",""+t.id)}_toggleDropDown(t,i){let n=this._getOuterElement(t);n.classList.contains("dropdown")&&((t=(t,e)=>{t=h.findOne(t,n);t&&t.classList.toggle(e,i)})(gn,M),t(".dropdown-menu",mn),n.setAttribute("aria-expanded",i))}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(M)}_getInnerElement(t){return t.matches(bn)?t:h.findOne(bn,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(e){return this.each(function(){var t=_n.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}})}}f.on(document,"click.bs.tab",yn,function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),a(this)||_n.getOrCreateInstance(this).show()}),f.on(window,"load.bs.tab",()=>{for(var t of h.find('.active[data-bs-toggle="tab"], .active[data-bs-toggle="pill"], .active[data-bs-toggle="list"]'))_n.getOrCreateInstance(t)}),t(_n);let wn="show",xn="showing",Cn={animation:"boolean",autohide:"boolean",delay:"number"},Tn={animation:!0,autohide:!0,delay:5e3};class En extends e{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Tn}static get DefaultType(){return Cn}static get NAME(){return"toast"}show(){f.trigger(this._element,"show.bs.toast").defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove("hide"),W(this._element),this._element.classList.add(wn,xn),this._queueCallback(()=>{this._element.classList.remove(xn),f.trigger(this._element,"shown.bs.toast"),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){!this.isShown()||f.trigger(this._element,"hide.bs.toast").defaultPrevented||(this._element.classList.add(xn),this._queueCallback(()=>{this._element.classList.add("hide"),this._element.classList.remove(xn,wn),f.trigger(this._element,"hidden.bs.toast")},this._element,this._config.animation))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(wn),super.dispose()}isShown(){return this._element.classList.contains(wn)}_maybeScheduleHide(){!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}e?this._clearTimeout():this._element===(t=t.relatedTarget)||this._element.contains(t)||this._maybeScheduleHide()}_setListeners(){f.on(this._element,"mouseover.bs.toast",t=>this._onInteraction(t,!0)),f.on(this._element,"mouseout.bs.toast",t=>this._onInteraction(t,!1)),f.on(this._element,"focusin.bs.toast",t=>this._onInteraction(t,!0)),f.on(this._element,"focusout.bs.toast",t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){var t=En.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}})}}return dt(En),t(En),{Alert:pt,Button:mt,Carousel:Ot,Collapse:zt,Dropdown:T,Modal:Oi,Offcanvas:j,Popover:on,ScrollSpy:hn,Tab:_n,Toast:En,Tooltip:tn}}),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(jQuery)})(function(h){var i=/\+/g;function d(t){return f.raw?t:encodeURIComponent(t)}function p(t,e){t=f.raw?t:(t=>{0===t.indexOf('"')&&(t=t.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\"));try{return t=decodeURIComponent(t.replace(i," ")),f.json?JSON.parse(t):t}catch(t){}})(t);return h.isFunction(e)?e(t):t}var f=h.cookie=function(t,e,i){var n,o;if(void 0!==e&&!h.isFunction(e))return"number"==typeof(i=h.extend({},f.defaults,i)).expires&&(n=i.expires,(o=i.expires=new Date).setTime(+o+864e5*n)),document.cookie=[d(t),"=",(o=e,d(f.json?JSON.stringify(o):String(o))),i.expires?"; expires="+i.expires.toUTCString():"",i.path?"; path="+i.path:"",i.domain?"; domain="+i.domain:"",i.secure?"; secure":""].join("");for(var r=t?void 0:{},s=document.cookie?document.cookie.split("; "):[],a=0,l=s.length;a<l;a++){var c=s[a].split("="),u=(u=c.shift(),f.raw?u:decodeURIComponent(u)),c=c.join("=");if(t&&t===u){r=p(c,e);break}t||void 0===(c=p(c))||(r[u]=c)}return r};f.defaults={},h.removeCookie=function(t,e){return void 0!==h.cookie(t)&&(h.cookie(t,"",h.extend({},e,{expires:-1})),!h.cookie(t))}}),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery)})(function(u){u.extend(u.fn,{validate:function(t){var n;if(this.length)return(n=u.data(this[0],"validator"))||(this.attr("novalidate","novalidate"),n=new u.validator(t,this[0]),u.data(this[0],"validator",n),n.settings.onsubmit&&(this.on("click.validate",":submit",function(t){n.submitButton=t.currentTarget,u(this).hasClass("cancel")&&(n.cancelSubmit=!0),void 0!==u(this).attr("formnovalidate")&&(n.cancelSubmit=!0)}),this.on("submit.validate",function(i){function t(){var t,e;return n.submitButton&&(n.settings.submitHandler||n.formSubmitted)&&(t=u("<input type='hidden'/>").attr("name",n.submitButton.name).val(u(n.submitButton).val()).appendTo(n.currentForm)),!(n.settings.submitHandler&&!n.settings.debug)||(e=n.settings.submitHandler.call(n,n.currentForm,i),t&&t.remove(),void 0!==e&&e)}return n.settings.debug&&i.preventDefault(),n.cancelSubmit?(n.cancelSubmit=!1,t()):n.form()?n.pendingRequest?!(n.formSubmitted=!0):t():(n.focusInvalid(),!1)})),n);t&&t.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing.")},valid:function(){var t,e,i;return u(this[0]).is("form")?t=this.validate().form():(i=[],t=!0,e=u(this[0].form).validate(),this.each(function(){(t=e.element(this)&&t)||(i=i.concat(e.errorList))}),e.errorList=i),t},rules:function(t,e){var i,n,o,r,s,a=this[0],l=void 0!==this.attr("contenteditable")&&"false"!==this.attr("contenteditable");if(null!=a&&(!a.form&&l&&(a.form=this.closest("form")[0],a.name=this.attr("name")),null!=a.form)){if(t)switch(i=u.data(a.form,"validator").settings,n=i.rules,o=u.validator.staticRules(a),t){case"add":u.extend(o,u.validator.normalizeRule(e)),delete o.messages,n[a.name]=o,e.messages&&(i.messages[a.name]=u.extend(i.messages[a.name],e.messages));break;case"remove":return e?(s={},u.each(e.split(/\s/),function(t,e){s[e]=o[e],delete o[e]}),s):(delete n[a.name],o)}return(l=u.validator.normalizeRules(u.extend({},u.validator.classRules(a),u.validator.attributeRules(a),u.validator.dataRules(a),u.validator.staticRules(a)),a)).required&&(r=l.required,delete l.required,l=u.extend({required:r},l)),l.remote&&(r=l.remote,delete l.remote,l=u.extend(l,{remote:r})),l}}});function e(t){return t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var i;u.extend(u.expr.pseudos||u.expr[":"],{blank:function(t){return!e(""+u(t).val())},filled:function(t){t=u(t).val();return null!==t&&!!e(""+t)},unchecked:function(t){return!u(t).prop("checked")}}),u.validator=function(t,e){this.settings=u.extend(!0,{},u.validator.defaults,t),this.currentForm=e,this.init()},u.validator.format=function(i,t){return 1===arguments.length?function(){var t=u.makeArray(arguments);return t.unshift(i),u.validator.format.apply(this,t)}:(void 0===t||((t=2<arguments.length&&t.constructor!==Array?u.makeArray(arguments).slice(1):t).constructor!==Array&&(t=[t]),u.each(t,function(t,e){i=i.replace(new RegExp("\\{"+t+"\\}","g"),function(){return e})})),i)},u.extend(u.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",pendingClass:"pending",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:u([]),errorLabelContainer:u([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,customElements:[],onfocusin:function(t){this.lastActive=t,this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,t,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(t)))},onfocusout:function(t){this.checkable(t)||!(t.name in this.submitted)&&this.optional(t)||this.element(t)},onkeyup:function(t,e){9===e.which&&""===this.elementValue(t)||-1!==u.inArray(e.keyCode,[16,17,18,20,35,36,37,38,39,40,45,144,225])||(t.name in this.submitted||t.name in this.invalid)&&this.element(t)},onclick:function(t){t.name in this.submitted?this.element(t):t.parentNode.name in this.submitted&&this.element(t.parentNode)},highlight:function(t,e,i){("radio"===t.type?this.findByName(t.name):u(t)).addClass(e).removeClass(i)},unhighlight:function(t,e,i){("radio"===t.type?this.findByName(t.name):u(t)).removeClass(e).addClass(i)}},setDefaults:function(t){u.extend(u.validator.defaults,t)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date (ISO).",number:"Please enter a valid number.",digits:"Please enter only digits.",equalTo:"Please enter the same value again.",maxlength:u.validator.format("Please enter no more than {0} characters."),minlength:u.validator.format("Please enter at least {0} characters."),rangelength:u.validator.format("Please enter a value between {0} and {1} characters long."),range:u.validator.format("Please enter a value between {0} and {1}."),max:u.validator.format("Please enter a value less than or equal to {0}."),min:u.validator.format("Please enter a value greater than or equal to {0}."),step:u.validator.format("Please enter a multiple of {0}.")},autoCreateRanges:!1,prototype:{init:function(){function t(t){var e,i,n=void 0!==u(this).attr("contenteditable")&&"false"!==u(this).attr("contenteditable");!this.form&&n&&(this.form=u(this).closest("form")[0],this.name=u(this).attr("name")),o===this.form&&(n=u.data(this.form,"validator"),e="on"+t.type.replace(/^validate/,""),(i=n.settings)[e])&&!u(this).is(i.ignore)&&i[e].call(n,this,t)}this.labelContainer=u(this.settings.errorLabelContainer),this.errorContext=this.labelContainer.length&&this.labelContainer||u(this.currentForm),this.containers=u(this.settings.errorContainer).add(this.settings.errorLabelContainer),this.submitted={},this.valueCache={},this.pendingRequest=0,this.pending={},this.invalid={},this.reset();var i,o=this.currentForm,n=this.groups={};u.each(this.settings.groups,function(i,t){"string"==typeof t&&(t=t.split(/\s/)),u.each(t,function(t,e){n[e]=i})}),i=this.settings.rules,u.each(i,function(t,e){i[t]=u.validator.normalizeRule(e)});u(this.currentForm).on("focusin.validate focusout.validate keyup.validate",[":text","[type='password']","[type='file']","select","textarea","[type='number']","[type='search']","[type='tel']","[type='url']","[type='email']","[type='datetime']","[type='date']","[type='month']","[type='week']","[type='time']","[type='datetime-local']","[type='range']","[type='color']","[type='radio']","[type='checkbox']","[contenteditable]","[type='button']"].concat(this.settings.customElements).join(", "),t).on("click.validate",["select","option","[type='radio']","[type='checkbox']"].concat(this.settings.customElements).join(", "),t),this.settings.invalidHandler&&u(this.currentForm).on("invalid-form.validate",this.settings.invalidHandler)},form:function(){return this.checkForm(),u.extend(this.submitted,this.errorMap),this.invalid=u.extend({},this.errorMap),this.valid()||u(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var t=0,e=this.currentElements=this.elements();e[t];t++)this.check(e[t]);return this.valid()},element:function(t){var e,i,n=this.clean(t),o=this.validationTargetFor(n),r=this,s=!0;return void 0===o?delete this.invalid[n.name]:(this.prepareElement(o),this.currentElements=u(o),(i=this.groups[o.name])&&u.each(this.groups,function(t,e){e===i&&t!==o.name&&(n=r.validationTargetFor(r.clean(r.findByName(t))))&&n.name in r.invalid&&(r.currentElements.push(n),s=r.check(n)&&s)}),e=!1!==this.check(o),s=s&&e,this.invalid[o.name]=!e,this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),u(t).attr("aria-invalid",!e)),s},showErrors:function(e){var i;e&&(u.extend((i=this).errorMap,e),this.errorList=u.map(this.errorMap,function(t,e){return{message:t,element:i.findByName(e)[0]}}),this.successList=u.grep(this.successList,function(t){return!(t.name in e)})),this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){u.fn.resetForm&&u(this.currentForm).resetForm(),this.invalid={},this.submitted={},this.prepareForm(),this.hideErrors();var t=this.elements().removeData("previousValue").removeAttr("aria-invalid");this.resetElements(t)},resetElements:function(t){var e;if(this.settings.unhighlight)for(e=0;t[e];e++)this.settings.unhighlight.call(this,t[e],this.settings.errorClass,""),this.findByName(t[e].name).removeClass(this.settings.validClass);else t.removeClass(this.settings.errorClass).removeClass(this.settings.validClass)},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(t){var e,i=0;for(e in t)null!=t[e]&&!1!==t[e]&&i++;return i},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(t){t.not(this.containers).text(""),this.addWrapper(t).hide()},valid:function(){return 0===this.size()},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{u(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").trigger("focus").trigger("focusin")}catch(t){}},findLastActive:function(){var e=this.lastActive;return e&&1===u.grep(this.errorList,function(t){return t.element.name===e.name}).length&&e},elements:function(){var i=this,n={};return u(this.currentForm).find(["input","select","textarea","[contenteditable]"].concat(this.settings.customElements).join(", ")).not(":submit, :reset, :image, :disabled").not(this.settings.ignore).filter(function(){var t=this.name||u(this).attr("name"),e=void 0!==u(this).attr("contenteditable")&&"false"!==u(this).attr("contenteditable");return!t&&i.settings.debug&&window.console&&console.error("%o has no name assigned",this),e&&(this.form=u(this).closest("form")[0],this.name=t),!(this.form!==i.currentForm||t in n||!i.objectLength(u(this).rules())||(n[t]=!0,0))})},clean:function(t){return u(t)[0]},errors:function(){var t=this.settings.errorClass.split(" ").join(".");return u(this.settings.errorElement+"."+t,this.errorContext)},resetInternals:function(){this.successList=[],this.errorList=[],this.errorMap={},this.toShow=u([]),this.toHide=u([])},reset:function(){this.resetInternals(),this.currentElements=u([])},prepareForm:function(){this.reset(),this.toHide=this.errors().add(this.containers)},prepareElement:function(t){this.reset(),this.toHide=this.errorsFor(t)},elementValue:function(t){var e=u(t),i=t.type,n=void 0!==e.attr("contenteditable")&&"false"!==e.attr("contenteditable");return"radio"===i||"checkbox"===i?this.findByName(t.name).filter(":checked").val():"number"===i&&void 0!==t.validity?t.validity.badInput?"NaN":e.val():(t=n?e.text():e.val(),"file"===i?"C:\\fakepath\\"===t.substr(0,12)?t.substr(12):0<=(n=t.lastIndexOf("/"))||0<=(n=t.lastIndexOf("\\"))?t.substr(n+1):t:"string"==typeof t?t.replace(/\r/g,""):t)},check:function(e){e=this.validationTargetFor(this.clean(e));var t,i,n,o,r=u(e).rules(),s=u.map(r,function(t,e){return e}).length,a=!1,l=this.elementValue(e);for(i in this.abortRequest(e),"function"==typeof r.normalizer?o=r.normalizer:"function"==typeof this.settings.normalizer&&(o=this.settings.normalizer),o&&(l=o.call(e,l),delete r.normalizer),r){n={method:i,parameters:r[i]};try{if("dependency-mismatch"===(t=u.validator.methods[i].call(this,l,e,n.parameters))&&1===s)a=!0;else{if(a=!1,"pending"===t)return void(this.toHide=this.toHide.not(this.errorsFor(e)));if(!t)return this.formatAndAdd(e,n),!1}}catch(t){throw this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+e.id+", check the '"+n.method+"' method.",t),t instanceof TypeError&&(t.message+=".  Exception occurred when checking element "+e.id+", check the '"+n.method+"' method."),t}}if(!a)return this.objectLength(r)&&this.successList.push(e),!0},customDataMessage:function(t,e){return u(t).data("msg"+e.charAt(0).toUpperCase()+e.substring(1).toLowerCase())||u(t).data("msg")},customMessage:function(t,e){t=this.settings.messages[t];return t&&(t.constructor===String?t:t[e])},findDefined:function(){for(var t=0;t<arguments.length;t++)if(void 0!==arguments[t])return arguments[t]},defaultMessage:function(t,e){var i=this.findDefined(this.customMessage(t.name,(e="string"==typeof e?{method:e}:e).method),this.customDataMessage(t,e.method),!this.settings.ignoreTitle&&t.title||void 0,u.validator.messages[e.method],"<strong>Warning: No message defined for "+t.name+"</strong>"),n=/\$?\{(\d+)\}/g;return"function"==typeof i?i=i.call(this,e.parameters,t):n.test(i)&&(i=u.validator.format(i.replace(n,"{$1}"),e.parameters)),i},formatAndAdd:function(t,e){var i=this.defaultMessage(t,e);this.errorList.push({message:i,element:t,method:e.method}),this.errorMap[t.name]=i,this.submitted[t.name]=i},addWrapper:function(t){return t=this.settings.wrapper?t.add(t.parent(this.settings.wrapper)):t},defaultShowErrors:function(){for(var t,e,i=0;this.errorList[i];i++)e=this.errorList[i],this.settings.highlight&&this.settings.highlight.call(this,e.element,this.settings.errorClass,this.settings.validClass),this.showLabel(e.element,e.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(i=0;this.successList[i];i++)this.showLabel(this.successList[i]);if(this.settings.unhighlight)for(i=0,t=this.validElements();t[i];i++)this.settings.unhighlight.call(this,t[i],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow),this.hideErrors(),this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return u(this.errorList).map(function(){return this.element})},showLabel:function(t,e){var i,n,o,r=this.errorsFor(t),s=this.idOrName(t),a=u(t).attr("aria-describedby");r.length?(r.removeClass(this.settings.validClass).addClass(this.settings.errorClass),this.settings&&this.settings.escapeHtml?r.text(e||""):r.html(e||"")):(r=u("<"+this.settings.errorElement+">").attr("id",s+"-error").addClass(this.settings.errorClass),this.settings&&this.settings.escapeHtml?r.text(e||""):r.html(e||""),n=r,this.settings.wrapper&&(n=r.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(n):this.settings.errorPlacement?this.settings.errorPlacement.call(this,n,u(t)):n.insertAfter(t),r.is("label")?r.attr("for",s):0===r.parents("label[for='"+this.escapeCssMeta(s)+"']").length&&(n=r.attr("id"),a?a.match(new RegExp("\\b"+this.escapeCssMeta(n)+"\\b"))||(a+=" "+n):a=n,u(t).attr("aria-describedby",a),i=this.groups[t.name])&&u.each((o=this).groups,function(t,e){e===i&&u("[name='"+o.escapeCssMeta(t)+"']",o.currentForm).attr("aria-describedby",r.attr("id"))})),!e&&this.settings.success&&(r.text(""),"string"==typeof this.settings.success?r.addClass(this.settings.success):this.settings.success(r,t)),this.toShow=this.toShow.add(r)},errorsFor:function(t){var e=this.escapeCssMeta(this.idOrName(t)),t=u(t).attr("aria-describedby"),e="label[for='"+e+"'], label[for='"+e+"'] *";return t&&(e=e+", #"+this.escapeCssMeta(t).replace(/\s+/g,", #")),this.errors().filter(e)},escapeCssMeta:function(t){return void 0===t?"":t.replace(/([\\!"#$%&'()*+,./:;<=>?@\[\]^`{|}~])/g,"\\$1")},idOrName:function(t){return this.groups[t.name]||!this.checkable(t)&&t.id||t.name},validationTargetFor:function(t){return this.checkable(t)&&(t=this.findByName(t.name)),u(t).not(this.settings.ignore)[0]},checkable:function(t){return/radio|checkbox/i.test(t.type)},findByName:function(t){return u(this.currentForm).find("[name='"+this.escapeCssMeta(t)+"']")},getLength:function(t,e){switch(e.nodeName.toLowerCase()){case"select":return u("option:selected",e).length;case"input":if(this.checkable(e))return this.findByName(e.name).filter(":checked").length}return t.length},depend:function(t,e){return!this.dependTypes[typeof t]||this.dependTypes[typeof t](t,e)},dependTypes:{boolean:function(t){return t},string:function(t,e){return!!u(t,e.form).length},function:function(t,e){return t(e)}},optional:function(t){var e=this.elementValue(t);return!u.validator.methods.required.call(this,e,t)&&"dependency-mismatch"},elementAjaxPort:function(t){return"validate"+t.name},startRequest:function(t){this.pending[t.name]||(this.pendingRequest++,u(t).addClass(this.settings.pendingClass),this.pending[t.name]=!0)},stopRequest:function(t,e){this.pendingRequest--,this.pendingRequest<0&&(this.pendingRequest=0),delete this.pending[t.name],u(t).removeClass(this.settings.pendingClass),e&&0===this.pendingRequest&&this.formSubmitted&&this.form()&&0===this.pendingRequest?(u(this.currentForm).trigger("submit"),this.submitButton&&u("input:hidden[name='"+this.submitButton.name+"']",this.currentForm).remove(),this.formSubmitted=!1):!e&&0===this.pendingRequest&&this.formSubmitted&&(u(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},abortRequest:function(t){var e;this.pending[t.name]&&(e=this.elementAjaxPort(t),u.ajaxAbort(e),this.pendingRequest--,this.pendingRequest<0&&(this.pendingRequest=0),delete this.pending[t.name],u(t).removeClass(this.settings.pendingClass))},previousValue:function(t,e){return e="string"==typeof e&&e||"remote",u.data(t,"previousValue")||u.data(t,"previousValue",{old:null,valid:!0,message:this.defaultMessage(t,{method:e})})},destroy:function(){this.resetForm(),u(this.currentForm).off(".validate").removeData("validator").find(".validate-equalTo-blur").off(".validate-equalTo").removeClass("validate-equalTo-blur").find(".validate-lessThan-blur").off(".validate-lessThan").removeClass("validate-lessThan-blur").find(".validate-lessThanEqual-blur").off(".validate-lessThanEqual").removeClass("validate-lessThanEqual-blur").find(".validate-greaterThanEqual-blur").off(".validate-greaterThanEqual").removeClass("validate-greaterThanEqual-blur").find(".validate-greaterThan-blur").off(".validate-greaterThan").removeClass("validate-greaterThan-blur")}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(t,e){t.constructor===String?this.classRuleSettings[t]=e:u.extend(this.classRuleSettings,t)},classRules:function(t){var e={},t=u(t).attr("class");return t&&u.each(t.split(" "),function(){this in u.validator.classRuleSettings&&u.extend(e,u.validator.classRuleSettings[this])}),e},normalizeAttributeRule:function(t,e,i,n){(n=/min|max|step/.test(i)&&(null===e||/number|range|text/.test(e))&&(n=Number(n),isNaN(n))?void 0:n)||0===n?t[i]=n:e===i&&"range"!==e&&(t["date"===e?"dateISO":i]=!0)},attributeRules:function(t){var e,i,n={},o=u(t),r=t.getAttribute("type");for(e in u.validator.methods)i="required"===e?(i=t.getAttribute(e),""===i&&(i=!0),!!i):o.attr(e),this.normalizeAttributeRule(n,r,e,i);return n.maxlength&&/-1|2147483647|524288/.test(n.maxlength)&&delete n.maxlength,n},dataRules:function(t){var e,i,n={},o=u(t),r=t.getAttribute("type");for(e in u.validator.methods)i=o.data("rule"+e.charAt(0).toUpperCase()+e.substring(1).toLowerCase()),""===i&&(i=!0),this.normalizeAttributeRule(n,r,e,i);return n},staticRules:function(t){var e={},i=u.data(t.form,"validator");return e=i.settings.rules?u.validator.normalizeRule(i.settings.rules[t.name])||{}:e},normalizeRules:function(n,o){return u.each(n,function(t,e){if(!1===e)delete n[t];else if(e.param||e.depends){var i=!0;switch(typeof e.depends){case"string":i=!!u(e.depends,o.form).length;break;case"function":i=e.depends.call(o,o)}i?n[t]=void 0===e.param||e.param:(u.data(o.form,"validator").resetElements(u(o)),delete n[t])}}),u.each(n,function(t,e){n[t]="function"==typeof e&&"normalizer"!==t?e(o):e}),u.each(["minlength","maxlength"],function(){n[this]&&(n[this]=Number(n[this]))}),u.each(["rangelength","range"],function(){var t;n[this]&&(Array.isArray(n[this])?n[this]=[Number(n[this][0]),Number(n[this][1])]:"string"==typeof n[this]&&(t=n[this].replace(/[\[\]]/g,"").split(/[\s,]+/),n[this]=[Number(t[0]),Number(t[1])]))}),u.validator.autoCreateRanges&&(null!=n.min&&null!=n.max&&(n.range=[n.min,n.max],delete n.min,delete n.max),null!=n.minlength)&&null!=n.maxlength&&(n.rangelength=[n.minlength,n.maxlength],delete n.minlength,delete n.maxlength),n},normalizeRule:function(t){var e;return"string"==typeof t&&(e={},u.each(t.split(/\s/),function(){e[this]=!0}),t=e),t},addMethod:function(t,e,i){u.validator.methods[t]=e,u.validator.messages[t]=void 0!==i?i:u.validator.messages[t],e.length<3&&u.validator.addClassRules(t,u.validator.normalizeRule(t))},methods:{required:function(t,e,i){return this.depend(i,e)?"select"===e.nodeName.toLowerCase()?(i=u(e).val())&&0<i.length:this.checkable(e)?0<this.getLength(t,e):null!=t&&0<t.length:"dependency-mismatch"},email:function(t,e){return this.optional(e)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)},url:function(t,e){return this.optional(e)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:(?:[^\]\[?\/<~#`!@$^&*()+=}|:";',>{ ]|%[0-9A-Fa-f]{2})+(?::(?:[^\]\[?\/<~#`!@$^&*()+=}|:";',>{ ]|%[0-9A-Fa-f]{2})*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(t)},date:(i=!1,function(t,e){return i||(i=!0,this.settings.debug&&window.console&&console.warn("The `date` method is deprecated and will be removed in version '2.0.0'.\nPlease don't use it, since it relies on the Date constructor, which\nbehaves very differently across browsers and locales. Use `dateISO`\ninstead or one of the locale specific methods in `localizations/`\nand `additional-methods.js`.")),this.optional(e)||!/Invalid|NaN/.test(new Date(t).toString())}),dateISO:function(t,e){return this.optional(e)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(t)},number:function(t,e){return this.optional(e)||/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:-?\.\d+)?$/.test(t)},digits:function(t,e){return this.optional(e)||/^\d+$/.test(t)},minlength:function(t,e,i){t=Array.isArray(t)?t.length:this.getLength(t,e);return this.optional(e)||i<=t},maxlength:function(t,e,i){t=Array.isArray(t)?t.length:this.getLength(t,e);return this.optional(e)||t<=i},rangelength:function(t,e,i){t=Array.isArray(t)?t.length:this.getLength(t,e);return this.optional(e)||t>=i[0]&&t<=i[1]},min:function(t,e,i){return this.optional(e)||i<=t},max:function(t,e,i){return this.optional(e)||t<=i},range:function(t,e,i){return this.optional(e)||t>=i[0]&&t<=i[1]},step:function(t,e,i){function n(t){return(t=(""+t).match(/(?:\.(\d+))?$/))&&t[1]?t[1].length:0}function o(t){return Math.round(t*Math.pow(10,r))}var r,s=u(e).attr("type"),a="Step attribute on input type "+s+" is not supported.",l=new RegExp("\\b"+s+"\\b"),c=!0;if(s&&!l.test(["text","number","range"].join()))throw new Error(a);return r=n(i),(n(t)>r||o(t)%o(i)!=0)&&(c=!1),this.optional(e)||c},equalTo:function(t,e,i){i=u(i);return this.settings.onfocusout&&i.not(".validate-equalTo-blur").length&&i.addClass("validate-equalTo-blur").on("blur.validate-equalTo",function(){u(e).valid()}),t===i.val()},remote:function(n,o,t,r){if(this.optional(o))return"dependency-mismatch";r="string"==typeof r&&r||"remote";var s,e,a=this.previousValue(o,r);return this.settings.messages[o.name]||(this.settings.messages[o.name]={}),a.originalMessage=a.originalMessage||this.settings.messages[o.name][r],this.settings.messages[o.name][r]=a.message,e=u.param(u.extend({data:n},(t="string"==typeof t?{url:t}:t).data)),null!==a.valid&&a.old===e?a.valid:(a.old=e,a.valid=null,(s=this).startRequest(o),(e={})[o.name]=n,u.ajax(u.extend(!0,{mode:"abort",port:this.elementAjaxPort(o),dataType:"json",data:e,context:s.currentForm,success:function(t){var e,i=!0===t||"true"===t;s.settings.messages[o.name][r]=a.originalMessage,i?(e=s.formSubmitted,s.toHide=s.errorsFor(o),s.formSubmitted=e,s.successList.push(o),s.invalid[o.name]=!1,s.showErrors()):(e={},t=t||s.defaultMessage(o,{method:r,parameters:n}),e[o.name]=a.message=t,s.invalid[o.name]=!0,s.showErrors(e)),a.valid=i,s.stopRequest(o,i)}},t)),"pending")}}});var n,o={};return u.ajaxPrefilter?u.ajaxPrefilter(function(t,e,i){var n=t.port;"abort"===t.mode&&(u.ajaxAbort(n),o[n]=i)}):(n=u.ajax,u.ajax=function(t){var e=("mode"in t?t:u.ajaxSettings).mode,t=("port"in t?t:u.ajaxSettings).port;return"abort"===e?(u.ajaxAbort(t),o[t]=n.apply(this,arguments),o[t]):n.apply(this,arguments)}),u.ajaxAbort=function(t){o[t]&&(o[t].abort(),delete o[t])},u}),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&"object"==typeof module.exports?exports=t(require("jquery")):t(jQuery)})(function(e){e.easing.jswing=e.easing.swing;var i=Math.pow,n=Math.sqrt,o=Math.sin,r=Math.cos,s=Math.PI,a=1.70158,l=2.5949095,c=2*s/3,u=2*s/4.5;function h(t){var e=7.5625;return t<1/2.75?e*t*t:t<2/2.75?e*(t-=1.5/2.75)*t+.75:t<2.5/2.75?e*(t-=2.25/2.75)*t+.9375:e*(t-=2.625/2.75)*t+.984375}e.extend(e.easing,{def:"easeOutQuad",swing:function(t){return e.easing[e.easing.def](t)},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return 1-(1-t)*(1-t)},easeInOutQuad:function(t){return t<.5?2*t*t:1-i(-2*t+2,2)/2},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return 1-i(1-t,3)},easeInOutCubic:function(t){return t<.5?4*t*t*t:1-i(-2*t+2,3)/2},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return 1-i(1-t,4)},easeInOutQuart:function(t){return t<.5?8*t*t*t*t:1-i(-2*t+2,4)/2},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return 1-i(1-t,5)},easeInOutQuint:function(t){return t<.5?16*t*t*t*t*t:1-i(-2*t+2,5)/2},easeInSine:function(t){return 1-r(t*s/2)},easeOutSine:function(t){return o(t*s/2)},easeInOutSine:function(t){return-(r(s*t)-1)/2},easeInExpo:function(t){return 0===t?0:i(2,10*t-10)},easeOutExpo:function(t){return 1===t?1:1-i(2,-10*t)},easeInOutExpo:function(t){return 0===t?0:1===t?1:t<.5?i(2,20*t-10)/2:(2-i(2,-20*t+10))/2},easeInCirc:function(t){return 1-n(1-i(t,2))},easeOutCirc:function(t){return n(1-i(t-1,2))},easeInOutCirc:function(t){return t<.5?(1-n(1-i(2*t,2)))/2:(n(1-i(-2*t+2,2))+1)/2},easeInElastic:function(t){return 0===t?0:1===t?1:-i(2,10*t-10)*o((10*t-10.75)*c)},easeOutElastic:function(t){return 0===t?0:1===t?1:i(2,-10*t)*o((10*t-.75)*c)+1},easeInOutElastic:function(t){return 0===t?0:1===t?1:t<.5?-(i(2,20*t-10)*o((20*t-11.125)*u))/2:i(2,-20*t+10)*o((20*t-11.125)*u)/2+1},easeInBack:function(t){return 2.70158*t*t*t-a*t*t},easeOutBack:function(t){return 1*********i(t-1,3)+a*i(t-1,2)},easeInOutBack:function(t){return t<.5?i(2*t,2)*(7.189819*t-l)/2:(i(2*t-2,2)*((1+l)*(2*t-2)+l)+2)/2},easeInBounce:function(t){return 1-h(1-t)},easeOutBounce:h,easeInOutBounce:function(t){return t<.5?(1-h(1-2*t))/2:(1+h(2*t-1))/2}})}),((t,e)=>{"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?e(require("jquery")):e(t.jQuery)})(this,function(n){n.fn.appear=function(i,t){var d=n.extend({data:void 0,one:!0,accX:0,accY:0},t);return this.each(function(){var u,e,t,h=n(this);h.appeared=!1,i?(u=n(window),e=function(){var t,e,i,n,o,r,s,a,l,c;h.is(":visible")&&(t=u.scrollLeft(),e=u.scrollTop(),i=(n=h.offset()).left,n=n.top,o=d.accX,r=d.accY,s=h.height(),a=u.height(),l=h.width(),c=u.width(),e<=n+s+r)&&n<=e+a+r&&t<=i+l+o&&i<=t+c+o?h.appeared||h.trigger("appear",d.data):h.appeared=!1},t=function(){var t;h.appeared=!0,d.one&&(u.unbind("scroll",e),0<=(t=n.inArray(e,n.fn.appear.checks)))&&n.fn.appear.checks.splice(t,1),i.apply(this,arguments)},d.one?h.one("appear",d.data,t):h.bind("appear",d.data,t),u.scroll(e),n.fn.appear.checks.push(e),e()):h.trigger("appear",d.data)})},n.extend(n.fn.appear,{checks:[],timeout:null,checkAll:function(){var t=n.fn.appear.checks.length;if(0<t)for(;t--;)n.fn.appear.checks[t]()},run:function(){n.fn.appear.timeout&&clearTimeout(n.fn.appear.timeout),n.fn.appear.timeout=setTimeout(n.fn.appear.checkAll,20)}}),n.each(["append","prepend","after","before","attr","removeAttr","addClass","removeClass","toggleClass","remove","css","show","hide"],function(t,e){var i=n.fn[e];i&&(n.fn[e]=function(){var t=i.apply(this,arguments);return n.fn.appear.run(),t})})}),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(jQuery)})(function(i){function s(t,s){function i(){s.scaleColor&&c(),s.trackColor&&l(s.trackColor,s.trackWidth||s.lineWidth,1)}var n,e=document.createElement("canvas"),o=(t.appendChild(e),"object"==typeof G_vmlCanvasManager&&G_vmlCanvasManager.initElement(e),e.getContext("2d")),r=(e.width=e.height=s.size,1),a=(1<window.devicePixelRatio&&(r=window.devicePixelRatio,e.style.width=e.style.height=[s.size,"px"].join(""),e.width=e.height=s.size*r,o.scale(r,r)),o.translate(s.size/2,s.size/2),o.rotate((s.rotate/180-.5)*Math.PI),(s.size-s.lineWidth)/2),l=(s.scaleColor&&s.scaleLength&&(a-=s.scaleLength+2),Date.now=Date.now||function(){return+new Date},function(t,e,i){var n=(i=Math.min(Math.max(-1,i||0),1))<=0;o.beginPath(),o.arc(0,0,a,0,2*Math.PI*i,n),o.strokeStyle=t,o.lineWidth=e,o.stroke()}),c=function(){var t,e;o.lineWidth=1,o.fillStyle=s.scaleColor,o.save();for(var i=24;0<i;--i)t=i%6==0?(e=s.scaleLength,0):(e=.6*s.scaleLength,s.scaleLength-e),o.fillRect(-s.size/2+t,0,e,1),o.rotate(Math.PI/12);o.restore()},u=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)};this.getCanvas=function(){return e},this.getCtx=function(){return o},this.clear=function(){o.clearRect(s.size/-2,s.size/-2,s.size,s.size)},this.draw=function(t){var e;s.scaleColor||s.trackColor?o.getImageData&&o.putImageData?n?o.putImageData(n,0,0):(i(),n=o.getImageData(0,0,s.size*r,s.size*r)):(this.clear(),i()):this.clear(),o.lineCap=s.lineCap,e="function"==typeof s.barColor?s.barColor(t):s.barColor,l(e,s.lineWidth,t/100)}.bind(this),this.animate=function(i,n){var o=Date.now(),r=(s.onStart(i,n),function(){var t=Math.min(Date.now()-o,s.animate.duration),e=s.easing(this,t,i,n-i,s.animate.duration);this.draw(e),s.onStep(i,n,e),t>=s.animate.duration?s.onStop(i,n):u(r)}.bind(this));u(r)}.bind(this)}function n(e,i){var n={barColor:"#ef1e25",trackColor:"#f9f9f9",scaleColor:"#dfe0e0",scaleLength:5,lineCap:"round",lineWidth:3,trackWidth:void 0,size:110,rotate:0,animate:{duration:1e3,enabled:!0},easing:function(t,e,i,n,o){return(e/=o/2)<1?n/2*e*e+i:-n/2*(--e*(e-2)-1)+i},onStart:function(t,e){},onStep:function(t,e,i){},onStop:function(t,e){}},o=(n.renderer=s,{}),r=0,t=function(){for(var t in this.el=e,this.options=o,n)n.hasOwnProperty(t)&&(o[t]=(i&&void 0!==i[t]?i:n)[t],"function"==typeof o[t])&&(o[t]=o[t].bind(this));"string"==typeof o.easing&&"undefined"!=typeof jQuery&&jQuery.isFunction(jQuery.easing[o.easing])?o.easing=jQuery.easing[o.easing]:o.easing=n.easing,"number"==typeof o.animate&&(o.animate={duration:o.animate,enabled:!0}),"boolean"!=typeof o.animate||o.animate||(o.animate={duration:1e3,enabled:o.animate}),this.renderer=new o.renderer(e,o),this.renderer.draw(r),e.dataset&&e.dataset.percent?this.update(parseFloat(e.dataset.percent)):e.getAttribute&&e.getAttribute("data-percent")&&this.update(parseFloat(e.getAttribute("data-percent")))}.bind(this);this.update=function(t){return t=parseFloat(t),o.animate.enabled?this.renderer.animate(r,t):this.renderer.draw(t),r=t,this}.bind(this),this.disableAnimation=function(){return o.animate.enabled=!1,this},this.enableAnimation=function(){return o.animate.enabled=!0,this},t()}i.fn.easyPieChart=function(e){return this.each(function(){var t;i.data(this,"easyPieChart")||(t=i.extend({},e,i(this).data()),i.data(this,"easyPieChart",new n(this,t)))})}}),((e,i)=>{"function"==typeof define&&define.amd?define("jquery-bridget/jquery-bridget",["jquery"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("jquery")):e.jQueryBridget=i(e,e.jQuery)})(window,function(t,e){function i(l,c,u){(u=u||e||t.jQuery)&&(c.prototype.option||(c.prototype.option=function(t){u.isPlainObject(t)&&(this.options=u.extend(!0,this.options,t))}),u.fn[l]=function(t){var e,n,o,r,s,a;return"string"==typeof t?(e=h.call(arguments,1),o=e,s="$()."+l+'("'+(n=t)+'")',(e=this).each(function(t,e){var i,e=u.data(e,l);e?(i=e[n])&&"_"!=n.charAt(0)?(i=i.apply(e,o),r=void 0===r?i:r):d(s+" is not a valid method"):d(l+" not initialized. Cannot call methods, i.e. "+s)}),void 0!==r?r:e):(a=t,this.each(function(t,e){var i=u.data(e,l);i?(i.option(a),i._init()):(i=new c(e,a),u.data(e,l,i))}),this)},n(u))}function n(t){t&&!t.bridget&&(t.bridget=i)}var h=Array.prototype.slice,o=t.console,d=void 0===o?function(){}:function(t){o.error(t)};return n(e||t.jQuery),i}),((t,e)=>{"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",e):"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()})("undefined"!=typeof window?window:this,function(){function t(){}var e=t.prototype;return e.on=function(t,e){var i;if(t&&e)return-1==(i=(i=this._events=this._events||{})[t]=i[t]||[]).indexOf(e)&&i.push(e),this},e.once=function(t,e){var i;if(t&&e)return this.on(t,e),((i=this._onceEvents=this._onceEvents||{})[t]=i[t]||{})[e]=!0,this},e.off=function(t,e){t=this._events&&this._events[t];if(t&&t.length)return-1!=(e=t.indexOf(e))&&t.splice(e,1),this},e.emitEvent=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){i=i.slice(0),e=e||[];for(var n=this._onceEvents&&this._onceEvents[t],o=0;o<i.length;o++){var r=i[o];n&&n[r]&&(this.off(t,r),delete n[r]),r.apply(this,e)}return this}},e.allOff=function(){delete this._events,delete this._onceEvents},t}),((t,e)=>{"function"==typeof define&&define.amd?define("get-size/get-size",e):"object"==typeof module&&module.exports?module.exports=e():t.getSize=e()})(window,function(){function g(t){var e=parseFloat(t);return-1==t.indexOf("%")&&!isNaN(e)&&e}function v(t){t=getComputedStyle(t);return t||e("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),t}function y(t){if(x||(x=!0,(u=document.createElement("div")).style.width="200px",u.style.padding="1px 2px 3px 4px",u.style.borderStyle="solid",u.style.borderWidth="1px 2px 3px 4px",u.style.boxSizing="border-box",(c=document.body||document.documentElement).appendChild(u),r=v(u),b=200==Math.round(g(r.width)),y.isBoxSizeOuter=b,c.removeChild(u)),(t="string"==typeof t?document.querySelector(t):t)&&"object"==typeof t&&t.nodeType){var e=v(t);if("none"==e.display){for(var i={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},n=0;n<w;n++)i[_[n]]=0;return i}var o={};o.width=t.offsetWidth,o.height=t.offsetHeight;for(var r=o.isBorderBox="border-box"==e.boxSizing,s=0;s<w;s++){var a=_[s],l=e[a],l=parseFloat(l);o[a]=isNaN(l)?0:l}var c=o.paddingLeft+o.paddingRight,u=o.paddingTop+o.paddingBottom,t=o.marginLeft+o.marginRight,h=o.marginTop+o.marginBottom,d=o.borderLeftWidth+o.borderRightWidth,p=o.borderTopWidth+o.borderBottomWidth,f=r&&b,m=g(e.width),m=(!1!==m&&(o.width=m+(f?0:c+d)),g(e.height));return!1!==m&&(o.height=m+(f?0:u+p)),o.innerWidth=o.width-(c+d),o.innerHeight=o.height-(u+p),o.outerWidth=o.width+t,o.outerHeight=o.height+h,o}var u,c,r}var b,e="undefined"==typeof console?function(){}:function(t){console.error(t)},_=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],w=_.length,x=!1;return y}),((t,e)=>{"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",e):"object"==typeof module&&module.exports?module.exports=e():t.matchesSelector=e()})(window,function(){var i=(()=>{var t=window.Element.prototype;if(t.matches)return"matches";if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],i=0;i<e.length;i++){var n=e[i]+"MatchesSelector";if(t[n])return n}})();return function(t,e){return t[i](e)}}),((e,i)=>{"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("desandro-matches-selector")):e.fizzyUIUtils=i(e,e.matchesSelector)})(window,function(i,r){var l={extend:function(t,e){for(var i in e)t[i]=e[i];return t},modulo:function(t,e){return(t%e+e)%e}},e=Array.prototype.slice,c=(l.makeArray=function(t){return Array.isArray(t)?t:null==t?[]:"object"==typeof t&&"number"==typeof t.length?e.call(t):[t]},l.removeFrom=function(t,e){e=t.indexOf(e);-1!=e&&t.splice(e,1)},l.getParent=function(t,e){for(;t.parentNode&&t!=document.body;)if(r(t=t.parentNode,e))return t},l.getQueryElement=function(t){return"string"==typeof t?document.querySelector(t):t},l.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},l.filterFindElements=function(t,n){t=l.makeArray(t);var o=[];return t.forEach(function(t){if(t instanceof HTMLElement)if(n){r(t,n)&&o.push(t);for(var e=t.querySelectorAll(n),i=0;i<e.length;i++)o.push(e[i])}else o.push(t)}),o},l.debounceMethod=function(t,e,n){n=n||100;var o=t.prototype[e],r=e+"Timeout";t.prototype[e]=function(){var t=this[r],e=(clearTimeout(t),arguments),i=this;this[r]=setTimeout(function(){o.apply(i,e),delete i[r]},n)}},l.docReady=function(t){var e=document.readyState;"complete"==e||"interactive"==e?setTimeout(t):document.addEventListener("DOMContentLoaded",t)},l.toDashed=function(t){return t.replace(/(.)([A-Z])/g,function(t,e,i){return e+"-"+i}).toLowerCase()},i.console);return l.htmlInit=function(s,a){l.docReady(function(){var t=l.toDashed(a),n="data-"+t,e=document.querySelectorAll("["+n+"]"),t=document.querySelectorAll(".js-"+t),e=l.makeArray(e).concat(l.makeArray(t)),o=n+"-options",r=i.jQuery;e.forEach(function(e){var t,i=e.getAttribute(n)||e.getAttribute(o);try{t=i&&JSON.parse(i)}catch(t){return void(c&&c.error("Error parsing "+n+" on "+e.className+": "+t))}i=new s(e,t);r&&r.data(e,a,i)})})},l}),((t,e)=>{"function"==typeof define&&define.amd?define("outlayer/item",["ev-emitter/ev-emitter","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("ev-emitter"),require("get-size")):(t.Outlayer={},t.Outlayer.Item=e(t.EvEmitter,t.getSize))})(window,function(t,e){function i(t,e){t&&(this.element=t,this.layout=e,this.position={x:0,y:0},this._create())}var n=document.documentElement.style,o="string"==typeof n.transition?"transition":"WebkitTransition",n="string"==typeof n.transform?"transform":"WebkitTransform",r={WebkitTransition:"webkitTransitionEnd",transition:"transitionend"}[o],s={transform:n,transition:o,transitionDuration:o+"Duration",transitionProperty:o+"Property",transitionDelay:o+"Delay"},t=i.prototype=Object.create(t.prototype),a=(t.constructor=i,t._create=function(){this._transn={ingProperties:{},clean:{},onEnd:{}},this.css({position:"absolute"})},t.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},t.getSize=function(){this.size=e(this.element)},t.css=function(t){var e,i=this.element.style;for(e in t)i[s[e]||e]=t[e]},t.getPosition=function(){var t=getComputedStyle(this.element),e=this.layout._getOption("originLeft"),i=this.layout._getOption("originTop"),n=t[e?"left":"right"],t=t[i?"top":"bottom"],o=parseFloat(n),r=parseFloat(t),s=this.layout.size;-1!=n.indexOf("%")&&(o=o/100*s.width),-1!=t.indexOf("%")&&(r=r/100*s.height),o=isNaN(o)?0:o,r=isNaN(r)?0:r,o-=e?s.paddingLeft:s.paddingRight,r-=i?s.paddingTop:s.paddingBottom,this.position.x=o,this.position.y=r},t.layoutPosition=function(){var t=this.layout.size,e={},i=this.layout._getOption("originLeft"),n=this.layout._getOption("originTop"),o=i?"right":"left",r=this.position.x+t[i?"paddingLeft":"paddingRight"],i=(e[i?"left":"right"]=this.getXValue(r),e[o]="",n?"paddingTop":"paddingBottom"),r=n?"bottom":"top",o=this.position.y+t[i];e[n?"top":"bottom"]=this.getYValue(o),e[r]="",this.css(e),this.emitEvent("layout",[this])},t.getXValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&!e?t/this.layout.size.width*100+"%":t+"px"},t.getYValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&e?t/this.layout.size.height*100+"%":t+"px"},t._transitionTo=function(t,e){this.getPosition();var i=this.position.x,n=this.position.y,o=t==this.position.x&&e==this.position.y;this.setPosition(t,e),o&&!this.isTransitioning?this.layoutPosition():((o={}).transform=this.getTranslate(t-i,e-n),this.transition({to:o,onTransitionEnd:{transform:this.layoutPosition},isCleaning:!0}))},t.getTranslate=function(t,e){return"translate3d("+(t=this.layout._getOption("originLeft")?t:-t)+"px, "+(e=this.layout._getOption("originTop")?e:-e)+"px, 0)"},t.goTo=function(t,e){this.setPosition(t,e),this.layoutPosition()},t.moveTo=t._transitionTo,t.setPosition=function(t,e){this.position.x=parseFloat(t),this.position.y=parseFloat(e)},t._nonTransition=function(t){for(var e in this.css(t.to),t.isCleaning&&this._removeStyles(t.to),t.onTransitionEnd)t.onTransitionEnd[e].call(this)},t.transition=function(t){if(parseFloat(this.layout.options.transitionDuration)){var e,i=this._transn;for(e in t.onTransitionEnd)i.onEnd[e]=t.onTransitionEnd[e];for(e in t.to)i.ingProperties[e]=!0,t.isCleaning&&(i.clean[e]=!0);t.from&&(this.css(t.from),this.element.offsetHeight,0),this.enableTransition(t.to),this.css(t.to),this.isTransitioning=!0}else this._nonTransition(t)},"opacity,"+n.replace(/([A-Z])/g,function(t){return"-"+t.toLowerCase()})),l=(t.enableTransition=function(){var t;this.isTransitioning||(t=this.layout.options.transitionDuration,this.css({transitionProperty:a,transitionDuration:t="number"==typeof t?t+"ms":t,transitionDelay:this.staggerDelay||0}),this.element.addEventListener(r,this,!1))},t.onwebkitTransitionEnd=function(t){this.ontransitionend(t)},t.onotransitionend=function(t){this.ontransitionend(t)},{"-webkit-transform":"transform"}),c=(t.ontransitionend=function(t){var e,i;t.target===this.element&&(e=this._transn,i=l[t.propertyName]||t.propertyName,delete e.ingProperties[i],(t=>{for(var e in t)return;return 1})(e.ingProperties)&&this.disableTransition(),i in e.clean&&(this.element.style[t.propertyName]="",delete e.clean[i]),i in e.onEnd&&(e.onEnd[i].call(this),delete e.onEnd[i]),this.emitEvent("transitionEnd",[this]))},t.disableTransition=function(){this.removeTransitionStyles(),this.element.removeEventListener(r,this,!1),this.isTransitioning=!1},t._removeStyles=function(t){var e,i={};for(e in t)i[e]="";this.css(i)},{transitionProperty:"",transitionDuration:"",transitionDelay:""});return t.removeTransitionStyles=function(){this.css(c)},t.stagger=function(t){t=isNaN(t)?0:t,this.staggerDelay=t+"ms"},t.removeElem=function(){this.element.parentNode.removeChild(this.element),this.css({display:""}),this.emitEvent("remove",[this])},t.remove=function(){return o&&parseFloat(this.layout.options.transitionDuration)?(this.once("transitionEnd",function(){this.removeElem()}),void this.hide()):void this.removeElem()},t.reveal=function(){delete this.isHidden,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("visibleStyle")]=this.onRevealTransitionEnd,this.transition({from:t.hiddenStyle,to:t.visibleStyle,isCleaning:!0,onTransitionEnd:e})},t.onRevealTransitionEnd=function(){this.isHidden||this.emitEvent("reveal")},t.getHideRevealTransitionEndProperty=function(t){var e,t=this.layout.options[t];if(t.opacity)return"opacity";for(e in t)return e},t.hide=function(){this.isHidden=!0,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("hiddenStyle")]=this.onHideTransitionEnd,this.transition({from:t.visibleStyle,to:t.hiddenStyle,isCleaning:!0,onTransitionEnd:e})},t.onHideTransitionEnd=function(){this.isHidden&&(this.css({display:"none"}),this.emitEvent("hide"))},t.destroy=function(){this.css({position:"",left:"",right:"",top:"",bottom:"",transition:"",transform:""})},i}),((o,r)=>{"function"==typeof define&&define.amd?define("outlayer/outlayer",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./item"],function(t,e,i,n){return r(o,t,e,i,n)}):"object"==typeof module&&module.exports?module.exports=r(o,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./item")):o.Outlayer=r(o,o.EvEmitter,o.getSize,o.fizzyUIUtils,o.Outlayer.Item)})(window,function(t,e,o,n,r){function s(t,e){var i=n.getQueryElement(t);i?(this.element=i,c&&(this.$element=c(this.element)),this.options=n.extend({},this.constructor.defaults),this.option(e),e=++u,this.element.outlayerGUID=e,(h[e]=this)._create(),this._getOption("initLayout")&&this.layout()):l&&l.error("Bad element for "+this.constructor.namespace+": "+(i||t))}function a(t){function e(){t.apply(this,arguments)}return(e.prototype=Object.create(t.prototype)).constructor=e}function i(){}var l=t.console,c=t.jQuery,u=0,h={},d=(s.namespace="outlayer",s.Item=r,s.defaults={containerStyle:{position:"relative"},initLayout:!0,originLeft:!0,originTop:!0,resize:!0,resizeContainer:!0,transitionDuration:"0.4s",hiddenStyle:{opacity:0,transform:"scale(0.001)"},visibleStyle:{opacity:1,transform:"scale(1)"}},s.prototype),p=(n.extend(d,e.prototype),d.option=function(t){n.extend(this.options,t)},d._getOption=function(t){var e=this.constructor.compatOptions[t];return e&&void 0!==this.options[e]?this.options[e]:this.options[t]},s.compatOptions={initLayout:"isInitLayout",horizontal:"isHorizontal",layoutInstant:"isLayoutInstant",originLeft:"isOriginLeft",originTop:"isOriginTop",resize:"isResizeBound",resizeContainer:"isResizingContainer"},d._create=function(){this.reloadItems(),this.stamps=[],this.stamp(this.options.stamp),n.extend(this.element.style,this.options.containerStyle),this._getOption("resize")&&this.bindResize()},d.reloadItems=function(){this.items=this._itemize(this.element.children)},d._itemize=function(t){for(var e=this._filterFindItemElements(t),i=this.constructor.Item,n=[],o=0;o<e.length;o++){var r=new i(e[o],this);n.push(r)}return n},d._filterFindItemElements=function(t){return n.filterFindElements(t,this.options.itemSelector)},d.getItemElements=function(){return this.items.map(function(t){return t.element})},d.layout=function(){this._resetLayout(),this._manageStamps();var t=this._getOption("layoutInstant"),t=void 0!==t?t:!this._isLayoutInited;this.layoutItems(this.items,t),this._isLayoutInited=!0},d._init=d.layout,d._resetLayout=function(){this.getSize()},d.getSize=function(){this.size=o(this.element)},d._getMeasurement=function(t,e){var i,n=this.options[t];n?("string"==typeof n?i=this.element.querySelector(n):n instanceof HTMLElement&&(i=n),this[t]=i?o(i)[e]:n):this[t]=0},d.layoutItems=function(t,e){t=this._getItemsForLayout(t),this._layoutItems(t,e),this._postLayout()},d._getItemsForLayout=function(t){return t.filter(function(t){return!t.isIgnored})},d._layoutItems=function(t,i){var n;this._emitCompleteOnItems("layout",t),t&&t.length&&(n=[],t.forEach(function(t){var e=this._getItemLayoutPosition(t);e.item=t,e.isInstant=i||t.isLayoutInstant,n.push(e)},this),this._processLayoutQueue(n))},d._getItemLayoutPosition=function(){return{x:0,y:0}},d._processLayoutQueue=function(t){this.updateStagger(),t.forEach(function(t,e){this._positionItem(t.item,t.x,t.y,t.isInstant,e)},this)},d.updateStagger=function(){var t,e=this.options.stagger;return null==e?void(this.stagger=0):(this.stagger="number"==typeof(e=e)?e:(t=(e=e.match(/(^\d*\.?\d*)(\w*)/))&&e[1],e=e&&e[2],t.length?(t=parseFloat(t))*(p[e]||1):0),this.stagger)},d._positionItem=function(t,e,i,n,o){n?t.goTo(e,i):(t.stagger(o*this.stagger),t.moveTo(e,i))},d._postLayout=function(){this.resizeContainer()},d.resizeContainer=function(){var t;this._getOption("resizeContainer")&&(t=this._getContainerSize())&&(this._setContainerMeasure(t.width,!0),this._setContainerMeasure(t.height,!1))},d._getContainerSize=i,d._setContainerMeasure=function(t,e){var i;void 0!==t&&((i=this.size).isBorderBox&&(t+=e?i.paddingLeft+i.paddingRight+i.borderLeftWidth+i.borderRightWidth:i.paddingBottom+i.paddingTop+i.borderTopWidth+i.borderBottomWidth),t=Math.max(t,0),this.element.style[e?"width":"height"]=t+"px")},d._emitCompleteOnItems=function(e,t){function i(){r.dispatchEvent(e+"Complete",null,[t])}function n(){++o==s&&i()}var o,r=this,s=t.length;t&&s?(o=0,t.forEach(function(t){t.once(e,n)})):i()},d.dispatchEvent=function(t,e,i){var n=e?[e].concat(i):i;this.emitEvent(t,n),c&&(this.$element=this.$element||c(this.element),e?((n=c.Event(e)).type=t,this.$element.trigger(n,i)):this.$element.trigger(t,i))},d.ignore=function(t){t=this.getItem(t);t&&(t.isIgnored=!0)},d.unignore=function(t){t=this.getItem(t);t&&delete t.isIgnored},d.stamp=function(t){(t=this._find(t))&&(this.stamps=this.stamps.concat(t),t.forEach(this.ignore,this))},d.unstamp=function(t){(t=this._find(t))&&t.forEach(function(t){n.removeFrom(this.stamps,t),this.unignore(t)},this)},d._find=function(t){if(t)return"string"==typeof t&&(t=this.element.querySelectorAll(t)),n.makeArray(t)},d._manageStamps=function(){this.stamps&&this.stamps.length&&(this._getBoundingRect(),this.stamps.forEach(this._manageStamp,this))},d._getBoundingRect=function(){var t=this.element.getBoundingClientRect(),e=this.size;this._boundingRect={left:t.left+e.paddingLeft+e.borderLeftWidth,top:t.top+e.paddingTop+e.borderTopWidth,right:t.right-(e.paddingRight+e.borderRightWidth),bottom:t.bottom-(e.paddingBottom+e.borderBottomWidth)}},d._manageStamp=i,d._getElementOffset=function(t){var e=t.getBoundingClientRect(),i=this._boundingRect,t=o(t);return{left:e.left-i.left-t.marginLeft,top:e.top-i.top-t.marginTop,right:i.right-e.right-t.marginRight,bottom:i.bottom-e.bottom-t.marginBottom}},d.handleEvent=n.handleEvent,d.bindResize=function(){t.addEventListener("resize",this),this.isResizeBound=!0},d.unbindResize=function(){t.removeEventListener("resize",this),this.isResizeBound=!1},d.onresize=function(){this.resize()},n.debounceMethod(s,"onresize",100),d.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&this.layout()},d.needsResizeLayout=function(){var t=o(this.element);return this.size&&t&&t.innerWidth!==this.size.innerWidth},d.addItems=function(t){t=this._itemize(t);return t.length&&(this.items=this.items.concat(t)),t},d.appended=function(t){t=this.addItems(t);t.length&&(this.layoutItems(t,!0),this.reveal(t))},d.prepended=function(t){var e,t=this._itemize(t);t.length&&(e=this.items.slice(0),this.items=t.concat(e),this._resetLayout(),this._manageStamps(),this.layoutItems(t,!0),this.reveal(t),this.layoutItems(e))},d.reveal=function(t){var i;this._emitCompleteOnItems("reveal",t),t&&t.length&&(i=this.updateStagger(),t.forEach(function(t,e){t.stagger(e*i),t.reveal()}))},d.hide=function(t){var i;this._emitCompleteOnItems("hide",t),t&&t.length&&(i=this.updateStagger(),t.forEach(function(t,e){t.stagger(e*i),t.hide()}))},d.revealItemElements=function(t){t=this.getItems(t);this.reveal(t)},d.hideItemElements=function(t){t=this.getItems(t);this.hide(t)},d.getItem=function(t){for(var e=0;e<this.items.length;e++){var i=this.items[e];if(i.element==t)return i}},d.getItems=function(t){t=n.makeArray(t);var e=[];return t.forEach(function(t){t=this.getItem(t);t&&e.push(t)},this),e},d.remove=function(t){t=this.getItems(t);this._emitCompleteOnItems("remove",t),t&&t.length&&t.forEach(function(t){t.remove(),n.removeFrom(this.items,t)},this)},d.destroy=function(){var t=this.element.style,t=(t.height="",t.position="",t.width="",this.items.forEach(function(t){t.destroy()}),this.unbindResize(),this.element.outlayerGUID);delete h[t],delete this.element.outlayerGUID,c&&c.removeData(this.element,this.constructor.namespace)},s.data=function(t){t=(t=n.getQueryElement(t))&&t.outlayerGUID;return t&&h[t]},s.create=function(t,e){var i=a(s);return i.defaults=n.extend({},s.defaults),n.extend(i.defaults,e),i.compatOptions=n.extend({},s.compatOptions),i.namespace=t,i.data=s.data,i.Item=a(r),n.htmlInit(i,t),c&&c.bridget&&c.bridget(t,i),i},{ms:1,s:1e3});return s.Item=r,s}),((t,e)=>{"function"==typeof define&&define.amd?define("isotope-layout/js/item",["outlayer/outlayer"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer")):(t.Isotope=t.Isotope||{},t.Isotope.Item=e(t.Outlayer))})(window,function(t){function e(){t.Item.apply(this,arguments)}var i=e.prototype=Object.create(t.Item.prototype),n=i._create,o=(i._create=function(){this.id=this.layout.itemGUID++,n.call(this),this.sortData={}},i.updateSortData=function(){if(!this.isIgnored){this.sortData.id=this.id,this.sortData["original-order"]=this.id,this.sortData.random=Math.random();var t,e=this.layout.options.getSortData,i=this.layout._sorters;for(t in e){var n=i[t];this.sortData[t]=n(this.element,this)}}},i.destroy);return i.destroy=function(){o.apply(this,arguments),this.css({display:""})},e}),((t,e)=>{"function"==typeof define&&define.amd?define("isotope-layout/js/layout-mode",["get-size/get-size","outlayer/outlayer"],e):"object"==typeof module&&module.exports?module.exports=e(require("get-size"),require("outlayer")):(t.Isotope=t.Isotope||{},t.Isotope.LayoutMode=e(t.getSize,t.Outlayer))})(window,function(e,i){function n(t){(this.isotope=t)&&(this.options=t.options[this.namespace],this.element=t.element,this.items=t.filteredItems,this.size=t.size)}var o=n.prototype;return["_resetLayout","_getItemLayoutPosition","_manageStamp","_getContainerSize","_getElementOffset","needsResizeLayout","_getOption"].forEach(function(t){o[t]=function(){return i.prototype[t].apply(this.isotope,arguments)}}),o.needsVerticalResizeLayout=function(){var t=e(this.isotope.element);return this.isotope.size&&t&&t.innerHeight!=this.isotope.size.innerHeight},o._getMeasurement=function(){this.isotope._getMeasurement.apply(this,arguments)},o.getColumnWidth=function(){this.getSegmentSize("column","Width")},o.getRowHeight=function(){this.getSegmentSize("row","Height")},o.getSegmentSize=function(t,e){var i,t=t+e,n="outer"+e;this._getMeasurement(t,n),this[t]||(i=this.getFirstItemSize(),this[t]=i&&i[n]||this.isotope.size["inner"+e])},o.getFirstItemSize=function(){var t=this.isotope.filteredItems[0];return t&&t.element&&e(t.element)},o.layout=function(){this.isotope.layout.apply(this.isotope,arguments)},o.getSize=function(){this.isotope.getSize(),this.size=this.isotope.size},n.modes={},n.create=function(t,e){function i(){n.apply(this,arguments)}return(i.prototype=Object.create(o)).constructor=i,e&&(i.options=e),n.modes[i.prototype.namespace=t]=i},n}),((t,e)=>{"function"==typeof define&&define.amd?define("masonry-layout/masonry",["outlayer/outlayer","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer"),require("get-size")):t.Masonry=e(t.Outlayer,t.getSize)})(window,function(t,a){var t=t.create("masonry"),e=(t.compatOptions.fitWidth="isFitWidth",t.prototype);return e._resetLayout=function(){this.getSize(),this._getMeasurement("columnWidth","outerWidth"),this._getMeasurement("gutter","outerWidth"),this.measureColumns(),this.colYs=[];for(var t=0;t<this.cols;t++)this.colYs.push(0);this.maxY=0,this.horizontalColIndex=0},e.measureColumns=function(){this.getContainerWidth(),this.columnWidth||(t=(t=this.items[0])&&t.element,this.columnWidth=t&&a(t).outerWidth||this.containerWidth);var t=this.columnWidth+=this.gutter,e=this.containerWidth+this.gutter,i=e/t,e=t-e%t,i=Math[e&&e<1?"round":"floor"](i);this.cols=Math.max(i,1)},e.getContainerWidth=function(){var t=this._getOption("fitWidth")?this.element.parentNode:this.element,t=a(t);this.containerWidth=t&&t.innerWidth},e._getItemLayoutPosition=function(t){t.getSize();for(var e=t.size.outerWidth%this.columnWidth,e=Math[e&&e<1?"round":"ceil"](t.size.outerWidth/this.columnWidth),e=Math.min(e,this.cols),i=this[this.options.horizontalOrder?"_getHorizontalColPosition":"_getTopColPosition"](e,t),n={x:this.columnWidth*i.col,y:i.y},o=i.y+t.size.outerHeight,r=e+i.col,s=i.col;s<r;s++)this.colYs[s]=o;return n},e._getTopColPosition=function(t){var t=this._getTopColGroup(t),e=Math.min.apply(Math,t);return{col:t.indexOf(e),y:e}},e._getTopColGroup=function(t){if(t<2)return this.colYs;for(var e=[],i=this.cols+1-t,n=0;n<i;n++)e[n]=this._getColGroupY(n,t);return e},e._getColGroupY=function(t,e){return e<2?this.colYs[t]:(t=this.colYs.slice(t,t+e),Math.max.apply(Math,t))},e._getHorizontalColPosition=function(t,e){var i=this.horizontalColIndex%this.cols,i=1<t&&i+t>this.cols?0:i,e=e.size.outerWidth&&e.size.outerHeight;return this.horizontalColIndex=e?i+t:this.horizontalColIndex,{col:i,y:this._getColGroupY(i,t)}},e._manageStamp=function(t){var e=a(t),t=this._getElementOffset(t),i=this._getOption("originLeft")?t.left:t.right,n=i+e.outerWidth,i=Math.floor(i/this.columnWidth),i=Math.max(0,i),o=Math.floor(n/this.columnWidth);o-=n%this.columnWidth?0:1;for(var o=Math.min(this.cols-1,o),r=(this._getOption("originTop")?t.top:t.bottom)+e.outerHeight,s=i;s<=o;s++)this.colYs[s]=Math.max(r,this.colYs[s])},e._getContainerSize=function(){this.maxY=Math.max.apply(Math,this.colYs);var t={height:this.maxY};return this._getOption("fitWidth")&&(t.width=this._getContainerFitWidth()),t},e._getContainerFitWidth=function(){for(var t=0,e=this.cols;--e&&0===this.colYs[e];)t++;return(this.cols-t)*this.columnWidth-this.gutter},e.needsResizeLayout=function(){var t=this.containerWidth;return this.getContainerWidth(),t!=this.containerWidth},t}),((t,e)=>{"function"==typeof define&&define.amd?define("isotope-layout/js/layout-modes/masonry",["../layout-mode","masonry-layout/masonry"],e):"object"==typeof module&&module.exports?module.exports=e(require("../layout-mode"),require("masonry-layout")):e(t.Isotope.LayoutMode,t.Masonry)})(window,function(t,e){var i,t=t.create("masonry"),n=t.prototype,o={_getElementOffset:!0,layout:!0,_getMeasurement:!0};for(i in e.prototype)o[i]||(n[i]=e.prototype[i]);var r=n.measureColumns,s=(n.measureColumns=function(){this.items=this.isotope.filteredItems,r.call(this)},n._getOption);return n._getOption=function(t){return"fitWidth"==t?void 0!==this.options.isFitWidth?this.options.isFitWidth:this.options.fitWidth:s.apply(this.isotope,arguments)},t}),((t,e)=>{"function"==typeof define&&define.amd?define("isotope-layout/js/layout-modes/fit-rows",["../layout-mode"],e):"object"==typeof exports?module.exports=e(require("../layout-mode")):e(t.Isotope.LayoutMode)})(window,function(t){var t=t.create("fitRows"),e=t.prototype;return e._resetLayout=function(){this.x=0,this.y=0,this.maxY=0,this._getMeasurement("gutter","outerWidth")},e._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth+this.gutter,i=this.isotope.size.innerWidth+this.gutter,i=(0!==this.x&&e+this.x>i&&(this.x=0,this.y=this.maxY),{x:this.x,y:this.y});return this.maxY=Math.max(this.maxY,this.y+t.size.outerHeight),this.x+=e,i},e._getContainerSize=function(){return{height:this.maxY}},t}),((t,e)=>{"function"==typeof define&&define.amd?define("isotope-layout/js/layout-modes/vertical",["../layout-mode"],e):"object"==typeof module&&module.exports?module.exports=e(require("../layout-mode")):e(t.Isotope.LayoutMode)})(window,function(t){var t=t.create("vertical",{horizontalAlignment:0}),e=t.prototype;return e._resetLayout=function(){this.y=0},e._getItemLayoutPosition=function(t){t.getSize();var e=(this.isotope.size.innerWidth-t.size.outerWidth)*this.options.horizontalAlignment,i=this.y;return this.y+=t.size.outerHeight,{x:e,y:i}},e._getContainerSize=function(){return{height:this.y}},t}),((s,a)=>{"function"==typeof define&&define.amd?define(["outlayer/outlayer","get-size/get-size","desandro-matches-selector/matches-selector","fizzy-ui-utils/utils","isotope-layout/js/item","isotope-layout/js/layout-mode","isotope-layout/js/layout-modes/masonry","isotope-layout/js/layout-modes/fit-rows","isotope-layout/js/layout-modes/vertical"],function(t,e,i,n,o,r){return a(s,t,0,i,n,o,r)}):"object"==typeof module&&module.exports?module.exports=a(s,require("outlayer"),require("get-size"),require("desandro-matches-selector"),require("fizzy-ui-utils"),require("isotope-layout/js/item"),require("isotope-layout/js/layout-mode"),require("isotope-layout/js/layout-modes/masonry"),require("isotope-layout/js/layout-modes/fit-rows"),require("isotope-layout/js/layout-modes/vertical")):s.Isotope=a(s,s.Outlayer,0,s.matchesSelector,s.fizzyUIUtils,s.Isotope.Item,s.Isotope.LayoutMode)})(window,function(t,i,e,n,r,o,s){var a=t.jQuery,l=String.prototype.trim?function(t){return t.trim()}:function(t){return t.replace(/^\s+|\s+$/g,"")},c=i.create("isotope",{layoutMode:"masonry",isJQueryFiltering:!0,sortAscending:!0}),t=(c.Item=o,c.LayoutMode=s,c.prototype),u=(t._create=function(){for(var t in this.itemGUID=0,this._sorters={},this._getSorters(),i.prototype._create.call(this),this.modes={},this.filteredItems=this.items,this.sortHistory=["original-order"],s.modes)this._initLayoutMode(t)},t.reloadItems=function(){this.itemGUID=0,i.prototype.reloadItems.call(this)},t._itemize=function(){for(var t=i.prototype._itemize.apply(this,arguments),e=0;e<t.length;e++)t[e].id=this.itemGUID++;return this._updateItemsSortData(t),t},t._initLayoutMode=function(t){var e=s.modes[t],i=this.options[t]||{};this.options[t]=e.options?r.extend(e.options,i):i,this.modes[t]=new e(this)},t.layout=function(){return!this._isLayoutInited&&this._getOption("initLayout")?void this.arrange():void this._layout()},t._layout=function(){var t=this._getIsInstant();this._resetLayout(),this._manageStamps(),this.layoutItems(this.filteredItems,t),this._isLayoutInited=!0},t.arrange=function(t){this.option(t),this._getIsInstant();t=this._filter(this.items);this.filteredItems=t.matches,this._bindArrangeComplete(),this._isInstant?this._noTransition(this._hideReveal,[t]):this._hideReveal(t),this._sort(),this._layout()},t._init=t.arrange,t._hideReveal=function(t){this.reveal(t.needReveal),this.hide(t.needHide)},t._getIsInstant=function(){var t=this._getOption("layoutInstant"),t=void 0!==t?t:!this._isLayoutInited;return this._isInstant=t},t._bindArrangeComplete=function(){function t(){e&&i&&n&&o.dispatchEvent("arrangeComplete",null,[o.filteredItems])}var e,i,n,o=this;this.once("layoutComplete",function(){e=!0,t()}),this.once("hideComplete",function(){i=!0,t()}),this.once("revealComplete",function(){n=!0,t()})},t._filter=function(t){for(var e=this.options.filter,i=[],n=[],o=[],r=this._getFilterTest(e||"*"),s=0;s<t.length;s++){var a,l=t[s];l.isIgnored||((a=r(l))&&i.push(l),a&&l.isHidden?n.push(l):a||l.isHidden||o.push(l))}return{matches:i,needReveal:n,needHide:o}},t._getFilterTest=function(e){return a&&this.options.isJQueryFiltering?function(t){return a(t.element).is(e)}:"function"==typeof e?function(t){return e(t.element)}:function(t){return n(t.element,e)}},t.updateSortData=function(t){t=t?(t=r.makeArray(t),this.getItems(t)):this.items;this._getSorters(),this._updateItemsSortData(t)},t._getSorters=function(){var t,e=this.options.getSortData;for(t in e){var i=e[t];this._sorters[t]=u(i)}},t._updateItemsSortData=function(t){for(var e=t&&t.length,i=0;e&&i<e;i++)t[i].updateSortData()},function(t){var e,i,n,o,r,s;return"string"!=typeof t?t:(i=(i=(e=(t=l(t).split(" "))[0]).match(/^\[(.+)\]$/))&&i[1],s=e,n=(r=i)?function(t){return t.getAttribute(r)}:function(t){t=t.querySelector(s);return t&&t.textContent},(o=c.sortDataParsers[t[1]])?function(t){return t&&o(n(t))}:function(t){return t&&n(t)})}),h=(c.sortDataParsers={parseInt:function(t){return parseInt(t,10)},parseFloat:function(t){return parseFloat(t)}},t._sort=function(){var t,s,a;this.options.sortBy&&(t=r.makeArray(this.options.sortBy),this._getIsSameSortBy(t)||(this.sortHistory=t.concat(this.sortHistory)),s=this.sortHistory,a=this.options.sortAscending,this.filteredItems.sort(function(t,e){for(var i=0;i<s.length;i++){var n=s[i],o=t.sortData[n],r=e.sortData[n];if(r<o||o<r)return(r<o?1:-1)*((void 0!==a[n]?a[n]:a)?1:-1)}return 0}))},t._getIsSameSortBy=function(t){for(var e=0;e<t.length;e++)if(t[e]!=this.sortHistory[e])return!1;return!0},t._mode=function(){var t=this.options.layoutMode,e=this.modes[t];if(e)return e.options=this.options[t],e;throw new Error("No layout mode: "+t)},t._resetLayout=function(){i.prototype._resetLayout.call(this),this._mode()._resetLayout()},t._getItemLayoutPosition=function(t){return this._mode()._getItemLayoutPosition(t)},t._manageStamp=function(t){this._mode()._manageStamp(t)},t._getContainerSize=function(){return this._mode()._getContainerSize()},t.needsResizeLayout=function(){return this._mode().needsResizeLayout()},t.appended=function(t){var t=this.addItems(t);t.length&&(t=this._filterRevealAdded(t),this.filteredItems=this.filteredItems.concat(t))},t.prepended=function(t){var e,t=this._itemize(t);t.length&&(this._resetLayout(),this._manageStamps(),e=this._filterRevealAdded(t),this.layoutItems(this.filteredItems),this.filteredItems=e.concat(this.filteredItems),this.items=t.concat(this.items))},t._filterRevealAdded=function(t){t=this._filter(t);return this.hide(t.needHide),this.reveal(t.matches),this.layoutItems(t.matches,!0),t.matches},t.insert=function(t){var e=this.addItems(t);if(e.length){for(var i,n=e.length,o=0;o<n;o++)i=e[o],this.element.appendChild(i.element);t=this._filter(e).matches;for(o=0;o<n;o++)e[o].isLayoutInstant=!0;for(this.arrange(),o=0;o<n;o++)delete e[o].isLayoutInstant;this.reveal(t)}},t.remove);return t.remove=function(t){t=r.makeArray(t);var e=this.getItems(t);h.call(this,t);for(var i=e&&e.length,n=0;i&&n<i;n++){var o=e[n];r.removeFrom(this.filteredItems,o)}},t.shuffle=function(){for(var t=0;t<this.items.length;t++)this.items[t].sortData.random=Math.random();this.options.sortBy="random",this._sort(),this._layout()},t._noTransition=function(t,e){var i=this.options.transitionDuration,t=(this.options.transitionDuration=0,t.apply(this,e));return this.options.transitionDuration=i,t},t.getFilteredItemElements=function(){return this.filteredItems.map(function(t){return t.element})},c}),((t,e)=>{"function"==typeof define&&define.amd?define("packery/js/rect",e):"object"==typeof module&&module.exports?module.exports=e():(t.Packery=t.Packery||{},t.Packery.Rect=e())})(window,function(){function a(t){for(var e in a.defaults)this[e]=a.defaults[e];for(e in t)this[e]=t[e]}a.defaults={x:0,y:0,width:0,height:0};var t=a.prototype;return t.contains=function(t){var e=t.width||0;return this.x<=t.x&&this.y<=t.y&&this.x+this.width>=t.x+e&&this.y+this.height>=t.y+(t.height||0)},t.overlaps=function(t){var e=this.x+this.width,i=this.y+this.height,n=t.x+t.width;return this.x<n&&e>t.x&&this.y<t.y+t.height&&i>t.y},t.getMaximalFreeRects=function(t){var e,i,n,o,r,s;return!!this.overlaps(t)&&(i=[],n=this.x+this.width,o=this.y+this.height,r=t.x+t.width,s=t.y+t.height,this.y<t.y&&(e=new a({x:this.x,y:this.y,width:this.width,height:t.y-this.y}),i.push(e)),r<n&&(e=new a({x:r,y:this.y,width:n-r,height:this.height}),i.push(e)),s<o&&(e=new a({x:this.x,y:s,width:this.width,height:o-s}),i.push(e)),this.x<t.x&&(e=new a({x:this.x,y:this.y,width:t.x-this.x,height:this.height}),i.push(e)),i)},t.canFit=function(t){return this.width>=t.width&&this.height>=t.height},a}),((t,e)=>{"function"==typeof define&&define.amd?define("packery/js/packer",["./rect"],e):"object"==typeof module&&module.exports?module.exports=e(require("./rect")):(t=t.Packery=t.Packery||{}).Packer=e(t.Rect)})(window,function(e){function t(t,e,i){this.width=t||0,this.height=e||0,this.sortDirection=i||"downwardLeftToRight",this.reset()}var i=t.prototype,n=(i.reset=function(){this.spaces=[];var t=new e({x:0,y:0,width:this.width,height:this.height});this.spaces.push(t),this.sorter=n[this.sortDirection]||n.downwardLeftToRight},i.pack=function(t){for(var e=0;e<this.spaces.length;e++){var i=this.spaces[e];if(i.canFit(t)){this.placeInSpace(t,i);break}}},i.columnPack=function(t){for(var e=0;e<this.spaces.length;e++){var i=this.spaces[e];if(i.x<=t.x&&i.x+i.width>=t.x+t.width&&i.height>=t.height-.01){t.y=i.y,this.placed(t);break}}},i.rowPack=function(t){for(var e=0;e<this.spaces.length;e++){var i=this.spaces[e];if(i.y<=t.y&&i.y+i.height>=t.y+t.height&&i.width>=t.width-.01){t.x=i.x,this.placed(t);break}}},i.placeInSpace=function(t,e){t.x=e.x,t.y=e.y,this.placed(t)},i.placed=function(t){for(var e=[],i=0;i<this.spaces.length;i++){var n=this.spaces[i],o=n.getMaximalFreeRects(t);o?e.push.apply(e,o):e.push(n)}this.spaces=e,this.mergeSortSpaces()},i.mergeSortSpaces=function(){t.mergeRects(this.spaces),this.spaces.sort(this.sorter)},i.addSpace=function(t){this.spaces.push(t),this.mergeSortSpaces()},t.mergeRects=function(t){var e=0,i=t[e];t:for(;i;){for(var n=0,o=t[e+n];o;){if(o==i)n++;else{if(o.contains(i)){t.splice(e,1),i=t[e];continue t}i.contains(o)?t.splice(e+n,1):n++}o=t[e+n]}i=t[++e]}return t},{downwardLeftToRight:function(t,e){return t.y-e.y||t.x-e.x},rightwardTopToBottom:function(t,e){return t.x-e.x||t.y-e.y}});return t}),((t,e)=>{"function"==typeof define&&define.amd?define("packery/js/item",["outlayer/outlayer","./rect"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer"),require("./rect")):t.Packery.Item=e(t.Outlayer,t.Packery.Rect)})(window,function(t,e){function i(){t.Item.apply(this,arguments)}var n="string"==typeof document.documentElement.style.transform?"transform":"WebkitTransform",o=i.prototype=Object.create(t.Item.prototype),r=o._create,s=(o._create=function(){r.call(this),this.rect=new e},o.moveTo);return o.moveTo=function(t,e){var i=Math.abs(this.position.x-t),n=Math.abs(this.position.y-e);return this.layout.dragItemCount&&!this.isPlacing&&!this.isTransitioning&&i<1&&n<1?void this.goTo(t,e):void s.apply(this,arguments)},o.enablePlacing=function(){this.removeTransitionStyles(),this.isTransitioning&&n&&(this.element.style[n]="none"),this.isTransitioning=!1,this.getSize(),this.layout._setRectSize(this.element,this.rect),this.isPlacing=!0},o.disablePlacing=function(){this.isPlacing=!1},o.removeElem=function(){this.element.parentNode.removeChild(this.element),this.layout.packer.addSpace(this.rect),this.emitEvent("remove",[this])},o.showDropPlaceholder=function(){var t=this.dropPlaceholder;t||((t=this.dropPlaceholder=document.createElement("div")).className="packery-drop-placeholder",t.style.position="absolute"),t.style.width=this.size.width+"px",t.style.height=this.size.height+"px",this.positionDropPlaceholder(),this.layout.element.appendChild(t)},o.positionDropPlaceholder=function(){this.dropPlaceholder.style[n]="translate("+this.rect.x+"px, "+this.rect.y+"px)"},o.hideDropPlaceholder=function(){this.layout.element.removeChild(this.dropPlaceholder)},i}),((t,e)=>{"function"==typeof define&&define.amd?define("packery/js/packery",["get-size/get-size","outlayer/outlayer","./rect","./packer","./item"],e):"object"==typeof module&&module.exports?module.exports=e(require("get-size"),require("outlayer"),require("./rect"),require("./packer"),require("./item")):t.Packery=e(t.getSize,t.Outlayer,t.Packery.Rect,t.Packery.Packer,t.Packery.Item)})(window,function(c,t,s,e,i){function n(t,e){return t.position.y-e.position.y||t.position.x-e.position.x}function o(t,e){return t.position.x-e.position.x||t.position.y-e.position.y}s.prototype.canFit=function(t){return this.width>=t.width-1&&this.height>=t.height-1};var r=t.create("packery"),i=(r.Item=i,r.prototype),a=(i._create=function(){t.prototype._create.call(this),this.packer=new e,this.shiftPacker=new e,this.isEnabled=!0,this.dragItemCount=0;var i=this;this.handleDraggabilly={dragStart:function(){i.itemDragStart(this.element)},dragMove:function(){i.itemDragMove(this.element,this.position.x,this.position.y)},dragEnd:function(){i.itemDragEnd(this.element)}},this.handleUIDraggable={start:function(t,e){e&&i.itemDragStart(t.currentTarget)},drag:function(t,e){e&&i.itemDragMove(t.currentTarget,e.position.left,e.position.top)},stop:function(t,e){e&&i.itemDragEnd(t.currentTarget)}}},i._resetLayout=function(){var t,e,i;this.getSize(),this._getMeasurements(),i=this._getOption("horizontal")?(t=1/0,e=this.size.innerHeight+this.gutter,"rightwardTopToBottom"):(t=this.size.innerWidth+this.gutter,e=1/0,"downwardLeftToRight"),this.packer.width=this.shiftPacker.width=t,this.packer.height=this.shiftPacker.height=e,this.packer.sortDirection=this.shiftPacker.sortDirection=i,this.packer.reset(),this.maxY=0,this.maxX=0},i._getMeasurements=function(){this._getMeasurement("columnWidth","width"),this._getMeasurement("rowHeight","height"),this._getMeasurement("gutter","width")},i._getItemLayoutPosition=function(t){var e;return this._setRectSize(t.element,t.rect),this.isShifting||0<this.dragItemCount?(e=this._getPackMethod(),this.packer[e](t.rect)):this.packer.pack(t.rect),this._setMaxXY(t.rect),t.rect},i.shiftLayout=function(){this.isShifting=!0,this.layout(),delete this.isShifting},i._getPackMethod=function(){return this._getOption("horizontal")?"rowPack":"columnPack"},i._setMaxXY=function(t){this.maxX=Math.max(t.x+t.width,this.maxX),this.maxY=Math.max(t.y+t.height,this.maxY)},i._setRectSize=function(t,e){var t=c(t),i=t.outerWidth,t=t.outerHeight;(i||t)&&(i=this._applyGridGutter(i,this.columnWidth),t=this._applyGridGutter(t,this.rowHeight)),e.width=Math.min(i,this.packer.width),e.height=Math.min(t,this.packer.height)},i._applyGridGutter=function(t,e){var i;return e?(i=t%(e+=this.gutter),Math[i&&i<1?"round":"ceil"](t/e)*e):t+this.gutter},i._getContainerSize=function(){return this._getOption("horizontal")?{width:this.maxX-this.gutter}:{height:this.maxY-this.gutter}},i._manageStamp=function(t){var e=this.getItem(t);e=e&&e.isPlacing?e.rect:(e=this._getElementOffset(t),new s({x:this._getOption("originLeft")?e.left:e.right,y:this._getOption("originTop")?e.top:e.bottom})),this._setRectSize(t,e),this.packer.placed(e),this._setMaxXY(e)},i.sortItemsByPosition=function(){var t=this._getOption("horizontal")?o:n;this.items.sort(t)},i.fit=function(t,e,i){t=this.getItem(t);t&&(this.stamp(t.element),t.enablePlacing(),this.updateShiftTargets(t),e=void 0===e?t.rect.x:e,i=void 0===i?t.rect.y:i,this.shift(t,e,i),this._bindFitEvents(t),t.moveTo(t.rect.x,t.rect.y),this.shiftLayout(),this.unstamp(t.element),this.sortItemsByPosition(),t.disablePlacing())},i._bindFitEvents=function(t){function e(){2==++n&&i.dispatchEvent("fitComplete",null,[t])}var i=this,n=0;t.once("layout",e),this.once("layoutComplete",e)},i.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&(this.options.shiftPercentResize?this.resizeShiftPercentLayout():this.layout())},i.needsResizeLayout=function(){var t=c(this.element),e=this._getOption("horizontal")?"innerHeight":"innerWidth";return t[e]!=this.size[e]},i.resizeShiftPercentLayout=function(){var i,e,n,t=this._getItemsForLayout(this.items),o=this._getOption("horizontal"),r=o?"y":"x",s=o?"height":"width",a=o?"rowHeight":"columnWidth",o=o?"innerHeight":"innerWidth",l=this[a];(l=l&&l+this.gutter)?(this._getMeasurements(),i=this[a]+this.gutter,t.forEach(function(t){var e=Math.round(t.rect[r]/l);t.rect[r]=e*i})):(e=c(this.element)[o]+this.gutter,n=this.packer[s],t.forEach(function(t){t.rect[r]=t.rect[r]/n*e})),this.shiftLayout()},i.itemDragStart=function(t){this.isEnabled&&(this.stamp(t),t=this.getItem(t))&&(t.enablePlacing(),t.showDropPlaceholder(),this.dragItemCount++,this.updateShiftTargets(t))},i.updateShiftTargets=function(t){this.shiftPacker.reset(),this._getBoundingRect();var i=this._getOption("originLeft"),n=this._getOption("originTop"),l=(this.stamps.forEach(function(t){var e=this.getItem(t);e&&e.isPlacing||(e=this._getElementOffset(t),e=new s({x:i?e.left:e.right,y:n?e.top:e.bottom}),this._setRectSize(t,e),this.shiftPacker.placed(e))},this),this._getOption("horizontal")),e=l?"rowHeight":"columnWidth",c=l?"height":"width";this.shiftTargetKeys=[],this.shiftTargets=[];var u=this[e];if(u=u&&u+this.gutter)for(var e=Math.ceil(t.rect[c]/u),o=Math.floor((this.shiftPacker[c]+this.gutter)/u),h=(o-e)*u,r=0;r<o;r++)this._addShiftTarget(r*u,0,h);else h=this.shiftPacker[c]+this.gutter-t.rect[c],this._addShiftTarget(0,0,h);var e=this._getItemsForLayout(this.items),d=this._getPackMethod();e.forEach(function(t){var e=t.rect,i=(this._setRectSize(t.element,e),this.shiftPacker[d](e),this._addShiftTarget(e.x,e.y,h),l?e.x+e.width:e.x),n=l?e.y:e.y+e.height;if(this._addShiftTarget(i,n,h),u)for(var o=Math.round(e[c]/u),r=1;r<o;r++){var s=l?i:e.x+u*r,a=l?e.y+u*r:n;this._addShiftTarget(s,a,h)}},this)},i._addShiftTarget=function(t,e,i){var n=this._getOption("horizontal")?e:t;0!==n&&i<n||-1!=this.shiftTargetKeys.indexOf(i=t+","+e)||(this.shiftTargetKeys.push(i),this.shiftTargets.push({x:t,y:e}))},i.shift=function(t,e,i){var n,o=1/0,r={x:e,y:i};this.shiftTargets.forEach(function(t){i=(e=r).x-t.x,e=e.y-t.y;var e,i=Math.sqrt(i*i+e*e);i<o&&(n=t,o=i)}),t.rect.x=n.x,t.rect.y=n.y},i.itemDragMove=function(t,e,i){function n(){o.shift(r,e,i),r.positionDropPlaceholder(),o.layout()}var o,r=this.isEnabled&&this.getItem(t);r&&(e-=this.size.paddingLeft,i-=this.size.paddingTop,o=this,t=new Date,this._itemDragTime&&t-this._itemDragTime<120?(clearTimeout(this.dragTimeout),this.dragTimeout=setTimeout(n,120)):(n(),this._itemDragTime=t))},i.itemDragEnd=function(t){function e(){2==++i&&(o.element.classList.remove("is-positioning-post-drag"),o.hideDropPlaceholder(),n.dispatchEvent("dragItemPositioned",null,[o]))}var i,n,o=this.isEnabled&&this.getItem(t);o&&(clearTimeout(this.dragTimeout),o.element.classList.add("is-positioning-post-drag"),i=0,n=this,o.once("layout",e),this.once("layoutComplete",e),o.moveTo(o.rect.x,o.rect.y),this.layout(),this.dragItemCount=Math.max(0,this.dragItemCount-1),this.sortItemsByPosition(),o.disablePlacing(),this.unstamp(o.element))},i.bindDraggabillyEvents=function(t){this._bindDraggabillyEvents(t,"on")},i.unbindDraggabillyEvents=function(t){this._bindDraggabillyEvents(t,"off")},i._bindDraggabillyEvents=function(t,e){var i=this.handleDraggabilly;t[e]("dragStart",i.dragStart),t[e]("dragMove",i.dragMove),t[e]("dragEnd",i.dragEnd)},i.bindUIDraggableEvents=function(t){this._bindUIDraggableEvents(t,"on")},i.unbindUIDraggableEvents=function(t){this._bindUIDraggableEvents(t,"off")},i._bindUIDraggableEvents=function(t,e){var i=this.handleUIDraggable;t[e]("dragstart",i.start)[e]("drag",i.drag)[e]("dragstop",i.stop)},i.destroy);return i.destroy=function(){a.apply(this,arguments),this.isEnabled=!1},r.Rect=s,r.Packer=e,r}),((t,e)=>{"function"==typeof define&&define.amd?define(["isotope-layout/js/layout-mode","packery/js/packery"],e):"object"==typeof module&&module.exports?module.exports=e(require("isotope-layout/js/layout-mode"),require("packery")):e(t.Isotope.LayoutMode,t.Packery)})(window,function(t,e){var i,t=t.create("packery"),n=t.prototype,o={_getElementOffset:!0,_getMeasurement:!0};for(i in e.prototype)o[i]||(n[i]=e.prototype[i]);var r=n._resetLayout,s=(n._resetLayout=function(){this.packer=this.packer||new e.Packer,this.shiftPacker=this.shiftPacker||new e.Packer,r.apply(this,arguments)},n._getItemLayoutPosition),a=(n._getItemLayoutPosition=function(t){return t.rect=t.rect||new e.Rect,s.call(this,t)},n.needsResizeLayout),l=(n.needsResizeLayout=function(){return this._getOption("horizontal")?this.needsVerticalResizeLayout():a.call(this)},n._getOption);return n._getOption=function(t){return"horizontal"==t?void 0!==this.options.isHorizontal?this.options.isHorizontal:this.options.horizontal:l.apply(this.isotope,arguments)},t}),((l,i,n,a)=>{function c(t,e){this.settings=null,this.options=l.extend({},c.Defaults,e),this.$element=l(t),this._handlers={},this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._widths=[],this._invalidated={},this._pipe=[],this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null},this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}},l.each(["onResize","onThrottledResize"],l.proxy(function(t,e){this._handlers[e]=l.proxy(this[e],this)},this)),l.each(c.Plugins,l.proxy(function(t,e){this._plugins[t.charAt(0).toLowerCase()+t.slice(1)]=new e(this)},this)),l.each(c.Workers,l.proxy(function(t,e){this._pipe.push({filter:e.filter,run:l.proxy(e.run,this)})},this)),this.setup(),this.initialize()}c.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:i,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"},c.Width={Default:"default",Inner:"inner",Outer:"outer"},c.Type={Event:"event",State:"state"},c.Plugins={},c.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(t){t.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(t){var e=this.settings.margin||"",i=!this.settings.autoWidth,n=this.settings.rtl,n={width:"auto","margin-left":n?e:"","margin-right":n?"":e};i||this.$stage.children().css(n),t.css=n}},{filter:["width","items","settings"],run:function(t){var e,i=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,n=this._items.length,o=!this.settings.autoWidth,r=[];for(t.items={merge:!1,width:i};n--;)e=this._mergers[n],e=this.settings.mergeFit&&Math.min(e,this.settings.items)||e,t.items.merge=1<e||t.items.merge,r[n]=o?i*e:this._items[n].width();this._widths=r}},{filter:["items","settings"],run:function(){var t=[],e=this._items,i=this.settings,n=Math.max(2*i.items,4),o=2*Math.ceil(e.length/2),r=i.loop&&e.length?i.rewind?n:Math.max(n,o):0,s="",a="";for(r/=2;0<r;)t.push(this.normalize(t.length/2,!0)),s+=e[t[t.length-1]][0].outerHTML,t.push(this.normalize(e.length-1-(t.length-1)/2,!0)),a=e[t[t.length-1]][0].outerHTML+a,--r;this._clones=t,l(s).addClass("cloned").appendTo(this.$stage),l(a).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var t,e,i=this.settings.rtl?1:-1,n=this._clones.length+this._items.length,o=-1,r=[];++o<n;)t=r[o-1]||0,e=this._widths[this.relative(o)]+this.settings.margin,r.push(t+e*i);this._coordinates=r}},{filter:["width","items","settings"],run:function(){var t=this.settings.stagePadding,e=this._coordinates,e={width:Math.ceil(Math.abs(e[e.length-1]))+2*t,"padding-left":t||"","padding-right":t||""};this.$stage.css(e)}},{filter:["width","items","settings"],run:function(t){var e=this._coordinates.length,i=!this.settings.autoWidth,n=this.$stage.children();if(i&&t.items.merge)for(;e--;)t.css.width=this._widths[this.relative(e)],n.eq(e).css(t.css);else i&&(t.css.width=t.items.width,n.css(t.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(t){t.current=t.current?this.$stage.children().index(t.current):0,t.current=Math.max(this.minimum(),Math.min(this.maximum(),t.current)),this.reset(t.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){for(var t,e,i=this.settings.rtl?1:-1,n=2*this.settings.stagePadding,o=this.coordinates(this.current())+n,r=o+this.width()*i,s=[],a=0,l=this._coordinates.length;a<l;a++)t=this._coordinates[a-1]||0,e=Math.abs(this._coordinates[a])+n*i,(this.op(t,"<=",o)&&this.op(t,">",r)||this.op(e,"<",o)&&this.op(e,">",r))&&s.push(a);this.$stage.children(".active").removeClass("active"),this.$stage.children(":eq("+s.join("), :eq(")+")").addClass("active"),this.$stage.children(".center").removeClass("center"),this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}],c.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass),this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=l("<"+this.settings.stageElement+">",{class:this.settings.stageClass}).wrap(l("<div/>",{class:this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))},c.prototype.initializeItems=function(){var t=this.$element.find(".owl-item");t.length?(this._items=t.get().map(function(t){return l(t)}),this._mergers=this._items.map(function(){return 1}),this.refresh()):(this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass))},c.prototype.initialize=function(){var t,e;this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading")&&(t=this.$element.find("img"),e=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:a,e=this.$element.children(e).width(),t.length)&&e<=0&&this.preloadAutoWidthImages(t),this.initializeStage(),this.initializeItems(),this.registerEventHandlers(),this.leave("initializing"),this.trigger("initialized")},c.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")},c.prototype.setup=function(){var e=this.viewport(),t=this.options.responsive,i=-1,n=null;t?(l.each(t,function(t){t<=e&&i<t&&(i=Number(t))}),"function"==typeof(n=l.extend({},this.options,t[i])).stagePadding&&(n.stagePadding=n.stagePadding()),delete n.responsive,n.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+i))):n=l.extend({},this.options),this.trigger("change",{property:{name:"settings",value:n}}),this._breakpoint=i,this.settings=n,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}})},c.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},c.prototype.prepare=function(t){var e=this.trigger("prepare",{content:t});return e.data||(e.data=l("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(t)),this.trigger("prepared",{content:e.data}),e.data},c.prototype.update=function(){for(var t=0,e=this._pipe.length,i=l.proxy(function(t){return this[t]},this._invalidated),n={};t<e;)(this._invalidated.all||0<l.grep(this._pipe[t].filter,i).length)&&this._pipe[t].run(n),t++;this._invalidated={},this.is("valid")||this.enter("valid")},c.prototype.width=function(t){switch(t=t||c.Width.Default){case c.Width.Inner:case c.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},c.prototype.refresh=function(){this.enter("refreshing"),this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$element.addClass(this.options.refreshClass),this.update(),this.$element.removeClass(this.options.refreshClass),this.leave("refreshing"),this.trigger("refreshed")},c.prototype.onThrottledResize=function(){i.clearTimeout(this.resizeTimer),this.resizeTimer=i.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)},c.prototype.onResize=function(){return!!this._items.length&&this._width!==this.$element.width()&&!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))},c.prototype.registerEventHandlers=function(){l.support.transition&&this.$stage.on(l.support.transition.end+".owl.core",l.proxy(this.onTransitionEnd,this)),!1!==this.settings.responsive&&this.on(i,"resize",this._handlers.onThrottledResize),this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1})),this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",l.proxy(this.onDragEnd,this)))},c.prototype.onDragStart=function(t){var e=null;3!==t.which&&(e=l.support.transform?{x:(e=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","))[16===e.length?12:4],y:e[16===e.length?13:5]}:(e=this.$stage.position(),{x:this.settings.rtl?e.left+this.$stage.width()-this.width()+this.settings.margin:e.left,y:e.top}),this.is("animating")&&(l.support.transform?this.animate(e.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===t.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=l(t.target),this._drag.stage.start=e,this._drag.stage.current=e,this._drag.pointer=this.pointer(t),l(n).on("mouseup.owl.core touchend.owl.core",l.proxy(this.onDragEnd,this)),l(n).one("mousemove.owl.core touchmove.owl.core",l.proxy(function(t){var e=this.difference(this._drag.pointer,this.pointer(t));l(n).on("mousemove.owl.core touchmove.owl.core",l.proxy(this.onDragMove,this)),Math.abs(e.x)<Math.abs(e.y)&&this.is("valid")||(t.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))},c.prototype.onDragMove=function(t){var e=null,i=null,n=this.difference(this._drag.pointer,this.pointer(t)),o=this.difference(this._drag.stage.start,n);this.is("dragging")&&(t.preventDefault(),this.settings.loop?(e=this.coordinates(this.minimum()),i=this.coordinates(this.maximum()+1)-e,o.x=((o.x-e)%i+i)%i+e):(e=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),i=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),t=this.settings.pullDrag?-1*n.x/5:0,o.x=Math.max(Math.min(o.x,e+t),i+t)),this._drag.stage.current=o,this.animate(o.x))},c.prototype.onDragEnd=function(t){var t=this.difference(this._drag.pointer,this.pointer(t)),e=this._drag.stage.current,i=0<t.x^this.settings.rtl?"left":"right";l(n).off(".owl.core"),this.$element.removeClass(this.options.grabClass),(0!==t.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(e.x,0!==t.x?i:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=i,3<Math.abs(t.x)||300<(new Date).getTime()-this._drag.time)&&this._drag.target.one("click.owl.core",function(){return!1}),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))},c.prototype.closest=function(i,n){var o=-1,r=this.width(),s=this.coordinates();return this.settings.freeDrag||l.each(s,l.proxy(function(t,e){return"left"===n&&e-30<i&&i<e+30?o=t:"right"===n&&e-r-30<i&&i<e-r+30?o=t+1:this.op(i,"<",e)&&this.op(i,">",s[t+1]!==a?s[t+1]:e-r)&&(o="left"===n?t+1:t),-1===o},this)),this.settings.loop||(this.op(i,">",s[this.minimum()])?o=i=this.minimum():this.op(i,"<",s[this.maximum()])&&(o=i=this.maximum())),o},c.prototype.animate=function(t){var e=0<this.speed();this.is("animating")&&this.onTransitionEnd(),e&&(this.enter("animating"),this.trigger("translate")),l.support.transform3d&&l.support.transition?this.$stage.css({transform:"translate3d("+t+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):e?this.$stage.animate({left:t+"px"},this.speed(),this.settings.fallbackEasing,l.proxy(this.onTransitionEnd,this)):this.$stage.css({left:t+"px"})},c.prototype.is=function(t){return this._states.current[t]&&0<this._states.current[t]},c.prototype.current=function(t){if(t!==a){if(0===this._items.length)return a;var e;t=this.normalize(t),this._current!==t&&((e=this.trigger("change",{property:{name:"position",value:t}})).data!==a&&(t=this.normalize(e.data)),this._current=t,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}}))}return this._current},c.prototype.invalidate=function(t){return"string"===l.type(t)&&(this._invalidated[t]=!0,this.is("valid"))&&this.leave("valid"),l.map(this._invalidated,function(t,e){return e})},c.prototype.reset=function(t){(t=this.normalize(t))!==a&&(this._speed=0,this._current=t,this.suppress(["translate","translated"]),this.animate(this.coordinates(t)),this.release(["translate","translated"]))},c.prototype.normalize=function(t,e){var i=this._items.length,e=e?0:this._clones.length;return!this.isNumeric(t)||i<1?t=a:(t<0||i+e<=t)&&(t=((t-e/2)%i+i)%i+e/2),t},c.prototype.relative=function(t){return t-=this._clones.length/2,this.normalize(t,!0)},c.prototype.maximum=function(t){var e,i,n,o=this.settings,r=this._coordinates.length;if(o.loop)r=this._clones.length/2+this._items.length-1;else if(o.autoWidth||o.merge){if(e=this._items.length)for(i=this._items[--e].width(),n=this.$element.width();e--&&!((i+=this._items[e].width()+this.settings.margin)>n););r=e+1}else r=o.center?this._items.length-1:this._items.length-o.items;return t&&(r-=this._clones.length/2),Math.max(r,0)},c.prototype.minimum=function(t){return t?0:this._clones.length/2},c.prototype.items=function(t){return t===a?this._items.slice():(t=this.normalize(t,!0),this._items[t])},c.prototype.mergers=function(t){return t===a?this._mergers.slice():(t=this.normalize(t,!0),this._mergers[t])},c.prototype.clones=function(i){function n(t){return t%2==0?o+t/2:e-(t+1)/2}var e=this._clones.length/2,o=e+this._items.length;return i===a?l.map(this._clones,function(t,e){return n(e)}):l.map(this._clones,function(t,e){return t===i?n(e):null})},c.prototype.speed=function(t){return t!==a&&(this._speed=t),this._speed},c.prototype.coordinates=function(t){var e,i=1,n=t-1;return t===a?l.map(this._coordinates,l.proxy(function(t,e){return this.coordinates(e)},this)):(this.settings.center?(this.settings.rtl&&(i=-1,n=t+1),e=this._coordinates[t],e+=(this.width()-e+(this._coordinates[n]||0))/2*i):e=this._coordinates[n]||0,Math.ceil(e))},c.prototype.duration=function(t,e,i){return 0===i?0:Math.min(Math.max(Math.abs(e-t),1),6)*Math.abs(i||this.settings.smartSpeed)},c.prototype.to=function(t,e){var i=this.current(),n=t-this.relative(i),o=(0<n)-(n<0),r=this._items.length,s=this.minimum(),a=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(n)>r/2&&(n+=-1*o*r),(o=(((t=i+n)-s)%r+r)%r+s)!==t&&o-n<=a&&0<o-n&&this.reset(i=(t=o)-n)):t=this.settings.rewind?(t%(a+=1)+a)%a:Math.max(s,Math.min(a,t)),this.speed(this.duration(i,t,e)),this.current(t),this.isVisible()&&this.update()},c.prototype.next=function(t){t=t||!1,this.to(this.relative(this.current())+1,t)},c.prototype.prev=function(t){t=t||!1,this.to(this.relative(this.current())-1,t)},c.prototype.onTransitionEnd=function(t){if(t!==a&&(t.stopPropagation(),(t.target||t.srcElement||t.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating"),this.trigger("translated")},c.prototype.viewport=function(){var t;return this.options.responsiveBaseElement!==i?t=l(this.options.responsiveBaseElement).width():i.innerWidth?t=i.innerWidth:n.documentElement&&n.documentElement.clientWidth?t=n.documentElement.clientWidth:console.warn("Can not detect viewport width."),t},c.prototype.replace=function(t){this.$stage.empty(),this._items=[],t=t&&(t instanceof jQuery?t:l(t)),(t=this.settings.nestedItemSelector?t.find("."+this.settings.nestedItemSelector):t).filter(function(){return 1===this.nodeType}).each(l.proxy(function(t,e){e=this.prepare(e),this.$stage.append(e),this._items.push(e),this._mergers.push(+e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)},this)),this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},c.prototype.add=function(t,e){var i=this.relative(this._current);e=e===a?this._items.length:this.normalize(e,!0),t=t instanceof jQuery?t:l(t),this.trigger("add",{content:t,position:e}),t=this.prepare(t),0===this._items.length||e===this._items.length?(0===this._items.length&&this.$stage.append(t),0!==this._items.length&&this._items[e-1].after(t),this._items.push(t),this._mergers.push(+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[e].before(t),this._items.splice(e,0,t),this._mergers.splice(e,0,+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)),this._items[i]&&this.reset(this._items[i].index()),this.invalidate("items"),this.trigger("added",{content:t,position:e})},c.prototype.remove=function(t){(t=this.normalize(t,!0))!==a&&(this.trigger("remove",{content:this._items[t],position:t}),this._items[t].remove(),this._items.splice(t,1),this._mergers.splice(t,1),this.invalidate("items"),this.trigger("removed",{content:null,position:t}))},c.prototype.preloadAutoWidthImages=function(t){t.each(l.proxy(function(t,e){this.enter("pre-loading"),e=l(e),l(new Image).one("load",l.proxy(function(t){e.attr("src",t.target.src),e.css("opacity",1),this.leave("pre-loading"),this.is("pre-loading")||this.is("initializing")||this.refresh()},this)).attr("src",e.attr("src")||e.attr("data-src")||e.attr("data-src-retina"))},this))},c.prototype.destroy=function(){for(var t in this.$element.off(".owl.core"),this.$stage.off(".owl.core"),l(n).off(".owl.core"),!1!==this.settings.responsive&&(i.clearTimeout(this.resizeTimer),this.off(i,"resize",this._handlers.onThrottledResize)),this._plugins)this._plugins[t].destroy();this.$stage.children(".cloned").remove(),this.$stage.unwrap(),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.remove(),this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")},c.prototype.op=function(t,e,i){var n=this.settings.rtl;switch(e){case"<":return n?i<t:t<i;case">":return n?t<i:i<t;case">=":return n?t<=i:i<=t;case"<=":return n?i<=t:t<=i}},c.prototype.on=function(t,e,i,n){t.addEventListener?t.addEventListener(e,i,n):t.attachEvent&&t.attachEvent("on"+e,i)},c.prototype.off=function(t,e,i,n){t.removeEventListener?t.removeEventListener(e,i,n):t.detachEvent&&t.detachEvent("on"+e,i)},c.prototype.trigger=function(t,e,i,n,o){var r={item:{count:this._items.length,index:this.current()}},s=l.camelCase(l.grep(["on",t,i],function(t){return t}).join("-").toLowerCase()),a=l.Event([t,"owl",i||"carousel"].join(".").toLowerCase(),l.extend({relatedTarget:this},r,e));return this._supress[t]||(l.each(this._plugins,function(t,e){e.onTrigger&&e.onTrigger(a)}),this.register({type:c.Type.Event,name:t}),this.$element.trigger(a),this.settings&&"function"==typeof this.settings[s]&&this.settings[s].call(this,a)),a},c.prototype.enter=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]===a&&(this._states.current[e]=0),this._states.current[e]++},this))},c.prototype.leave=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]--},this))},c.prototype.register=function(i){var e;i.type===c.Type.Event?(l.event.special[i.name]||(l.event.special[i.name]={}),l.event.special[i.name].owl||(e=l.event.special[i.name]._default,l.event.special[i.name]._default=function(t){return!e||!e.apply||t.namespace&&-1!==t.namespace.indexOf("owl")?t.namespace&&-1<t.namespace.indexOf("owl"):e.apply(this,arguments)},l.event.special[i.name].owl=!0)):i.type===c.Type.State&&(this._states.tags[i.name]?this._states.tags[i.name]=this._states.tags[i.name].concat(i.tags):this._states.tags[i.name]=i.tags,this._states.tags[i.name]=l.grep(this._states.tags[i.name],l.proxy(function(t,e){return l.inArray(t,this._states.tags[i.name])===e},this)))},c.prototype.suppress=function(t){l.each(t,l.proxy(function(t,e){this._supress[e]=!0},this))},c.prototype.release=function(t){l.each(t,l.proxy(function(t,e){delete this._supress[e]},this))},c.prototype.pointer=function(t){var e={x:null,y:null};return(t=(t=t.originalEvent||t||i.event).touches&&t.touches.length?t.touches[0]:t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t).pageX?(e.x=t.pageX,e.y=t.pageY):(e.x=t.clientX,e.y=t.clientY),e},c.prototype.isNumeric=function(t){return!isNaN(parseFloat(t))},c.prototype.difference=function(t,e){return{x:t.x-e.x,y:t.y-e.y}},l.fn.owlCarousel=function(e){var n=Array.prototype.slice.call(arguments,1);return this.each(function(){var t=l(this),i=t.data("owl.carousel");i||(i=new c(this,"object"==typeof e&&e),t.data("owl.carousel",i),l.each(["next","prev","to","destroy","refresh","replace","add","remove"],function(t,e){i.register({type:c.Type.Event,name:e}),i.$element.on(e+".owl.carousel.core",l.proxy(function(t){t.namespace&&t.relatedTarget!==this&&(this.suppress([e]),i[e].apply(this,[].slice.call(arguments,1)),this.release([e]))},i))})),"string"==typeof e&&"_"!==e.charAt(0)&&i[e].apply(i,n)})},l.fn.owlCarousel.Constructor=c})(window.Zepto||window.jQuery,window,document),((e,i)=>{function n(t){this._core=t,this._interval=null,this._visible=null,this._handlers={"initialized.owl.carousel":e.proxy(function(t){t.namespace&&this._core.settings.autoRefresh&&this.watch()},this)},this._core.options=e.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers)}n.Defaults={autoRefresh:!0,autoRefreshInterval:500},n.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=i.setInterval(e.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))},n.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible)&&this._core.invalidate("width")&&this._core.refresh()},n.prototype.destroy=function(){var t,e;for(t in i.clearInterval(this._interval),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},e.fn.owlCarousel.Constructor.Plugins.AutoRefresh=n})(window.Zepto||window.jQuery,window,document),((a,o)=>{function e(t){this._core=t,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":a.proxy(function(t){if(t.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(t.property&&"position"==t.property.name||"initialized"==t.type)){var e=this._core.settings,i=e.center&&Math.ceil(e.items/2)||e.items,n=e.center&&-1*i||0,o=(t.property&&void 0!==t.property.value?t.property.value:this._core.current())+n,r=this._core.clones().length,s=a.proxy(function(t,e){this.load(e)},this);for(0<e.lazyLoadEager&&(i+=e.lazyLoadEager,e.loop)&&(o-=e.lazyLoadEager,i++);n++<i;)this.load(r/2+this._core.relative(o)),r&&a.each(this._core.clones(this._core.relative(o)),s),o++}},this)},this._core.options=a.extend({},e.Defaults,this._core.options),this._core.$element.on(this._handlers)}e.Defaults={lazyLoad:!1,lazyLoadEager:0},e.prototype.load=function(t){var t=this._core.$stage.children().eq(t),e=t&&t.find(".owl-lazy");!e||-1<a.inArray(t.get(0),this._loaded)||(e.each(a.proxy(function(t,e){var i=a(e),n=1<o.devicePixelRatio&&i.attr("data-src-retina")||i.attr("data-src")||i.attr("data-srcset");this._core.trigger("load",{element:i,url:n},"lazy"),i.is("img")?i.one("load.owl.lazy",a.proxy(function(){i.css("opacity",1),this._core.trigger("loaded",{element:i,url:n},"lazy")},this)).attr("src",n):i.is("source")?i.one("load.owl.lazy",a.proxy(function(){this._core.trigger("loaded",{element:i,url:n},"lazy")},this)).attr("srcset",n):((e=new Image).onload=a.proxy(function(){i.css({"background-image":'url("'+n+'")',opacity:"1"}),this._core.trigger("loaded",{element:i,url:n},"lazy")},this),e.src=n)},this)),this._loaded.push(t.get(0)))},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this._core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},a.fn.owlCarousel.Constructor.Plugins.Lazy=e})(window.Zepto||window.jQuery,window,document),((o,i)=>{function n(t){this._core=t,this._previousHeight=null,this._handlers={"initialized.owl.carousel refreshed.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&"position"===t.property.name&&this.update()},this),"loaded.owl.lazy":o.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&t.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()},this)},this._core.options=o.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers),this._intervalId=null;var e=this;o(i).on("load",function(){e._core.settings.autoHeight&&e.update()}),o(i).resize(function(){e._core.settings.autoHeight&&(null!=e._intervalId&&clearTimeout(e._intervalId),e._intervalId=setTimeout(function(){e.update()},250))})}n.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},n.prototype.update=function(){var t=this._core._current,e=t+this._core.settings.items,i=this._core.settings.lazyLoad,t=this._core.$stage.children().toArray().slice(t,e),n=[],e=0;o.each(t,function(t,e){n.push(o(e).height())}),(e=Math.max.apply(null,n))<=1&&i&&this._previousHeight&&(e=this._previousHeight),this._previousHeight=e,this._core.$stage.parent().height(e).addClass(this._core.settings.autoHeightClass)},n.prototype.destroy=function(){var t,e;for(t in this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},o.fn.owlCarousel.Constructor.Plugins.AutoHeight=n})(window.Zepto||window.jQuery,window,document),((u,e)=>{function i(t){this._core=t,this._videos={},this._playing=null,this._handlers={"initialized.owl.carousel":u.proxy(function(t){t.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":u.proxy(function(t){t.namespace&&this._core.settings.video&&this.isInFullScreen()&&t.preventDefault()},this),"refreshed.owl.carousel":u.proxy(function(t){t.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},this),"changed.owl.carousel":u.proxy(function(t){t.namespace&&"position"===t.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":u.proxy(function(t){var e;t.namespace&&(e=u(t.content).find(".owl-video")).length&&(e.css("display","none"),this.fetch(e,u(t.content)))},this)},this._core.options=u.extend({},i.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",u.proxy(function(t){this.play(t)},this))}i.Defaults={video:!1,videoHeight:!1,videoWidth:!1},i.prototype.fetch=function(t,e){var i=t.attr("data-vimeo-id")?"vimeo":t.attr("data-vzaar-id")?"vzaar":"youtube",n=t.attr("data-vimeo-id")||t.attr("data-youtube-id")||t.attr("data-vzaar-id"),o=t.attr("data-width")||this._core.settings.videoWidth,r=t.attr("data-height")||this._core.settings.videoHeight,s=t.attr("href");if(!s)throw new Error("Missing video URL.");if(-1<(n=s.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/))[3].indexOf("youtu"))i="youtube";else if(-1<n[3].indexOf("vimeo"))i="vimeo";else{if(!(-1<n[3].indexOf("vzaar")))throw new Error("Video URL not supported.");i="vzaar"}n=n[6],this._videos[s]={type:i,id:n,width:o,height:r},e.attr("data-video",s),this.thumbnail(t,this._videos[s])},i.prototype.thumbnail=function(e,t){function i(t){n=c.lazyLoad?u("<div/>",{class:"owl-video-tn "+l,srcType:t}):u("<div/>",{class:"owl-video-tn",style:"opacity:1;background-image:url("+t+")"}),e.after(n),e.after('<div class="owl-video-play-icon"></div>')}var n,o,r=t.width&&t.height?"width:"+t.width+"px;height:"+t.height+"px;":"",s=e.find("img"),a="src",l="",c=this._core.settings;if(e.wrap(u("<div/>",{class:"owl-video-wrapper",style:r})),this._core.settings.lazyLoad&&(a="data-src",l="owl-lazy"),s.length)return i(s.attr(a)),s.remove(),!1;"youtube"===t.type?(o="//img.youtube.com/vi/"+t.id+"/hqdefault.jpg",i(o)):"vimeo"===t.type?u.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t[0].thumbnail_large,i(o)}}):"vzaar"===t.type&&u.ajax({type:"GET",url:"//vzaar.com/api/videos/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t.framegrab_url,i(o)}})},i.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null,this._core.leave("playing"),this._core.trigger("stopped",null,"video")},i.prototype.play=function(t){var e,t=u(t.target).closest("."+this._core.settings.itemClass),i=this._videos[t.attr("data-video")],n=i.width||"100%",o=i.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),t=this._core.items(this._core.relative(t.index())),this._core.reset(t.index()),(e=u('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ></iframe>')).attr("height",o),e.attr("width",n),"youtube"===i.type?e.attr("src","//www.youtube.com/embed/"+i.id+"?autoplay=1&rel=0&v="+i.id):"vimeo"===i.type?e.attr("src","//player.vimeo.com/video/"+i.id+"?autoplay=1"):"vzaar"===i.type&&e.attr("src","//view.vzaar.com/"+i.id+"/player?autoplay=true"),u(e).wrap('<div class="owl-video-frame" />').insertAfter(t.find(".owl-video")),this._playing=t.addClass("owl-video-playing"))},i.prototype.isInFullScreen=function(){var t=e.fullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement;return t&&u(t).parent().hasClass("owl-video-frame")},i.prototype.destroy=function(){var t,e;for(t in this._core.$element.off("click.owl.video"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},u.fn.owlCarousel.Constructor.Plugins.Video=i})(window.Zepto||window.jQuery,document),(s=>{function e(t){this.core=t,this.core.options=s.extend({},e.Defaults,this.core.options),this.swapping=!0,this.previous=void 0,this.next=void 0,this.handlers={"change.owl.carousel":s.proxy(function(t){t.namespace&&"position"==t.property.name&&(this.previous=this.core.current(),this.next=t.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":s.proxy(function(t){t.namespace&&(this.swapping="translated"==t.type)},this),"translate.owl.carousel":s.proxy(function(t){t.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)},this.core.$element.on(this.handlers)}e.Defaults={animateOut:!1,animateIn:!1},e.prototype.swap=function(){var t,e,i,n,o,r;1===this.core.settings.items&&s.support.animation&&s.support.transition&&(this.core.speed(0),e=s.proxy(this.clear,this),i=this.core.$stage.children().eq(this.previous),n=this.core.$stage.children().eq(this.next),o=this.core.settings.animateIn,r=this.core.settings.animateOut,this.core.current()!==this.previous)&&(r&&(t=this.core.coordinates(this.previous)-this.core.coordinates(this.next),i.one(s.support.animation.end,e).css({left:t+"px"}).addClass("animated owl-animated-out").addClass(r)),o)&&n.one(s.support.animation.end,e).addClass("animated owl-animated-in").addClass(o)},e.prototype.clear=function(t){s(t.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.onTransitionEnd()},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this.core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},s.fn.owlCarousel.Constructor.Plugins.Animate=e})(window.Zepto||window.jQuery,document),((n,o,e)=>{function i(t){this._core=t,this._call=null,this._time=0,this._timeout=0,this._paused=!0,this._handlers={"changed.owl.carousel":n.proxy(function(t){t.namespace&&"settings"===t.property.name?this._core.settings.autoplay?this.play():this.stop():t.namespace&&"position"===t.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":n.proxy(function(t){t.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":n.proxy(function(t,e,i){t.namespace&&this.play(e,i)},this),"stop.owl.autoplay":n.proxy(function(t){t.namespace&&this.stop()},this),"mouseover.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this.play()},this)},this._core.$element.on(this._handlers),this._core.options=n.extend({},i.Defaults,this._core.options)}i.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},i.prototype._next=function(t){this._call=o.setTimeout(n.proxy(this._next,this,t),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read()),this._core.is("interacting")||e.hidden||this._core.next(t||this._core.settings.autoplaySpeed)},i.prototype.read=function(){return(new Date).getTime()-this._time},i.prototype.play=function(t,e){var i;this._core.is("rotating")||this._core.enter("rotating"),t=t||this._core.settings.autoplayTimeout,i=Math.min(this._time%(this._timeout||t),t),this._paused?(this._time=this.read(),this._paused=!1):o.clearTimeout(this._call),this._time+=this.read()%t-i,this._timeout=t,this._call=o.setTimeout(n.proxy(this._next,this,e),t-i)},i.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,o.clearTimeout(this._call),this._core.leave("rotating"))},i.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,o.clearTimeout(this._call))},i.prototype.destroy=function(){var t,e;for(t in this.stop(),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.autoplay=i})(window.Zepto||window.jQuery,window,document),(o=>{function e(t){this._core=t,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+o(t.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"</div>")},this),"added.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,0,this._templates.pop())},this),"remove.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,1)},this),"changed.owl.carousel":o.proxy(function(t){t.namespace&&"position"==t.property.name&&this.draw()},this),"initialized.owl.carousel":o.proxy(function(t){t.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":o.proxy(function(t){t.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)},this._core.options=o.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers)}e.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;</span>','<span aria-label="Next">&#x203a;</span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1},e.prototype.initialize=function(){var t,i=this._core.settings;for(t in this._controls.$relative=(i.navContainer?o(i.navContainer):o("<div>").addClass(i.navContainerClass).appendTo(this.$element)).addClass("disabled"),this._controls.$previous=o("<"+i.navElement+">").addClass(i.navClass[0]).html(i.navText[0]).prependTo(this._controls.$relative).on("click",o.proxy(function(t){this.prev(i.navSpeed)},this)),this._controls.$next=o("<"+i.navElement+">").addClass(i.navClass[1]).html(i.navText[1]).appendTo(this._controls.$relative).on("click",o.proxy(function(t){this.next(i.navSpeed)},this)),i.dotsData||(this._templates=[o('<button role="button">').addClass(i.dotClass).append(o("<span>")).prop("outerHTML")]),this._controls.$absolute=(i.dotsContainer?o(i.dotsContainer):o("<div>").addClass(i.dotsClass).appendTo(this.$element)).addClass("disabled"),this._controls.$absolute.on("click","button",o.proxy(function(t){var e=(o(t.target).parent().is(this._controls.$absolute)?o(t.target):o(t.target).parent()).index();t.preventDefault(),this.to(e,i.dotsSpeed)},this)),this._overrides)this._core[t]=o.proxy(this[t],this)},e.prototype.destroy=function(){var t,e,i,n,o=this._core.settings;for(t in this._handlers)this.$element.off(t,this._handlers[t]);for(e in this._controls)"$relative"===e&&o.navContainer?this._controls[e].html(""):this._controls[e].remove();for(n in this.overides)this._core[n]=this._overrides[n];for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)},e.prototype.update=function(){var t,e,i=this._core.clones().length/2,n=i+this._core.items().length,o=this._core.maximum(!0),r=this._core.settings,s=r.center||r.autoWidth||r.dotsData?1:r.dotsEach||r.items;if("page"!==r.slideBy&&(r.slideBy=Math.min(r.slideBy,r.items)),r.dots||"page"==r.slideBy)for(this._pages=[],t=i,e=0;t<n;t++){if(s<=e||0===e){if(this._pages.push({start:Math.min(o,t-i),end:t-i+s-1}),Math.min(o,t-i)===o)break;e=0,0}e+=this._core.mergers(this._core.relative(t))}},e.prototype.draw=function(){var t=this._core.settings,e=this._core.items().length<=t.items,i=this._core.relative(this._core.current()),n=t.loop||t.rewind;this._controls.$relative.toggleClass("disabled",!t.nav||e),t.nav&&(this._controls.$previous.toggleClass("disabled",!n&&i<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!n&&i>=this._core.maximum(!0))),this._controls.$absolute.toggleClass("disabled",!t.dots||e),t.dots&&(n=this._pages.length-this._controls.$absolute.children().length,t.dotsData&&0!=n?this._controls.$absolute.html(this._templates.join("")):0<n?this._controls.$absolute.append(new Array(1+n).join(this._templates[0])):n<0&&this._controls.$absolute.children().slice(n).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(o.inArray(this.current(),this._pages)).addClass("active"))},e.prototype.onTrigger=function(t){var e=this._core.settings;t.page={index:o.inArray(this.current(),this._pages),count:this._pages.length,size:e&&(e.center||e.autoWidth||e.dotsData?1:e.dotsEach||e.items)}},e.prototype.current=function(){var i=this._core.relative(this._core.current());return o.grep(this._pages,o.proxy(function(t,e){return t.start<=i&&t.end>=i},this)).pop()},e.prototype.getPosition=function(t){var e,i,n=this._core.settings;return"page"==n.slideBy?(e=o.inArray(this.current(),this._pages),i=this._pages.length,t?++e:--e,e=this._pages[(e%i+i)%i].start):(e=this._core.relative(this._core.current()),i=this._core.items().length,t?e+=n.slideBy:e-=n.slideBy),e},e.prototype.next=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!0),t)},e.prototype.prev=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!1),t)},e.prototype.to=function(t,e,i){!i&&this._pages.length?(i=this._pages.length,o.proxy(this._overrides.to,this._core)(this._pages[(t%i+i)%i].start,e)):o.proxy(this._overrides.to,this._core)(t,e)},o.fn.owlCarousel.Constructor.Plugins.Navigation=e})(window.Zepto||window.jQuery,document),((n,o)=>{function e(t){this._core=t,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":n.proxy(function(t){t.namespace&&"URLHash"===this._core.settings.startPosition&&n(o).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":n.proxy(function(t){var e;t.namespace&&(e=n(t.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash"))&&(this._hashes[e]=t.content)},this),"changed.owl.carousel":n.proxy(function(t){var i;t.namespace&&"position"===t.property.name&&(i=this._core.items(this._core.relative(this._core.current())),t=n.map(this._hashes,function(t,e){return t===i?e:null}).join())&&o.location.hash.slice(1)!==t&&(o.location.hash=t)},this)},this._core.options=n.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers),n(o).on("hashchange.owl.navigation",n.proxy(function(t){var e=o.location.hash.substring(1),i=this._core.$stage.children(),i=this._hashes[e]&&i.index(this._hashes[e]);void 0!==i&&i!==this._core.current()&&this._core.to(this._core.relative(i),!1,!0)},this))}e.Defaults={URLhashListener:!1},e.prototype.destroy=function(){var t,e;for(t in n(o).off("hashchange.owl.navigation"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.Hash=e})(window.Zepto||window.jQuery,window,document),(o=>{function e(t,i){var n=!1,e=t.charAt(0).toUpperCase()+t.slice(1);return o.each((t+" "+s.join(e+" ")+e).split(" "),function(t,e){if(void 0!==r[e])return n=!i||e,!1}),n}function t(t){return e(t,!0)}var r=o("<support>").get(0).style,s="Webkit Moz O ms".split(" "),i={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},n=function(){return!!e("transform")},a=function(){return!!e("perspective")},l=function(){return!!e("animation")};!function(){return!!e("transition")}()||(o.support.transition=new String(t("transition")),o.support.transition.end=i.transition.end[o.support.transition]),l()&&(o.support.animation=new String(t("animation")),o.support.animation.end=i.animation.end[o.support.animation]),n()&&(o.support.transform=new String(t("transform")),o.support.transform3d=a())})(window.Zepto||window.jQuery,document),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(window.jQuery||window.Zepto)})(function(c){function t(){}function u(t,e){f.ev.on("mfp"+t+T,e)}function h(t,e,i,n){var o=document.createElement("div");return o.className="mfp-"+t,i&&(o.innerHTML=i),n?e&&e.appendChild(o):(o=c(o),e&&o.appendTo(e)),o}function d(t,e){f.ev.triggerHandler("mfp"+t,e),f.st.callbacks&&(t=t.charAt(0).toLowerCase()+t.slice(1),f.st.callbacks[t])&&f.st.callbacks[t].apply(f,Array.isArray(e)?e:[e])}function p(t){return t===i&&f.currTemplate.closeBtn||(f.currTemplate.closeBtn=c(f.st.closeMarkup.replace("%title%",f.st.tClose)),i=t),f.currTemplate.closeBtn}function r(){c.magnificPopup.instance||((f=new t).init(),c.magnificPopup.instance=f)}function s(){y&&(v.after(y.addClass(l)).detach(),y=null)}function o(){b&&c(document.body).removeClass(b)}function e(){o(),f.req&&f.req.abort()}var f,n,m,a,g,i,l,v,y,b,_,w="Close",x="BeforeClose",C="MarkupParse",T=".mfp",E="mfp-ready",z="mfp-removing",S="mfp-prevent-close",k=!!window.jQuery,I=c(window),A=(c.magnificPopup={instance:null,proto:t.prototype={constructor:t,init:function(){var t=navigator.appVersion;f.isLowIE=f.isIE8=document.all&&!document.addEventListener,f.isAndroid=/android/gi.test(t),f.isIOS=/iphone|ipad|ipod/gi.test(t),f.supportsTransition=(()=>{var t=document.createElement("p").style,e=["ms","O","Moz","Webkit"];if(void 0!==t.transition)return!0;for(;e.length;)if(e.pop()+"Transition"in t)return!0;return!1})(),f.probablyMobile=f.isAndroid||f.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),m=c(document),f.popupsCache={}},open:function(t){if(!1===t.isObj){f.items=t.items.toArray(),f.index=0;for(var e,i=t.items,n=0;n<i.length;n++)if(((e=i[n]).parsed?e.el[0]:e)===t.el[0]){f.index=n;break}}else f.items=Array.isArray(t.items)?t.items:[t.items],f.index=t.index||0;if(!f.isOpen){f.types=[],g="",t.mainEl&&t.mainEl.length?f.ev=t.mainEl.eq(0):f.ev=m,t.key?(f.popupsCache[t.key]||(f.popupsCache[t.key]={}),f.currTemplate=f.popupsCache[t.key]):f.currTemplate={},f.st=c.extend(!0,{},c.magnificPopup.defaults,t),f.fixedContentPos="auto"===f.st.fixedContentPos?!f.probablyMobile:f.st.fixedContentPos,f.st.modal&&(f.st.closeOnContentClick=!1,f.st.closeOnBgClick=!1,f.st.showCloseBtn=!1,f.st.enableEscapeKey=!1),f.bgOverlay||(f.bgOverlay=h("bg").on("click.mfp",function(){f.close()}),f.wrap=h("wrap").attr("tabindex",-1).on("click.mfp",function(t){f._checkIfClose(t.target)&&f.close()}),f.container=h("container",f.wrap)),f.contentContainer=h("content"),f.st.preloader&&(f.preloader=h("preloader",f.container,f.st.tLoading));for(var o=c.magnificPopup.modules,n=0;n<o.length;n++){var r=(r=o[n]).charAt(0).toUpperCase()+r.slice(1);f["init"+r].call(f)}d("BeforeOpen"),f.st.showCloseBtn&&(f.st.closeBtnInside?(u(C,function(t,e,i,n){i.close_replaceWith=p(n.type)}),g+=" mfp-close-btn-in"):f.wrap.append(p())),f.st.alignTop&&(g+=" mfp-align-top"),f.fixedContentPos?f.wrap.css({overflow:f.st.overflowY,overflowX:"hidden",overflowY:f.st.overflowY}):f.wrap.css({top:I.scrollTop(),position:"absolute"}),!1!==f.st.fixedBgPos&&("auto"!==f.st.fixedBgPos||f.fixedContentPos)||f.bgOverlay.css({height:m.height(),position:"absolute"}),f.st.enableEscapeKey&&m.on("keyup.mfp",function(t){27===t.keyCode&&f.close()}),I.on("resize.mfp",function(){f.updateSize()}),f.st.closeOnContentClick||(g+=" mfp-auto-cursor"),g&&f.wrap.addClass(g);var s=f.wH=I.height(),a={},l=(f.fixedContentPos&&f._hasScrollBar(s)&&(l=f._getScrollbarSize())&&(a.marginRight=l),f.fixedContentPos&&(f.isIE7?c("body, html").css("overflow","hidden"):a.overflow="hidden"),f.st.mainClass);return f.isIE7&&(l+=" mfp-ie7"),l&&f._addClassToMFP(l),f.updateItemHTML(),d("BuildControls"),c("html").css(a),f.bgOverlay.add(f.wrap).prependTo(f.st.prependTo||c(document.body)),f._lastFocusedEl=document.activeElement,setTimeout(function(){f.content?(f._addClassToMFP(E),f._setFocus()):f.bgOverlay.addClass(E),m.on("focusin.mfp",f._onFocusIn)},16),f.isOpen=!0,f.updateSize(s),d("Open"),t}f.updateItemHTML()},close:function(){f.isOpen&&(d(x),f.isOpen=!1,f.st.removalDelay&&!f.isLowIE&&f.supportsTransition?(f._addClassToMFP(z),setTimeout(function(){f._close()},f.st.removalDelay)):f._close())},_close:function(){d(w);var t=z+" "+E+" ";f.bgOverlay.detach(),f.wrap.detach(),f.container.empty(),f.st.mainClass&&(t+=f.st.mainClass+" "),f._removeClassFromMFP(t),f.fixedContentPos&&(t={marginRight:""},f.isIE7?c("body, html").css("overflow",""):t.overflow="",c("html").css(t)),m.off("keyup.mfp focusin.mfp"),f.ev.off(T),f.wrap.attr("class","mfp-wrap").removeAttr("style"),f.bgOverlay.attr("class","mfp-bg"),f.container.attr("class","mfp-container"),!f.st.showCloseBtn||f.st.closeBtnInside&&!0!==f.currTemplate[f.currItem.type]||f.currTemplate.closeBtn&&f.currTemplate.closeBtn.detach(),f.st.autoFocusLast&&f._lastFocusedEl&&c(f._lastFocusedEl).trigger("focus"),f.currItem=null,f.content=null,f.currTemplate=null,f.prevHeight=0,d("AfterClose")},updateSize:function(t){var e;f.isIOS?(e=document.documentElement.clientWidth/window.innerWidth,f.wrap.css("height",e*=window.innerHeight),f.wH=e):f.wH=t||I.height(),f.fixedContentPos||f.wrap.css("height",f.wH),d("Resize")},updateItemHTML:function(){var t=f.items[f.index],e=(f.contentContainer.detach(),f.content&&f.content.detach(),(t=t.parsed?t:f.parseEl(f.index)).type),i=(d("BeforeChange",[f.currItem?f.currItem.type:"",e]),f.currItem=t,f.currTemplate[e]||(d("FirstMarkupParse",i=!!f.st[e]&&f.st[e].markup),f.currTemplate[e]=!i||c(i)),a&&a!==t.type&&f.container.removeClass("mfp-"+a+"-holder"),f["get"+e.charAt(0).toUpperCase()+e.slice(1)](t,f.currTemplate[e]));f.appendContent(i,e),t.preloaded=!0,d("Change",t),a=t.type,f.container.prepend(f.contentContainer),d("AfterChange")},appendContent:function(t,e){(f.content=t)?f.st.showCloseBtn&&f.st.closeBtnInside&&!0===f.currTemplate[e]?f.content.find(".mfp-close").length||f.content.append(p()):f.content=t:f.content="",d("BeforeAppend"),f.container.addClass("mfp-"+e+"-holder"),f.contentContainer.append(f.content)},parseEl:function(t){var e,i=f.items[t];if((i=i.tagName?{el:c(i)}:(e=i.type,{data:i,src:i.src})).el){for(var n=f.types,o=0;o<n.length;o++)if(i.el.hasClass("mfp-"+n[o])){e=n[o];break}i.src=i.el.attr("data-mfp-src"),i.src||(i.src=i.el.attr("href"))}return i.type=e||f.st.type||"inline",i.index=t,i.parsed=!0,f.items[t]=i,d("ElementParse",i),f.items[t]},addGroup:function(e,i){function t(t){t.mfpEl=this,f._openClick(t,e,i)}var n="click.magnificPopup";(i=i||{}).mainEl=e,i.items?(i.isObj=!0,e.off(n).on(n,t)):(i.isObj=!1,i.delegate?e.off(n).on(n,i.delegate,t):(i.items=e).off(n).on(n,t))},_openClick:function(t,e,i){var n=(void 0!==i.midClick?i:c.magnificPopup.defaults).midClick;if(n||!(2===t.which||t.ctrlKey||t.metaKey||t.altKey||t.shiftKey)){if(n=(void 0!==i.disableOn?i:c.magnificPopup.defaults).disableOn)if("function"==typeof n){if(!n.call(f))return!0}else if(I.width()<n)return!0;t.type&&(t.preventDefault(),f.isOpen)&&t.stopPropagation(),i.el=c(t.mfpEl),i.delegate&&(i.items=e.find(i.delegate)),f.open(i)}},updateStatus:function(t,e){var i;f.preloader&&(n!==t&&f.container.removeClass("mfp-s-"+n),d("UpdateStatus",i={status:t,text:e=e||"loading"!==t?e:f.st.tLoading}),t=i.status,e=i.text,f.st.allowHTMLInStatusIndicator?f.preloader.html(e):f.preloader.text(e),f.preloader.find("a").on("click",function(t){t.stopImmediatePropagation()}),f.container.addClass("mfp-s-"+t),n=t)},_checkIfClose:function(t){if(!c(t).closest("."+S).length){var e=f.st.closeOnContentClick,i=f.st.closeOnBgClick;if(e&&i)return!0;if(!f.content||c(t).closest(".mfp-close").length||f.preloader&&t===f.preloader[0])return!0;if(t===f.content[0]||c.contains(f.content[0],t)){if(e)return!0}else if(i&&c.contains(document,t))return!0;return!1}},_addClassToMFP:function(t){f.bgOverlay.addClass(t),f.wrap.addClass(t)},_removeClassFromMFP:function(t){this.bgOverlay.removeClass(t),f.wrap.removeClass(t)},_hasScrollBar:function(t){return(f.isIE7?m.height():document.body.scrollHeight)>(t||I.height())},_setFocus:function(){(f.st.focus?f.content.find(f.st.focus).eq(0):f.wrap).trigger("focus")},_onFocusIn:function(t){if(t.target!==f.wrap[0]&&!c.contains(f.wrap[0],t.target))return f._setFocus(),!1},_parseMarkup:function(o,t,e){var r;e.data&&(t=c.extend(e.data,t)),d(C,[o,t,e]),c.each(t,function(t,e){if(void 0===e||!1===e)return!0;var i,n;1<(r=t.split("_")).length?0<(i=o.find(".mfp-"+r[0])).length&&("replaceWith"===(n=r[1])?i[0]!==e[0]&&i.replaceWith(e):"img"===n?i.is("img")?i.attr("src",e):i.replaceWith(c("<img>").attr("src",e).attr("class",i.attr("class"))):i.attr(r[1],e)):f.st.allowHTMLInTemplate?o.find(".mfp-"+t).html(e):o.find(".mfp-"+t).text(e)})},_getScrollbarSize:function(){var t;return void 0===f.scrollbarSize&&((t=document.createElement("div")).style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(t),f.scrollbarSize=t.offsetWidth-t.clientWidth,document.body.removeChild(t)),f.scrollbarSize}},modules:[],open:function(t,e){return r(),(t=t?c.extend(!0,{},t):{}).isObj=!0,t.index=e||0,this.instance.open(t)},close:function(){return c.magnificPopup.instance&&c.magnificPopup.instance.close()},registerModule:function(t,e){e.options&&(c.magnificPopup.defaults[t]=e.options),c.extend(this.proto,e.proto),this.modules.push(t)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&#215;</button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0,allowHTMLInStatusIndicator:!1,allowHTMLInTemplate:!1}},c.fn.magnificPopup=function(t){r();var e,i,n,o=c(this);return"string"==typeof t?"open"===t?(e=k?o.data("magnificPopup"):o[0].magnificPopup,i=parseInt(arguments[1],10)||0,n=e.items?e.items[i]:(n=o,(n=e.delegate?n.find(e.delegate):n).eq(i)),f._openClick({mfpEl:n},o,e)):f.isOpen&&f[t].apply(f,Array.prototype.slice.call(arguments,1)):(t=c.extend(!0,{},t),k?o.data("magnificPopup",t):o[0].magnificPopup=t,f.addGroup(o,t)),o},"inline"),O=(c.magnificPopup.registerModule(A,{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){f.types.push(A),u(w+"."+A,function(){s()})},getInline:function(t,e){var i,n,o;return s(),t.src?(i=f.st.inline,(n=c(t.src)).length?((o=n[0].parentNode)&&o.tagName&&(v||(l=i.hiddenClass,v=h(l),l="mfp-"+l),y=n.after(v).detach().removeClass(l)),f.updateStatus("ready")):(f.updateStatus("error",i.tNotFound),n=c("<div>")),t.inlineElement=n):(f.updateStatus("ready"),f._parseMarkup(e,{},t),e)}}}),"ajax");function L(t){var e;f.currTemplate[j]&&(e=f.currTemplate[j].find("iframe")).length&&(t||(e[0].src="//about:blank"),f.isIE8)&&e.css("display",t?"block":"none")}function D(t){var e=f.items.length;return e-1<t?t-e:t<0?e+t:t}function R(t,e,i){return t.replace(/%curr%/gi,e+1).replace(/%total%/gi,i)}c.magnificPopup.registerModule(O,{options:{settings:null,cursor:"mfp-ajax-cur",tError:"The content could not be loaded."},proto:{initAjax:function(){f.types.push(O),b=f.st.ajax.cursor,u(w+"."+O,e),u("BeforeChange.ajax",e)},getAjax:function(n){b&&c(document.body).addClass(b),f.updateStatus("loading");var t=c.extend({url:n.src,success:function(t,e,i){d("ParseAjax",t={data:t,xhr:i}),f.appendContent(c(t.data),O),n.finished=!0,o(),f._setFocus(),setTimeout(function(){f.wrap.addClass(E)},16),f.updateStatus("ready"),d("AjaxContentAdded")},error:function(){o(),n.finished=n.loadError=!0,f.updateStatus("error",f.st.ajax.tError.replace("%url%",n.src))}},f.st.ajax.settings);return f.req=c.ajax(t),""}}}),c.magnificPopup.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:"The image could not be loaded."},proto:{initImage:function(){var t=f.st.image,e=".image";f.types.push("image"),u("Open"+e,function(){"image"===f.currItem.type&&t.cursor&&c(document.body).addClass(t.cursor)}),u(w+e,function(){t.cursor&&c(document.body).removeClass(t.cursor),I.off("resize.mfp")}),u("Resize"+e,f.resizeImage),f.isLowIE&&u("AfterChange",f.resizeImage)},resizeImage:function(){var t,e=f.currItem;e&&e.img&&f.st.image.verticalFit&&(t=0,f.isLowIE&&(t=parseInt(e.img.css("padding-top"),10)+parseInt(e.img.css("padding-bottom"),10)),e.img.css("max-height",f.wH-t))},_onImageHasSize:function(t){t.img&&(t.hasSize=!0,_&&clearInterval(_),t.isCheckingImgSize=!1,d("ImageHasSize",t),t.imgHidden)&&(f.content&&f.content.removeClass("mfp-loading"),t.imgHidden=!1)},findImageSize:function(i){var n=0,o=i.img[0];!function t(e){_&&clearInterval(_),_=setInterval(function(){0<o.naturalWidth?f._onImageHasSize(i):(200<n&&clearInterval(_),3==++n?t(10):40===n?t(50):100===n&&t(500))},e)}(1)},getImage:function(e,t){function i(){e&&(e.img.off(".mfploader"),e===f.currItem&&(f._onImageHasSize(e),f.updateStatus("error",r.tError.replace("%url%",e.src))),e.hasSize=!0,e.loaded=!0,e.loadError=!0)}var n,o=0,r=f.st.image,s=t.find(".mfp-img");return s.length&&((n=document.createElement("img")).className="mfp-img",e.el&&e.el.find("img").length&&(n.alt=e.el.find("img").attr("alt")),e.img=c(n).on("load.mfploader",function t(){e&&(e.img[0].complete?(e.img.off(".mfploader"),e===f.currItem&&(f._onImageHasSize(e),f.updateStatus("ready")),e.hasSize=!0,e.loaded=!0,d("ImageLoadComplete")):++o<200?setTimeout(t,100):i())}).on("error.mfploader",i),n.src=e.src,s.is("img")&&(e.img=e.img.clone()),0<(n=e.img[0]).naturalWidth?e.hasSize=!0:n.width||(e.hasSize=!1)),f._parseMarkup(t,{title:(t=>{if(t.data&&void 0!==t.data.title)return t.data.title;var e=f.st.image.titleSrc;if(e){if("function"==typeof e)return e.call(f,t);if(t.el)return t.el.attr(e)||""}return""})(e),img_replaceWith:e.img},e),f.resizeImage(),e.hasSize?(_&&clearInterval(_),e.loadError?(t.addClass("mfp-loading"),f.updateStatus("error",r.tError.replace("%url%",e.src))):(t.removeClass("mfp-loading"),f.updateStatus("ready"))):(f.updateStatus("loading"),e.loading=!0,e.hasSize||(e.imgHidden=!0,t.addClass("mfp-loading"),f.findImageSize(e))),t}}}),c.magnificPopup.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(t){return t.is("img")?t:t.find("img")}},proto:{initZoom:function(){var t,e,i,n,o,r,s=f.st.zoom;s.enabled&&f.supportsTransition&&(e=s.duration,i=function(t){var t=t.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),e="all "+s.duration/1e3+"s "+s.easing,i={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},n="transition";return i["-webkit-"+n]=i["-moz-"+n]=i["-o-"+n]=i[n]=e,t.css(i),t},n=function(){f.content.css("visibility","visible")},u("BuildControls.zoom",function(){f._allowZoom()&&(clearTimeout(o),f.content.css("visibility","hidden"),(t=f._getItemToZoom())?((r=i(t)).css(f._getOffset()),f.wrap.append(r),o=setTimeout(function(){r.css(f._getOffset(!0)),o=setTimeout(function(){n(),setTimeout(function(){r.remove(),t=r=null,d("ZoomAnimationEnded")},16)},e)},16)):n())}),u(x+".zoom",function(){if(f._allowZoom()){if(clearTimeout(o),f.st.removalDelay=e,!t){if(!(t=f._getItemToZoom()))return;r=i(t)}r.css(f._getOffset(!0)),f.wrap.append(r),f.content.css("visibility","hidden"),setTimeout(function(){r.css(f._getOffset())},16)}}),u(w+".zoom",function(){f._allowZoom()&&(n(),r&&r.remove(),t=null)}))},_allowZoom:function(){return"image"===f.currItem.type},_getItemToZoom:function(){return!!f.currItem.hasSize&&f.currItem.img},_getOffset:function(t){var e=(t=t?f.currItem.img:f.st.zoom.opener(f.currItem.el||f.currItem)).offset(),i=parseInt(t.css("padding-top"),10),n=parseInt(t.css("padding-bottom"),10),t=(e.top-=c(window).scrollTop()-i,{width:t.width(),height:(k?t.innerHeight():t[0].offsetHeight)-n-i});return(P=void 0===P?void 0!==document.createElement("p").style.MozTransform:P)?t["-moz-transform"]=t.transform="translate("+e.left+"px,"+e.top+"px)":(t.left=e.left,t.top=e.top),t}}});var P,j="iframe",M=(c.magnificPopup.registerModule(j,{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){f.types.push(j),u("BeforeChange",function(t,e,i){e!==i&&(e===j?L():i===j&&L(!0))}),u(w+"."+j,function(){L()})},getIframe:function(t,e){var i=t.src,n=f.st.iframe,o=(c.each(n.patterns,function(){if(-1<i.indexOf(this.index))return this.id&&(i="string"==typeof this.id?i.substr(i.lastIndexOf(this.id)+this.id.length,i.length):this.id.call(this,i)),i=this.src.replace("%id%",i),!1}),{});return n.srcAction&&(o[n.srcAction]=i),f._parseMarkup(e,o,t),f.updateStatus("ready"),e}}}),c.magnificPopup.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%",langDir:null,loop:!0},proto:{initGallery:function(){var r=f.st.gallery,t=".mfp-gallery";if(f.direction=!0,!r||!r.enabled)return!1;r.langDir||(r.langDir=document.dir||"ltr"),g+=" mfp-gallery",u("Open"+t,function(){r.navigateByImgClick&&f.wrap.on("click"+t,".mfp-img",function(){if(1<f.items.length)return f.next(),!1}),m.on("keydown"+t,function(t){37===t.keyCode?"rtl"===r.langDir?f.next():f.prev():39===t.keyCode&&("rtl"===r.langDir?f.prev():f.next())}),f.updateGalleryButtons()}),u("UpdateStatus"+t,function(){f.updateGalleryButtons()}),u("UpdateStatus"+t,function(t,e){e.text&&(e.text=R(e.text,f.currItem.index,f.items.length))}),u(C+t,function(t,e,i,n){var o=f.items.length;i.counter=1<o?R(r.tCounter,n.index,o):""}),u("BuildControls"+t,function(){var t,e,i,n,o;1<f.items.length&&r.arrows&&!f.arrowLeft&&(e="rtl"===r.langDir?(n=r.tNext,t=r.tPrev,o="next","prev"):(n=r.tPrev,t=r.tNext,o="prev","next"),i=r.arrowMarkup,n=f.arrowLeft=c(i.replace(/%title%/gi,n).replace(/%action%/gi,o).replace(/%dir%/gi,"left")).addClass(S),o=f.arrowRight=c(i.replace(/%title%/gi,t).replace(/%action%/gi,e).replace(/%dir%/gi,"right")).addClass(S),"rtl"===r.langDir?(f.arrowNext=n,f.arrowPrev=o):(f.arrowNext=o,f.arrowPrev=n),n.on("click",function(){"rtl"===r.langDir?f.next():f.prev()}),o.on("click",function(){"rtl"===r.langDir?f.prev():f.next()}),f.container.append(n.add(o)))}),u("Change"+t,function(){f._preloadTimeout&&clearTimeout(f._preloadTimeout),f._preloadTimeout=setTimeout(function(){f.preloadNearbyImages(),f._preloadTimeout=null},16)}),u(w+t,function(){m.off(t),f.wrap.off("click"+t),f.arrowRight=f.arrowLeft=null})},next:function(){var t=D(f.index+1);if(!f.st.gallery.loop&&0===t)return!1;f.direction=!0,f.index=t,f.updateItemHTML()},prev:function(){var t=f.index-1;if(!f.st.gallery.loop&&t<0)return!1;f.direction=!1,f.index=D(t),f.updateItemHTML()},goTo:function(t){f.direction=t>=f.index,f.index=t,f.updateItemHTML()},preloadNearbyImages:function(){for(var t=f.st.gallery.preload,e=Math.min(t[0],f.items.length),i=Math.min(t[1],f.items.length),n=1;n<=(f.direction?i:e);n++)f._preloadItem(f.index+n);for(n=1;n<=(f.direction?e:i);n++)f._preloadItem(f.index-n)},_preloadItem:function(t){var e;t=D(t),f.items[t].preloaded||(d("LazyLoad",e=(e=f.items[t]).parsed?e:f.parseEl(t)),"image"===e.type&&(e.img=c('<img class="mfp-img" />').on("load.mfploader",function(){e.hasSize=!0}).on("error.mfploader",function(){e.hasSize=!0,e.loadError=!0,d("LazyLoadError",e)}).attr("src",e.src)),e.preloaded=!0)},updateGalleryButtons:function(){f.st.gallery.loop||"object"!=typeof f.arrowPrev||null===f.arrowPrev||(0===f.index?f.arrowPrev.hide():f.arrowPrev.show(),f.index===f.items.length-1?f.arrowNext.hide():f.arrowNext.show())}}}),"retina");c.magnificPopup.registerModule(M,{options:{replaceSrc:function(t){return t.src.replace(/\.\w+$/,function(t){return"@2x"+t})},ratio:1},proto:{initRetina:function(){var i,n;1<window.devicePixelRatio&&(i=f.st.retina,n=i.ratio,1<(n=isNaN(n)?n():n))&&(u("ImageHasSize."+M,function(t,e){e.img.css({"max-width":e.img[0].naturalWidth/n,width:"100%"})}),u("ElementParse."+M,function(t,e){e.src=i.replaceSrc(e,n)}))}}}),r()}),(()=>{function e(t){if(void 0===t)throw new Error('Pathformer [constructor]: "element" parameter is required');if(t.constructor===String&&!(t=document.getElementById(t)))throw new Error('Pathformer [constructor]: "element" parameter is not related to an existing ID');if(!(t instanceof window.SVGElement||t instanceof window.SVGGElement||/^svg$/i.test(t.nodeName)))throw new Error('Pathformer [constructor]: "element" parameter must be a string or a SVGelement');this.el=t,this.scan(t)}var n,i,t,h;function o(t,e,i){n(),this.isReady=!1,this.setElement(t,e),this.setOptions(e),this.setCallback(i),this.isReady&&this.init()}e.prototype.TYPES=["line","ellipse","circle","polygon","polyline","rect"],e.prototype.ATTR_WATCH=["cx","cy","points","r","rx","ry","x","x1","x2","y","y1","y2"],e.prototype.scan=function(t){for(var e,i,n=t.querySelectorAll(this.TYPES.join(",")),o=0;o<n.length;o++)i=(0,this[(e=n[o]).tagName.toLowerCase()+"ToPath"])(this.parseAttr(e.attributes)),i=this.pathMaker(e,i),e.parentNode.replaceChild(i,e)},e.prototype.lineToPath=function(t){var e={},i=t.x1||0;return e.d="M"+i+","+(t.y1||0)+"L"+(t.x2||0)+","+(t.y2||0),e},e.prototype.rectToPath=function(t){var e,i={},n=parseFloat(t.x)||0,o=parseFloat(t.y)||0,r=parseFloat(t.width)||0,s=parseFloat(t.height)||0;return t.rx||t.ry?(e=parseInt(t.rx,10)||-1,t=parseInt(t.ry,10)||-1,e=Math.min(Math.max(e<0?t:e,0),r/2),t=Math.min(Math.max(t<0?e:t,0),s/2),i.d="M "+(n+e)+","+o+" L "+(n+r-e)+","+o+" A "+e+","+t+",0,0,1,"+(n+r)+","+(o+t)+" L "+(n+r)+","+(o+s-t)+" A "+e+","+t+",0,0,1,"+(n+r-e)+","+(o+s)+" L "+(n+e)+","+(o+s)+" A "+e+","+t+",0,0,1,"+n+","+(o+s-t)+" L "+n+","+(o+t)+" A "+e+","+t+",0,0,1,"+(n+e)+","+o):i.d="M"+n+" "+o+" L"+(n+r)+" "+o+" L"+(n+r)+" "+(o+s)+" L"+n+" "+(o+s)+" Z",i},e.prototype.polylineToPath=function(t){var e,i={},n=t.points.trim().split(" ");if(-1===t.points.indexOf(",")){for(var o=[],r=0;r<n.length;r+=2)o.push(n[r]+","+n[r+1]);n=o}for(e="M"+n[0],r=1;r<n.length;r++)-1!==n[r].indexOf(",")&&(e+="L"+n[r]);return i.d=e,i},e.prototype.polygonToPath=function(t){t=e.prototype.polylineToPath(t);return t.d+="Z",t},e.prototype.ellipseToPath=function(t){var e={},i=parseFloat(t.rx)||0,n=parseFloat(t.ry)||0,o=parseFloat(t.cx)||0,t=parseFloat(t.cy)||0,r=o-i,s=t,o=parseFloat(o)+parseFloat(i);return e.d="M"+r+","+s+"A"+i+","+n+" 0,1,1 "+o+","+t+"A"+i+","+n+" 0,1,1 "+r+","+t,e},e.prototype.circleToPath=function(t){var e={},i=parseFloat(t.r)||0,n=parseFloat(t.cx)||0,t=parseFloat(t.cy)||0,o=n-i,r=t,n=parseFloat(n)+parseFloat(i);return e.d="M"+o+","+r+"A"+i+","+i+" 0,1,1 "+n+","+t+"A"+i+","+i+" 0,1,1 "+o+","+t,e},e.prototype.pathMaker=function(t,e){for(var i,n=document.createElementNS("http://www.w3.org/2000/svg","path"),o=0;o<t.attributes.length;o++)i=t.attributes[o],-1===this.ATTR_WATCH.indexOf(i.name)&&n.setAttribute(i.name,i.value);for(o in e)n.setAttribute(o,e[o]);return n},e.prototype.parseAttr=function(t){for(var e,i={},n=0;n<t.length;n++){if(-1!==this.ATTR_WATCH.indexOf((e=t[n]).name)&&-1!==e.value.indexOf("%"))throw new Error("Pathformer [parseAttr]: a SVG shape got values in percentage. This cannot be transformed into 'path' tags. Please use 'viewBox'.");i[e.name]=e.value}return i},o.LINEAR=function(t){return t},o.EASE=function(t){return-Math.cos(t*Math.PI)/2+.5},o.EASE_OUT=function(t){return 1-Math.pow(1-t,3)},o.EASE_IN=function(t){return Math.pow(t,3)},o.EASE_OUT_BOUNCE=function(t){var e=1-Math.cos(t*(.5*Math.PI)),e=Math.pow(e,1.5),t=Math.pow(1-t,2);return 1-t+(1-Math.abs(Math.cos(e*(2.5*Math.PI))))*t},o.prototype.setElement=function(e,i){var n,t,o;if(void 0===e)throw new Error('Vivus [constructor]: "element" parameter is required');if(e.constructor===String&&!(e=document.getElementById(e)))throw new Error('Vivus [constructor]: "element" parameter is not related to an existing ID');if(this.parentEl=e,i&&i.file)n=this,t=function(){var t=document.createElement("div"),t=(t.innerHTML=this.responseText,t.querySelector("svg"));if(!t)throw new Error("Vivus [load]: Cannot find the SVG in the loaded file : "+i.file);n.el=t,n.el.setAttribute("width","100%"),n.el.setAttribute("height","100%"),n.parentEl.appendChild(n.el),n.isReady=!0,n.init(),n=null},(o=new window.XMLHttpRequest).addEventListener("load",t),o.open("GET",i.file),o.send();else switch(e.constructor){case window.SVGSVGElement:case window.SVGElement:case window.SVGGElement:this.el=e,this.isReady=!0;break;case window.HTMLObjectElement:n=this,(t=function(t){if(!n.isReady){if(n.el=e.contentDocument&&e.contentDocument.querySelector("svg"),!n.el&&t)throw new Error("Vivus [constructor]: object loaded does not contain any SVG");n.el&&(e.getAttribute("built-by-vivus")&&(n.parentEl.insertBefore(n.el,e),n.parentEl.removeChild(e),n.el.setAttribute("width","100%"),n.el.setAttribute("height","100%")),n.isReady=!0,n.init(),n=null)}})()||e.addEventListener("load",t);break;default:throw new Error('Vivus [constructor]: "element" parameter is not valid (or miss the "file" attribute)')}},o.prototype.setOptions=function(t){var e=["delayed","sync","async","nsync","oneByOne","scenario","scenario-sync"],i=["inViewport","manual","autostart"];if(void 0!==t&&t.constructor!==Object)throw new Error('Vivus [constructor]: "options" parameter must be an object');if((t=t||{}).type&&-1===e.indexOf(t.type))throw new Error("Vivus [constructor]: "+t.type+" is not an existing animation `type`");if(this.type=t.type||e[0],t.start&&-1===i.indexOf(t.start))throw new Error("Vivus [constructor]: "+t.start+" is not an existing `start` option");if(this.start=t.start||i[0],this.isIE=-1!==window.navigator.userAgent.indexOf("MSIE")||-1!==window.navigator.userAgent.indexOf("Trident/")||-1!==window.navigator.userAgent.indexOf("Edge/"),this.duration=h(t.duration,120),this.delay=h(t.delay,null),this.dashGap=h(t.dashGap,1),this.forceRender=t.hasOwnProperty("forceRender")?!!t.forceRender:this.isIE,this.reverseStack=!!t.reverseStack,this.selfDestroy=!!t.selfDestroy,this.onReady=t.onReady,this.map=[],this.frameLength=this.currentFrame=this.delayUnit=this.speed=this.handle=null,this.ignoreInvisible=!!t.hasOwnProperty("ignoreInvisible")&&!!t.ignoreInvisible,this.animTimingFunction=t.animTimingFunction||o.LINEAR,this.pathTimingFunction=t.pathTimingFunction||o.LINEAR,this.delay>=this.duration)throw new Error("Vivus [constructor]: delay must be shorter than duration")},o.prototype.setCallback=function(t){if(t&&t.constructor!==Function)throw new Error('Vivus [constructor]: "callback" parameter must be a function');this.callback=t||function(){}},o.prototype.mapping=function(){var t,e,i,n,o=i=n=0,r=this.el.querySelectorAll("path");for(l=!1,t=0;t<r.length;t++){var s,a,l,c,u=r[t];this.isInvisible(u)||(c={el:u,length:0,startAt:0,duration:0,isResizeSensitive:!1},"non-scaling-stroke"===u.getAttribute("vector-effect")?(a=u.getBoundingClientRect(),s=u.getBBox(),a=Math.max(a.width/s.width,a.height/s.height),l=c.isResizeSensitive=!0):a=1,c.length=Math.ceil(u.getTotalLength()*a),isNaN(c.length)?window.console&&console.warn&&console.warn("Vivus [mapping]: cannot retrieve a path element length",u):(this.map.push(c),u.style.strokeDasharray=c.length+" "+(c.length+2*this.dashGap),u.style.strokeDashoffset=c.length+this.dashGap,c.length+=this.dashGap,i+=c.length,this.renderPath(t)))}for(l&&console.warn("Vivus: this SVG contains non-scaling-strokes. You should call instance.recalc() when the SVG is resized or you will encounter unwanted behaviour. See https://github.com/maxwellito/vivus#non-scaling for more info."),i=0===i?1:i,this.delay=null===this.delay?this.duration/3:this.delay,this.delayUnit=this.delay/(1<r.length?r.length-1:1),this.reverseStack&&this.map.reverse(),t=0;t<this.map.length;t++){switch(c=this.map[t],this.type){case"delayed":c.startAt=this.delayUnit*t,c.duration=this.duration-this.delay;break;case"oneByOne":c.startAt=n/i*this.duration,c.duration=c.length/i*this.duration;break;case"sync":case"async":case"nsync":c.startAt=0,c.duration=this.duration;break;case"scenario-sync":u=c.el,e=this.parseAttr(u),c.startAt=o+(h(e["data-delay"],this.delayUnit)||0),c.duration=h(e["data-duration"],this.duration),o=void 0!==e["data-async"]?c.startAt:c.startAt+c.duration,this.frameLength=Math.max(this.frameLength,c.startAt+c.duration);break;case"scenario":u=c.el,e=this.parseAttr(u),c.startAt=h(e["data-start"],this.delayUnit)||0,c.duration=h(e["data-duration"],this.duration),this.frameLength=Math.max(this.frameLength,c.startAt+c.duration)}n+=c.length,this.frameLength=this.frameLength||this.duration}},o.prototype.recalc=function(){this.mustRecalcScale||(this.mustRecalcScale=i(function(){this.performLineRecalc()}.bind(this)))},o.prototype.performLineRecalc=function(){for(var t,e,i,n,o=0;o<this.map.length;o++)(t=this.map[o]).isResizeSensitive&&(n=(e=t.el).getBoundingClientRect(),i=e.getBBox(),n=Math.max(n.width/i.width,n.height/i.height),t.length=Math.ceil(e.getTotalLength()*n),e.style.strokeDasharray=t.length+" "+(t.length+2*this.dashGap));this.trace(),this.mustRecalcScale=null},o.prototype.draw=function(){var t=this;if(this.currentFrame+=this.speed,this.currentFrame<=0)this.stop(),this.reset();else{if(!(this.currentFrame>=this.frameLength))return this.trace(),void(this.handle=i(function(){t.draw()}));this.stop(),this.currentFrame=this.frameLength,this.trace(),this.selfDestroy&&this.destroy()}this.callback(this),this.instanceCallback&&(this.instanceCallback(this),this.instanceCallback=null)},o.prototype.trace=function(){for(var t,e,i=this.animTimingFunction(this.currentFrame/this.frameLength)*this.frameLength,n=0;n<this.map.length;n++)t=(i-(e=this.map[n]).startAt)/e.duration,t=this.pathTimingFunction(Math.max(0,Math.min(1,t))),e.progress!==t&&(e.progress=t,e.el.style.strokeDashoffset=Math.floor(e.length*(1-t)),this.renderPath(n))},o.prototype.renderPath=function(t){var e;this.forceRender&&this.map&&this.map[t]&&(e=(t=this.map[t]).el.cloneNode(!0),t.el.parentNode.replaceChild(e,t.el),t.el=e)},o.prototype.init=function(){this.frameLength=0,this.currentFrame=0,this.map=[],new e(this.el),this.mapping(),this.starter(),this.onReady&&this.onReady(this)},o.prototype.starter=function(){switch(this.start){case"manual":return;case"autostart":this.play();break;case"inViewport":var t=this,e=function(){t.isInViewport(t.parentEl,1)&&(t.play(),window.removeEventListener("scroll",e))};window.addEventListener("scroll",e),e()}},o.prototype.getStatus=function(){return 0===this.currentFrame?"start":this.currentFrame===this.frameLength?"end":"progress"},o.prototype.reset=function(){return this.setFrameProgress(0)},o.prototype.finish=function(){return this.setFrameProgress(1)},o.prototype.setFrameProgress=function(t){return t=Math.min(1,Math.max(0,t)),this.currentFrame=Math.round(this.frameLength*t),this.trace(),this},o.prototype.play=function(t,e){if(this.instanceCallback=null,t&&"function"==typeof t)this.instanceCallback=t,t=null;else if(t&&"number"!=typeof t)throw new Error("Vivus [play]: invalid speed");return e&&"function"==typeof e&&!this.instanceCallback&&(this.instanceCallback=e),this.speed=t||1,this.handle||this.draw(),this},o.prototype.stop=function(){return this.handle&&(t(this.handle),this.handle=null),this},o.prototype.destroy=function(){var t,e;for(this.stop(),t=0;t<this.map.length;t++)(e=this.map[t]).el.style.strokeDashoffset=null,e.el.style.strokeDasharray=null,this.renderPath(t)},o.prototype.isInvisible=function(t){var e=t.getAttribute("data-ignore");return null!==e?"false"!==e:!!this.ignoreInvisible&&!(e=t.getBoundingClientRect()).width&&!e.height},o.prototype.parseAttr=function(t){var e,i={};if(t&&t.attributes)for(var n=0;n<t.attributes.length;n++)i[(e=t.attributes[n]).name]=e.value;return i},o.prototype.isInViewport=function(t,e){var i=this.scrollY(),n=i+this.getViewportH(),t=t.getBoundingClientRect(),o=t.height,t=i+t.top;return t+o*(e=e||0)<=n&&i<=t+o},o.prototype.getViewportH=function(){var t=this.docElem.clientHeight,e=window.innerHeight;return t<e?e:t},o.prototype.scrollY=function(){return window.pageYOffset||this.docElem.scrollTop},n=function(){o.prototype.docElem||(o.prototype.docElem=window.document.documentElement,i=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return window.setTimeout(t,1e3/60)},t=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.oCancelAnimationFrame||window.msCancelAnimationFrame||function(t){return window.clearTimeout(t)})},h=function(t,e){t=parseInt(t,10);return 0<=t?t:e},"function"==typeof define&&define.amd?define([],function(){return o}):"object"==typeof exports?module.exports=o:window.Vivus=o})(),(()=>{var m,i,g,v;function r(t){try{return t.defaultView&&t.defaultView.frameElement||null}catch(t){return null}}function c(t){this.time=t.time,this.target=t.target,this.rootBounds=n(t.rootBounds),this.boundingClientRect=n(t.boundingClientRect),this.intersectionRect=n(t.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!t.intersectionRect;var t=this.boundingClientRect,t=t.width*t.height,e=this.intersectionRect,e=e.width*e.height;this.intersectionRatio=t?Number((e/t).toFixed(4)):this.isIntersecting?1:0}function t(t,e){var i,n,o,e=e||{};if("function"!=typeof t)throw new Error("callback must be a function");if(e.root&&1!=e.root.nodeType&&9!=e.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=(i=this._checkForIntersections.bind(this),n=this.THROTTLE_TIMEOUT,o=null,function(){o=o||setTimeout(function(){i(),o=null},n)}),this._callback=t,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(e.rootMargin),this.thresholds=this._initThresholds(e.threshold),this.root=e.root||null,this.rootMargin=this._rootMarginValues.map(function(t){return t.value+t.unit}).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(t,e,i,n){"function"==typeof t.addEventListener?t.addEventListener(e,i,n||!1):"function"==typeof t.attachEvent&&t.attachEvent("on"+e,i)}function a(t,e,i,n){"function"==typeof t.removeEventListener?t.removeEventListener(e,i,n||!1):"function"==typeof t.detatchEvent&&t.detatchEvent("on"+e,i)}function y(t){var e;try{e=t.getBoundingClientRect()}catch(t){}return e?e=e.width&&e.height?e:{top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:e.right-e.left,height:e.bottom-e.top}:{top:0,bottom:0,left:0,right:0,width:0,height:0}}function n(t){return!t||"x"in t?t:{top:t.top,y:t.top,bottom:t.bottom,left:t.left,x:t.left,right:t.right,width:t.width,height:t.height}}function b(t,e){var i=e.top-t.top,t=e.left-t.left;return{top:i,left:t,height:e.height,width:e.width,bottom:i+e.height,right:t+e.width}}function o(t,e){for(var i=e;i;){if(i==t)return!0;i=_(i)}return!1}function _(t){var e=t.parentNode;return 9==t.nodeType&&t!=m?r(t):(e=e&&e.assignedSlot?e.assignedSlot.parentNode:e)&&11==e.nodeType&&e.host?e.host:e}function l(t){return t&&9===t.nodeType}"object"==typeof window&&("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype?"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return 0<this.intersectionRatio}}):(m=(()=>{for(var t=window.document,e=r(t);e;)e=r(t=e.ownerDocument);return t})(),i=[],v=g=null,t.prototype.THROTTLE_TIMEOUT=100,t.prototype.POLL_INTERVAL=null,t.prototype.USE_MUTATION_OBSERVER=!0,t._setupCrossOriginUpdater=function(){return g=g||function(t,e){v=t&&e?b(t,e):{top:0,bottom:0,left:0,right:0,width:0,height:0},i.forEach(function(t){t._checkForIntersections()})}},t._resetCrossOriginUpdater=function(){v=g=null},t.prototype.observe=function(e){if(!this._observationTargets.some(function(t){return t.element==e})){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},t.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter(function(t){return t.element!=e}),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},t.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},t.prototype.takeRecords=function(){var t=this._queuedEntries.slice();return this._queuedEntries=[],t},t.prototype._initThresholds=function(t){t=t||[0];return(t=Array.isArray(t)?t:[t]).sort().filter(function(t,e,i){if("number"!=typeof t||isNaN(t)||t<0||1<t)throw new Error("threshold must be a number between 0 and 1 inclusively");return t!==i[e-1]})},t.prototype._parseRootMargin=function(t){t=(t||"0px").split(/\s+/).map(function(t){t=/^(-?\d*\.?\d+)(px|%)$/.exec(t);if(t)return{value:parseFloat(t[1]),unit:t[2]};throw new Error("rootMargin must be specified in pixels or percent")});return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},t.prototype._monitorIntersections=function(e){var i,n,o,t=e.defaultView;t&&-1==this._monitoringDocuments.indexOf(e)&&(i=this._checkForIntersections,o=n=null,this.POLL_INTERVAL?n=t.setInterval(i,this.POLL_INTERVAL):(s(t,"resize",i,!0),s(e,"scroll",i,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in t&&(o=new t.MutationObserver(i)).observe(e,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(e),this._monitoringUnsubscribes.push(function(){var t=e.defaultView;t&&(n&&t.clearInterval(n),a(t,"resize",i,!0)),a(e,"scroll",i,!0),o&&o.disconnect()}),t=this.root&&(this.root.ownerDocument||this.root)||m,e!=t)&&(t=r(e))&&this._monitorIntersections(t.ownerDocument)},t.prototype._unmonitorIntersections=function(n){var o,t,e=this._monitoringDocuments.indexOf(n);-1!=e&&(o=this.root&&(this.root.ownerDocument||this.root)||m,this._observationTargets.some(function(t){var e=t.element.ownerDocument;if(e==n)return!0;for(;e&&e!=o;){var i=r(e);if((e=i&&i.ownerDocument)==n)return!0}return!1})||(t=this._monitoringUnsubscribes[e],this._monitoringDocuments.splice(e,1),this._monitoringUnsubscribes.splice(e,1),t(),n!=o&&(e=r(n))&&this._unmonitorIntersections(e.ownerDocument)))},t.prototype._unmonitorAllIntersections=function(){var t=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0;for(var e=this._monitoringUnsubscribes.length=0;e<t.length;e++)t[e]()},t.prototype._checkForIntersections=function(){var a,l;(this.root||!g||v)&&(a=this._rootIsInDom(),l=a?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0},this._observationTargets.forEach(function(t){var e=t.element,i=y(e),n=this._rootContainsTarget(e),o=t.entry,r=a&&n&&this._computeTargetAndRootIntersection(e,i,l),s=null,t=(this._rootContainsTarget(e)?g&&!this.root||(s=l):s={top:0,bottom:0,left:0,right:0,width:0,height:0},t.entry=new c({time:window.performance&&performance.now&&performance.now(),target:e,boundingClientRect:i,rootBounds:s,intersectionRect:r}));o?a&&n?this._hasCrossedThreshold(o,t)&&this._queuedEntries.push(t):o&&o.isIntersecting&&this._queuedEntries.push(t):this._queuedEntries.push(t)},this),this._queuedEntries.length)&&this._callback(this.takeRecords(),this)},t.prototype._computeTargetAndRootIntersection=function(t,e,i){if("none"!=window.getComputedStyle(t).display){for(var n,o,r,s,a=e,l=_(t),c=!1;!c&&l;){var u,h,d,p=null,f=1==l.nodeType?window.getComputedStyle(l):{};if("none"==f.display)return null;if(l==this.root||9==l.nodeType?(c=!0,l==this.root||l==m?g&&!this.root?!v||0==v.width&&0==v.height?a=p=l=null:p=v:p=i:(u=(d=_(l))&&y(d),h=d&&this._computeTargetAndRootIntersection(d,u,i),u&&h?(l=d,p=b(u,h)):a=l=null)):l!=(d=l.ownerDocument).body&&l!=d.documentElement&&"visible"!=f.overflow&&(p=y(l)),p&&(u=p,h=a,0,f=Math.max(u.top,h.top),p=Math.min(u.bottom,h.bottom),n=Math.max(u.left,h.left),s=p-f,a=0<=(r=(o=Math.min(u.right,h.right))-n)&&0<=s?{top:f,bottom:p,left:n,right:o,width:r,height:s}:null),!a)break;l=l&&_(l)}return a}},t.prototype._getRootRect=function(){var t,e;return e=this.root&&!l(this.root)?y(this.root):(e=(t=l(this.root)?this.root:m).documentElement,t=t.body,{top:0,left:0,right:e.clientWidth||t.clientWidth,width:e.clientWidth||t.clientWidth,bottom:e.clientHeight||t.clientHeight,height:e.clientHeight||t.clientHeight}),this._expandRectByRootMargin(e)},t.prototype._expandRectByRootMargin=function(i){var t=this._rootMarginValues.map(function(t,e){return"px"==t.unit?t.value:t.value*(e%2?i.width:i.height)/100}),t={top:i.top-t[0],right:i.right+t[1],bottom:i.bottom+t[2],left:i.left-t[3]};return t.width=t.right-t.left,t.height=t.bottom-t.top,t},t.prototype._hasCrossedThreshold=function(t,e){var i=t&&t.isIntersecting?t.intersectionRatio||0:-1,n=e.isIntersecting?e.intersectionRatio||0:-1;if(i!==n)for(var o=0;o<this.thresholds.length;o++){var r=this.thresholds[o];if(r==i||r==n||r<i!=r<n)return!0}},t.prototype._rootIsInDom=function(){return!this.root||o(m,this.root)},t.prototype._rootContainsTarget=function(t){var e=this.root&&(this.root.ownerDocument||this.root)||m;return o(e,t)&&(!this.root||e==t.ownerDocument)},t.prototype._registerInstance=function(){i.indexOf(this)<0&&i.push(this)},t.prototype._unregisterInstance=function(){var t=i.indexOf(this);-1!=t&&i.splice(t,1)},window.IntersectionObserver=t,window.IntersectionObserverEntry=c))})(),(d=>{d.fn.pin=function(l){function i(){n(),t()}var a=0,c=[],u=!1,h=d(window),n=(l=l||{},function(){for(var t=0,e=c.length;t<e;t++){var i,n,o,r,s,a=c[t];l.minWidth&&h.outerWidth()<=l.minWidth?(a.parent().is(".pin-wrapper")&&a.unwrap(),a.css({width:"",left:"",top:"",position:""}),l.activeClass&&a.removeClass(l.activeClass),u=!0):(u=!1,i=l.containerSelector?a.closest(l.containerSelector):d(document.body),n=a.offset(),o=i.offset(),r=a.parent().offset(),s=(a.parent().is(".pin-wrapper")||a.wrap("<div class='pin-wrapper'>"),d.extend({top:0,bottom:0},l.padding||{})),a.data("pin",{pad:s,from:(l.containerSelector?o:n).top-s.top,to:o.top+i.height()-a.outerHeight()-s.bottom,end:o.top+i.height(),parentTop:r.top}),a.css({width:a.outerWidth()}),a.parent().css("height",a.outerHeight()))}}),t=function(){if(!u){a=h.scrollTop();for(var t=[],e=0,i=c.length;e<i;e++){var n,o,r=d(c[e]),s=r.data("pin");s&&(t.push(r),n=s.from-s.pad.bottom,o=s.to-s.pad.top,n+r.outerHeight()>s.end?r.css("position",""):n<a&&a<o?("fixed"!=r.css("position")&&r.css({left:r.offset().left,top:s.pad.top}).css("position","fixed"),l.activeClass&&r.addClass(l.activeClass)):o<=a?(r.css({left:"",top:o-s.parentTop+s.pad.top}).css("position","absolute"),l.activeClass&&r.addClass(l.activeClass)):(r.css({position:"",top:"",left:""}),l.activeClass&&r.removeClass(l.activeClass)))}c=t}};return this.each(function(){var t=d(this),e=d(this).data("pin")||{};e&&e.update||(c.push(t),d("img",this).one("load",n),e.update=i,d(this).data("pin",e))}),h.scroll(t),h.resize(function(){n()}),n(),h.on("load",i),this}})(jQuery),(i=>{var o,r={action:function(){},runOnLoad:!1,duration:500},s=!1,n={init:function(){for(var t=0;t<=arguments.length;t++){var e=arguments[t];switch(typeof e){case"function":r.action=e;break;case"boolean":r.runOnLoad=e;break;case"number":r.duration=e}}return this.each(function(){r.runOnLoad&&r.action(),i(this).resize(function(){n.timedAction.call(this)})})}};n.timedAction=function(t,e){function i(t){s=setTimeout(n,t)}var n=function(){var t=r.duration;if(s){var e=new Date-o;if((t=r.duration-e)<=0)return clearTimeout(s),s=!1,void r.action()}i(t)};o=new Date,"number"==typeof e&&(r.duration=e),"function"==typeof t&&(r.action=t),s||n()},i.fn.afterResize=function(t){return n[t]?n[t].apply(this,Array.prototype.slice.call(arguments,1)):n.init.apply(this,arguments)}})(jQuery),jQuery(document).ready(function(s){var a=2500,r=3800,l=r-3e3,o=50,c=150,u=500,h=u+800,d=600,n=1500,t=".word-rotator",p=a;function f(t){var e,i,n=v(t);t.parents(".word-rotator").hasClass("type")?((e=t.parent(".word-rotator-words")).addClass("selected").removeClass("waiting"),setTimeout(function(){e.removeClass("selected"),t.removeClass("is-visible").addClass("is-hidden").children("i").removeClass("in").addClass("out")},u),setTimeout(function(){m(n,c)},h)):t.parents(".word-rotator").hasClass("letters")?(i=t.children("i").length>=n.children("i").length,function t(e,i,n,o){e.removeClass("in").addClass("out");e.is(":last-child")?n&&setTimeout(function(){f(v(i))},a):setTimeout(function(){t(e.next(),i,n,o)},o);{var r;e.is(":last-child")&&s("html").hasClass("no-csstransitions")&&(r=v(i),y(i,r))}}(t.find("i").eq(0),t,i,o),g(n.find("i").eq(0),n,i,o)):t.parents(".word-rotator").hasClass("clip")?t.parents(".word-rotator-words").stop(!0,!0).animate({width:"2px"},d,function(){y(t,n),m(n)}):t.parents(".word-rotator").hasClass("loading-bar")?(t.parents(".word-rotator-words").removeClass("is-loading"),y(t,n),setTimeout(function(){f(n)},r),setTimeout(function(){t.parents(".word-rotator-words").addClass("is-loading")},l)):(y(t,n),setTimeout(function(){f(n)},a))}function m(t,e){t.parents(".word-rotator").hasClass("type")?(g(t.find("i").eq(0),t,!1,e),t.addClass("is-visible").removeClass("is-hidden")):t.parents(".word-rotator").hasClass("clip")&&(document.hasFocus()?t.parents(".word-rotator-words").stop(!0,!0).animate({width:t.outerWidth()+10},d,function(){setTimeout(function(){f(t)},n)}):(t.parents(".word-rotator-words").stop(!0,!0).animate({width:t.outerWidth()+10}),setTimeout(function(){f(t)},n)))}function g(t,e,i,n){t.addClass("in").removeClass("out"),t.is(":last-child")?(e.parents(".word-rotator").hasClass("type")&&setTimeout(function(){e.parents(".word-rotator-words").addClass("waiting")},200),i||setTimeout(function(){f(e)},a),e.closest(".word-rotator").hasClass("type")||e.closest(".word-rotator-words").stop(!0,!0).animate({width:e.outerWidth()})):setTimeout(function(){g(t.next(),e,i,n)},n)}function v(t){return t.is(":last-child")?t.parent().children().eq(0):t.next()}function y(t,e){var i;t.removeClass("is-visible").addClass("is-hidden"),e.removeClass("is-hidden").addClass("is-visible"),e.closest(".word-rotator").hasClass("clip")||(i=0,t=e.outerWidth()>t.outerWidth()?0:600,(e.closest(".word-rotator").hasClass("loading-bar")||e.closest(".word-rotator").hasClass("slide"))&&(i=3,t=0),setTimeout(function(){e.closest(".word-rotator-words").stop(!0,!0).animate({width:e.outerWidth()+i})},t))}theme.fn.intObs(t,function(){s(this).hasClass("letters")&&s(this).find("b").each(function(){var t=s(this),e=t.text().split(""),n=t.hasClass("is-visible");for(i in e)0<t.parents(".rotate-2").length&&(e[i]="<em>"+e[i]+"</em>"),e[i]=n?'<i class="in">'+e[i]+"</i>":"<i>"+e[i]+"</i>";var o=e.join("");t.html(o).css("opacity",1)});var t,e,n,o=s(this);o.hasClass("loading-bar")?(p=r,setTimeout(function(){o.find(".word-rotator-words").addClass("is-loading")},l)):o.hasClass("clip")?(t=(e=o.find(".word-rotator-words")).outerWidth()+10,e.css("width",t)):o.hasClass("type")||(e=o.find(".word-rotator-words b"),n=0,e.each(function(){var t=s(this).outerWidth();n<t&&(n=t)}),o.find(".word-rotator-words").css("width",n)),setTimeout(function(){f(o.find(".is-visible").eq(0))},p)},{})}),(e=>{e.fn.hover3d=function(t){var c=e.extend({selector:null,perspective:1e3,sensitivity:20,invert:!1,shine:!1,hoverInClass:"hover-in",hoverOutClass:"hover-out",hoverClass:"hover-3d"},t);return this.each(function(){var t=e(this),a=t.find(c.selector),l=(currentX=0,currentY=0,c.shine&&a.append('<div class="shine"></div>'),e(this).find(".shine"));t.css({perspective:c.perspective+"px",transformStyle:"preserve-3d"}),a.css({perspective:c.perspective+"px",transformStyle:"preserve-3d"}),l.css({position:"absolute",top:0,left:0,bottom:0,right:0,transform:"translateZ(1px)","z-index":9}),t.on("mouseenter",function(){a.addClass(c.hoverInClass+" "+c.hoverClass),currentX=currentY=0,setTimeout(function(){a.removeClass(c.hoverInClass)},1e3)}),t.on("mousemove",function(t){return t=t,e=a.innerWidth(),i=a.innerHeight(),n=Math.round(t.pageX-a.offset().left),o=Math.round(t.pageY-a.offset().top),r=c.invert?(e/2-n)/c.sensitivity:-(e/2-n)/c.sensitivity,s=c.invert?-(i/2-o)/c.sensitivity:(i/2-o)/c.sensitivity,(o=180*Math.atan2(o-i/2,n-e/2)/Math.PI-90)<0&&(o+=360),a.css({perspective:c.perspective+"px",transformStyle:"preserve-3d",transform:"rotateY("+r+"deg) rotateX("+s+"deg)"}),void l.css("background","linear-gradient("+o+"deg, rgba(255,255,255,"+t.offsetY/i*.5+") 0%,var(--light-rgba-0) 80%)");var e,i,n,o,r,s}),t.on("mouseleave",function(){a.addClass(c.hoverOutClass+" "+c.hoverClass),a.css({perspective:c.perspective+"px",transformStyle:"preserve-3d",transform:"rotateX(0) rotateY(0)"}),setTimeout(function(){a.removeClass(c.hoverOutClass+" "+c.hoverClass),currentX=currentY=0},1e3)})})}})(jQuery),((r,e)=>{function n(t){e.console&&e.console.error(t)}r.HoverDir=function(t,e){this.$el=r(e),this._init(t)},r.HoverDir.defaults={speed:300,easing:"ease",hoverDelay:0,inverse:!1,hoverElem:".thumb-info-wrapper-overlay"},r.HoverDir.prototype={_init:function(t){this.options=r.extend(!0,{},r.HoverDir.defaults,t),this.transitionProp="all "+this.options.speed+"ms "+this.options.easing,this.support=!0,this._loadEvents()},_loadEvents:function(){var o=this;this.$el.on("mouseenter.hoverdir, mouseleave.hoverdir",function(t){var e=r(this),i=e.find(o.options.hoverElem),e=o._getDir(e,{x:t.pageX,y:t.pageY}),n=o._getStyle(e);"mouseenter"===t.type?(i.hide().css(n.from),clearTimeout(o.tmhover),o.tmhover=setTimeout(function(){i.show(0,function(){var t=r(this);o.support&&t.css("transition",o.transitionProp),o._applyAnimation(t,n.to,o.options.speed)})},o.options.hoverDelay)):(o.support&&i.css("transition",o.transitionProp),clearTimeout(o.tmhover),o._applyAnimation(i,n.from,o.options.speed))})},_getDir:function(t,e){var i=t.width(),n=t.height(),o=(e.x-t.offset().left-i/2)*(n<i?n/i:1),e=(e.y-t.offset().top-n/2)*(i<n?i/n:1);return Math.round((Math.atan2(e,o)*(180/Math.PI)+180)/90+3)%4},_getStyle:function(t){var e,i,n={left:"0px",top:"-100%"},o={left:"0px",top:"100%"},r={left:"-100%",top:"0px"},s={left:"100%",top:"0px"},a={top:"0px"},l={left:"0px"};switch(t){case 0:e=this.options.inverse?o:n,i=a;break;case 1:e=this.options.inverse?r:s,i=l;break;case 2:e=this.options.inverse?n:o,i=a;break;case 3:e=this.options.inverse?s:r,i=l}return{from:e,to:i}},_applyAnimation:function(t,e,i){r.fn.applyStyle=this.support?r.fn.css:r.fn.animate,t.stop().applyStyle(e,r.extend(!0,[],{duration:i+"ms"}))}},r.fn.hoverdir=function(t){var e,i=r.data(this,"hoverdir");return"string"==typeof t?(e=Array.prototype.slice.call(arguments,1),this.each(function(){i?r.isFunction(i[t])&&"_"!==t.charAt(0)?i[t].apply(i,e):n("no such method '"+t+"' for hoverdir instance"):n("cannot call methods on hoverdir prior to initialization; attempted to call method '"+t+"'")})):this.each(function(){i?i._init():i=r.data(this,"hoverdir",new r.HoverDir(t,this))}),i}})(jQuery,window),((t,e)=>{"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?e(require("jquery")):e(t.jQuery)})(this,function(p){var n="vide",o={volume:1,playbackRate:1,muted:!0,loop:!0,autoplay:!0,position:"50% 50%",posterType:"detect",resizing:!0,bgColor:"transparent",className:""},f="Not implemented";function r(t){for(var e,i,n,o,r={},s=t.replace(/\s*:\s*/g,":").replace(/\s*,\s*/g,",").split(","),a=0,l=s.length;a<l&&-1===(i=s[a]).search(/^(http|https|ftp):\/\//)&&-1!==i.search(":");a++)e=i.indexOf(":"),n=i.substring(0,e),"string"==typeof(o="string"==typeof(o=(o=i.substring(e+1))||void 0)?"true"===o||"false"!==o&&o:o)&&(o=isNaN(o)?o:+o),r[n]=o;return null==n&&null==o?t:r}function s(t,e,i){if(this.$element=p(t),"string"==typeof e&&(e=r(e)),i?"string"==typeof i&&(i=r(i)):i={},"string"==typeof e)e=e.replace(/\.\w*$/,"");else if("object"==typeof e)for(var n in e)e.hasOwnProperty(n)&&(e[n]=e[n].replace(/\.\w*$/,""));this.settings=p.extend({},o,i),this.path=e;try{this.init()}catch(t){if(t.message!==f)throw t}}s.prototype.init=function(){var t,e,i,n,o=this,r=o.path,s=r,a="",l=o.$element,c=o.settings,u=(t=>{for(var e,i=(t=""+t).split(/\s+/),n="50%",o="50%",r=0,s=i.length;r<s;r++)"left"===(e=i[r])?n="0%":"right"===e?n="100%":"top"===e?o="0%":"bottom"===e?o="100%":"center"===e?0===r?n="50%":o="50%":0===r?n=e:o=e;return{x:n,y:o}})(c.position),h=c.posterType;function d(){n(this.src)}e=o.$wrapper=p("<div>").addClass(c.className).css({position:"absolute","z-index":-1,top:0,left:0,bottom:0,right:0,overflow:"hidden","-webkit-background-size":"cover","-moz-background-size":"cover","-o-background-size":"cover","background-size":"cover","background-color":c.bgColor,"background-repeat":"no-repeat","background-position":u.x+" "+u.y}),"object"==typeof r&&(r.poster?s=r.poster:r.mp4?s=r.mp4:r.webm?s=r.webm:r.ogv&&(s=r.ogv)),"detect"===h?(n=function(t){e.css("background-image","url("+t+")")},p('<img src="'+(i=s)+'.gif">').on("load",d),p('<img src="'+i+'.jpg">').on("load",d),p('<img src="'+i+'.jpeg">').on("load",d),p('<img src="'+i+'.png">').on("load",d)):"none"!==h&&e.css("background-image","url("+s+"."+h+")"),"static"===l.css("position")&&l.css("position","relative"),l.prepend(e),t="object"==typeof r?(r.mp4&&(a+='<source src="'+r.mp4+'.mp4" type="video/mp4">'),r.webm&&(a+='<source src="'+r.webm+'.webm" type="video/webm">'),r.ogv&&(a+='<source src="'+r.ogv+'.ogv" type="video/ogg">'),o.$video=p("<video>"+a+"</video>")):o.$video=p('<video><source src="'+r+'.mp4" type="video/mp4"><source src="'+r+'.webm" type="video/webm"><source src="'+r+'.ogv" type="video/ogg"></video>');try{t.prop({autoplay:c.autoplay,loop:c.loop,volume:c.volume,muted:c.muted,defaultMuted:c.muted,playbackRate:c.playbackRate,defaultPlaybackRate:c.playbackRate}),c.autoplay&&t.attr("playsinline","")}catch(t){throw new Error(f)}t.css({margin:"auto",position:"absolute","z-index":-1,top:u.y,left:u.x,"-webkit-transform":"translate(-"+u.x+", -"+u.y+")","-ms-transform":"translate(-"+u.x+", -"+u.y+")","-moz-transform":"translate(-"+u.x+", -"+u.y+")",transform:"translate(-"+u.x+", -"+u.y+")",opacity:0}).one("canplaythrough.vide",function(){o.resize()}).one("playing.vide",function(){t.css({visibility:"visible",opacity:1}),e.css("background-image","none")}),l.on("resize.vide",function(){c.resizing&&o.resize()}),e.append(t)},s.prototype.getVideoObject=function(){return this.$video[0]},s.prototype.resize=function(){var t,e,i,n,o;this.$video&&(t=this.$wrapper,o=(i=(e=this.$video)[0]).videoHeight,i=i.videoWidth,(n=t.height())/o<(o=t.width())/i?e.css({width:o+2,height:"auto"}):e.css({width:"auto",height:n+2}))},s.prototype.destroy=function(){delete p.vide.lookup[this.index],this.$video&&this.$video.off(n),this.$element.off(n).removeData(n),this.$wrapper.remove()},p.vide={lookup:[]},p.fn.vide=function(t,e){var i;return this.each(function(){(i=p.data(this,n))&&i.destroy(),(i=new s(this,t,e)).index=p.vide.lookup.push(i)-1,p.data(this,n,i)}),this},p(document).ready(function(){var t=p(window);t.on("resize.vide",function(){for(var t,e=p.vide.lookup.length,i=0;i<e;i++)(t=p.vide.lookup[i])&&t.settings.resizing&&t.resize()}),t.on("pagehide.vide",function(){return!1}),p(document).find("[data-vide-bg]").each(function(t,e){var e=p(e),i=e.data("vide-options"),n=e.data("vide-bg");e.vide(n,i)})})}),(t=>{"function"==typeof define&&define.amd&&define.amd.jQuery?define(["jquery"],t):t("undefined"!=typeof module&&module.exports?require("jquery"):jQuery)})(function(at){function n(z,l){function t(t){if(!(!0===k.data(Dt+"_intouch")||0<at(t.target).closest(l.excludedElements,k).length)){var e,i,n,o=t.originalEvent||t;if(!o.pointerType||"mouse"!=o.pointerType||0!=l.fallbackToMouseEvents)return n=(i=o.touches)?i[0]:o,I=Et,i?A=i.length:!1!==l.preventDefaultEvents&&t.preventDefault(),E=b=y=null,C=1,T=x=w=_=v=0,(t={})[lt]=a(lt),t[ct]=a(ct),t[ut]=a(ut),t[ht]=a(ht),S=t,Z(),d(0,n),!i||A===l.fingers||l.fingers===Ct||u()?(rt=p(),2==A&&(d(1,i[1]),w=x=et(O[0].start,O[1].start)),(l.swipeStatus||l.pinchStatus)&&(e=c(o,I))):e=!1,!1===e?(c(o,I=It),e):(l.hold&&(M=setTimeout(at.proxy(function(){k.trigger("hold",[o.target]),l.hold&&(e=l.hold.call(k,o,o.target))},this),l.longTapThreshold)),s(!0),null)}}function e(t){var e=t.originalEvent||t;if(I!==kt&&I!==It&&!h()){var i,n=e.touches,o=K(n?n[0]:e);if(L=p(),n&&(A=n.length),l.hold&&clearTimeout(M),I=St,2==A&&(0==w?(d(1,n[1]),w=x=et(O[0].start,O[1].start)):(K(n[1]),x=et(O[0].end,O[1].end),O[0].end,O[1].end,E=C<1?pt:dt),C=(x/w*1).toFixed(2),T=Math.abs(w-x)),A===l.fingers||l.fingers===Ct||!n||u()){y=it(o.start,o.end),b=it(o.last,o.end);var r,s=t,n=b;if(!1!==l.preventDefaultEvents)if(l.allowPageScroll===ft)s.preventDefault();else{var a=l.allowPageScroll===mt;switch(n){case lt:(l.swipeLeft&&a||!a&&l.allowPageScroll!=wt)&&s.preventDefault();break;case ct:(l.swipeRight&&a||!a&&l.allowPageScroll!=wt)&&s.preventDefault();break;case ut:(l.swipeUp&&a||!a&&l.allowPageScroll!=xt)&&s.preventDefault();break;case ht:(l.swipeDown&&a||!a&&l.allowPageScroll!=xt)&&s.preventDefault()}}t=o.start,n=o.end,v=Math.round(Math.sqrt(Math.pow(n.x-t.x,2)+Math.pow(n.y-t.y,2))),_=tt(),n=v,(t=y)!=ft&&(n=Math.max(n,J(t)),S[t].distance=n),i=c(e,I),l.triggerOnTouchEnd&&!l.triggerOnTouchLeave||(t=!0,l.triggerOnTouchLeave&&(r={left:(r=(n=at(n=this)).offset()).left,right:r.left+n.outerWidth(),top:r.top,bottom:r.top+n.outerHeight()},n=o.end,o=r,t=n.x>o.left&&n.x<o.right&&n.y>o.top&&n.y<o.bottom),!l.triggerOnTouchEnd&&t?I=q(St):l.triggerOnTouchLeave&&!t&&(I=q(kt)),I!=It&&I!=kt)||c(e,I)}else c(e,I=It);!1===i&&c(e,I=It)}}function i(t){var e,i=t.originalEvent||t,n=i.touches;if(n){if(n.length&&!h())return e=i,D=p(),st=e.touches.length+1,!0;if(n.length&&h())return!0}return h()&&(A=st),L=p(),_=tt(),F()||!H()?c(i,I=It):l.triggerOnTouchEnd||!1===l.triggerOnTouchEnd&&I===St?(!1!==l.preventDefaultEvents&&!1!==t.cancelable&&t.preventDefault(),c(i,I=kt)):!l.triggerOnTouchEnd&&Y()?r(i,I=kt,yt):I===St&&c(i,I=It),s(!1),null}function o(){x=w=rt=L=A=0,Z(),s(!(C=1))}function R(t){t=t.originalEvent||t;l.triggerOnTouchLeave&&c(t,I=q(kt))}function N(){k.off(f,t),k.off(g,o),k.off(nt,e),k.off(ot,i),m&&k.off(m,R),s(!1)}function q(t){var e=t,i=W(),n=H(),o=F();return!i||o?e=It:!n||t!=St||l.triggerOnTouchEnd&&!l.triggerOnTouchLeave?!n&&t==kt&&l.triggerOnTouchLeave&&(e=It):e=kt,e}function c(t,e){var i,n=t.touches;return(B()&&V()||V())&&(i=r(t,e,gt)),($()&&u()||u())&&!1!==i&&(i=r(t,e,vt)),G()&&X()&&!1!==i?i=r(t,e,bt):_>l.longTapThreshold&&v<Tt&&l.longTap&&!1!==i?i=r(t,e,_t):1!==A&&At||!(isNaN(v)||v<l.threshold)||!Y()||!1===i||(i=r(t,e,yt)),e===It&&o(),e!==kt||n&&n.length||o(),i}function r(t,e,i){var n;if(i==gt){if(k.trigger("swipeStatus",[e,y||null,v||0,_||0,A,O,b]),l.swipeStatus&&!1===(n=l.swipeStatus.call(k,t,e,y||null,v||0,_||0,A,O,b)))return!1;if(e==kt&&B()){if(clearTimeout(j),clearTimeout(M),k.trigger("swipe",[y,v,_,A,O,b]),l.swipe&&!1===(n=l.swipe.call(k,t,y,v,_,A,O,b)))return!1;switch(y){case lt:k.trigger("swipeLeft",[y,v,_,A,O,b]),l.swipeLeft&&(n=l.swipeLeft.call(k,t,y,v,_,A,O,b));break;case ct:k.trigger("swipeRight",[y,v,_,A,O,b]),l.swipeRight&&(n=l.swipeRight.call(k,t,y,v,_,A,O,b));break;case ut:k.trigger("swipeUp",[y,v,_,A,O,b]),l.swipeUp&&(n=l.swipeUp.call(k,t,y,v,_,A,O,b));break;case ht:k.trigger("swipeDown",[y,v,_,A,O,b]),l.swipeDown&&(n=l.swipeDown.call(k,t,y,v,_,A,O,b))}}}if(i==vt){if(k.trigger("pinchStatus",[e,E||null,T||0,_||0,A,C,O]),l.pinchStatus&&!1===(n=l.pinchStatus.call(k,t,e,E||null,T||0,_||0,A,C,O)))return!1;if(e==kt&&$())switch(E){case dt:k.trigger("pinchIn",[E||null,T||0,_||0,A,C,O]),l.pinchIn&&(n=l.pinchIn.call(k,t,E||null,T||0,_||0,A,C,O));break;case pt:k.trigger("pinchOut",[E||null,T||0,_||0,A,C,O]),l.pinchOut&&(n=l.pinchOut.call(k,t,E||null,T||0,_||0,A,C,O))}}return i==yt?e!==It&&e!==kt||(clearTimeout(j),clearTimeout(M),X()&&!G()?(P=p(),j=setTimeout(at.proxy(function(){P=null,k.trigger("tap",[t.target]),l.tap&&(n=l.tap.call(k,t,t.target))},this),l.doubleTapThreshold)):(P=null,k.trigger("tap",[t.target]),l.tap&&(n=l.tap.call(k,t,t.target)))):i==bt?e!==It&&e!==kt||(clearTimeout(j),clearTimeout(M),P=null,k.trigger("doubletap",[t.target]),l.doubleTap&&(n=l.doubleTap.call(k,t,t.target))):i!=_t||e!==It&&e!==kt||(clearTimeout(j),P=null,k.trigger("longtap",[t.target]),l.longTap&&(n=l.longTap.call(k,t,t.target))),n}function H(){var t=!0;return t=null!==l.threshold?v>=l.threshold:t}function F(){var t=!1;return t=null!==l.cancelThreshold&&null!==y?J(y)-v>=l.cancelThreshold:t}function W(){return!(l.maxTimeThreshold&&_>=l.maxTimeThreshold)}function $(){var t=U(),e=Q(),i=null===l.pinchThreshold||T>=l.pinchThreshold;return t&&e&&i}function u(){return l.pinchStatus||l.pinchIn||l.pinchOut}function B(){var t=W(),e=H(),i=U(),n=Q();return!F()&&n&&i&&e&&t}function V(){return l.swipe||l.swipeStatus||l.swipeLeft||l.swipeRight||l.swipeUp||l.swipeDown}function U(){return A===l.fingers||l.fingers===Ct||!At}function Q(){return 0!==O[0].end.x}function Y(){return!!l.tap}function X(){return!!l.doubleTap}function G(){var t;return null!=P&&(t=p(),X())&&t-P<=l.doubleTapThreshold}function Z(){st=D=0}function h(){var t=!1;return t=D&&p()-D<=l.fingerReleaseThreshold?!0:t}function s(t){k&&(!0===t?(k.on(nt,e),k.on(ot,i),m&&k.on(m,R)):(k.off(nt,e,!1),k.off(ot,i,!1),m&&k.off(m,R,!1)),k.data(Dt+"_intouch",!0===t))}function d(t,e){var i={start:{x:0,y:0},last:{x:0,y:0},end:{x:0,y:0}};return i.start.x=i.last.x=i.end.x=e.pageX||e.clientX,i.start.y=i.last.y=i.end.y=e.pageY||e.clientY,O[t]=i}function K(t){var e=void 0!==t.identifier?t.identifier:0,i=O[e]||null;return(i=null===i?d(e,t):i).last.x=i.end.x,i.last.y=i.end.y,i.end.x=t.pageX||t.clientX,i.end.y=t.pageY||t.clientY,i}function J(t){return S[t]?S[t].distance:void 0}function a(t){return{direction:t,distance:0}}function tt(){return L-rt}function et(t,e){var i=Math.abs(t.x-e.x),t=Math.abs(t.y-e.y);return Math.round(Math.sqrt(i*i+t*t))}function it(t,e){var i,n;return i=e,(n=t).x==i.x&&n.y==i.y?ft:(n=e,t=(i=t).x-e.x,n=Math.atan2(e.y-i.y,t),i=(i=Math.round(180*n/Math.PI))<0?360-Math.abs(i):i,i<=45&&0<=i||i<=360&&315<=i?lt:135<=i&&i<=225?ct:45<i&&i<135?ht:ut)}function p(){return(new Date).getTime()}var l=at.extend({},l),n=At||Lt||!l.fallbackToMouseEvents,f=n?Lt?Ot?"MSPointerDown":"pointerdown":"touchstart":"mousedown",nt=n?Lt?Ot?"MSPointerMove":"pointermove":"touchmove":"mousemove",ot=n?Lt?Ot?"MSPointerUp":"pointerup":"touchend":"mouseup",m=!n||Lt?"mouseleave":null,g=Lt?Ot?"MSPointerCancel":"pointercancel":"touchcancel",v=0,y=null,b=null,_=0,w=0,x=0,C=1,T=0,E=0,S=null,k=at(z),I="start",A=0,O={},rt=0,L=0,D=0,st=0,P=0,j=null,M=null;try{k.on(f,t),k.on(g,o)}catch(t){at.error("events not supported "+f+","+g+" on jQuery.swipe")}this.enable=function(){return this.disable(),k.on(f,t),k.on(g,o),k},this.disable=function(){return N(),k},this.destroy=function(){N(),k.data(Dt,null),k=null},this.option=function(t,e){if("object"==typeof t)l=at.extend(l,t);else if(void 0!==l[t]){if(void 0===e)return l[t];l[t]=e}else{if(!t)return l;at.error("Option "+t+" does not exist on jQuery.swipe.options")}return null}}var lt="left",ct="right",ut="up",ht="down",dt="in",pt="out",ft="none",mt="auto",gt="swipe",vt="pinch",yt="tap",bt="doubletap",_t="longtap",wt="horizontal",xt="vertical",Ct="all",Tt=10,Et="start",St="move",kt="end",It="cancel",At="ontouchstart"in window,Ot=window.navigator.msPointerEnabled&&!window.PointerEvent&&!At,Lt=(window.PointerEvent||window.navigator.msPointerEnabled)&&!At,Dt="TouchSwipe";at.fn.swipe=function(t){var e=at(this),i=e.data(Dt);if(i&&"string"==typeof t){if(i[t])return i[t].apply(i,Array.prototype.slice.call(arguments,1));at.error("Method "+t+" does not exist on jQuery.swipe")}else if(i&&"object"==typeof t)i.option.apply(i,arguments);else if(!(i||"object"!=typeof t&&t))return function(i){return!i||void 0!==i.allowPageScroll||void 0===i.swipe&&void 0===i.swipeStatus||(i.allowPageScroll=ft),void 0!==i.click&&void 0===i.tap&&(i.tap=i.click),i=i||{},i=at.extend({},at.fn.swipe.defaults,i),this.each(function(){var t,e=at(this);e.data(Dt)||(t=new n(this,i),e.data(Dt,t))})}.apply(this,arguments);return e},at.fn.swipe.version="1.6.18",at.fn.swipe.defaults={fingers:1,threshold:75,cancelThreshold:null,pinchThreshold:20,maxTimeThreshold:null,fingerReleaseThreshold:250,longTapThreshold:500,doubleTapThreshold:200,swipe:null,swipeLeft:null,swipeRight:null,swipeUp:null,swipeDown:null,swipeStatus:null,pinchIn:null,pinchOut:null,pinchStatus:null,click:null,tap:null,doubleTap:null,longTap:null,hold:null,triggerOnTouchEnd:!0,triggerOnTouchLeave:!1,allowPageScroll:"auto",fallbackToMouseEvents:!0,excludedElements:".noSwipe",preventDefaultEvents:!0},at.fn.swipe.phases={PHASE_START:Et,PHASE_MOVE:St,PHASE_END:kt,PHASE_CANCEL:It},at.fn.swipe.directions={LEFT:lt,RIGHT:ct,UP:ut,DOWN:ht,IN:dt,OUT:pt},at.fn.swipe.pageScroll={NONE:ft,HORIZONTAL:wt,VERTICAL:xt,AUTO:mt},at.fn.swipe.fingers={ONE:1,TWO:2,THREE:3,FOUR:4,FIVE:5,ALL:Ct}}),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(jQuery)})(function(r){function s(t,e){this.$element=r(t),this.options=r.extend({},s.DEFAULTS,this.dataOptions(),e),this.init()}s.DEFAULTS={from:0,to:0,speed:1e3,refreshInterval:100,decimals:0,formatter:function(t,e){return t.toFixed(e.decimals)},onUpdate:null,onComplete:null},s.prototype.init=function(){this.value=this.options.from,this.loops=Math.ceil(this.options.speed/this.options.refreshInterval),this.loopCount=0,this.increment=(this.options.to-this.options.from)/this.loops},s.prototype.dataOptions=function(){var t,e={from:this.$element.data("from"),to:this.$element.data("to"),speed:this.$element.data("speed"),refreshInterval:this.$element.data("refresh-interval"),decimals:this.$element.data("decimals")},i=Object.keys(e);for(t in i){var n=i[t];void 0===e[n]&&delete e[n]}return e},s.prototype.update=function(){this.value+=this.increment,this.loopCount++,this.render(),"function"==typeof this.options.onUpdate&&this.options.onUpdate.call(this.$element,this.value),this.loopCount>=this.loops&&(clearInterval(this.interval),this.value=this.options.to,"function"==typeof this.options.onComplete)&&this.options.onComplete.call(this.$element,this.value)},s.prototype.render=function(){var t=this.options.formatter.call(this.$element,this.value,this.options);this.$element.text(t)},s.prototype.restart=function(){this.stop(),this.init(),this.start()},s.prototype.start=function(){this.stop(),this.render(),this.interval=setInterval(this.update.bind(this),this.options.refreshInterval)},s.prototype.stop=function(){this.interval&&clearInterval(this.interval)},s.prototype.toggle=function(){this.interval?this.stop():this.start()},r.fn.countTo=function(o){return this.each(function(){var t=r(this),e=t.data("countTo"),i="object"==typeof o?o:{},n="string"==typeof o?o:"start";e&&"object"!=typeof o||(e&&e.stop(),t.data("countTo",e=new s(this,i))),e[n].call(e)})}}),(p=>{p.fn.visible=function(t,e,i,n){var o,r,s,a,l,c,u,h,d;if(!(this.length<1))return u=1<this.length?this.eq(0):this,n=p((c=null!=n)?n:window),h=c?n.position():0,r=u.get(0),l=n.outerWidth(),d=n.outerHeight(),i=i||"both",e=!0!==e||r.offsetWidth*r.offsetHeight,"function"==typeof r.getBoundingClientRect?(r=r.getBoundingClientRect(),s=c?0<=r.top-h.top&&r.top<d+h.top:0<=r.top&&r.top<d,a=c?0<r.bottom-h.top&&r.bottom<=d+h.top:0<r.bottom&&r.bottom<=d,o=c?0<=r.left-h.left&&r.left<l+h.left:0<=r.left&&r.left<l,r=c?0<r.right-h.left&&r.right<l+h.left:0<r.right&&r.right<=l,s=t?s||a:s&&a,a=t?o||r:o&&r,"both"===i?e&&s&&a:"vertical"===i?e&&s:"horizontal"===i?e&&a:void 0):(r=(o=c?0:h)+d,a=(s=n.scrollLeft())+l,d=(h=(c=u.position()).top)+u.height(),l=(n=c.left)+u.width(),c=!0===t?d:h,u=!0===t?h:d,h=!0===t?l:n,d=!0===t?n:l,"both"===i?!!e&&u<=r&&o<=c&&d<=a&&s<=h:"vertical"===i?!!e&&u<=r&&o<=c:"horizontal"===i?!!e&&d<=a&&s<=h:void 0)}})(jQuery),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery)})(function(s){function a(t){return parseFloat(t)||0}function l(t){var t=s(t),n=null,o=[];return t.each(function(){var t=s(this),e=t.offset().top-a(t.css("margin-top")),i=0<o.length?o[o.length-1]:null;null!==i&&Math.floor(Math.abs(n-e))<=1?o[o.length-1]=i.add(t):o.push(t),n=e}),o}function c(t){var e={byRow:!0,property:"height",target:null,remove:!1};return"object"==typeof t?s.extend(e,t):("boolean"==typeof t?e.byRow=t:"remove"===t&&(e.remove=!0),e)}function n(t){u._beforeUpdate&&u._beforeUpdate(t,u._groups),s.each(u._groups,function(){u._apply(this.elements,this.options)}),u._afterUpdate&&u._afterUpdate(t,u._groups)}var o=-1,r=-1,u=s.fn.matchHeight=function(t){var i,t=c(t);return t.remove?((i=this).css(t.property,""),s.each(u._groups,function(t,e){e.elements=e.elements.not(i)})):this.length<=1&&!t.target||(u._groups.push({elements:this,options:t}),u._apply(this,t)),this},t=(u.version="0.7.2",u._groups=[],u._throttle=80,u._maintainScroll=!1,u._beforeUpdate=null,u._afterUpdate=null,u._rows=l,u._parse=a,u._parseOptions=c,u._apply=function(t,e){var o=c(e),e=s(t),t=[e],i=s(window).scrollTop(),n=s("html").outerHeight(!0),r=e.parents().filter(":hidden");return r.each(function(){var t=s(this);t.data("style-cache",t.attr("style"))}),r.css("display","block"),o.byRow&&!o.target&&(e.each(function(){var t=s(this),e=t.css("display");"inline-block"!==e&&"flex"!==e&&"inline-flex"!==e&&(e="block"),t.data("style-cache",t.attr("style")),t.css({display:e,"padding-top":"0","padding-bottom":"0","margin-top":"0","margin-bottom":"0","border-top-width":"0","border-bottom-width":"0",height:"100px",overflow:"hidden"})}),t=l(e),e.each(function(){var t=s(this);t.attr("style",t.data("style-cache")||"")})),s.each(t,function(t,e){var e=s(e),n=0;if(o.target)n=o.target.outerHeight(!1);else{if(o.byRow&&e.length<=1)return void e.css(o.property,"");e.each(function(){var t=s(this),e=t.attr("style"),i=t.css("display"),i={display:i="inline-block"!==i&&"flex"!==i&&"inline-flex"!==i?"block":i};i[o.property]="",t.css(i),t.outerHeight(!1)>n&&(n=t.outerHeight(!1)),e?t.attr("style",e):t.css("display","")})}e.each(function(){var t=s(this),e=0;o.target&&t.is(o.target)||("border-box"!==t.css("box-sizing")&&(e=(e+=a(t.css("border-top-width"))+a(t.css("border-bottom-width")))+(a(t.css("padding-top"))+a(t.css("padding-bottom")))),t.css(o.property,n-e+"px"))})}),r.each(function(){var t=s(this);t.attr("style",t.data("style-cache")||null)}),u._maintainScroll&&s(window).scrollTop(i/n*s("html").outerHeight(!0)),this},u._applyDataApi=function(){var i={};s("[data-match-height], [data-mh]").each(function(){var t=s(this),e=t.attr("data-mh")||t.attr("data-match-height");e in i?i[e]=i[e].add(t):i[e]=t}),s.each(i,function(){this.matchHeight(!0)})},u._update=function(t,e){if(e&&"resize"===e.type){var i=s(window).width();if(i===o)return;o=i}t?-1===r&&(r=setTimeout(function(){n(e),r=-1},u._throttle)):n(e)},s(u._applyDataApi),s.fn.on?"on":"bind");s(window)[t]("load",function(t){u._update(!1,t)}),s(window)[t]("resize orientationchange",function(t){u._update(!0,t)})}),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(jQuery)})(function(d){var t,p="waitForImages",f=(t=new Image).srcset&&t.sizes;d.waitForImages={hasImageProperties:["backgroundImage","listStyleImage","borderImage","borderCornerImage","cursor"],hasImageAttributes:["srcset"]},d.expr.pseudos["has-src"]=function(t){return d(t).is('img[src][src!=""]')},d.expr.pseudos.uncached=function(t){return!!d(t).is(":has-src")&&!t.complete},d.fn.waitForImages=function(){var o,r,e,s=0,a=0,l=d.Deferred(),c=this,u=[],i=d.waitForImages.hasImageProperties||[],n=d.waitForImages.hasImageAttributes||[],h=/url\(\s*(['"]?)(.*?)\1\s*\)/g;if(d.isPlainObject(arguments[0])?(e=arguments[0].waitForAll,r=arguments[0].each,o=arguments[0].finished):e=1===arguments.length&&"boolean"===d.type(arguments[0])?arguments[0]:(o=arguments[0],r=arguments[1],arguments[2]),o=o||d.noop,r=r||d.noop,e=!!e,d.isFunction(o)&&d.isFunction(r))return this.each(function(){var t=d(this);e?t.find("*").addBack().each(function(){var o=d(this);o.is("img:has-src")&&!o.is("[srcset]")&&u.push({src:o.attr("src"),element:o[0]}),d.each(i,function(t,e){var i,n=o.css(e);if(!n)return!0;for(;i=h.exec(n);)u.push({src:i[2],element:o[0]})}),d.each(n,function(t,e){if(!o.attr(e))return!0;u.push({src:o.attr("src"),srcset:o.attr("srcset"),element:o[0]})})}):t.find("img:has-src").each(function(){u.push({src:this.src,element:this})})}),(a=0)===(s=u.length)&&(o.call(c),l.resolveWith(c)),d.each(u,function(t,i){var e=new Image,n="load."+p+" error."+p;d(e).one(n,function t(e){e=[a,s,"load"==e.type];if(a++,r.apply(i.element,e),l.notifyWith(i.element,e),d(this).off(n,t),a==s)return o.call(c[0]),l.resolveWith(c[0]),!1}),f&&i.srcset&&(e.srcset=i.srcset,e.sizes=i.sizes),e.src=i.src}),l.promise();throw new TypeError("An invalid callback was supplied.")}}),(h=>{fontSpy=function(t,e){var i=h("html"),n=h("body");if("string"!=typeof t||""===t)throw"A valid fontName is required. fontName must be a string and must not be an empty string.";function o(){i.addClass("no-"+a.fontClass),a&&a.failure&&a.failure(),a.callback(new Error("FontSpy timeout")),l.remove()}function r(){a.callback(),i.addClass(a.fontClass),a&&a.success&&a.success(),l.remove()}function s(){setTimeout(u,a.delay),a.timeOut=a.timeOut-a.delay}var t={font:t,fontClass:t.toLowerCase().replace(/\s/g,""),success:function(){},failure:function(){},testFont:"Courier New",testString:"QW@HhsXJ",glyphs:"",delay:50,timeOut:1e3,callback:h.noop},a=h.extend(t,e),l=h("<span>"+a.testString+a.glyphs+"</span>").css("position","absolute").css("top","-9999px").css("left","-9999px").css("visibility","hidden").css("fontFamily",a.testFont).css("fontSize","250px"),c=(n.append(l),l.outerWidth()),u=(l.css("fontFamily",a.font+","+a.testFont),function(){var t=l.outerWidth();(c!==t?r:a.timeOut<0?o:s)()});u()}})(jQuery),((t,e)=>{"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.observeElementInViewport={})})(this,function(t){function o(n,o,r,t){if(void 0===r&&(r=function(){}),void 0===t&&(t={}),!n)throw new Error("Target element to observe should be a valid DOM Node");var s,e,t=Object.assign({},{viewport:null,modTop:"0px",modRight:"0px",modBottom:"0px",modLeft:"0px",threshold:[0]},t),i=t.viewport,a=t.modTop,l=t.modLeft,c=t.modBottom,u=t.modRight,t=t.threshold;if(Array.isArray(t)||"number"==typeof t)return t=Array.isArray(t)?t.map(function(t){return Math.floor(t%101)/100}):[Math.floor(t?t%101:0)/100],s=Math.min.apply(Math,t),i={root:i instanceof Node?i:null,rootMargin:a+" "+u+" "+c+" "+l,threshold:t},(e=new IntersectionObserver(function(t,e){function i(){return e.unobserve(n)}t=t.filter(function(t){return t.target===n})[0];t&&(t.isInViewport=t.isIntersecting&&t.intersectionRatio>=s,(t.isInViewport?o:r)(t,i,n))},i)).observe(n),function(){return e.unobserve(n)};throw new Error("threshold should be a number or an array of numbers")}t.observeElementInViewport=o,t.isInViewport=function(t,n){return void 0===n&&(n={}),new Promise(function(i,e){try{o(t,function(t,e){e(),i(!0)},function(t,e){e(),i(!1)},n)}catch(t){e(t)}})}}),(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"undefined"!=typeof exports?module.exports=t(require("jquery")):t(jQuery)})(function(O){O.fn.marquee=function(A){return this.each(function(){var i,n,o,r,s,a=O.extend({},O.fn.marquee.defaults,A),l=O(this),c=3,t="animation-play-state",u=!1,h=function(t,e,i){for(var n=["webkit","moz","MS","o",""],o=0;o<n.length;o++)n[o]||(e=e.toLowerCase()),t.addEventListener(n[o]+e,i,!1)},d=function(t){var e,i=[];for(e in t)t.hasOwnProperty(e)&&i.push(e+":"+t[e]);return i.push(),"{"+i.join(",")+"}"},p=function(){l.timer=setTimeout(I,a.delayBeforeStart)},e={pause:function(){u&&a.allowCss3Support?i.css(t,"paused"):O.fn.pause&&i.pause(),l.data("runningStatus","paused"),l.trigger("paused")},resume:function(){u&&a.allowCss3Support?i.css(t,"running"):O.fn.resume&&i.resume(),l.data("runningStatus","resumed"),l.trigger("resumed")},toggle:function(){e["resumed"===l.data("runningStatus")?"pause":"resume"]()},destroy:function(){clearTimeout(l.timer),l.find("*").addBack().off(),l.html(l.find(".js-marquee:first").html())}};if("string"==typeof A)O.isFunction(e[A])&&(i=i||l.find(".js-marquee-wrapper"),!0===l.data("css3AnimationIsSupported")&&(u=!0),e[A]());else{O.each(a,function(t){if(void 0!==(f=l.attr("data-"+t))){switch(f){case"true":f=!0;break;case"false":f=!1}a[t]=f}}),a.speed&&(a.duration=parseInt(l.width(),10)/a.speed*1e3),r="up"===a.direction||"down"===a.direction,a.gap=a.duplicated?parseInt(a.gap):0,l.wrapInner('<div class="js-marquee"></div>');var f,m,g,v=l.find(".js-marquee").css({"margin-right":a.gap,float:"left"});if(a.duplicated){a.duplicateCount<=0&&(a.duplicateCount=1);for(let t=0;t<a.duplicateCount;t++)v.clone(!0).appendTo(l)}if(l.wrapInner('<div style="width:100000px" class="js-marquee-wrapper"></div>'),i=l.find(".js-marquee-wrapper"),r?(m=l.height(),i.removeAttr("style"),l.height(m),l.find(".js-marquee").css({float:"none","margin-bottom":a.gap,"margin-right":0}),a.duplicated&&l.find(".js-marquee:last").css({"margin-bottom":0}),g=l.find(".js-marquee:first").height()+a.gap,a.startVisible&&!a.duplicated?(a._completeDuration=(parseInt(g,10)+parseInt(m,10))/parseInt(m,10)*a.duration,a.duration=parseInt(g,10)/parseInt(m,10)*a.duration):a.duration=(parseInt(g,10)+parseInt(m,10))/parseInt(m,10)*a.duration):(s=l.find(".js-marquee:first").width()+a.gap,n=l.width(),a.startVisible&&!a.duplicated?(a._completeDuration=(parseInt(s,10)+parseInt(n,10))/parseInt(n,10)*a.duration,a.duration=parseInt(s,10)/parseInt(n,10)*a.duration):a.duration=(parseInt(s,10)+parseInt(n,10))/parseInt(n,10)*a.duration),a.duplicated&&(a.duration=a.duration/2),a.allowCss3Support){var y=document.body||document.createElement("div"),b="marqueeAnimation-"+Math.floor(1e7*Math.random()),_="Webkit Moz O ms Khtml".split(" "),w="animation",x="",C="";if(void 0!==y.style.animation&&(C="@keyframes "+b+" ",u=!0),!1===u)for(var T=0;T<_.length;T++)if(void 0!==y.style[_[T]+"AnimationName"]){var E="-"+_[T].toLowerCase()+"-",w=E+w,t=E+t,C="@"+E+"keyframes "+b+" ",u=!0;break}u&&(x=b+" "+a.duration/1e3+"s "+a.delayBeforeStart/1e3+"s infinite "+a.css3easing,l.data("css3AnimationIsSupported",!0))}var S=function(){i.css("transform","translateY("+("up"===a.direction?m+"px":"-"+g+"px")+")")},k=function(){i.css("transform","translateX("+("left"===a.direction?n+"px":"-"+s+"px")+")")},I=(a.duplicated?(r?a.startVisible?i.css("transform","translateY(0)"):i.css("transform","translateY("+("up"===a.direction?m+"px":"-"+(2*g-a.gap)+"px")+")"):a.startVisible?i.css("transform","translateX(0)"):i.css("transform","translateX("+("left"===a.direction?n+"px":"-"+(2*s-a.gap)+"px")+")"),a.startVisible||(c=1)):a.startVisible?c=2:(r?S:k)(),function(){var t,e;a.duplicated&&(1===c?(a._originalDuration=a.duration,a.duration=r?"up"===a.direction?a.duration+m/(g/a.duration):2*a.duration:"left"===a.direction?a.duration+n/(s/a.duration):2*a.duration,x=x&&b+" "+a.duration/1e3+"s "+a.delayBeforeStart/1e3+"s "+a.css3easing,c++):2===c&&(a.duration=a._originalDuration,x&&(b+="0",C=O.trim(C)+"0 ",x=b+" "+a.duration/1e3+"s 0s infinite "+a.css3easing),c++)),r?a.duplicated?(2<c&&i.css("transform","translateY("+("up"===a.direction?0:"-"+g+"px")+")"),o={transform:"translateY("+("up"===a.direction?"-"+g+"px":0)+")"}):a.startVisible?2===c?(x=x&&b+" "+a.duration/1e3+"s "+a.delayBeforeStart/1e3+"s "+a.css3easing,o={transform:"translateY("+("up"===a.direction?"-"+g+"px":m+"px")+")"},c++):3===c&&(a.duration=a._completeDuration,x&&(b+="0",C=O.trim(C)+"0 ",x=b+" "+a.duration/1e3+"s 0s infinite "+a.css3easing),S()):(S(),o={transform:"translateY("+("up"===a.direction?"-"+i.height()+"px":m+"px")+")"}):a.duplicated?(2<c&&i.css("transform","translateX("+("left"===a.direction?0:"-"+s+"px")+")"),o={transform:"translateX("+("left"===a.direction?"-"+s+"px":0)+")"}):a.startVisible?2===c?(x=x&&b+" "+a.duration/1e3+"s "+a.delayBeforeStart/1e3+"s "+a.css3easing,o={transform:"translateX("+("left"===a.direction?"-"+s+"px":n+"px")+")"},c++):3===c&&(a.duration=a._completeDuration,x&&(b+="0",C=O.trim(C)+"0 ",x=b+" "+a.duration/1e3+"s 0s infinite "+a.css3easing),k()):(k(),o={transform:"translateX("+("left"===a.direction?"-"+s+"px":n+"px")+")"}),l.trigger("beforeStarting"),u?(i.css(w,x),t=C+" { 100%  "+d(o)+"}",0!==(e=i.find("style")).length?e.filter(":last").html(t):O("head").append("<style>"+t+"</style>"),h(i[0],"AnimationIteration",function(){l.trigger("finished")}),h(i[0],"AnimationEnd",function(){I(),l.trigger("finished")})):i.animate(o,a.duration,a.easing,function(){l.trigger("finished"),(a.pauseOnCycle?p:I)()}),l.data("runningStatus","resumed")});l.on("pause",e.pause),l.on("resume",e.resume),a.pauseOnHover&&(l.on("mouseenter",e.pause),l.on("mouseleave",e.resume)),(u&&a.allowCss3Support?I:p)()}})},O.fn.marquee.defaults={allowCss3Support:!0,css3easing:"linear",easing:"linear",delayBeforeStart:1e3,direction:"left",duplicated:!1,duplicateCount:1,duration:5e3,speed:0,gap:20,pauseOnCycle:!1,pauseOnHover:!1,startVisible:!1}});