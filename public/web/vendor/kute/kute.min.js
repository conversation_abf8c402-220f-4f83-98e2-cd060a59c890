((t,e)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).KUTE=e()})(this,function(){function e(t,e,n,r,a){function i(t){return o.sampleCurveY(o.solveCurveX(t))}var o=this,t=t||0,e=e||0,n=n||1,r=r||1;return this.cx=3*t,this.bx=3*(n-t)-this.cx,this.ax=1-this.cx-this.bx,this.cy=3*e,this.by=3*(r-e)-this.cy,this.ay=1-this.cy-this.by,Object.defineProperty(i,"name",{writable:!0}),i.name=a||"cubic-bezier("+[t,e,n,r]+")",i}e.prototype.sampleCurveX=function(t){return((this.ax*t+this.bx)*t+this.cx)*t},e.prototype.sampleCurveY=function(t){return((this.ay*t+this.by)*t+this.cy)*t},e.prototype.sampleCurveDerivativeX=function(t){return(3*this.ax*t+2*this.bx)*t+this.cx},e.prototype.solveCurveX=function(t){if(t<=0)return 0;if(1<=t)return 1;for(var e,n=t,r=0,a=0;a<8;a+=1){if(r=this.sampleCurveX(n)-t,Math.abs(r)<1e-6)return n;if(e=this.sampleCurveDerivativeX(n),Math.abs(e)<1e-6)break;n-=r/e}for(var i=0,o=1,n=t;i<o;){if(r=this.sampleCurveX(n),Math.abs(r-t)<1e-6)return n;r<t?i=n:o=n,n=.5*(o-i)+i}return n};function n(t){for(var e=0;e<r.length;)r[e].update(t)?e+=1:r.splice(e,1);a=requestAnimationFrame(n)}Object.assign(e,{Version:"1.0.1"});var s={},r=[],L="undefined"!=typeof global?global:"undefined"!=typeof window?window.self:{},o={},l={},q="undefined"==typeof self&&"undefined"!=typeof process&&process.hrtime?function(){var t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:"undefined"!=typeof self&&void 0!==self.performance&&void 0!==self.performance.now?self.performance.now.bind(self.performance):"undefined"!=typeof Date&&Date.now?Date.now:function(){return(new Date).getTime()},H={},a=(H.now=q,0);function F(){setTimeout(function(){!r.length&&a&&(cancelAnimationFrame(a),a=null,Object.keys(l).forEach(function(t){"function"==typeof l[t]?s[t]&&delete s[t]:Object.keys(l[t]).forEach(function(t){s[t]&&delete s[t]})}),Object.keys(o).forEach(function(t){s[t]&&delete s[t]}))},64)}function Q(t){return r.push(t)}function U(t){-1!==(t=r.indexOf(t))&&r.splice(t,1)}var D={Tick:a,Ticker:n,Tweens:r,Time:H},u=(Object.keys(D).forEach(function(t){s[t]||(s[t]="Time"===t?H.now:D[t])}),L._KUTE=s,{}),p={},c={duration:700,delay:0,easing:"linear",repeat:0,repeatDelay:0,yoyo:!1,resetStart:!1,offset:0},h={},f={},y={},d={},g={},q={supportedProperties:u,defaultValues:p,defaultOptions:c,prepareProperty:h,prepareStart:f,crossCheck:y,onStart:l,onComplete:d,linkProperty:g},X={};function Z(){var a=this;Object.keys(g).forEach(function(t){var r=g[t],e=u[t];Object.keys(r).forEach(function(n){"function"==typeof r[n]&&Object.keys(a.valuesEnd).some(function(t){return e&&e.includes(t)||"attr"===t&&Object.keys(a.valuesEnd[t]).some(function(t){return e&&e.includes(t)})})?s[n]||(s[n]=r[n]):Object.keys(a.valuesEnd).forEach(function(t){t=a.valuesEnd[t];t instanceof Object&&Object.keys(t).forEach(function(e){"function"==typeof r[e]?s[e]||(s[e]=r[e]):Object.keys(r[n]).forEach(function(t){!r[e]||"function"!=typeof r[e][t]||s[t]||(s[t]=r[e][t])})})})})})}L={add:Q,remove:U,getAll:function(){return r},removeAll:function(){r.length=0},stop:F,linkInterpolation:Z};function B(t){var n,r;return!!t.style&&(t=t.style.cssText.replace(/\s/g,"").split(";"),n={},r=["translate3d","translate","scale3d","skew"],t.forEach(function(t){/transform/i.test(t)&&t.split(":")[1].split(")").forEach(function(t){var t=t.split("("),e=t[0],t=t[1];/matrix/.test(e)||(n[e]=r.includes(e)?t.split(","):t)})}),n)}function i(t,e){var n=p[e],r=t.style,t=getComputedStyle(t)||t.currentStyle,a=(r[e]&&!/auto|initial|none|unset/.test(r[e])?r:t)[e];return n="transform"!==e&&(e in t||e in r)?a:n}function m(i,t){var o=this,s="start"===t?this.valuesStart:this.valuesEnd;Object.keys(h).forEach(function(t){var r=h[t],a=u[t];Object.keys(r).forEach(function(e){var n={};Object.keys(i).forEach(function(t){p[t]&&r[t]?s[t]=r[t].call(o,t,i[t]):!p[e]&&"transform"===e&&a.includes(t)?n[t]=i[t]:p[t]||"transform"!==t?!p[e]&&a&&a.includes(t)&&(s[t]=r[e].call(o,t,i[t])):s[t]=i[t]}),Object.keys(n).length&&(s[e]=r[e].call(o,e,n))})})}function R(){var a=this,i={},e=B(this.element);Object.keys(this.valuesStart).forEach(function(r){Object.keys(f).forEach(function(e){var n=f[e];Object.keys(n).forEach(function(t){(t===r&&n[r]||u[e]&&u[e].includes(r))&&(i[r]=n[t].call(a,r,a.valuesStart[r]))})})}),Object.keys(e).forEach(function(t){t in a.valuesStart||(i[t]=e[t]||p[t])}),this.valuesStart={},m.call(this,i,"start")}var Y={getInlineStyle:B,getStyleForProperty:i,getStartValues:R,prepareObject:m},v={tween:null,processEasing:null},x={linear:new e(0,0,1,1,"linear"),easingSinusoidalIn:new e(.47,0,.745,.715,"easingSinusoidalIn"),easingSinusoidalOut:new e(.39,.575,.565,1,"easingSinusoidalOut"),easingSinusoidalInOut:new e(.445,.05,.55,.95,"easingSinusoidalInOut"),easingQuadraticIn:new e(.55,.085,.68,.53,"easingQuadraticIn"),easingQuadraticOut:new e(.25,.46,.45,.94,"easingQuadraticOut"),easingQuadraticInOut:new e(.455,.03,.515,.955,"easingQuadraticInOut"),easingCubicIn:new e(.55,.055,.675,.19,"easingCubicIn"),easingCubicOut:new e(.215,.61,.355,1,"easingCubicOut"),easingCubicInOut:new e(.645,.045,.355,1,"easingCubicInOut"),easingQuarticIn:new e(.895,.03,.685,.22,"easingQuarticIn"),easingQuarticOut:new e(.165,.84,.44,1,"easingQuarticOut"),easingQuarticInOut:new e(.77,0,.175,1,"easingQuarticInOut"),easingQuinticIn:new e(.755,.05,.855,.06,"easingQuinticIn"),easingQuinticOut:new e(.23,1,.32,1,"easingQuinticOut"),easingQuinticInOut:new e(.86,0,.07,1,"easingQuinticInOut"),easingExponentialIn:new e(.95,.05,.795,.035,"easingExponentialIn"),easingExponentialOut:new e(.19,1,.22,1,"easingExponentialOut"),easingExponentialInOut:new e(1,0,0,1,"easingExponentialInOut"),easingCircularIn:new e(.6,.04,.98,.335,"easingCircularIn"),easingCircularOut:new e(.075,.82,.165,1,"easingCircularOut"),easingCircularInOut:new e(.785,.135,.15,.86,"easingCircularInOut"),easingBackIn:new e(.6,-.28,.735,.045,"easingBackIn"),easingBackOut:new e(.175,.885,.32,1.275,"easingBackOut"),easingBackInOut:new e(.68,-.55,.265,1.55,"easingBackInOut")};function b(e,t){try{var n,r=t?(n=e instanceof Array&&e.every(function(t){return t instanceof Element}),e instanceof HTMLCollection||e instanceof NodeList||n?e:document.querySelectorAll(e)):e instanceof Element||e===window?e:document.querySelector(e);return r}catch(t){throw TypeError("KUTE.js - Element(s) not found: "+e+".")}}function z(){var n=this;Object.keys(l).forEach(function(e){"function"==typeof l[e]?l[e].call(n,e):Object.keys(l[e]).forEach(function(t){l[e][t].call(n,t)})}),Z.call(this)}v.processEasing=function(t){return"function"==typeof t?t:"function"==typeof x[t]?x[t]:/bezier/.test(t)?(t=t.replace(/bezier|\s|\(|\)/g,"").split(","),new e(+t[0],+t[1],+t[2],+t[3])):x.linear};function t(t,e,n,r){var a=this,i=(this.element=t,this.playing=!1,this._startTime=null,this._startFired=!1,this.valuesEnd=n,this.valuesStart=e,r||{}),t=(this._resetStart=i.resetStart||0,this._easing="function"==typeof i.easing?i.easing:v.processEasing(i.easing),this._duration=i.duration||c.duration,this._delay=i.delay||c.delay,Object.keys(i).forEach(function(t){var e="_"+t;e in a||(a[e]=i[t])}),this._easing.name);return l[t]||(l[t]=function(t){s[t]||t!==this._easing.name||(s[t]=this._easing)}),this}function M(t,n,r,e){var a=this,i=v.tween,o=(this.tweens=[],e||{}),s=(o.delay=o.delay||c.delay,[]);return Array.from(t).forEach(function(t,e){if(s[e]=o||{},s[e].delay=0<e?o.delay+(o.offset||c.offset):o.delay,!(t instanceof Element))throw Error("KUTE - "+t+" is not instanceof Element");a.tweens.push(new i(t,n,r,s[e]))}),this.length=this.tweens.length,this}t.prototype.start=function(t){return Q(this),this.playing=!0,this._startTime=void 0!==t?t:s.Time(),this._startTime+=this._delay,this._startFired||(this._onStart&&this._onStart.call(this),z.call(this),this._startFired=!0),a||n(),this},t.prototype.stop=function(){return this.playing&&(U(this),this.playing=!1,this._onStop&&this._onStop.call(this),this.close()),this},t.prototype.close=function(){var n=this;Object.keys(d).forEach(function(e){Object.keys(d[e]).forEach(function(t){d[e][t].call(n,t)})}),this._startFired=!1,F.call(this)},t.prototype.chain=function(t){return this._chain=[],this._chain=t.length?t:this._chain.concat(t),this},t.prototype.stopChainedTweens=function(){this._chain&&this._chain.length&&this._chain.forEach(function(t){return t.stop()})},t.prototype.update=function(t){var e=this,t=void 0!==t?t:s.Time();if(t<this._startTime&&this.playing)return!0;t=(t-this._startTime)/this._duration;var t=0===this._duration||1<t?1:t,n=this._easing(t);return Object.keys(this.valuesEnd).forEach(function(t){s[t](e.element,e.valuesStart[t],e.valuesEnd[t],n)}),this._onUpdate&&this._onUpdate.call(this),1!=t||(this._onComplete&&this._onComplete.call(this),this.playing=!1,this.close(),void 0!==this._chain&&this._chain.length&&this._chain.map(function(t){return t.start()}),!1)};var K=(o=>{function t(){for(var n=this,t=[],e=arguments.length;e--;)t[e]=arguments[e];o.apply(this,t),this.valuesStart={},this.valuesEnd={};var r=t.slice(1),a=r[0],i=r[2];return m.call(this,r[1],"end"),this._resetStart?this.valuesStart=a:m.call(this,a,"start"),this._resetStart||Object.keys(y).forEach(function(e){Object.keys(y[e]).forEach(function(t){y[e][t].call(n,t)})}),this.paused=!1,this._pauseTime=null,this._repeat=i.repeat||c.repeat,this._repeatDelay=i.repeatDelay||c.repeatDelay,this._repeatOption=this._repeat,this.valuesRepeat={},this._yoyo=i.yoyo||c.yoyo,this._reversed=!1,this}return o&&(t.__proto__=o),((t.prototype=Object.create(o&&o.prototype)).constructor=t).prototype.start=function(t){var n=this;return this._resetStart&&(this.valuesStart=this._resetStart,R.call(this),Object.keys(y).forEach(function(e){Object.keys(y[e]).forEach(function(t){y[e][t].call(n,t)})})),this.paused=!1,this._yoyo&&Object.keys(this.valuesEnd).forEach(function(t){n.valuesRepeat[t]=n.valuesStart[t]}),o.prototype.start.call(this,t),this},t.prototype.stop=function(){return o.prototype.stop.call(this),!this.paused&&this.playing&&(this.paused=!1,this.stopChainedTweens()),this},t.prototype.close=function(){return o.prototype.close.call(this),0<this._repeatOption&&(this._repeat=this._repeatOption),this._yoyo&&!0===this._reversed&&(this.reverse(),this._reversed=!1),this},t.prototype.resume=function(){return this.paused&&this.playing&&(this.paused=!1,void 0!==this._onResume&&this._onResume.call(this),z.call(this),this._startTime+=s.Time()-this._pauseTime,Q(this),a||n()),this},t.prototype.pause=function(){return!this.paused&&this.playing&&(U(this),this.paused=!0,this._pauseTime=s.Time(),void 0!==this._onPause)&&this._onPause.call(this),this},t.prototype.reverse=function(){var n=this;Object.keys(this.valuesEnd).forEach(function(t){var e=n.valuesRepeat[t];n.valuesRepeat[t]=n.valuesEnd[t],n.valuesEnd[t]=e,n.valuesStart[t]=n.valuesRepeat[t]})},t.prototype.update=function(t){var e=this,t=void 0!==t?t:s.Time();if(t<this._startTime&&this.playing)return!0;n=(t-this._startTime)/this._duration;var n=0===this._duration||1<n?1:n,r=this._easing(n);return Object.keys(this.valuesEnd).forEach(function(t){s[t](e.element,e.valuesStart[t],e.valuesEnd[t],r)}),this._onUpdate&&this._onUpdate.call(this),1!=n||(0<this._repeat?(Number.isFinite(this._repeat)&&--this._repeat,this._startTime=t,Number.isFinite(this._repeat)&&this._yoyo&&!this._reversed&&(this._startTime+=this._repeatDelay),this._yoyo&&(this._reversed=!this._reversed,this.reverse()),!0):(this._onComplete&&this._onComplete.call(this),this.playing=!1,this.close(),void 0!==this._chain&&this._chain.length&&this._chain.forEach(function(t){return t.start()}),!1))},t})(v.tween=t),$=(v.tween=K,M.prototype.start=function(t){var e=void 0===t?s.Time():t;return this.tweens.map(function(t){return t.start(e)}),this},M.prototype.stop=function(){return this.tweens.map(function(t){return t.stop()}),this},M.prototype.pause=function(){return this.tweens.map(function(t){return t.pause()}),this},M.prototype.resume=function(){return this.tweens.map(function(t){return t.resume()}),this},M.prototype.chain=function(t){var e=this.tweens[this.length-1];if(t instanceof M)e.chain(t.tweens);else{if(!(t instanceof v.tween))throw new TypeError("KUTE.js - invalid chain value");e.chain(t)}return this},M.prototype.playing=function(){return this.tweens.some(function(t){return t.playing})},M.prototype.removeTweens=function(){this.tweens=[]},M.prototype.getMaxDuration=function(){var e=[];return this.tweens.forEach(function(t){e.push(t._duration+t._delay+t._repeat*t._repeatDelay)}),Math.max(e)},v.tween);var G=v.tween;function W(r){try{if(r.component in u)throw Error("KUTE - "+r.component+" already registered");if(r.property in p)throw Error("KUTE - "+r.property+" already registered")}catch(t){throw Error(t)}var n=r.component,a={prepareProperty:h,prepareStart:f,onStart:l,onComplete:d,crossCheck:y},t=r.category,i=r.property,e=r.properties&&r.properties.length||r.subProperties&&r.subProperties.length;return u[n]=r.properties||r.subProperties||r.property,"defaultValue"in r?(p[i]=r.defaultValue,this.supports=i+" property"):r.defaultValues&&(Object.keys(r.defaultValues).forEach(function(t){p[t]=r.defaultValues[t]}),this.supports=(e||i)+" "+(i||t)+" properties"),r.defaultOptions&&Object.assign(c,r.defaultOptions),r.functions&&Object.keys(a).forEach(function(e){e in r.functions&&("function"==typeof r.functions[e]?(a[e][n]||(a[e][n]={}),a[e][n][t||i]||(a[e][n][t||i]=r.functions[e])):Object.keys(r.functions[e]).forEach(function(t){a[e][n]||(a[e][n]={}),a[e][n][t]||(a[e][n][t]=r.functions[e][t])}))}),r.Interpolate&&(Object.keys(r.Interpolate).forEach(function(e){var n=r.Interpolate[e];"function"!=typeof n||o[e]?Object.keys(n).forEach(function(t){"function"!=typeof n[t]||o[e]||(o[e]=n[t])}):o[e]=n}),g[n]=r.Interpolate),r.Util&&Object.keys(r.Util).forEach(function(t){X[t]||(X[t]=r.Util[t])}),this}function w(t,e){for(var n,r=parseInt(t,10)||0,a=["px","%","deg","rad","em","rem","vh","vw"],i=0;i<a.length;i+=1)if("string"==typeof t&&t.includes(a[i])){n=a[i];break}return{v:r,u:n=void 0===n?e?"deg":"px":n}}function E(t,e,n){return+t+(e-t)*n}function J(a){a in this.valuesEnd&&!s[a]&&(s[a]=function(t,e,n,r){t.style[a]=(.99<r||r<.01?(10*E(e,n,r)>>0)/10:E(e,n,r)>>0)+"px"})}function _(t){var e,n;return/rgb|rgba/.test(t)&&(e=(e=(n=t.replace(/\s|\)/,"").split("(")[1].split(","))[3]||null)?{r:parseInt(n[0],10),g:parseInt(n[1],10),b:parseInt(n[2],10),a:parseFloat(e)}:{r:parseInt(n[0],10),g:parseInt(n[1],10),b:parseInt(n[2],10)}),/^#/.test(t)&&(e={r:(n=nt(t)).r,g:n.g,b:n.b}),/transparent|none|initial|inherit/.test(t)&&(e={r:0,g:0,b:0,a:0}),/^#|^rgb/.test(t)||((n=document.getElementsByTagName("head")[0]).style.color=t,t=getComputedStyle(n,null).color,t=/rgb/.test(t)?t.replace(/[^\d,]/g,"").split(","):[0,0,0],n.style.color="",e={r:parseInt(t[0],10),g:parseInt(t[1],10),b:parseInt(t[2],10)}),e}var tt=["top","left","width","height"],et={},k=(tt.forEach(function(t){et[t]=J}),{prepareStart:function(t){return i(this.element,t)||p[t]},prepareProperty:function(t,e){return"%"===(e=w(e)).u?e.v*this.element["height"===t?"offsetHeight":"offsetWidth"]/100:e.v},onStart:et}),tt={component:"essentialBoxModel",category:"boxModel",properties:tt,defaultValues:{top:0,left:0,width:0,height:0},Interpolate:{numbers:E},functions:k,Util:{trueDimension:w}},nt=function(t){t=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(t,e,n,r){return e+e+n+n+r+r}),t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null};function rt(e,n,r){var a={};return Object.keys(n).forEach(function(t){"a"!==t?a[t]=E(e[t],n[t],r)>>0||0:e[t]&&n[t]&&(a[t]=(100*E(e[t],n[t],r)>>0)/100)}),a.a?"rgba("+a.r+","+a.g+","+a.b+","+a.a+")":"rgb("+a.r+","+a.g+","+a.b+")"}function at(a){this.valuesEnd[a]&&!s[a]&&(s[a]=function(t,e,n,r){t.style[a]=rt(e,n,r)})}var k=["color","backgroundColor","outlineColor","borderColor","borderTopColor","borderRightColor","borderBottomColor","borderLeftColor"],it={},ot=(k.forEach(function(t){it[t]="#000"}),{});k.forEach(function(t){ot[t]=at});var k={component:"colorProperties",category:"colors",properties:k,defaultValues:it,Interpolate:{numbers:E,colors:rt},functions:{prepareStart:function(t){return i(this.element,t)||p[t]},prepareProperty:function(t,e){return _(e)},onStart:ot},Util:{trueColor:_}},O={},st="htmlAttributes",ut=["fill","stroke","stop-color"];function ct(t){return t.replace(/[A-Z]/g,"-$&").toLowerCase()}var lt={component:st,property:"attr",subProperties:["fill","stroke","stop-color","fill-opacity","stroke-opacity"],defaultValue:{fill:"rgb(0,0,0)",stroke:"rgb(0,0,0)","stop-color":"rgb(0,0,0)",opacity:1,"stroke-opacity":1,"fill-opacity":1},Interpolate:{numbers:E,colors:rt},functions:{prepareStart:function(t,e){var r=this,a={};return Object.keys(e).forEach(function(t){var e=ct(t).replace(/_+[a-z]+/,""),n=r.element.getAttribute(e);a[e]=ut.includes(e)?n||"rgba(0,0,0,0)":n||(/opacity/i.test(t)?1:0)}),a},prepareProperty:function(o,s){var u=this,c={};return Object.keys(s).forEach(function(t){var e,i,n=ct(t),r=/(%|[a-z]+)$/,a=u.element.getAttribute(n.replace(/_+[a-z]+/,""));ut.includes(n)?(l[st][n]=function(t){!u.valuesEnd[o]||!u.valuesEnd[o][t]||t in O||(O[t]=function(t,e,n,r,a){t.setAttribute(e,rt(n,r,a))})},c[n]=_(s[t])||p.htmlAttributes[t]):null!==a&&r.test(a)?(e=w(a).u||w(s[t]).u,i=/%/.test(e)?"_percent":"_"+e,l[st][n+i]=function(t){!u.valuesEnd[o]||!u.valuesEnd[o][t]||t in O||(O[t]=function(t,e,n,r,a){e=e.replace(i,"");t.setAttribute(e,(1e3*E(n.v,r.v,a)>>0)/1e3+r.u)})},c[n+i]=w(s[t])):r.test(s[t])&&null!==a&&(!a||r.test(a))||(l[st][n]=function(t){!u.valuesEnd[o]||!u.valuesEnd[o][t]||t in O||(O[t]=function(t,e,n,r,a){t.setAttribute(e,(1e3*E(n,r,a)>>0)/1e3)})},c[n]=parseFloat(s[t]))}),c},onStart:{attr:function(t){!s[t]&&this.valuesEnd[t]&&(s[t]=function(e,n,r,a){Object.keys(r).forEach(function(t){s.attributes[t](e,t,n[t],r[t],a)})})},attributes:function(t){!s[t]&&this.valuesEnd.attr&&(s[t]=O)}}},Util:{replaceUppercase:ct,trueColor:_,trueDimension:w}};var pt={component:"opacityProperty",property:"opacity",defaultValue:1,Interpolate:{numbers:E},functions:{prepareStart:function(t){return i(this.element,t)},prepareProperty:function(t,e){return parseFloat(e)},onStart:function(a){a in this.valuesEnd&&!s[a]&&(s[a]=function(t,e,n,r){t.style[a]=(1e3*E(e,n,r)>>0)/1e3})}}},ht=String("abcdefghijklmnopqrstuvwxyz").split(""),ft=String("abcdefghijklmnopqrstuvwxyz").toUpperCase().split(""),yt=String("~!@#$%^&*()_+{}[];'<>,./?=-").split(""),dt=String("0123456789").split(""),gt=ht.concat(ft,dt),mt=gt.concat(yt),vt={alpha:ht,upper:ft,symbols:yt,numeric:dt,alphanumeric:gt,all:mt};function C(t,e){var n,r;return"string"==typeof t?((n=document.createElement("SPAN")).innerHTML=t,n.className=e,n):(t.children.length&&(t.children.length,t.children[0].className===e)?t.children.length&&t.children[0].className===e&&(r=t.children[0]):(n=t.innerHTML,(r=document.createElement("SPAN")).className=e,r.innerHTML=n,t.appendChild(r),t.innerHTML=r.outerHTML),r)}function xt(t,e){var n=[],r=t.children.length;if(r){for(var a,i,o,s=[],u=t.innerHTML,c=0,l=void 0,p=void 0;c<r;c+=1)i=(l=t.children[c]).outerHTML,""!==(a=u.split(i))[0]?(p=C(a[0],e),s.push(p),u=u.replace(a[0],"")):""!==a[1]&&(p=C(a[1].split("<")[0],e),s.push(p),u=u.replace(a[0].split("<")[0],"")),l.classList.contains(e)||l.classList.add(e),s.push(l),u=u.replace(i,"");""!==u&&(o=C(u,e),s.push(o)),n=n.concat(s)}else n=n.concat([C(t,e)]);return n}function bt(t,e,n,r){return"perspective("+(1e3*(t+(e-t)*r)>>0)/1e3+n+")"}function Mt(t,e,n,r){for(var a=[],i=0;i<3;i+=1)a[i]=(t[i]||e[i]?(1e3*(t[i]+(e[i]-t[i])*r)>>0)/1e3:0)+n;return"translate3d("+a.join(",")+")"}function wt(t,e,n,r){var a="";return(a+=t[0]||e[0]?"rotateX("+(1e3*(t[0]+(e[0]-t[0])*r)>>0)/1e3+n+")":"")+(t[1]||e[1]?"rotateY("+(1e3*(t[1]+(e[1]-t[1])*r)>>0)/1e3+n+")":"")+(t[2]||e[2]?"rotateZ("+(1e3*(t[2]+(e[2]-t[2])*r)>>0)/1e3+n+")":"")}function Et(t,e,n){return"scale("+(1e3*(t+(e-t)*n)>>0)/1e3+")"}function _t(t,e,n,r){var a=[];return a[0]=(t[0]===e[0]?e[0]:(1e3*(t[0]+(e[0]-t[0])*r)>>0)/1e3)+n,a[1]=t[1]||e[1]?(t[1]===e[1]?e[1]:(1e3*(t[1]+(e[1]-t[1])*r)>>0)/1e3)+n:"0","skew("+a.join(",")+")"}function kt(t,e){return parseFloat(t)/100*e}function Ot(t){return 2*t.getAttribute("width")+2*t.getAttribute("height")}function Ct(t){var e=t.getAttribute("points").split(" "),n=0;if(1<e.length){var r=function(t){t=t.split(",");return 2!==t.length||Number.isNaN(+t[0])||Number.isNaN(+t[1])?0:[parseFloat(t[0]),parseFloat(t[1])]},a=function(t,e){return void 0!==t&&void 0!==e?Math.sqrt(Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)):0};if(2<e.length)for(var i=0;i<e.length-1;i+=1)n+=a(r(e[i]),r(e[i+1]));n+="polygon"===t.tagName?a(r(e[0]),r(e[e.length-1])):0}return n}function Tt(t){var e=t.getAttribute("x1"),n=t.getAttribute("x2"),r=t.getAttribute("y1"),t=t.getAttribute("y2");return Math.sqrt(Math.pow(n-e,2)+Math.pow(t-r,2))}function St(t){t=t.getAttribute("r");return 2*Math.PI*t}function It(t){var e=2*t.getAttribute("rx"),t=2*t.getAttribute("ry");return Math.sqrt(.5*(e*e+t*t))*(2*Math.PI)/2}function jt(t){return"rect"===t.tagName?Ot(t):"circle"===t.tagName?St(t):"ellipse"===t.tagName?It(t):["polygon","polyline"].includes(t.tagName)?Ct(t):"line"===t.tagName?Tt(t):0}function At(t,e){var n,r,a=/path|glyph/.test(t.tagName)?t.getTotalLength():jt(t);return e instanceof Object&&Object.keys(e).every(function(t){return["s","e","l"].includes(t)})?e:("string"==typeof e?(r=e.split(/,|\s/),n=/%/.test(r[0])?kt(r[0].trim(),a):parseFloat(r[0]),r=/%/.test(r[1])?kt(r[1].trim(),a):parseFloat(r[1])):void 0===e&&(e=parseFloat(i(t,"stroke-dashoffset")),t=i(t,"stroke-dasharray").split(","),n=0-e,r=parseFloat(t[0])+n||a),{s:n,e:r,l:a})}var T={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0};function Pt(t){return Array.isArray(t)&&t.every(function(t){var e=t[0].toLowerCase();return T[e]===t.length-1&&"achlmqstvz".includes(e)})}function Lt(t){return Pt(t)&&t.every(function(t){t=t[0];return t===t.toUpperCase()})}function Nt(t){return Lt(t)&&t.every(function(t){t=t[0];return"ACLMQZ".includes(t)})}function S(t){return t.map(function(t){return Array.isArray(t)?[].concat(t):t})}function Vt(t){for(var e=t.pathValue[t.segmentStart],n=e.toLowerCase(),r=t.data;r.length>=T[n]&&("m"===n&&2<r.length?(t.segments.push([e].concat(r.splice(0,2))),n="l",e="m"===e?"l":"L"):t.segments.push([e].concat(r.splice(0,T[n]))),T[n]););}var I="SVGPathCommander error";function j(t){return 48<=t&&t<=57}var A="Invalid path value";function qt(t){for(var e,n=t.pathValue,r=t.max;t.index<r&&(10===(e=n.charCodeAt(t.index))||13===e||8232===e||8233===e||32===e||9===e||11===e||12===e||160===e||5760<=e&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(e));)t.index+=1}function Ht(t){var e,n,r,a,i,o=t.max,s=t.pathValue,u=t.index,c=s.charCodeAt(u),l=T[s[u].toLowerCase()];if(t.segmentStart=u,(t=>{switch(32|t){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:return 1;default:return}})(c)){if(t.index+=1,qt(t),t.data=[],l)for(;;){for(var p=l;0<p;--p){if(97!=(32|c)||3!==p&&4!==p?(t=>{var e,n,r=t.max,a=t.pathValue,i=t.index,o=i,s=!1,u=!1,c=!1;if(r<=o)t.err=I+": "+A+" at index "+o+', "pathValue" is missing param';else if(j(n=43!==(n=a.charCodeAt(o))&&45!==n?n:a.charCodeAt(o+=1))||46===n){if(46!==n){if(e=48===n,n=a.charCodeAt(o+=1),e&&o<r&&n&&j(n))return t.err=I+": "+A+" at index "+i+', "'+a[i]+'" illegal number';for(;o<r&&j(a.charCodeAt(o));)o+=1,s=!0;n=a.charCodeAt(o)}if(46===n){for(c=!0,o+=1;j(a.charCodeAt(o));)o+=1,u=!0;n=a.charCodeAt(o)}if(101===n||69===n){if(c&&!s&&!u)return t.err=I+": "+A+" at index "+o+', "'+a[o]+'" invalid float exponent';if(43!==(n=a.charCodeAt(o+=1))&&45!==n||(o+=1),!(o<r&&j(a.charCodeAt(o))))return t.err=I+": "+A+" at index "+o+', "'+a[o]+'" invalid integer exponent';for(;o<r&&j(a.charCodeAt(o));)o+=1}t.index=o,t.param=+t.pathValue.slice(i,o)}else t.err=I+": "+A+" at index "+o+', "'+a[o]+'" is not a number'})(t):(i=a=r=void 0,r=(n=t).index,48===(i=(a=n.pathValue).charCodeAt(r))?(n.param=0,n.index+=1):49===i?(n.param=1,n.index+=1):n.err=I+': invalid Arc flag "'+a[r]+'", expecting 0 or 1 at index '+r),t.err.length)return;t.data.push(t.param),qt(t),t.index<o&&44===s.charCodeAt(t.index)&&(t.index+=1,qt(t))}if(t.index>=t.max)break;if(!(48<=(e=s.charCodeAt(t.index))&&e<=57||43===e||45===e||46===e))break}Vt(t)}else t.err=I+": "+A+' "'+s[u]+'" is not a path command'}function Ft(t){this.segments=[],this.pathValue=t,this.max=t.length,this.index=0,this.param=0,this.segmentStart=0,this.data=[],this.err=""}function Qt(t){var o,s,u,c;return Lt(t)?S(t):(t=(t=>{if(Pt(t))return S(t);var e=new Ft(t);for(qt(e);e.index<e.max&&!e.err.length;)Ht(e);return e.err||e.segments})(t),c=u=s=o=0,t.map(function(t){var e=t.slice(1).map(Number),t=t[0],n=t.toUpperCase();if("M"===t)return o=e[0],["M",u=o,c=s=e[1]];var r=[];if(t!==n)switch(n){case"A":r=[n,e[0],e[1],e[2],e[3],e[4],e[5]+o,e[6]+s];break;case"V":r=[n,e[0]+s];break;case"H":r=[n,e[0]+o];break;default:var a=e.map(function(t,e){return t+(e%2?s:o)}),r=[n].concat(a)}else r=[n].concat(e);var i=r.length;switch(n){case"Z":o=u,s=c;break;case"H":o=r[1];break;case"V":s=r[1];break;default:o=r[i-2],s=r[i-1],"M"===n&&(u=o,c=s)}return r}))}var Ut={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null};function Dt(t){if(Nt(t))return S(t);for(var e,n,r,a,i,o,s,u=Qt(t),c=Object.assign({},Ut),l=u.length,p=0;p<l;p+=1){u[p][0],u[p]=(e=u[p],n=c,s=o=f=i=h=a=r=void 0,r=e[0],a=n.x1,h=n.y1,i=n.x2,f=n.y2,o=e.slice(1).map(Number),s=e,"TQ".includes(r)||(n.qx=null,n.qy=null),"H"===r?s=["L",e[1],h]:"V"===r?s=["L",a,e[1]]:"S"===r?(e=2*h-f,s=["C",n.x1=2*a-i,n.y1=e].concat(o)):"T"===r?(f=2*a-n.qx,i=2*h-n.qy,s=["Q",n.qx=f,n.qy=i].concat(o)):"Q"===r&&(e=o[0],a=o[1],n.qx=e,n.qy=a),s);var h=u[p],f=h.length;c.x1=+h[f-2],c.y1=+h[f-1],c.x2=+h[f-4]||c.x1,c.y2=+h[f-3]||c.y1}return u}function Xt(t,e,n){return{x:t*Math.cos(n)-e*Math.sin(n),y:t*Math.sin(n)+e*Math.cos(n)}}function P(t,e,n){var r=t[0],t=t[1];return[r+(e[0]-r)*n,t+(e[1]-t)*n]}function N(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function V(t,e,n,r,a){var i=N([t,e],[n,r]),o={x:0,y:0};return{length:i,point:o="number"==typeof a?a<=0?{x:t,y:e}:i<=a?{x:n,y:r}:{x:(a=P([t,e],[n,r],a/i))[0],y:a[1]}:o,min:{x:Math.min(t,n),y:Math.min(e,r)},max:{x:Math.max(t,n),y:Math.max(e,r)}}}function Zt(t,e,n,r){var t=[t,e],e=[n,r],a=P(t,e,.5),i=P(e,a,.5),o=P(a,i,.5),s=P(i,o,.5),u=P(o,s,.5),t=t.concat(a,o,u,[.5]),a=V.apply(void 0,t).point,o=u.concat(s,i,e,[0]),t=V.apply(void 0,o).point;return[a.x,a.y,t.x,t.y,n,r]}function Bt(t,e){var n,r=t[0],a=t.slice(1).map(Number),i=a[0],o=a[1],s=e.x1,u=e.y1,c=e.x,l=e.y;switch("TQ".includes(r)||(e.qx=null,e.qy=null),r){case"M":return e.x=i,e.y=o,t;case"A":return n=[s,u].concat(a),["C"].concat(function t(e,n,r,a,i,o,s,u,c,l){var p=120*Math.PI/180,h=Math.PI/180*(+i||0),f=[],y=(l?(b=l[0],M=l[1],v=l[2],x=l[3]):(e=(g=Xt(e,n,-h)).x,n=g.y,1<(y=(d=(e-(u=(g=Xt(u,c,-h)).x))/2)*d/(r*r)+(g=(n-(c=g.y))/2)*g/(a*a))&&(r*=y=Math.sqrt(y),a*=y),y=r*r,m=a*a,v=(o=(o===s?-1:1)*Math.sqrt(Math.abs((y*m-y*g*g-m*d*d)/(y*g*g+m*d*d))))*r*g/a+(e+u)/2,x=o*-a*d/r+(n+c)/2,b=Math.asin(((n-x)/a*Math.pow(10,9)>>0)/Math.pow(10,9)),M=Math.asin(((c-x)/a*Math.pow(10,9)>>0)/Math.pow(10,9)),b=e<v?Math.PI-b:b,M=u<v?Math.PI-M:M,b<0&&(b=2*Math.PI+b),M<0&&(M=2*Math.PI+M),s&&M<b&&(b-=2*Math.PI),!s&&b<M&&(M-=2*Math.PI)),M-b),d=(Math.abs(y)>p&&(m=M,g=u,o=c,M=b+p*(s&&b<M?1:-1),f=t(u=v+r*Math.cos(M),c=x+a*Math.sin(M),r,a,i,0,s,g,o,[M,m,v,x])),y=M-b,Math.cos(b)),p=Math.sin(b),i=Math.cos(M),s=Math.sin(M),g=Math.tan(y/4),o=4/3*r*g,m=4/3*a*g,v=[e,n],x=[e+o*p,n-m*d],b=[u+o*s,c-m*i],M=[u,c];if(x[0]=2*v[0]-x[0],x[1]=2*v[1]-x[1],l)return x.concat(b,M,f);for(var w=[],E=0,_=(f=x.concat(b,M,f)).length;E<_;E+=1)w[E]=E%2?Xt(f[E-1],f[E],h).y:Xt(f[E],f[E+1],h).x;return w}.apply(void 0,n));case"Q":return e.qx=i,e.qy=o,n=[s,u].concat(a),["C"].concat(function(t,e,n,r,a,i){return[1/3*t+2/3*n,1/3*e+2/3*r,1/3*a+2/3*n,1/3*i+2/3*r,a,i]}.apply(void 0,n));case"L":return["C"].concat(Zt(s,u,i,o));case"Z":return["C"].concat(Zt(s,u,c,l))}return t}var Rt={origin:[0,0,0],round:4};function Yt(t,e){var n,r=Rt.round;return"off"===e||"off"===r?S(t):(n="number"==typeof(r=0<=e?e:r)&&1<=r?Math.pow(10,r):1,t.map(function(t){var e=t.slice(1).map(Number).map(function(t){return r?Math.round(t*n)/n:Math.round(t)});return[t[0]].concat(e)}))}function zt(t,e){var n=t.x,t=t.y,r=e.x,e=e.y,a=n*r+t*e,i=Math.sqrt((Math.pow(n,2)+Math.pow(t,2))*(Math.pow(r,2)+Math.pow(e,2)));return(n*e-t*r<0?-1:1)*Math.acos(a/i)}function Kt(t,e,n,r,a,i,o,s,u,c){for(var l,p,h,f,y,d,g,m,v,x,b,M,w,E,_,k,O="number"==typeof c,C=t,T=e,S=0,I=[C,T,S],j=[C,T],A={x:0,y:0},P=[{x:C,y:T}],L=(O&&c<=0&&(A={x:C,y:T}),0);L<=300;L+=1)l=t,p=e,h=n,f=r,y=a,d=i,g=o,m=s,v=u,x=L/300,M=k=_=E=w=M=b=void 0,M=Math.abs,w=Math.sin,E=Math.cos,_=Math.sqrt,k=Math.PI,h=M(h),M=M(f),f=k/180*((y%360+360)%360),C=(b=l===m&&p===v?{x:l,y:p}:0===h||0===M?V(l,p,m,v,x).point:(y=(l-m)/2,b=(p-v)/2,y={x:E(f)*y+w(f)*b,y:-w(f)*y+E(f)*b},1<(b=Math.pow(y.x,2)/Math.pow(h,2)+Math.pow(y.y,2)/Math.pow(M,2))&&(h*=_(b),M*=_(b)),b=(Math.pow(h,2)*Math.pow(M,2)-Math.pow(h,2)*Math.pow(y.y,2)-Math.pow(M,2)*Math.pow(y.x,2))/(Math.pow(h,2)*Math.pow(y.y,2)+Math.pow(M,2)*Math.pow(y.x,2)),d=(d!==g?1:-1)*_(b<0?0:b),_=h*y.y/M*d,b=-M*y.x/h*d,d=E(f)*_-w(f)*b+(l+m)/2,l=w(f)*_+E(f)*b+(p+v)/2,p=zt({x:1,y:0},m={x:(y.x-_)/h,y:(y.y-b)/M}),v=zt(m,{x:(-y.x-_)/h,y:(-y.y-b)/M}),!g&&0<v?v-=2*k:g&&v<0&&(v+=2*k),m=p+(v%=2*k)*x,_=h*E(m),y=M*w(m),{x:E(f)*_-w(f)*y+d,y:w(f)*_+E(f)*y+l})).x,P=P.concat([{x:C,y:T=b.y}]),S+=N(j,[C,T]),j=[C,T],O&&c<S&&c>I[2]&&(g=(S-c)/(S-I[2]),A={x:j[0]*(1-g)+I[0]*g,y:j[1]*(1-g)+I[1]*g}),I=[C,T,S];return{length:S,point:A=O&&S<=c?{x:s,y:u}:A,min:{x:Math.min.apply(Math,P.map(function(t){return t.x})),y:Math.min.apply(Math,P.map(function(t){return t.y}))},max:{x:Math.max.apply(Math,P.map(function(t){return t.x})),y:Math.max.apply(Math,P.map(function(t){return t.y}))}}}function $t(t,e,n,r,a,i,o,s,u){for(var c,l,p,h,f,y,d,g,m,v,x="number"==typeof u,b=t,M=e,w=0,E=[b,M,w],_=[b,M],k={x:0,y:0},O=[{x:b,y:M}],C=(x&&u<=0&&(k={x:b,y:M}),0);C<=300;C+=1)c=t,l=e,p=n,h=r,f=a,y=i,d=o,g=s,v=void 0,v=1-(m=C/300),c={x:Math.pow(v,3)*c+3*Math.pow(v,2)*m*p+3*v*Math.pow(m,2)*f+Math.pow(m,3)*d,y:Math.pow(v,3)*l+3*Math.pow(v,2)*m*h+3*v*Math.pow(m,2)*y+Math.pow(m,3)*g},O=O.concat([{x:b=c.x,y:M=c.y}]),w+=N(_,[b,M]),_=[b,M],x&&u<w&&u>E[2]&&(p=(w-u)/(w-E[2]),k={x:_[0]*(1-p)+E[0]*p,y:_[1]*(1-p)+E[1]*p}),E=[b,M,w];return{length:w,point:k=x&&w<=u?{x:o,y:s}:k,min:{x:Math.min.apply(Math,O.map(function(t){return t.x})),y:Math.min.apply(Math,O.map(function(t){return t.y}))},max:{x:Math.max.apply(Math,O.map(function(t){return t.x})),y:Math.max.apply(Math,O.map(function(t){return t.y}))}}}function Gt(t,e,n,r,a,i,o){for(var s,u,c,l,p,h,f,y,d="number"==typeof o,g=t,m=e,v=0,x=[g,m,v],b=[g,m],M={x:0,y:0},w=[{x:g,y:m}],E=(d&&o<=0&&(M={x:g,y:m}),0);E<=300;E+=1)s=t,u=e,c=n,l=r,p=a,h=i,y=void 0,y=1-(f=E/300),s={x:Math.pow(y,2)*s+2*y*f*c+Math.pow(f,2)*p,y:Math.pow(y,2)*u+2*y*f*l+Math.pow(f,2)*h},w=w.concat([{x:g=s.x,y:m=s.y}]),v+=N(b,[g,m]),b=[g,m],d&&o<v&&o>x[2]&&(c=(v-o)/(v-x[2]),M={x:b[0]*(1-c)+x[0]*c,y:b[1]*(1-c)+x[1]*c}),x=[g,m,v];return{length:v,point:M=d&&v<=o?{x:a,y:i}:M,min:{x:Math.min.apply(Math,w.map(function(t){return t.x})),y:Math.min.apply(Math,w.map(function(t){return t.y}))},max:{x:Math.max.apply(Math,w.map(function(t){return t.x})),y:Math.max.apply(Math,w.map(function(t){return t.y}))}}}function Wt(t,e){for(var n,r,a,i=Dt(t),o="number"==typeof e,s=[],u=0,c=0,l=0,p=0,h=[],f=[],y=0,d={x:0,y:0},g=d,m=d,v=d,x=0,b=0,M=i.length;b<M;b+=1)s=(n="M"===(r=(a=i[b])[0]))?s:[u,c].concat(a.slice(1)),n?(g=d={x:l=(n=a)[1],y:p=n[2]},y=0,o&&e<.001&&(v=d)):"L"===r?(y=(n=V.apply(void 0,s.concat([(e||0)-x]))).length,d=n.min,g=n.max,m=n.point):"A"===r?(y=(n=Kt.apply(void 0,s.concat([(e||0)-x]))).length,d=n.min,g=n.max,m=n.point):"C"===r?(y=(n=$t.apply(void 0,s.concat([(e||0)-x]))).length,d=n.min,g=n.max,m=n.point):"Q"===r?(y=(n=Gt.apply(void 0,s.concat([(e||0)-x]))).length,d=n.min,g=n.max,m=n.point):"Z"===r&&(y=(n=V.apply(void 0,(s=[u,c,l,p]).concat([(e||0)-x]))).length,d=n.min,g=n.max,m=n.point),o&&x<e&&e<=x+y&&(v=m),f=f.concat([g]),h=h.concat([d]),x+=y,u=(n="Z"!==r?a.slice(-2):[l,p])[0],c=n[1];return{length:x,point:v=o&&x<=e?{x:u,y:c}:v,min:{x:Math.min.apply(Math,h.map(function(t){return t.x})),y:Math.min.apply(Math,h.map(function(t){return t.y}))},max:{x:Math.max.apply(Math,f.map(function(t){return t.x})),y:Math.max.apply(Math,f.map(function(t){return t.y}))}}}function Jt(t){return Wt(t).length}function te(t,e){return Wt(t,e).point}function ee(t){for(var e,n=t.length,r=-1,a=t[n-1],i=0;++r<n;)e=a,a=t[r],i+=e[1]*a[0]-e[0]*a[1];return i/2}var ne=1e-9;function re(t,e,n,r){for(var a=[],i=0;i<n;i+=1){a[i]=[];for(var o=0;o<2;o+=1)a[i].push((1e3*(t[i][o]+(e[i][o]-t[i][o])*r)>>0)/1e3)}return a}function ae(t,e){r=[],a=-1,t.forEach(function(t){"M"===t[0]?(n=[t],a+=1):n=n.concat([t]),r[a]=n});var n,r,a,i,o=Dt(r[0]),s=Jt(o),u=[],c=3;e&&!Number.isNaN(e)&&0<+e&&(c=Math.max(c,Math.ceil(s/e)));for(var l=0;l<c;l+=1)i=te(o,s*l/c),u.push([i.x,i.y]);return 0<ee(u)&&u.reverse(),{polygon:u,skipBisect:!0}}function ie(t,e){t=Dt(t);return(t=>{var e,n,r=[],a=t.length;if(!t.length||"M"!==t[0][0])return!1;for(var i=0;i<a&&!("M"===(n=(e=t[i])[0])&&i||"Z"===n);i+=1){if(!"ML".includes(n))return!1;r.push([e[1],e[2]])}return!!a&&{polygon:r}})(t)||ae(t,e)}function oe(t,e){for(var n,r,a,i=t.length,o=1/0,s=0,u=0;u<i;u+=1){for(var s=0,c=0;c<e.length;c+=1)s+=(a=N(t[(u+c)%i],e[c]))*a;s<o&&(o=s,n=u)}n&&(r=t.splice(0,n),t.splice.apply(t,[t.length,0].concat(r)))}function se(t,e){for(var r,n,a,i,o=t.length+e,s=(r=t).reduce(function(t,e,n){return n?t+N(r[n-1],e):0},0)/e,u=0,c=0,l=s/2;t.length<o;)l<=c+(i=N(n=t[u],a=t[(u+1)%t.length]))?(t.splice(u+1,0,i?P(n,a,(l-c)/i):n.slice(0)),l+=s):(c+=i,u+=1)}function ue(t,e){void 0===e&&(e=1/0);for(var n,r=[],a=0;a<t.length;a+=1)for(n=t[a],r=a===t.length-1?t[0]:t[a+1];N(n,r)>e;)r=P(n,r,.5),t.splice(a+1,0,r)}function ce(t){return Array.isArray(t)&&t.every(function(t){return Array.isArray(t)&&2===t.length&&!Number.isNaN(t[0])&&!Number.isNaN(t[1])})}function le(t,e){if("string"==typeof t)var n=ie(t,e),r=n.polygon,n=n.skipBisect;else if(!Array.isArray(t))throw Error(A+": "+t);t=[].concat(r);if(ce(t))return 1<t.length&&N(t[0],t[t.length-1])<ne&&t.pop(),!n&&e&&!Number.isNaN(e)&&0<+e&&ue(t,e),t;throw Error(A+": "+t)}function pe(t,e,n){n=n||c.morphPrecision,t=le(t,n),e=le(e,n),n=t.length-e.length;return se(t,n<0?-1*n:0),se(e,0<n?n:0),oe(t,e),[Yt(t),Yt(e)]}var he={EssentialBoxModel:tt,ColorsProperties:k,HTMLAttributes:lt,OpacityProperty:pt,TextWriteProp:{component:"textWriteProperties",category:"textWrite",properties:["text","number"],defaultValues:{text:" ",number:"0"},defaultOptions:{textChars:"alpha"},Interpolate:{numbers:E},functions:{prepareStart:function(){return this.element.innerHTML},prepareProperty:function(t,e){return"number"===t?parseFloat(e):""===e?" ":e},onStart:{text:function(t){var e,l;!s[t]&&this.valuesEnd[t]&&(e=this._textChars,l=vt[c.textChars],e in vt?l=vt[e]:e&&e.length&&(l=e),s[t]=function(t,e,n,r){var a="",i="",o=""===n?" ":n,s=e.substring(0),u=n.substring(0),c=l[Math.random()*l.length>>0];" "===e?(i=u.substring(Math.min(r*u.length,u.length)>>0,0),t.innerHTML=r<1?i+c:o):" "===n?(a=s.substring(0,Math.min((1-r)*s.length,s.length)>>0),t.innerHTML=r<1?a+c:o):(a=s.substring(s.length,Math.min(r*s.length,s.length)>>0),i=u.substring(0,Math.min(r*u.length,u.length)>>0),t.innerHTML=r<1?i+c+a:o)})},number:function(t){t in this.valuesEnd&&!s[t]&&(s[t]=function(t,e,n,r){t.innerHTML=E(e,n,r)>>0})}}},Util:{charSet:vt,createTextTweens:function(n,r,t){var a,i,e,o,s,u,c,l;return!n.playing&&((a=t||{}).duration=1e3,"auto"===t.duration?a.duration="auto":Number.isFinite(+t.duration)&&(a.duration=+t.duration),i=v.tween,t=r,u=xt(e=n,"text-part"),t=xt(C(t),"text-part"),e.innerHTML="",e.innerHTML+=u.map(function(t){return t.className+=" oldText",t.outerHTML}).join(""),e.innerHTML+=t.map(function(t){return t.className+=" newText",t.outerHTML.replace(t.innerHTML,"")}).join(""),o=(e=[u,t])[0],s=e[1],u=[].slice.call(n.getElementsByClassName("oldText")).reverse(),t=[].slice.call(n.getElementsByClassName("newText")),c=0,(l=(l=(l=[]).concat(u.map(function(t,e){return a.duration="auto"===a.duration?75*o[e].innerHTML.length:a.duration,a.delay=c,a.onComplete=null,c+=a.duration,new i(t,{text:t.innerHTML},{text:""},a)}))).concat(t.map(function(t,e){return a.duration="auto"===a.duration?75*s[e].innerHTML.length:a.duration,a.delay=c,a.onComplete=e===s.length-1?function(){n.innerHTML=r,n.playing=!1}:null,c+=a.duration,new i(t,{text:""},{text:s[e].innerHTML},a)}))).start=function(){n.playing||(l.forEach(function(t){return t.start()}),n.playing=!0)},l)}}},TransformFunctions:{component:"transformFunctions",property:"transform",subProperties:["perspective","translate3d","translateX","translateY","translateZ","translate","rotate3d","rotateX","rotateY","rotateZ","rotate","skewX","skewY","skew","scale"],defaultValues:{perspective:400,translate3d:[0,0,0],translateX:0,translateY:0,translateZ:0,translate:[0,0],rotate3d:[0,0,0],rotateX:0,rotateY:0,rotateZ:0,rotate:0,skewX:0,skewY:0,skew:[0,0],scale:1},functions:{prepareStart:function(t){var e=B(this.element);return e[t]||p[t]},prepareProperty:function(t,u){var c=["X","Y","Z"],l={},p=[],h=[],f=[],y=["translate3d","translate","rotate3d","skew"];return Object.keys(u).forEach(function(t){var e="object"==typeof u[t]&&u[t].length?u[t].map(function(t){return parseInt(t,10)}):parseInt(u[t],10);if(y.includes(t)){var n="translate"===t||"rotate"===t?t+"3d":t;l[n]="skew"===t?e.length?[e[0]||0,e[1]||0]:[e||0,0]:"translate"!==t||e.length?[e[0]||0,e[1]||0,e[2]||0]:[e||0,0,0]}else if(/[XYZ]/.test(t)){var r=t.replace(/[XYZ]/,""),n="skew"===r?r:r+"3d",a="skew"===r?2:3,i=[];"translate"===r?i=p:"rotate"===r?i=h:"skew"===r&&(i=f);for(var o=0;o<a;o+=1){var s=c[o];i[o]=""+r+s in u?parseInt(u[""+r+s],10):0}l[n]=i}else"rotate"===t?l.rotate3d=[0,0,e]:l[t]="scale"===t?parseFloat(u[t]):e}),l},onStart:function(a){!s[a]&&this.valuesEnd[a]&&(s[a]=function(t,e,n,r){t.style[a]=(e.perspective||n.perspective?bt(e.perspective,n.perspective,"px",r):"")+(e.translate3d?Mt(e.translate3d,n.translate3d,"px",r):"")+(e.rotate3d?wt(e.rotate3d,n.rotate3d,"deg",r):"")+(e.skew?_t(e.skew,n.skew,"deg",r):"")+(e.scale||n.scale?Et(e.scale,n.scale,r):"")})},crossCheck:function(t){this.valuesEnd[t]&&(this.valuesEnd[t],this.valuesEnd[t].perspective)&&!this.valuesStart[t].perspective&&(this.valuesStart[t].perspective=this.valuesEnd[t].perspective)}},Interpolate:{perspective:bt,translate3d:Mt,rotate3d:wt,translate:function(t,e,n,r){var a=[];return a[0]=(t[0]===e[0]?e[0]:(1e3*(t[0]+(e[0]-t[0])*r)>>0)/1e3)+n,a[1]=t[1]||e[1]?(t[1]===e[1]?e[1]:(1e3*(t[1]+(e[1]-t[1])*r)>>0)/1e3)+n:"0","translate("+a.join(",")+")"},rotate:function(t,e,n,r){return"rotate("+(1e3*(t+(e-t)*r)>>0)/1e3+n+")"},scale:Et,skew:_t}},SVGDraw:{component:"svgDraw",property:"draw",defaultValue:"0% 0%",Interpolate:{numbers:E},functions:{prepareStart:function(){return At(this.element)},prepareProperty:function(t,e){return At(this.element,e)},onStart:function(t){t in this.valuesEnd&&!s[t]&&(s[t]=function(t,e,n,r){var a=(100*e.l>>0)/100,i=0-(100*E(e.s,n.s,r)>>0)/100,e=(100*E(e.e,n.e,r)>>0)/100+i;t.style.strokeDashoffset=i+"px",t.style.strokeDasharray=(100*(e<1?0:e)>>0)/100+"px, "+a+"px"})}},Util:{getRectLength:Ot,getPolyLength:Ct,getLineLength:Tt,getCircleLength:St,getEllipseLength:It,getTotalLength:jt,resetDraw:function(t){t.style.strokeDashoffset="",t.style.strokeDasharray=""},getDraw:At,percent:kt}},SVGMorph:{component:"svgMorph",property:"path",defaultValue:[],Interpolate:re,defaultOptions:{morphPrecision:10},functions:{prepareStart:function(){return this.element.getAttribute("d")},prepareProperty:function(t,e){var n={},r=new RegExp("\\n","ig"),a=null;return e instanceof SVGPathElement?a=e:/^\.|^#/.test(e)&&(a=b(e)),"object"==typeof e&&e.polygon?e:(a&&["path","glyph"].includes(a.tagName)?n.original=a.getAttribute("d").replace(r,""):a||"string"!=typeof e||(n.original=e.replace(r,"")),n)},onStart:function(t){!s[t]&&this.valuesEnd[t]&&(s[t]=function(t,e,n,r){var e=e.polygon,a=n.polygon,i=a.length;t.setAttribute("d",1===r?n.original:"M"+re(e,a,i,r).join("L")+"Z")})},crossCheck:function(t){var e,n;this.valuesEnd[t]&&(n=this.valuesStart[t].polygon,e=this.valuesEnd[t].polygon,n&&e&&n.length===e.length||(e=(n=pe(this.valuesStart[t].original,this.valuesEnd[t].original,this._morphPrecision?parseInt(this._morphPrecision,10):c.morphPrecision))[0],n=n[1],this.valuesStart[t].polygon=e,this.valuesEnd[t].polygon=n))}},Util:{addPoints:se,bisect:ue,getPolygon:le,validPolygon:ce,getInterpolationPoints:pe,pathStringToPolygon:ie,distanceSquareRoot:N,midPoint:P,approximatePolygon:ae,rotatePolygon:oe,pathToString:function(t,e){return Yt(t,e).map(function(t){return t[0]+t.slice(1).join(" ")}).join("")},pathToCurve:function(t){var e;if(Nt(e=t)&&e.every(function(t){t=t[0];return"MC".includes(t)}))return S(t);for(var n=Dt(t),r=Object.assign({},Ut),a=[],i=n.length,o=0;o<i;o+=1){h=n[o][0],a[o]=h,n[o]=Bt(n[o],r),p=l=c=u=s=void 0;var s=n,u=a,c=o;if(7<s[c].length){s[c].shift();for(var l=s[c],p=c;l.length;)u[c]="A",s.splice(p+=1,0,["C"].concat(l.splice(0,6)));s.splice(c,1)}var i=n.length,h=n[o],f=h.length;r.x1=+h[f-2],r.y1=+h[f-1],r.x2=+h[f-4]||r.x1,r.y2=+h[f-3]||r.y1}return n},getTotalLength:Jt,getPointAtLength:te,polygonArea:ee,roundPath:Yt}}};Object.keys(he).forEach(function(t){var e=he[t];he[t]=new W(e)});return{Animation:W,Components:he,Tween:K,fromTo:function(t,e,n,r){return r=r||{},new G(b(t),e,n,r)},to:function(t,e,n){return(n=n||{}).resetStart=e,new $(b(t),e,e,n)},TweenCollection:M,allFromTo:function(t,e,n,r){return r=r||{},new M(b(t,!0),e,n,r)},allTo:function(t,e,n){return(n=n||{}).resetStart=e,new M(b(t,!0),e,e,n)},Objects:q,Util:X,Easing:x,CubicBezier:e,Render:D,Interpolate:o,Process:Y,Internals:L,Selector:b,Version:"2.2.4"}});