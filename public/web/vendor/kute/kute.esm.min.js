// KUTE.js Standard v2.2.4 | thednp © 2022 | MIT-License
class t{constructor(t,e,n,s,r){const i=t||0,a=e||0,o=n||1,l=s||1;this.cx=3*i,this.bx=3*(o-i)-this.cx,this.ax=1-this.cx-this.bx,this.cy=3*a,this.by=3*(l-a)-this.cy,this.ay=1-this.cy-this.by;const c=t=>this.sampleCurveY(this.solveCurveX(t));return Object.defineProperty(c,"name",{writable:!0}),c.name=r||`cubic-bezier(${[i,a,o,l]})`,c}sampleCurveX(t){return((this.ax*t+this.bx)*t+this.cx)*t}sampleCurveY(t){return((this.ay*t+this.by)*t+this.cy)*t}sampleCurveDerivativeX(t){return(3*this.ax*t+2*this.bx)*t+this.cx}solveCurveX(t){const e=1e-6;if(t<=0)return 0;if(t>=1)return 1;let n=t,s=0,r=0;for(let i=0;i<8;i+=1){if(s=this.sampleCurveX(n)-t,Math.abs(s)<e)return n;if(r=this.sampleCurveDerivativeX(n),Math.abs(r)<e)break;n-=s/r}let i=0,a=1;for(n=t;i<a;){if(s=this.sampleCurveX(n),Math.abs(s-t)<e)return n;t>s?i=n:a=n,n=.5*(a-i)+i}return n}}Object.assign(t,{Version:"1.0.1"});const e={},n=[];let s;s="undefined"!=typeof global?global:"undefined"!=typeof window?window.self:{};const r=s,i={},a={};let o;o="undefined"==typeof self&&"undefined"!=typeof process&&process.hrtime?()=>{const t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:"undefined"!=typeof self&&void 0!==self.performance&&void 0!==self.performance.now?self.performance.now.bind(self.performance):"undefined"!=typeof Date&&Date.now?Date.now:()=>(new Date).getTime();const l=o,c={};c.now=l;let u=0;const h=t=>{let e=0;for(;e<n.length;)n[e].update(t)?e+=1:n.splice(e,1);u=requestAnimationFrame(h)};function p(){setTimeout((()=>{!n.length&&u&&(cancelAnimationFrame(u),u=null,Object.keys(a).forEach((t=>{"function"==typeof a[t]?e[t]&&delete e[t]:Object.keys(a[t]).forEach((t=>{e[t]&&delete e[t]}))})),Object.keys(i).forEach((t=>{e[t]&&delete e[t]})))}),64)}const f={Tick:u,Ticker:h,Tweens:n,Time:c};Object.keys(f).forEach((t=>{e[t]||(e[t]="Time"===t?c.now:f[t])})),r._KUTE=e;const d={},y={},g={duration:700,delay:0,easing:"linear",repeat:0,repeatDelay:0,yoyo:!1,resetStart:!1,offset:0},m={},x={},b={},v={},E={},w={supportedProperties:d,defaultValues:y,defaultOptions:g,prepareProperty:m,prepareStart:x,crossCheck:b,onStart:a,onComplete:v,linkProperty:E},M={},k=t=>n.push(t),_=t=>{const e=n.indexOf(t);-1!==e&&n.splice(e,1)};function O(){Object.keys(E).forEach((t=>{const n=E[t],s=d[t];Object.keys(n).forEach((t=>{"function"==typeof n[t]&&Object.keys(this.valuesEnd).some((t=>s&&s.includes(t)||"attr"===t&&Object.keys(this.valuesEnd[t]).some((t=>s&&s.includes(t)))))?e[t]||(e[t]=n[t]):Object.keys(this.valuesEnd).forEach((s=>{const r=this.valuesEnd[s];r instanceof Object&&Object.keys(r).forEach((s=>{"function"==typeof n[s]?e[s]||(e[s]=n[s]):Object.keys(n[t]).forEach((t=>{n[s]&&"function"==typeof n[s][t]&&(e[t]||(e[t]=n[s][t]))}))}))}))}))}))}const C={add:k,remove:_,getAll:()=>n,removeAll:()=>{n.length=0},stop:p,linkInterpolation:O};function T(t){if(!t.style)return!1;const e=t.style.cssText.replace(/\s/g,"").split(";"),n={},s=["translate3d","translate","scale3d","skew"];return e.forEach((t=>{if(/transform/i.test(t)){t.split(":")[1].split(")").forEach((t=>{const e=t.split("("),r=e[0],i=e[1];/matrix/.test(r)||(n[r]=s.includes(r)?i.split(","):i)}))}})),n}function I(t,e){let n=y[e];const s=t.style,r=getComputedStyle(t)||t.currentStyle,i=s[e]&&!/auto|initial|none|unset/.test(s[e])?s[e]:r[e];return"transform"!==e&&(e in r||e in s)&&(n=i),n}function S(t,e){const n="start"===e?this.valuesStart:this.valuesEnd;Object.keys(m).forEach((e=>{const s=m[e],r=d[e];Object.keys(s).forEach((e=>{const i={};Object.keys(t).forEach((a=>{y[a]&&s[a]?n[a]=s[a].call(this,a,t[a]):!y[e]&&"transform"===e&&r.includes(a)?i[a]=t[a]:y[a]||"transform"!==a?!y[e]&&r&&r.includes(a)&&(n[a]=s[e].call(this,a,t[a])):n[a]=t[a]})),Object.keys(i).length&&(n[e]=s[e].call(this,e,i))}))}))}function A(){const t={},e=T(this.element);Object.keys(this.valuesStart).forEach((e=>{Object.keys(x).forEach((n=>{const s=x[n];Object.keys(s).forEach((r=>{(r===e&&s[e]||d[n]&&d[n].includes(e))&&(t[e]=s[r].call(this,e,this.valuesStart[e]))}))}))})),Object.keys(e).forEach((n=>{n in this.valuesStart||(t[n]=e[n]||y[n])})),this.valuesStart={},S.call(this,t,"start")}var j={getInlineStyle:T,getStyleForProperty:I,getStartValues:A,prepareObject:S};const $={tween:null,processEasing:null},P={linear:new t(0,0,1,1,"linear"),easingSinusoidalIn:new t(.47,0,.745,.715,"easingSinusoidalIn"),easingSinusoidalOut:new t(.39,.575,.565,1,"easingSinusoidalOut"),easingSinusoidalInOut:new t(.445,.05,.55,.95,"easingSinusoidalInOut"),easingQuadraticIn:new t(.55,.085,.68,.53,"easingQuadraticIn"),easingQuadraticOut:new t(.25,.46,.45,.94,"easingQuadraticOut"),easingQuadraticInOut:new t(.455,.03,.515,.955,"easingQuadraticInOut"),easingCubicIn:new t(.55,.055,.675,.19,"easingCubicIn"),easingCubicOut:new t(.215,.61,.355,1,"easingCubicOut"),easingCubicInOut:new t(.645,.045,.355,1,"easingCubicInOut"),easingQuarticIn:new t(.895,.03,.685,.22,"easingQuarticIn"),easingQuarticOut:new t(.165,.84,.44,1,"easingQuarticOut"),easingQuarticInOut:new t(.77,0,.175,1,"easingQuarticInOut"),easingQuinticIn:new t(.755,.05,.855,.06,"easingQuinticIn"),easingQuinticOut:new t(.23,1,.32,1,"easingQuinticOut"),easingQuinticInOut:new t(.86,0,.07,1,"easingQuinticInOut"),easingExponentialIn:new t(.95,.05,.795,.035,"easingExponentialIn"),easingExponentialOut:new t(.19,1,.22,1,"easingExponentialOut"),easingExponentialInOut:new t(1,0,0,1,"easingExponentialInOut"),easingCircularIn:new t(.6,.04,.98,.335,"easingCircularIn"),easingCircularOut:new t(.075,.82,.165,1,"easingCircularOut"),easingCircularInOut:new t(.785,.135,.15,.86,"easingCircularInOut"),easingBackIn:new t(.6,-.28,.735,.045,"easingBackIn"),easingBackOut:new t(.175,.885,.32,1.275,"easingBackOut"),easingBackInOut:new t(.68,-.55,.265,1.55,"easingBackInOut")};function L(t,e){try{let n,s;return e?(s=t instanceof Array&&t.every((t=>t instanceof Element)),n=t instanceof HTMLCollection||t instanceof NodeList||s?t:document.querySelectorAll(t)):n=t instanceof Element||t===window?t:document.querySelector(t),n}catch(e){throw TypeError(`KUTE.js - Element(s) not found: ${t}.`)}}function N(){Object.keys(a).forEach((t=>{"function"==typeof a[t]?a[t].call(this,t):Object.keys(a[t]).forEach((e=>{a[t][e].call(this,e)}))})),O.call(this)}$.processEasing=function(e){if("function"==typeof e)return e;if("function"==typeof P[e])return P[e];if(/bezier/.test(e)){const n=e.replace(/bezier|\s|\(|\)/g,"").split(",");return new t(1*n[0],1*n[1],1*n[2],1*n[3])}return P.linear};class V{constructor(t,n,s,r){this.element=t,this.playing=!1,this._startTime=null,this._startFired=!1,this.valuesEnd=s,this.valuesStart=n;const i=r||{};this._resetStart=i.resetStart||0,this._easing="function"==typeof i.easing?i.easing:$.processEasing(i.easing),this._duration=i.duration||g.duration,this._delay=i.delay||g.delay,Object.keys(i).forEach((t=>{const e=`_${t}`;e in this||(this[e]=i[t])}));const o=this._easing.name;return a[o]||(a[o]=function(t){e[t]||t!==this._easing.name||(e[t]=this._easing)}),this}start(t){return k(this),this.playing=!0,this._startTime=void 0!==t?t:e.Time(),this._startTime+=this._delay,this._startFired||(this._onStart&&this._onStart.call(this),N.call(this),this._startFired=!0),u||h(),this}stop(){return this.playing&&(_(this),this.playing=!1,this._onStop&&this._onStop.call(this),this.close()),this}close(){Object.keys(v).forEach((t=>{Object.keys(v[t]).forEach((e=>{v[t][e].call(this,e)}))})),this._startFired=!1,p.call(this)}chain(t){return this._chain=[],this._chain=t.length?t:this._chain.concat(t),this}stopChainedTweens(){this._chain&&this._chain.length&&this._chain.forEach((t=>t.stop()))}update(t){const n=void 0!==t?t:e.Time();let s;if(n<this._startTime&&this.playing)return!0;s=(n-this._startTime)/this._duration,s=0===this._duration||s>1?1:s;const r=this._easing(s);return Object.keys(this.valuesEnd).forEach((t=>{e[t](this.element,this.valuesStart[t],this.valuesEnd[t],r)})),this._onUpdate&&this._onUpdate.call(this),1!==s||(this._onComplete&&this._onComplete.call(this),this.playing=!1,this.close(),void 0!==this._chain&&this._chain.length&&this._chain.map((t=>t.start())),!1)}}$.tween=V;class q extends V{constructor(...t){super(...t),this.valuesStart={},this.valuesEnd={};const[e,n,s]=t.slice(1);return S.call(this,n,"end"),this._resetStart?this.valuesStart=e:S.call(this,e,"start"),this._resetStart||Object.keys(b).forEach((t=>{Object.keys(b[t]).forEach((e=>{b[t][e].call(this,e)}))})),this.paused=!1,this._pauseTime=null,this._repeat=s.repeat||g.repeat,this._repeatDelay=s.repeatDelay||g.repeatDelay,this._repeatOption=this._repeat,this.valuesRepeat={},this._yoyo=s.yoyo||g.yoyo,this._reversed=!1,this}start(t){return this._resetStart&&(this.valuesStart=this._resetStart,A.call(this),Object.keys(b).forEach((t=>{Object.keys(b[t]).forEach((e=>{b[t][e].call(this,e)}))}))),this.paused=!1,this._yoyo&&Object.keys(this.valuesEnd).forEach((t=>{this.valuesRepeat[t]=this.valuesStart[t]})),super.start(t),this}stop(){return super.stop(),!this.paused&&this.playing&&(this.paused=!1,this.stopChainedTweens()),this}close(){return super.close(),this._repeatOption>0&&(this._repeat=this._repeatOption),this._yoyo&&!0===this._reversed&&(this.reverse(),this._reversed=!1),this}resume(){return this.paused&&this.playing&&(this.paused=!1,void 0!==this._onResume&&this._onResume.call(this),N.call(this),this._startTime+=e.Time()-this._pauseTime,k(this),u||h()),this}pause(){return!this.paused&&this.playing&&(_(this),this.paused=!0,this._pauseTime=e.Time(),void 0!==this._onPause&&this._onPause.call(this)),this}reverse(){Object.keys(this.valuesEnd).forEach((t=>{const e=this.valuesRepeat[t];this.valuesRepeat[t]=this.valuesEnd[t],this.valuesEnd[t]=e,this.valuesStart[t]=this.valuesRepeat[t]}))}update(t){const n=void 0!==t?t:e.Time();let s;if(n<this._startTime&&this.playing)return!0;s=(n-this._startTime)/this._duration,s=0===this._duration||s>1?1:s;const r=this._easing(s);return Object.keys(this.valuesEnd).forEach((t=>{e[t](this.element,this.valuesStart[t],this.valuesEnd[t],r)})),this._onUpdate&&this._onUpdate.call(this),1!==s||(this._repeat>0?(Number.isFinite(this._repeat)&&(this._repeat-=1),this._startTime=n,Number.isFinite(this._repeat)&&this._yoyo&&!this._reversed&&(this._startTime+=this._repeatDelay),this._yoyo&&(this._reversed=!this._reversed,this.reverse()),!0):(this._onComplete&&this._onComplete.call(this),this.playing=!1,this.close(),void 0!==this._chain&&this._chain.length&&this._chain.forEach((t=>t.start())),!1))}}$.tween=q;class H{constructor(t,e,n,s){const r=$.tween;this.tweens=[];const i=s||{};i.delay=i.delay||g.delay;const a=[];return Array.from(t).forEach(((t,s)=>{if(a[s]=i||{},a[s].delay=s>0?i.delay+(i.offset||g.offset):i.delay,!(t instanceof Element))throw Error(`KUTE - ${t} is not instanceof Element`);this.tweens.push(new r(t,e,n,a[s]))})),this.length=this.tweens.length,this}start(t){const n=void 0===t?e.Time():t;return this.tweens.map((t=>t.start(n))),this}stop(){return this.tweens.map((t=>t.stop())),this}pause(){return this.tweens.map((t=>t.pause())),this}resume(){return this.tweens.map((t=>t.resume())),this}chain(t){const e=this.tweens[this.length-1];if(t instanceof H)e.chain(t.tweens);else{if(!(t instanceof $.tween))throw new TypeError("KUTE.js - invalid chain value");e.chain(t)}return this}playing(){return this.tweens.some((t=>t.playing))}removeTweens(){this.tweens=[]}getMaxDuration(){const t=[];return this.tweens.forEach((e=>{t.push(e._duration+e._delay+e._repeat*e._repeatDelay)})),Math.max(t)}}const{tween:F}=$;const{tween:Q}=$;class U{constructor(t){try{if(t.component in d)throw Error(`KUTE - ${t.component} already registered`);if(t.property in y)throw Error(`KUTE - ${t.property} already registered`)}catch(t){throw Error(t)}const e=this,n=t.component,s={prepareProperty:m,prepareStart:x,onStart:a,onComplete:v,crossCheck:b},r=t.category,o=t.property,l=t.properties&&t.properties.length||t.subProperties&&t.subProperties.length;return d[n]=t.properties||t.subProperties||t.property,"defaultValue"in t?(y[o]=t.defaultValue,e.supports=`${o} property`):t.defaultValues&&(Object.keys(t.defaultValues).forEach((e=>{y[e]=t.defaultValues[e]})),e.supports=`${l||o} ${o||r} properties`),t.defaultOptions&&Object.assign(g,t.defaultOptions),t.functions&&Object.keys(s).forEach((e=>{e in t.functions&&("function"==typeof t.functions[e]?(s[e][n]||(s[e][n]={}),s[e][n][r||o]||(s[e][n][r||o]=t.functions[e])):Object.keys(t.functions[e]).forEach((r=>{s[e][n]||(s[e][n]={}),s[e][n][r]||(s[e][n][r]=t.functions[e][r])})))})),t.Interpolate&&(Object.keys(t.Interpolate).forEach((e=>{const n=t.Interpolate[e];"function"!=typeof n||i[e]?Object.keys(n).forEach((t=>{"function"!=typeof n[t]||i[e]||(i[e]=n[t])})):i[e]=n})),E[n]=t.Interpolate),t.Util&&Object.keys(t.Util).forEach((e=>{M[e]||(M[e]=t.Util[e])})),e}}const D=(t,e)=>{const n=parseInt(t,10)||0,s=["px","%","deg","rad","em","rem","vh","vw"];let r;for(let e=0;e<s.length;e+=1)if("string"==typeof t&&t.includes(s[e])){r=s[e];break}return void 0===r&&(r=e?"deg":"px"),{v:n,u:r}};function X(t,e,n){return+t+(e-t)*n}function Z(t){t in this.valuesEnd&&!e[t]&&(e[t]=(e,n,s,r)=>{e.style[t]=(r>.99||r<.01?(10*X(n,s,r)>>0)/10:X(n,s,r)>>0)+"px"})}const B=["top","left","width","height"],R={};B.forEach((t=>{R[t]=Z}));const Y={component:"essentialBoxModel",category:"boxModel",properties:B,defaultValues:{top:0,left:0,width:0,height:0},Interpolate:{numbers:X},functions:{prepareStart:function(t){return I(this.element,t)||y[t]},prepareProperty:function(t,e){const n=D(e),s="height"===t?"offsetHeight":"offsetWidth";return"%"===n.u?n.v*this.element[s]/100:n.v},onStart:R},Util:{trueDimension:D}},z=t=>{let e;if(/rgb|rgba/.test(t)){const n=t.replace(/\s|\)/,"").split("(")[1].split(","),s=n[3]?n[3]:null;e=s?{r:parseInt(n[0],10),g:parseInt(n[1],10),b:parseInt(n[2],10),a:parseFloat(s)}:{r:parseInt(n[0],10),g:parseInt(n[1],10),b:parseInt(n[2],10)}}if(/^#/.test(t)){const n=(t=>{const e=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,((t,e,n,s)=>e+e+n+n+s+s)),n=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return n?{r:parseInt(n[1],16),g:parseInt(n[2],16),b:parseInt(n[3],16)}:null})(t);e={r:n.r,g:n.g,b:n.b}}if(/transparent|none|initial|inherit/.test(t)&&(e={r:0,g:0,b:0,a:0}),!/^#|^rgb/.test(t)){const n=document.getElementsByTagName("head")[0];n.style.color=t;let s=getComputedStyle(n,null).color;s=/rgb/.test(s)?s.replace(/[^\d,]/g,"").split(","):[0,0,0],n.style.color="",e={r:parseInt(s[0],10),g:parseInt(s[1],10),b:parseInt(s[2],10)}}return e};function K(t,e,n){const s={},r=",";return Object.keys(e).forEach((r=>{"a"!==r?s[r]=X(t[r],e[r],n)>>0||0:t[r]&&e[r]&&(s[r]=(100*X(t[r],e[r],n)>>0)/100)})),s.a?"rgba("+s.r+r+s.g+r+s.b+r+s.a+")":"rgb("+s.r+r+s.g+r+s.b+")"}function G(t){this.valuesEnd[t]&&!e[t]&&(e[t]=(e,n,s,r)=>{e.style[t]=K(n,s,r)})}const W=["color","backgroundColor","outlineColor","borderColor","borderTopColor","borderRightColor","borderBottomColor","borderLeftColor"],J={};W.forEach((t=>{J[t]="#000"}));const tt={};W.forEach((t=>{tt[t]=G}));const et={component:"colorProperties",category:"colors",properties:W,defaultValues:J,Interpolate:{numbers:X,colors:K},functions:{prepareStart:function(t){return I(this.element,t)||y[t]},prepareProperty:function(t,e){return z(e)},onStart:tt},Util:{trueColor:z}},nt={},st=["fill","stroke","stop-color"];function rt(t){return t.replace(/[A-Z]/g,"-$&").toLowerCase()}const it={prepareStart:function(t,e){const n={};return Object.keys(e).forEach((t=>{const e=rt(t).replace(/_+[a-z]+/,""),s=this.element.getAttribute(e);n[e]=st.includes(e)?s||"rgba(0,0,0,0)":s||(/opacity/i.test(t)?1:0)})),n},prepareProperty:function(t,e){const n={};return Object.keys(e).forEach((s=>{const r=rt(s),i=/(%|[a-z]+)$/,o=this.element.getAttribute(r.replace(/_+[a-z]+/,""));if(st.includes(r))a.htmlAttributes[r]=e=>{this.valuesEnd[t]&&this.valuesEnd[t][e]&&!(e in nt)&&(nt[e]=(t,e,n,s,r)=>{t.setAttribute(e,K(n,s,r))})},n[r]=z(e[s])||y.htmlAttributes[s];else if(null!==o&&i.test(o)){const i=D(o).u||D(e[s]).u,l=/%/.test(i)?"_percent":`_${i}`;a.htmlAttributes[r+l]=e=>{this.valuesEnd[t]&&this.valuesEnd[t][e]&&!(e in nt)&&(nt[e]=(t,e,n,s,r)=>{const i=e.replace(l,"");t.setAttribute(i,(1e3*X(n.v,s.v,r)>>0)/1e3+s.u)})},n[r+l]=D(e[s])}else(!i.test(e[s])||null===o||o&&!i.test(o))&&(a.htmlAttributes[r]=e=>{this.valuesEnd[t]&&this.valuesEnd[t][e]&&!(e in nt)&&(nt[e]=(t,e,n,s,r)=>{t.setAttribute(e,(1e3*X(n,s,r)>>0)/1e3)})},n[r]=parseFloat(e[s]))})),n},onStart:{attr(t){!e[t]&&this.valuesEnd[t]&&(e[t]=(t,n,s,r)=>{Object.keys(s).forEach((i=>{e.attributes[i](t,i,n[i],s[i],r)}))})},attributes(t){!e[t]&&this.valuesEnd.attr&&(e[t]=nt)}}},at={component:"htmlAttributes",property:"attr",subProperties:["fill","stroke","stop-color","fill-opacity","stroke-opacity"],defaultValue:{fill:"rgb(0,0,0)",stroke:"rgb(0,0,0)","stop-color":"rgb(0,0,0)",opacity:1,"stroke-opacity":1,"fill-opacity":1},Interpolate:{numbers:X,colors:K},functions:it,Util:{replaceUppercase:rt,trueColor:z,trueDimension:D}};const ot={prepareStart:function(t){return I(this.element,t)},prepareProperty:function(t,e){return parseFloat(e)},onStart:function(t){t in this.valuesEnd&&!e[t]&&(e[t]=(e,n,s,r)=>{e.style[t]=(1e3*X(n,s,r)>>0)/1e3})}},lt={component:"opacityProperty",property:"opacity",defaultValue:1,Interpolate:{numbers:X},functions:ot},ct=String("abcdefghijklmnopqrstuvwxyz").split(""),ut=String("abcdefghijklmnopqrstuvwxyz").toUpperCase().split(""),ht=String("~!@#$%^&*()_+{}[];'<>,./?=-").split(""),pt=String("0123456789").split(""),ft=ct.concat(ut,pt),dt=ft.concat(ht),yt={alpha:ct,upper:ut,symbols:ht,numeric:pt,alphanumeric:ft,all:dt},gt={text(t){if(!e[t]&&this.valuesEnd[t]){const n=this._textChars;let s=yt[g.textChars];n in yt?s=yt[n]:n&&n.length&&(s=n),e[t]=(t,e,n,r)=>{let i="",a="";const o=""===n?" ":n,l=e.substring(0),c=n.substring(0),u=s[Math.random()*s.length>>0];" "===e?(a=c.substring(Math.min(r*c.length,c.length)>>0,0),t.innerHTML=r<1?a+u:o):" "===n?(i=l.substring(0,Math.min((1-r)*l.length,l.length)>>0),t.innerHTML=r<1?i+u:o):(i=l.substring(l.length,Math.min(r*l.length,l.length)>>0),a=c.substring(0,Math.min(r*c.length,c.length)>>0),t.innerHTML=r<1?a+u+i:o)}}},number(t){t in this.valuesEnd&&!e[t]&&(e[t]=(t,e,n,s)=>{t.innerHTML=X(e,n,s)>>0})}};function mt(t,e){let n,s;if("string"==typeof t)return s=document.createElement("SPAN"),s.innerHTML=t,s.className=e,s;if(!t.children.length||t.children.length&&t.children[0].className!==e){const s=t.innerHTML;n=document.createElement("SPAN"),n.className=e,n.innerHTML=s,t.appendChild(n),t.innerHTML=n.outerHTML}else t.children.length&&t.children[0].className===e&&([n]=t.children);return n}function xt(t,e){let n=[];const s=t.children.length;if(s){const r=[];let i,a=t.innerHTML;for(let n,o,l,c=0;c<s;c+=1)n=t.children[c],o=n.outerHTML,i=a.split(o),""!==i[0]?(l=mt(i[0],e),r.push(l),a=a.replace(i[0],"")):""!==i[1]&&(l=mt(i[1].split("<")[0],e),r.push(l),a=a.replace(i[0].split("<")[0],"")),n.classList.contains(e)||n.classList.add(e),r.push(n),a=a.replace(o,"");if(""!==a){const t=mt(a,e);r.push(t)}n=n.concat(r)}else n=n.concat([mt(t,e)]);return n}function bt(t,e,n,s){return`perspective(${(1e3*(t+(e-t)*s)>>0)/1e3}${n})`}function vt(t,e,n,s){const r=[];for(let i=0;i<3;i+=1)r[i]=(t[i]||e[i]?(1e3*(t[i]+(e[i]-t[i])*s)>>0)/1e3:0)+n;return`translate3d(${r.join(",")})`}function Et(t,e,n,s){let r="";return r+=t[0]||e[0]?`rotateX(${(1e3*(t[0]+(e[0]-t[0])*s)>>0)/1e3}${n})`:"",r+=t[1]||e[1]?`rotateY(${(1e3*(t[1]+(e[1]-t[1])*s)>>0)/1e3}${n})`:"",r+=t[2]||e[2]?`rotateZ(${(1e3*(t[2]+(e[2]-t[2])*s)>>0)/1e3}${n})`:"",r}function wt(t,e,n){return`scale(${(1e3*(t+(e-t)*n)>>0)/1e3})`}function Mt(t,e,n,s){const r=[];return r[0]=(t[0]===e[0]?e[0]:(1e3*(t[0]+(e[0]-t[0])*s)>>0)/1e3)+n,r[1]=t[1]||e[1]?(t[1]===e[1]?e[1]:(1e3*(t[1]+(e[1]-t[1])*s)>>0)/1e3)+n:"0",`skew(${r.join(",")})`}function kt(t,e){return parseFloat(t)/100*e}function _t(t){return 2*t.getAttribute("width")+2*t.getAttribute("height")}function Ot(t){const e=t.getAttribute("points").split(" ");let n=0;if(e.length>1){const s=t=>{const e=t.split(",");return 2!==e.length||Number.isNaN(1*e[0])||Number.isNaN(1*e[1])?0:[parseFloat(e[0]),parseFloat(e[1])]},r=(t,e)=>void 0!==t&&void 0!==e?Math.sqrt((e[0]-t[0])**2+(e[1]-t[1])**2):0;if(e.length>2)for(let t=0;t<e.length-1;t+=1)n+=r(s(e[t]),s(e[t+1]));n+="polygon"===t.tagName?r(s(e[0]),s(e[e.length-1])):0}return n}function Ct(t){const e=t.getAttribute("x1"),n=t.getAttribute("x2"),s=t.getAttribute("y1"),r=t.getAttribute("y2");return Math.sqrt((n-e)**2+(r-s)**2)}function Tt(t){const e=t.getAttribute("r");return 2*Math.PI*e}function It(t){const e=2*t.getAttribute("rx"),n=2*t.getAttribute("ry");return Math.sqrt(.5*(e*e+n*n))*(2*Math.PI)/2}function St(t){return"rect"===t.tagName?_t(t):"circle"===t.tagName?Tt(t):"ellipse"===t.tagName?It(t):["polygon","polyline"].includes(t.tagName)?Ot(t):"line"===t.tagName?Ct(t):0}function At(t,e){const n=/path|glyph/.test(t.tagName)?t.getTotalLength():St(t);let s,r,i,a;if(e instanceof Object&&Object.keys(e).every((t=>["s","e","l"].includes(t))))return e;if("string"==typeof e){const t=e.split(/,|\s/);s=/%/.test(t[0])?kt(t[0].trim(),n):parseFloat(t[0]),r=/%/.test(t[1])?kt(t[1].trim(),n):parseFloat(t[1])}else void 0===e&&(a=parseFloat(I(t,"stroke-dashoffset")),i=I(t,"stroke-dasharray").split(","),s=0-a,r=parseFloat(i[0])+s||n);return{s:s,e:r,l:n}}const jt={prepareStart:function(){return At(this.element)},prepareProperty:function(t,e){return At(this.element,e)},onStart:function(t){t in this.valuesEnd&&!e[t]&&(e[t]=(t,e,n,s)=>{const r=(100*e.l>>0)/100,i=0-(100*X(e.s,n.s,s)>>0)/100,a=(100*X(e.e,n.e,s)>>0)/100+i;t.style.strokeDashoffset=`${i}px`,t.style.strokeDasharray=`${(100*(a<1?0:a)>>0)/100}px, ${r}px`})}};function $t(t,e,n){if(t[n].length>7){t[n].shift();const s=t[n];let r=n;for(;s.length;)e[n]="A",t.splice(r+=1,0,["C",...s.splice(0,6)]);t.splice(n,1)}}const Pt={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0};function Lt(t){return Array.isArray(t)&&t.every((t=>{const e=t[0].toLowerCase();return Pt[e]===t.length-1&&"achlmqstvz".includes(e)}))}function Nt(t){return Lt(t)&&t.every((([t])=>t===t.toUpperCase()))}function Vt(t){return Nt(t)&&t.every((([t])=>"ACLMQZ".includes(t)))}function qt(t){return t.map((t=>Array.isArray(t)?[...t]:t))}function Ht(t){let e=t.pathValue[t.segmentStart],n=e.toLowerCase();const{data:s}=t;for(;s.length>=Pt[n]&&("m"===n&&s.length>2?(t.segments.push([e,...s.splice(0,2)]),n="l",e="m"===e?"l":"L"):t.segments.push([e,...s.splice(0,Pt[n])]),Pt[n]););}const Ft="SVGPathCommander error";function Qt(t){const{index:e,pathValue:n}=t,s=n.charCodeAt(e);return 48===s?(t.param=0,void(t.index+=1)):49===s?(t.param=1,void(t.index+=1)):void(t.err=`${Ft}: invalid Arc flag "${n[e]}", expecting 0 or 1 at index ${e}`)}function Ut(t){return t>=48&&t<=57}function Dt(t){const{max:e,pathValue:n,index:s}=t;let r,i=s,a=!1,o=!1,l=!1,c=!1;if(i>=e)t.err=`${Ft}: Invalid path value at index ${i}, "pathValue" is missing param`;else if(r=n.charCodeAt(i),43!==r&&45!==r||(i+=1,r=n.charCodeAt(i)),Ut(r)||46===r){if(46!==r){if(a=48===r,i+=1,r=n.charCodeAt(i),a&&i<e&&r&&Ut(r))return void(t.err=`${Ft}: Invalid path value at index ${s}, "${n[s]}" illegal number`);for(;i<e&&Ut(n.charCodeAt(i));)i+=1,o=!0;r=n.charCodeAt(i)}if(46===r){for(c=!0,i+=1;Ut(n.charCodeAt(i));)i+=1,l=!0;r=n.charCodeAt(i)}if(101===r||69===r){if(c&&!o&&!l)return void(t.err=`${Ft}: Invalid path value at index ${i}, "${n[i]}" invalid float exponent`);if(i+=1,r=n.charCodeAt(i),43!==r&&45!==r||(i+=1),!(i<e&&Ut(n.charCodeAt(i))))return void(t.err=`${Ft}: Invalid path value at index ${i}, "${n[i]}" invalid integer exponent`);for(;i<e&&Ut(n.charCodeAt(i));)i+=1}t.index=i,t.param=+t.pathValue.slice(s,i)}else t.err=`${Ft}: Invalid path value at index ${i}, "${n[i]}" is not a number`}function Xt(t){const{pathValue:e,max:n}=t;for(;t.index<n&&(10===(s=e.charCodeAt(t.index))||13===s||8232===s||8233===s||32===s||9===s||11===s||12===s||160===s||s>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(s));)t.index+=1;var s}function Zt(t){return t>=48&&t<=57||43===t||45===t||46===t}function Bt(t){const{max:e,pathValue:n,index:s}=t,r=n.charCodeAt(s),i=Pt[n[s].toLowerCase()];if(t.segmentStart=s,function(t){switch(32|t){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:return!0;default:return!1}}(r))if(t.index+=1,Xt(t),t.data=[],i){for(;;){for(let s=i;s>0;s-=1){if(97!=(32|r)||3!==s&&4!==s?Dt(t):Qt(t),t.err.length)return;t.data.push(t.param),Xt(t),t.index<e&&44===n.charCodeAt(t.index)&&(t.index+=1,Xt(t))}if(t.index>=t.max)break;if(!Zt(n.charCodeAt(t.index)))break}Ht(t)}else Ht(t);else t.err=`${Ft}: Invalid path value "${n[s]}" is not a path command`}function Rt(t){this.segments=[],this.pathValue=t,this.max=t.length,this.index=0,this.param=0,this.segmentStart=0,this.data=[],this.err=""}function Yt(t){if(Nt(t))return qt(t);const e=function(t){if(Lt(t))return qt(t);const e=new Rt(t);for(Xt(e);e.index<e.max&&!e.err.length;)Bt(e);return e.err?e.err:e.segments}(t);let n=0,s=0,r=0,i=0;return e.map((t=>{const e=t.slice(1).map(Number),[a]=t,o=a.toUpperCase();if("M"===a)return[n,s]=e,r=n,i=s,["M",n,s];let l=[];if(a!==o)switch(o){case"A":l=[o,e[0],e[1],e[2],e[3],e[4],e[5]+n,e[6]+s];break;case"V":l=[o,e[0]+s];break;case"H":l=[o,e[0]+n];break;default:l=[o,...e.map(((t,e)=>t+(e%2?s:n)))]}else l=[o,...e];const c=l.length;switch(o){case"Z":n=r,s=i;break;case"H":[,n]=l;break;case"V":[,s]=l;break;default:n=l[c-2],s=l[c-1],"M"===o&&(r=n,i=s)}return l}))}function zt(t,e){const[n]=t,{x1:s,y1:r,x2:i,y2:a}=e,o=t.slice(1).map(Number);let l=t;if("TQ".includes(n)||(e.qx=null,e.qy=null),"H"===n)l=["L",t[1],r];else if("V"===n)l=["L",s,t[1]];else if("S"===n){const t=2*s-i,n=2*r-a;e.x1=t,e.y1=n,l=["C",t,n,...o]}else if("T"===n){const t=2*s-e.qx,n=2*r-e.qy;e.qx=t,e.qy=n,l=["Q",t,n,...o]}else if("Q"===n){const[t,n]=o;e.qx=t,e.qy=n}return l}const Kt={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null};function Gt(t){if(Vt(t))return qt(t);const e=Yt(t),n={...Kt},s=e.length;for(let t=0;t<s;t+=1){e[t],e[t]=zt(e[t],n);const s=e[t],r=s.length;n.x1=+s[r-2],n.y1=+s[r-1],n.x2=+s[r-4]||n.x1,n.y2=+s[r-3]||n.y1}return e}function Wt(t,e,n){return{x:t*Math.cos(n)-e*Math.sin(n),y:t*Math.sin(n)+e*Math.cos(n)}}function Jt(t,e,n,s,r,i,a,o,l,c){let u=t,h=e,p=n,f=s,d=o,y=l;const g=120*Math.PI/180,m=Math.PI/180*(+r||0);let x,b,v,E,w,M=[];if(c)[b,v,E,w]=c;else{x=Wt(u,h,-m),u=x.x,h=x.y,x=Wt(d,y,-m),d=x.x,y=x.y;const t=(u-d)/2,e=(h-y)/2;let n=t*t/(p*p)+e*e/(f*f);n>1&&(n=Math.sqrt(n),p*=n,f*=n);const s=p*p,r=f*f,o=(i===a?-1:1)*Math.sqrt(Math.abs((s*r-s*e*e-r*t*t)/(s*e*e+r*t*t)));E=o*p*e/f+(u+d)/2,w=o*-f*t/p+(h+y)/2,b=Math.asin(((h-w)/f*10**9>>0)/10**9),v=Math.asin(((y-w)/f*10**9>>0)/10**9),b=u<E?Math.PI-b:b,v=d<E?Math.PI-v:v,b<0&&(b=2*Math.PI+b),v<0&&(v=2*Math.PI+v),a&&b>v&&(b-=2*Math.PI),!a&&v>b&&(v-=2*Math.PI)}let k=v-b;if(Math.abs(k)>g){const t=v,e=d,n=y;v=b+g*(a&&v>b?1:-1),d=E+p*Math.cos(v),y=w+f*Math.sin(v),M=Jt(d,y,p,f,r,0,a,e,n,[v,t,E,w])}k=v-b;const _=Math.cos(b),O=Math.sin(b),C=Math.cos(v),T=Math.sin(v),I=Math.tan(k/4),S=4/3*p*I,A=4/3*f*I,j=[u,h],$=[u+S*O,h-A*_],P=[d+S*T,y-A*C],L=[d,y];if($[0]=2*j[0]-$[0],$[1]=2*j[1]-$[1],c)return[...$,...P,...L,...M];M=[...$,...P,...L,...M];const N=[];for(let t=0,e=M.length;t<e;t+=1)N[t]=t%2?Wt(M[t-1],M[t],m).y:Wt(M[t],M[t+1],m).x;return N}function te(t,e,n,s,r,i){const a=1/3,o=2/3;return[a*t+o*n,a*e+o*s,a*r+o*n,a*i+o*s,r,i]}function ee(t,e,n){const[s,r]=t,[i,a]=e;return[s+(i-s)*n,r+(a-r)*n]}function ne(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function se(t,e,n,s,r){const i=ne([t,e],[n,s]);let a={x:0,y:0};if("number"==typeof r)if(r<=0)a={x:t,y:e};else if(r>=i)a={x:n,y:s};else{const[o,l]=ee([t,e],[n,s],r/i);a={x:o,y:l}}return{length:i,point:a,min:{x:Math.min(t,n),y:Math.min(e,s)},max:{x:Math.max(t,n),y:Math.max(e,s)}}}function re(t,e,n,s){const r=.5,i=[t,e],a=[n,s],o=ee(i,a,r),l=ee(a,o,r),c=ee(o,l,r),u=ee(l,c,r),h=ee(c,u,r),p=se(...[...i,...o,...c,...h,r]).point,f=se(...[...h,...u,...l,...a,0]).point;return[p.x,p.y,f.x,f.y,n,s]}function ie(t,e){const[n]=t,s=t.slice(1).map(Number),[r,i]=s;let a;const{x1:o,y1:l,x:c,y:u}=e;switch("TQ".includes(n)||(e.qx=null,e.qy=null),n){case"M":return e.x=r,e.y=i,t;case"A":return a=[o,l,...s],["C",...Jt(...a)];case"Q":return e.qx=r,e.qy=i,a=[o,l,...s],["C",...te(...a)];case"L":return["C",...re(o,l,r,i)];case"Z":return["C",...re(o,l,c,u)]}return t}const ae={origin:[0,0,0],round:4};function oe(t,e){let{round:n}=ae;if("off"===e||"off"===n)return qt(t);n=e>=0?e:n;const s="number"==typeof n&&n>=1?10**n:1;return t.map((t=>{const e=t.slice(1).map(Number).map((t=>n?Math.round(t*s)/s:Math.round(t)));return[t[0],...e]}))}function le(t,e){const{x:n,y:s}=t,{x:r,y:i}=e,a=n*r+s*i,o=Math.sqrt((n**2+s**2)*(r**2+i**2));return(n*i-s*r<0?-1:1)*Math.acos(a/o)}function ce(t,e,n,s,r,i,a,o,l,c){const{abs:u,sin:h,cos:p,sqrt:f,PI:d}=Math;let y=u(n),g=u(s);const m=(r%360+360)%360*(d/180);if(t===o&&e===l)return{x:t,y:e};if(0===y||0===g)return se(t,e,o,l,c).point;const x=(t-o)/2,b=(e-l)/2,v=p(m)*x+h(m)*b,E=-h(m)*x+p(m)*b,w=v**2/y**2+E**2/g**2;w>1&&(y*=f(w),g*=f(w));let M=(y**2*g**2-y**2*E**2-g**2*v**2)/(y**2*E**2+g**2*v**2);M=M<0?0:M;const k=(i!==a?1:-1)*f(M),_=k*(y*E/g),O=k*(-g*v/y),C=p(m)*_-h(m)*O+(t+o)/2,T=h(m)*_+p(m)*O+(e+l)/2,I={x:(v-_)/y,y:(E-O)/g},S=le({x:1,y:0},I);let A=le(I,{x:(-v-_)/y,y:(-E-O)/g});!a&&A>0?A-=2*d:a&&A<0&&(A+=2*d),A%=2*d;const j=S+A*c,$=y*p(j),P=g*h(j);return{x:p(m)*$-h(m)*P+C,y:h(m)*$+p(m)*P+T}}function ue(t,e,n,s,r,i,a,o,l,c){const u="number"==typeof c;let h=t,p=e,f=0,d=[h,p,f],y=[h,p],g=0,m={x:0,y:0},x=[{x:h,y:p}];u&&c<=0&&(m={x:h,y:p});for(let b=0;b<=300;b+=1){if(g=b/300,({x:h,y:p}=ce(t,e,n,s,r,i,a,o,l,g)),x=[...x,{x:h,y:p}],f+=ne(y,[h,p]),y=[h,p],u&&f>c&&c>d[2]){const t=(f-c)/(f-d[2]);m={x:y[0]*(1-t)+d[0]*t,y:y[1]*(1-t)+d[1]*t}}d=[h,p,f]}return u&&c>=f&&(m={x:o,y:l}),{length:f,point:m,min:{x:Math.min(...x.map((t=>t.x))),y:Math.min(...x.map((t=>t.y)))},max:{x:Math.max(...x.map((t=>t.x))),y:Math.max(...x.map((t=>t.y)))}}}function he(t,e,n,s,r,i,a,o,l){const c=1-l;return{x:c**3*t+3*c**2*l*n+3*c*l**2*r+l**3*a,y:c**3*e+3*c**2*l*s+3*c*l**2*i+l**3*o}}function pe(t,e,n,s,r,i,a,o,l){const c="number"==typeof l;let u=t,h=e,p=0,f=[u,h,p],d=[u,h],y=0,g={x:0,y:0},m=[{x:u,y:h}];c&&l<=0&&(g={x:u,y:h});for(let x=0;x<=300;x+=1){if(y=x/300,({x:u,y:h}=he(t,e,n,s,r,i,a,o,y)),m=[...m,{x:u,y:h}],p+=ne(d,[u,h]),d=[u,h],c&&p>l&&l>f[2]){const t=(p-l)/(p-f[2]);g={x:d[0]*(1-t)+f[0]*t,y:d[1]*(1-t)+f[1]*t}}f=[u,h,p]}return c&&l>=p&&(g={x:a,y:o}),{length:p,point:g,min:{x:Math.min(...m.map((t=>t.x))),y:Math.min(...m.map((t=>t.y)))},max:{x:Math.max(...m.map((t=>t.x))),y:Math.max(...m.map((t=>t.y)))}}}function fe(t,e,n,s,r,i,a){const o=1-a;return{x:o**2*t+2*o*a*n+a**2*r,y:o**2*e+2*o*a*s+a**2*i}}function de(t,e,n,s,r,i,a){const o="number"==typeof a;let l=t,c=e,u=0,h=[l,c,u],p=[l,c],f=0,d={x:0,y:0},y=[{x:l,y:c}];o&&a<=0&&(d={x:l,y:c});for(let g=0;g<=300;g+=1){if(f=g/300,({x:l,y:c}=fe(t,e,n,s,r,i,f)),y=[...y,{x:l,y:c}],u+=ne(p,[l,c]),p=[l,c],o&&u>a&&a>h[2]){const t=(u-a)/(u-h[2]);d={x:p[0]*(1-t)+h[0]*t,y:p[1]*(1-t)+h[1]*t}}h=[l,c,u]}return o&&a>=u&&(d={x:r,y:i}),{length:u,point:d,min:{x:Math.min(...y.map((t=>t.x))),y:Math.min(...y.map((t=>t.y)))},max:{x:Math.max(...y.map((t=>t.x))),y:Math.max(...y.map((t=>t.y)))}}}function ye(t,e){const n=Gt(t),s="number"==typeof e;let r,i,a,o=[],l=0,c=0,u=0,h=0,p=[],f=[],d=0,y={x:0,y:0},g=y,m=y,x=y,b=0;for(let t=0,v=n.length;t<v;t+=1)a=n[t],[i]=a,r="M"===i,o=r?o:[l,c,...a.slice(1)],r?([,u,h]=a,y={x:u,y:h},g=y,d=0,s&&e<.001&&(x=y)):"L"===i?({length:d,min:y,max:g,point:m}=se(...o,(e||0)-b)):"A"===i?({length:d,min:y,max:g,point:m}=ue(...o,(e||0)-b)):"C"===i?({length:d,min:y,max:g,point:m}=pe(...o,(e||0)-b)):"Q"===i?({length:d,min:y,max:g,point:m}=de(...o,(e||0)-b)):"Z"===i&&(o=[l,c,u,h],({length:d,min:y,max:g,point:m}=se(...o,(e||0)-b))),s&&b<e&&b+d>=e&&(x=m),f=[...f,g],p=[...p,y],b+=d,[l,c]="Z"!==i?a.slice(-2):[u,h];return s&&e>=b&&(x={x:l,y:c}),{length:b,point:x,min:{x:Math.min(...p.map((t=>t.x))),y:Math.min(...p.map((t=>t.y)))},max:{x:Math.max(...f.map((t=>t.x))),y:Math.max(...f.map((t=>t.y)))}}}function ge(t){return ye(t).length}function me(t,e){return ye(t,e).point}function xe(t){const e=t.length;let n,s=-1,r=t[e-1],i=0;for(;++s<e;)n=r,r=t[s],i+=n[1]*r[0]-n[0]*r[1];return i/2}function be(t,e,n,s){const r=[];for(let i=0;i<n;i+=1){r[i]=[];for(let n=0;n<2;n+=1)r[i].push((1e3*(t[i][n]+(e[i][n]-t[i][n])*s)>>0)/1e3)}return r}function ve(t,e){const n=Gt(function(t){const e=[];let n,s=-1;return t.forEach((t=>{"M"===t[0]?(n=[t],s+=1):n=[...n,t],e[s]=n})),e}(t)[0]),s=ge(n),r=[];let i,a=3;e&&!Number.isNaN(e)&&+e>0&&(a=Math.max(a,Math.ceil(s/e)));for(let t=0;t<a;t+=1)i=me(n,s*t/a),r.push([i.x,i.y]);return xe(r)>0&&r.reverse(),{polygon:r,skipBisect:!0}}function Ee(t,e){const n=Gt(t);return function(t){const e=[],n=t.length;let s=[],r="";if(!t.length||"M"!==t[0][0])return!1;for(let i=0;i<n&&(s=t[i],[r]=s,!("M"===r&&i||"Z"===r));i+=1){if(!"ML".includes(r))return!1;e.push([s[1],s[2]])}return!!n&&{polygon:e}}(n)||ve(n,e)}function we(t,e){const n=t.length;let s,r,i,a,o=1/0,l=0;for(let r=0;r<n;r+=1){l=0;for(let s=0;s<e.length;s+=1)a=e[s],i=ne(t[(r+s)%n],a),l+=i*i;l<o&&(o=l,s=r)}s&&(r=t.splice(0,s),t.splice(t.length,0,...r))}function Me(t,e){const n=t.length+e,s=function(t){return t.reduce(((e,n,s)=>s?e+ne(t[s-1],n):0),0)}(t)/e;let r,i,a,o=0,l=0,c=s/2;for(;t.length<n;)r=t[o],i=t[(o+1)%t.length],a=ne(r,i),c<=l+a?(t.splice(o+1,0,a?ee(r,i,(c-l)/a):r.slice(0)),c+=s):(l+=a,o+=1)}function ke(t,e=1/0){let n=[],s=[];for(let r=0;r<t.length;r+=1)for(n=t[r],s=r===t.length-1?t[0]:t[r+1];ne(n,s)>e;)s=ee(n,s,.5),t.splice(r+1,0,s)}function _e(t){return Array.isArray(t)&&t.every((t=>Array.isArray(t)&&2===t.length&&!Number.isNaN(t[0])&&!Number.isNaN(t[1])))}function Oe(t,e){let n,s;if("string"==typeof t){const r=Ee(t,e);({polygon:s,skipBisect:n}=r)}else if(!Array.isArray(t))throw Error(`Invalid path value: ${t}`);const r=[...s];if(!_e(r))throw Error(`Invalid path value: ${r}`);return r.length>1&&ne(r[0],r[r.length-1])<1e-9&&r.pop(),!n&&e&&!Number.isNaN(e)&&+e>0&&ke(r,e),r}function Ce(t,e,n){const s=n||g.morphPrecision,r=Oe(t,s),i=Oe(e,s),a=r.length-i.length;return Me(r,a<0?-1*a:0),Me(i,a>0?a:0),we(r,i),[oe(r),oe(i)]}const Te={prepareStart:function(){return this.element.getAttribute("d")},prepareProperty:function(t,e){const n={},s=new RegExp("\\n","ig");let r=null;return e instanceof SVGPathElement?r=e:/^\.|^#/.test(e)&&(r=L(e)),"object"==typeof e&&e.polygon?e:(r&&["path","glyph"].includes(r.tagName)?n.original=r.getAttribute("d").replace(s,""):r||"string"!=typeof e||(n.original=e.replace(s,"")),n)},onStart:function(t){!e[t]&&this.valuesEnd[t]&&(e[t]=(t,e,n,s)=>{const r=e.polygon,i=n.polygon,a=i.length;t.setAttribute("d",1===s?n.original:`M${be(r,i,a,s).join("L")}Z`)})},crossCheck:function(t){if(this.valuesEnd[t]){const e=this.valuesStart[t].polygon,n=this.valuesEnd[t].polygon;if(!e||!n||e.length!==n.length){const e=this.valuesStart[t].original,n=this.valuesEnd[t].original,s=this._morphPrecision?parseInt(this._morphPrecision,10):g.morphPrecision,[r,i]=Ce(e,n,s);this.valuesStart[t].polygon=r,this.valuesEnd[t].polygon=i}}}},Ie={EssentialBoxModel:Y,ColorsProperties:et,HTMLAttributes:at,OpacityProperty:lt,TextWriteProp:{component:"textWriteProperties",category:"textWrite",properties:["text","number"],defaultValues:{text:" ",number:"0"},defaultOptions:{textChars:"alpha"},Interpolate:{numbers:X},functions:{prepareStart:function(){return this.element.innerHTML},prepareProperty:function(t,e){return"number"===t?parseFloat(e):""===e?" ":e},onStart:gt},Util:{charSet:yt,createTextTweens:function(t,e,n){if(t.playing)return!1;const s=n||{};s.duration=1e3,"auto"===n.duration?s.duration="auto":Number.isFinite(1*n.duration)&&(s.duration=1*n.duration);const r=$.tween,i=function(t,e){const n=xt(t,"text-part"),s=xt(mt(e),"text-part");return t.innerHTML="",t.innerHTML+=n.map((t=>(t.className+=" oldText",t.outerHTML))).join(""),t.innerHTML+=s.map((t=>(t.className+=" newText",t.outerHTML.replace(t.innerHTML,"")))).join(""),[n,s]}(t,e),a=i[0],o=i[1],l=[].slice.call(t.getElementsByClassName("oldText")).reverse(),c=[].slice.call(t.getElementsByClassName("newText"));let u=[],h=0;return u=u.concat(l.map(((t,e)=>(s.duration="auto"===s.duration?75*a[e].innerHTML.length:s.duration,s.delay=h,s.onComplete=null,h+=s.duration,new r(t,{text:t.innerHTML},{text:""},s))))),u=u.concat(c.map(((n,i)=>(s.duration="auto"===s.duration?75*o[i].innerHTML.length:s.duration,s.delay=h,s.onComplete=i===o.length-1?function(){t.innerHTML=e,t.playing=!1}:null,h+=s.duration,new r(n,{text:""},{text:o[i].innerHTML},s))))),u.start=function(){t.playing||(u.forEach((t=>t.start())),t.playing=!0)},u}}},TransformFunctions:{component:"transformFunctions",property:"transform",subProperties:["perspective","translate3d","translateX","translateY","translateZ","translate","rotate3d","rotateX","rotateY","rotateZ","rotate","skewX","skewY","skew","scale"],defaultValues:{perspective:400,translate3d:[0,0,0],translateX:0,translateY:0,translateZ:0,translate:[0,0],rotate3d:[0,0,0],rotateX:0,rotateY:0,rotateZ:0,rotate:0,skewX:0,skewY:0,skew:[0,0],scale:1},functions:{prepareStart:function(t){const e=T(this.element);return e[t]?e[t]:y[t]},prepareProperty:function(t,e){const n=["X","Y","Z"],s={},r=[],i=[],a=[],o=["translate3d","translate","rotate3d","skew"];return Object.keys(e).forEach((t=>{const l="object"==typeof e[t]&&e[t].length?e[t].map((t=>parseInt(t,10))):parseInt(e[t],10);if(o.includes(t)){const e="translate"===t||"rotate"===t?`${t}3d`:t;s[e]="skew"===t?l.length?[l[0]||0,l[1]||0]:[l||0,0]:"translate"===t?l.length?[l[0]||0,l[1]||0,l[2]||0]:[l||0,0,0]:[l[0]||0,l[1]||0,l[2]||0]}else if(/[XYZ]/.test(t)){const o=t.replace(/[XYZ]/,""),l="skew"===o?o:`${o}3d`,c="skew"===o?2:3;let u=[];"translate"===o?u=r:"rotate"===o?u=i:"skew"===o&&(u=a);for(let t=0;t<c;t+=1){const s=n[t];u[t]=`${o}${s}`in e?parseInt(e[`${o}${s}`],10):0}s[l]=u}else"rotate"===t?s.rotate3d=[0,0,l]:s[t]="scale"===t?parseFloat(e[t]):l})),s},onStart:function(t){!e[t]&&this.valuesEnd[t]&&(e[t]=(e,n,s,r)=>{e.style[t]=(n.perspective||s.perspective?bt(n.perspective,s.perspective,"px",r):"")+(n.translate3d?vt(n.translate3d,s.translate3d,"px",r):"")+(n.rotate3d?Et(n.rotate3d,s.rotate3d,"deg",r):"")+(n.skew?Mt(n.skew,s.skew,"deg",r):"")+(n.scale||s.scale?wt(n.scale,s.scale,r):"")})},crossCheck:function(t){this.valuesEnd[t]&&this.valuesEnd[t]&&this.valuesEnd[t].perspective&&!this.valuesStart[t].perspective&&(this.valuesStart[t].perspective=this.valuesEnd[t].perspective)}},Interpolate:{perspective:bt,translate3d:vt,rotate3d:Et,translate:function(t,e,n,s){const r=[];return r[0]=(t[0]===e[0]?e[0]:(1e3*(t[0]+(e[0]-t[0])*s)>>0)/1e3)+n,r[1]=t[1]||e[1]?(t[1]===e[1]?e[1]:(1e3*(t[1]+(e[1]-t[1])*s)>>0)/1e3)+n:"0",`translate(${r.join(",")})`},rotate:function(t,e,n,s){return`rotate(${(1e3*(t+(e-t)*s)>>0)/1e3}${n})`},scale:wt,skew:Mt}},SVGDraw:{component:"svgDraw",property:"draw",defaultValue:"0% 0%",Interpolate:{numbers:X},functions:jt,Util:{getRectLength:_t,getPolyLength:Ot,getLineLength:Ct,getCircleLength:Tt,getEllipseLength:It,getTotalLength:St,resetDraw:function(t){t.style.strokeDashoffset="",t.style.strokeDasharray=""},getDraw:At,percent:kt}},SVGMorph:{component:"svgMorph",property:"path",defaultValue:[],Interpolate:be,defaultOptions:{morphPrecision:10},functions:Te,Util:{addPoints:Me,bisect:ke,getPolygon:Oe,validPolygon:_e,getInterpolationPoints:Ce,pathStringToPolygon:Ee,distanceSquareRoot:ne,midPoint:ee,approximatePolygon:ve,rotatePolygon:we,pathToString:function(t,e){return oe(t,e).map((t=>t[0]+t.slice(1).join(" "))).join("")},pathToCurve:function(t){if(function(t){return Vt(t)&&t.every((([t])=>"MC".includes(t)))}(t))return qt(t);const e=Gt(t),n={...Kt},s=[];let r="",i=e.length;for(let t=0;t<i;t+=1){[r]=e[t],s[t]=r,e[t]=ie(e[t],n),$t(e,s,t),i=e.length;const a=e[t],o=a.length;n.x1=+a[o-2],n.y1=+a[o-1],n.x2=+a[o-4]||n.x1,n.y2=+a[o-3]||n.y1}return e},getTotalLength:ge,getPointAtLength:me,polygonArea:xe,roundPath:oe}}};Object.keys(Ie).forEach((t=>{const e=Ie[t];Ie[t]=new U(e)}));const Se={Animation:U,Components:Ie,Tween:q,fromTo:function(t,e,n,s){const r=s||{};return new Q(L(t),e,n,r)},to:function(t,e,n){const s=n||{};return s.resetStart=e,new F(L(t),e,e,s)},TweenCollection:H,allFromTo:function(t,e,n,s){const r=s||{};return new H(L(t,!0),e,n,r)},allTo:function(t,e,n){const s=n||{};return s.resetStart=e,new H(L(t,!0),e,e,s)},Objects:w,Util:M,Easing:P,CubicBezier:t,Render:f,Interpolate:i,Process:j,Internals:C,Selector:L,Version:"2.2.4"};export{Se as default};
