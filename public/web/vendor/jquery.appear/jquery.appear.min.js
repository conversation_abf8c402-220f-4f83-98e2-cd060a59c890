!function(e,a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof module&&module.exports?a(require("jquery")):a(e.jQuery)}(this,function(n){n.fn.appear=function(r,e){var d=n.extend({data:void 0,one:!0,accX:0,accY:0},e);return this.each(function(){var s,a,e,u=n(this);u.appeared=!1,r?(s=n(window),a=function(){var e,a,r,n,p,t,c,i,o,f;u.is(":visible")?(e=s.scrollLeft(),a=s.scrollTop(),r=(f=u.offset()).left,n=f.top,p=d.accX,t=d.accY,c=u.height(),i=s.height(),o=u.width(),f=s.width(),a<=n+c+t&&n<=a+i+t&&e<=r+o+p&&r<=e+f+p?u.appeared||u.trigger("appear",d.data):u.appeared=!1):u.appeared=!1},e=function(){var e;u.appeared=!0,d.one&&(s.unbind("scroll",a),0<=(e=n.inArray(a,n.fn.appear.checks))&&n.fn.appear.checks.splice(e,1)),r.apply(this,arguments)},d.one?u.one("appear",d.data,e):u.bind("appear",d.data,e),s.scroll(a),n.fn.appear.checks.push(a),a()):u.trigger("appear",d.data)})},n.extend(n.fn.appear,{checks:[],timeout:null,checkAll:function(){var e=n.fn.appear.checks.length;if(0<e)for(;e--;)n.fn.appear.checks[e]()},run:function(){n.fn.appear.timeout&&clearTimeout(n.fn.appear.timeout),n.fn.appear.timeout=setTimeout(n.fn.appear.checkAll,20)}}),n.each(["append","prepend","after","before","attr","removeAttr","addClass","removeClass","toggleClass","remove","css","show","hide"],function(e,a){var r=n.fn[a];r&&(n.fn[a]=function(){var e=r.apply(this,arguments);return n.fn.appear.run(),e})})});