# Simple Line Icons

![Bower version](https://img.shields.io/bower/v/simple-line-icons.svg)
[![npm version](https://img.shields.io/npm/v/simple-line-icons.svg)](https://www.npmjs.com/package/simple-line-icons)
[![Build Status](https://travis-ci.org/thesabbir/simple-line-icons.svg?branch=master)](https://travis-ci.org/thesabbir/simple-line-icons)

Simple line icons with CSS, SASS, LESS & Web-fonts files.

## Preview & Docs

You can find a cheat sheet of the complete set of icons at [Simple Line Icons - GitHub Pages](https://thesabbir.github.io/simple-line-icons/)

## Installation


via [npm](https://www.npmjs.com/package/simple-line-icons)

```bash
npm install simple-line-icons --save
```

via [bower](http://bower.io/search/?q=simple-line-icons)

```bash
bower install simple-line-icons --save
```

via [cdnjs](http://cdnjs.com/libraries/simple-line-icons)

Alternatively, you can also clone or [download this repository](https://github.com/thesabbir/simple-line-icons/archive/master.zip) as zip.

If you are a designer, you can use this [creative cloud library](http://adobe.ly/2bQ48wl) in your project.

## Customizing LESS/SASS variables

### LESS:

```less
@simple-line-font-path     : "/path/to/font/files";
@simple-line-font-family   : "desired-name-font-family";
@simple-line-icon-prefix   : prefix-;
```

### SASS:

```sass
$simple-line-font-path     : "/path/to/font/files";
$simple-line-font-family   : "desired-name-font-family";
$simple-line-icon-prefix   : "prefix-";
```


## Credits

[Jamal Jama](https://twitter.com/byjml) for creating this awesome webfont & [Ahmad Firoz](https://twitter.com/firoz_usf) for extending it further.

## Contributors

[Check Here](https://github.com/thesabbir/simple-line-icons/graphs/contributors)

## Contributions

Contributions are more then welcome. Keep them coming!
Please make sure you have read our [guide line](./CONTRIBUTING.md).

## License

You're free to use the web-font in a template/theme intended for sale on marketplaces like ThemeForest.

CSS, SCSS & LESS files are under [MIT License](./LICENSE.md).
