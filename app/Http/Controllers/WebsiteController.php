<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Product;
use Illuminate\View\View;
use GuzzleHttp\Client;

class WebsiteController extends Controller
{
    /**
     * Show website index.
     * @return View
     */
    public function welcome(): View
    {
        return view('welcome');
    }

    /**
     * Show website index.
     * @return View
     */
    public function index(): View
    {
        return view('web.index')->with([
            'metaTitle' => 'La Gran M',
            'description' => '',
            'keywords' => '',
            'metaImage' => 'meta-default.jpg',
            'schema' => 1,
            'noIndex' => false,
            'posts' => Post::all()
        ]);
    }

    /**
     * Show about page.
     * @return View
     */
    public function company(): View
    {
        return view('web.company')->with([
            'title' => 'Acerca de',
            'metaTitle' => 'Acerca de | Andamios y Construcciones',
            'description' => 'Andamios y Construcciones en Orizaba, Veracruz: Especialistas en renta y venta de andamios, maquinaria ligera y materiales de construcción.',
            'keywords' => 'andamios Orizaba, renta de andamios, venta de andamios, maquinaria ligera, material de construcción, andamiaje Veracruz, equipo de construcción, seguridad en construcción, andamios certificados, construcción Orizaba',
            'metaImage' => 'meta-default.jpg',
            'schema' => 1,
            'noIndex' => true
        ]);
    }

    /**
     * Show contact page.
     * @return View
     */
    public function contact(): View
    {
        return view('web.contact')->with([
            'title' => 'Contacto',
            'metaTitle' => 'Contacto | Andamios y Construcciones',
            'description' => '¿Listo para llevar tu empresa o proyecto al siguiente nivel digital? Descubra nuestras soluciones web a medida diseñadas solo para ti.',
            'keywords' => 'agencia SEO, optimización de motores de búsqueda, marketing online, marketing en Internet, análisis web, marketing de contenidos, optimización de la tasa de conversión, generación de leads, optimización de sitios web, estrategia de marketing, investigación de palabras clave, construcción de enlaces, presencia en línea, tráfico orgánico, ranking de búsqueda, gestión de campañas, análisis de datos, auditoría de sitios web, desarrollo web, call tracking',
            'metaImage' => 'meta-contact.jpg',
            'schema' => 1,
            'noIndex' => false
        ]);
    }

    /**
     * Show privacy policy page.
     * @return View
     */
    public function privacy(): View
    {
        return view('web.privacy')->with([
            'title' => 'Aviso de privacidad',
            'metaTitle' => 'Aviso de privacidad | Andamios y Construcciones',
            'description' => 'Descubre cómo protegemos tu información personal y garantizamos tu privacidad en Andamios y Construcciones. Nuestra política de privacidad te brinda transparencia y confianza en el manejo de tus datos personales.',
            'keywords' => '',
            'metaImage' => 'meta-default.jpg',
            'schema' => 1,
            'noIndex' => true
        ]);
    }

    /**
     * Show privacy policy page.
     * @return View
     */
    public function sitemap(): View
    {
        return view('web.sitemap')->with([
            'title' => 'Mapa del sitio',
            'metaTitle' => 'Mapa del sitio  | Andamios y Construcciones',
            'description' => 'Encuentra una visión general de nuestro sitio web y navega fácilmente por nuestras páginas y servicios. Explora nuestro mapa del sitio aquí.',
            'keywords' => '',
            'metaImage' => 'meta-default.jpg',
            'schema' => 1,
            'noIndex' => true,
            'posts' => Post::all(),
            'products' => Product::all()
        ]);
    }

    /**
     * Show website dev page.
     * @return View
     */
    public function dev(): View
    {
        return view('web.dev')->with([
            'metaTitle' => 'Dev',
            'description' => '',
            'keywords' => '',
            'metaImage' => 'meta-default.jpg',
            'schema' => 1,
            'noIndex' => true
        ]);
    }
}
