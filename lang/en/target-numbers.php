<?php

return [

    /*
    |--------------------------------------------------------------------------
    | TargetNumber Language Lines
    |--------------------------------------------------------------------------
    */

    // General
    'title' => 'Target Number',
    'model' => 'target-number',

    // Actions
    'add' => 'Add new target number',

    // Attributes
    'phone' => 'Phone',
    'main-target-number' => 'Main Target Number',

    // Actions datatable
    'delete-question' => 'Once deleted, you will not be able to recover this target number.',

    // Form page
    'information' => 'Create Target Number',
    'information-description' => 'Here you can add target phone numbers related to you enterprise to receive incoming calls from your website.',
    'choice' => 'Choice main Target Number',
    'choice-description' => 'All the website calls will be redirected to the target phone number selected in here',

    // Labels
    'country' => 'Country',
    'note' => 'Note',
    'help-phone' => 'This phone number should be in an international format. If you don\'t know the dial code, try to find it in the list.',

    // Messages
    'created-success' => 'Target Number successfully created.',
    'created-error' => 'An error occurred while creating the target number.',
    'updated-success' => 'Target Number successfully updated',
    'updated-error' => 'An error occurred while updating the target number.',
    'deleted-success' => 'Target Number successfully deleted.',
    'deleted-error' => 'An error occurred while deleting the target number.',
    'non-existent' => 'Non-existent target number',
    'format-error' => 'The international phone number provided is invalid.'
];
