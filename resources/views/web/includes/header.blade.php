<header id="header" class="header-transparent header-semi-transparent header-semi-transparent-light" data-plugin-options="{'stickyEnabled': true, 'stickyEnableOnBoxed': true, 'stickyEnableOnMobile': false, 'stickyStartAt': 1, 'stickySetTop': '1'}">
    <div class="header-body border-0">
        <div class="header-container container">
            <div class="header-row">
                <div class="header-column">
                    <div class="header-row">
                        <div class="header-logo custom-header-logo">
                            <a href="{{ route('index') }}">
                                <img class="logo" alt="Logo Blanco" width="150" height="32" src="{{ asset('web/svg/logo-white.svg') }}">
                                <img class="logo-sticky" alt="Logo" width="170" height="36" src="{{ asset('web/svg/logo.svg') }}">
                            </a>
                        </div>
                    </div>
                </div>
                <div class="header-column justify-content-end">
                    <div class="header-row">
                        <div class="header-nav header-nav-links order-3 order-lg-1">
                            <div class="header-nav-main header-nav-main-square header-nav-main-text-capitalize header-nav-main-effect-1 header-nav-main-sub-effect-1">
                                <nav class="collapse px-3-5">
                                    <ul class="nav nav-pills" id="mainNav">
                                        <li>
                                            <a @class(['nav-link', 'active' => Request::url() == route('index')]) href="{{ route('index') }}">
                                                Inicio
                                            </a>
                                        </li>
                                        <li>
                                            <a @class(['nav-link', 'active' => Request::url() == route('company')]) href="{{ route('company') }}">
                                                Acerca de
                                            </a>
                                        </li>
                                        <li class="dropdown">
                                            <a @class(['nav-link dropdown-toggle',
                                                        'active' =>
                                                        Request::url() == url('renta-andamios-orizaba-veracruz') ||
                                                        Request::url() == url('venta-andamios-orizaba-veracruz') ||
                                                        Request::url() == url('maquinaria-ligera-renta-orizaba') ||
                                                        Request::url() == url('ferreteria-materiales-construccion-orizaba')
                                                        ])
                                                href="#">
                                                Servicios
                                            </a>
                                            <ul class="dropdown-menu">
                                                @foreach($services as $service)
                                                    <li><a class="dropdown-item" href="{{ url($service->url) }}">{{ $service->name }}</a></li>
                                                @endforeach
                                            </ul>
                                        </li>
                                        <li class="dropdown">
                                            <a @class(['nav-link dropdown-toggle',
                                                        'active' =>
                                                        Request::url() == url('andamios/andamio-estandar-convencional') ||
                                                        Request::url() == url('andamios/andamio-banquetero') ||
                                                        Request::url() == url('andamios/andamio-de-tunel-o-pasillo') ||
                                                        Request::url() == url('andamios/escalera-de-andamio') ||
                                                        Request::url() == url('andamios/barandal-de-andamio') ||
                                                        Request::url() == url('andamios/rueda-para-andamio') ||
                                                        Request::url() == url('andamios/crucetas-de-andamio-estandar')
                                                        ]) href="#">
                                                Andamios
                                            </a>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url('andamios/andamio-estandar-convencional') }}">Andamio Estándar</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/andamio-banquetero') }}">Andamio Banquetero</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/andamio-de-tunel-o-pasillo') }}">Andamio Pasillo</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/escalera-de-andamio') }}">Escalera de andamio</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/barandal-de-andamio') }}">Barandal de andamio</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/rueda-para-andamio') }}">Rueda para andamio</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/crucetas-de-andamio-estandar') }}">Cruceta estándar</a></li>
                                            </ul>
                                        </li>
                                        <li class="dropdown">
                                            <a @class(['nav-link dropdown-toggle',
                                                        'active' =>
                                                        Request::url() == url('andamios/andamio-para-cimbra') ||
                                                        Request::url() == url('andamios/andamio-cimbra-alta-resistencia') ||
                                                        Request::url() == url('andamios/viga-metalica') ||
                                                        Request::url() == url('andamios/puntales-metalicos')
                                                        ]) href="#">
                                                Cimbra
                                            </a>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url('andamios/andamio-para-cimbra') }}">Andamios para Cimbra</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/andamio-cimbra-alta-resistencia') }}">Andamios para Cimbra Alta Resistencia</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/viga-metalica') }}">Viga Metálica</a></li>
                                                <li><a class="dropdown-item" href="{{ url('andamios/puntales-metalicos') }}">Puntales metálicos</a></li>
                                            </ul>
                                        </li>
                                        <li>
                                            <a @class(['nav-link', 'active' => Request::url() == route('contact')]) href="{{ route('contact') }}">
                                                Contacto
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                            <button class="btn header-btn-collapse-nav" data-bs-toggle="collapse" data-bs-target=".header-nav-main nav">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                        <div class="header-nav-features header-nav-features-no-border header-nav-features-lg-show-border d-none d-sm-flex ms-3 order-1 order-lg-2">
                            <ul class="header-social-icons social-icons d-none d-sm-block social-icons-clean social-icons-medium ms-0">
                                <li class="social-icons-facebook"><a href="https://www.facebook.com/{{ $settings['facebook']->value }}" target="_blank" title="{{ $settings['website_name']->value }}"><i class="fab fa-facebook-f"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
