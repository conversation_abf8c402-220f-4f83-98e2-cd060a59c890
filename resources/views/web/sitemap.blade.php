@extends('layouts.web')

@section('schema')
    @include('web.includes.schema')
@endsection

@section('content')
    @include('web.includes.breadcrumbs', ['breadcrumbs' => 'Mapa del Sitio'])
    <section class="section bg-color-light position-relative border-0 pt-3 m-0">
        <div class="container py-4">
            <div class="row">
                <div class="col-lg-4">
                    <h3>Servicios</h3>
                    <ul>
                        @foreach($services as $service)
                            <li><span data-target="{{ url($service->url) }}" class="special-action">{{ $service->name }}</span></li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h3>Productos</h3>
                    <ul>
                        @foreach($products as $product)
                            <li><span data-target="{{ url($product->url) }}" class="special-action">{{ $product->name }}</span></li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h3>Otras páginas</h3>
                    <ul>
                        <li><span data-target="{{ route('company') }}" class="special-action">Nosotros</span></li>
                        <li><span data-target="{{ route('contact') }}" class="special-action">Contacto</span></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h3>Links de interés</h3>
                    <ul>
                        <li><span data-target="{{ route('privacy') }}" class="special-action">Aviso de privacidad</span></li>
                        <li><span data-target="{{ route('sitemap') }}" class="special-action">Mapa del sitio</span></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h3>Artículos de blog</h3>
                    <ul>
                        @foreach($posts as $post)
                            <li><span data-target="{{ route('single', $post->url) }}" class="special-action">{{ $post->title }}</span></li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </section>
@endsection
